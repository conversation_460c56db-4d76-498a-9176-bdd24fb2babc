package util

import (
	"encoding/json"
	"lui-api/internal/data/dto"
	"math/rand"
	"reflect"
	"strings"
	"time"
	"unicode"

	"github.com/google/uuid"
)

func InSliceString(needle string, slice []string) bool {
	if len(slice) == 0 {
		return false
	}
	for _, item := range slice {
		if item == needle {
			return true
		}
	}
	return false
}

func Marshal(i interface{}) string {
	str, _ := json.Marshal(i)
	return string(str)
}

func UniqueSlice[T comparable](slice []T) []T {
	if len(slice) < 2 {
		return slice
	}
	result := make([]T, 0, len(slice))
	tmpMap := map[T]bool{}
	for _, v := range slice {
		if _, ok := tmpMap[v]; !ok {
			tmpMap[v] = true
			result = append(result, v)
		}
	}

	return result
}

// SliceContainStr 函数检查字符串切片中是否包含特定的字符串
func SliceContainStr(slice []string, str string) bool {
	for _, item := range slice {
		if item == str {
			return true
		}
	}
	return false
}

func FilterChars(input string, charset string) string {
	result := ""

	for _, char := range input {
		if IsCharInCharset(char, charset) {
			result += string(char)
			return result
		}
	}
	return result
}

func IsCharInCharset(char rune, charset string) bool {
	for _, c := range charset {
		if c == char {
			return true
		}
	}
	return false
}

func NewUUID() string {
	return strings.ReplaceAll(uuid.New().String(), "-", "")
}

// ReverseArray 使用反射将任意类型的数组或切片倒序
func ReverseArray(arr interface{}) {
	val := reflect.ValueOf(arr)
	if val.Kind() != reflect.Slice && val.Kind() != reflect.Array {
		panic("reverseArray: not a slice or array")
	}

	n := val.Len()
	for i := 0; i < n/2; i++ {
		j := n - i - 1
		tmp := reflect.ValueOf(val.Index(i).Interface())
		val.Index(i).Set(val.Index(j))
		val.Index(j).Set(tmp)
	}
}

func RandomInt(n int) int {
	// 设置随机数生成器的种子
	rand.NewSource(time.Now().UnixNano())
	// 生成一个随机索引
	return rand.Intn(n)
}

func IsChinese(str string) bool {
	for _, v := range str {
		if unicode.Is(unicode.Han, v) {
			return true
		}
	}
	return false
}

// ConvertInt2Float 将整数切片转换为浮点数切片
func ConvertInt2Float[T ~int | ~int32](src []T) (dest []float64) {
	for _, v := range src {
		dest = append(dest, float64(v))
	}
	return
}

// ConvertCoordinate 转换坐标
func ConvertCoordinate(point []dto.PointInt) []int32 {
	result := make([]int32, 0, len(point)*2)

	sortPoints := SortPointsAsRectangle(point)
	for _, p := range sortPoints {
		result = append(result, int32(p.X), int32(p.Y))
	}
	return result
}

// SortPointsAsRectangle 按照左上、右上、右下、左下顺序排列坐标点
func SortPointsAsRectangle(points []dto.PointInt) []dto.PointInt {
	if len(points) != 4 {
		return nil
	}
	// 创建结果数组
	result := make([]dto.PointInt, 4)

	// 找出左上角 (x+y最小)
	topLeftIdx := 0
	topLeftSum := points[0].X + points[0].Y
	for i := 1; i < 4; i++ {
		if points[i].X+points[i].Y < topLeftSum {
			topLeftSum = points[i].X + points[i].Y
			topLeftIdx = i
		}
	}
	result[0] = points[topLeftIdx]

	// 找出右下角 (x+y最大)
	bottomRightIdx := 0
	bottomRightSum := points[0].X + points[0].Y
	for i := 1; i < 4; i++ {
		if points[i].X+points[i].Y > bottomRightSum {
			bottomRightSum = points[i].X + points[i].Y
			bottomRightIdx = i
		}
	}
	result[2] = points[bottomRightIdx]

	// 找出剩下的两个点
	remainingIndices := []int{}
	for i := 0; i < 4; i++ {
		if i != topLeftIdx && i != bottomRightIdx {
			remainingIndices = append(remainingIndices, i)
		}
	}

	// 根据 x-y 的值区分右上和左下
	// 右上角的 x-y 值较大，左下角的 x-y 值较小
	if points[remainingIndices[0]].X-points[remainingIndices[0]].Y >
		points[remainingIndices[1]].X-points[remainingIndices[1]].Y {
		// remainingIndices[0] 是右上角
		result[1] = points[remainingIndices[0]]
		result[3] = points[remainingIndices[1]]
	} else {
		// remainingIndices[1] 是右上角
		result[1] = points[remainingIndices[1]]
		result[3] = points[remainingIndices[0]]
	}

	return result
}

func ConvertPointsToPointInts(points []dto.Point) []dto.PointInt {
	pointInts := make([]dto.PointInt, len(points))
	for i, point := range points {
		pointInts[i] = dto.PointInt{
			X: int(point.X),
			Y: int(point.Y),
		}
	}
	return pointInts
}

// CoordinateTranslation 实现一个方法，将表示正方形的两个对角坐标转换为表示长方形的四个顶点坐标
// 输入：两个对角坐标，[1,2,3,4]。其中1,2是左上角的坐标，3,4是右下角的坐标
// 输出：四个顶点坐标，[1,2,3,2,3,4,1,4]
func CoordinateTranslation(coordinates []int) []int {
	// 请在此处实现代码
	if len(coordinates) != 4 {
		return []int{}
	}
	x1, y1, x2, y2 := coordinates[0], coordinates[1], coordinates[2], coordinates[3]
	return []int{x1, y1, x2, y1, x2, y2, x1, y2}
}
