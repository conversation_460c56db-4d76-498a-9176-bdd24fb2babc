package util

import (
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"hash/fnv"
	"strings"
)

func GetSHA256Hex(input string) string {
	// 创建 SHA256 哈希对象
	hash := sha256.New()

	// 将字符串转换为字节数组并计算哈希值
	hash.Write([]byte(input))

	// 计算摘要并转换为十六进制字符串
	sha256Sum := hash.Sum(nil)
	sha256Hex := hex.EncodeToString(sha256Sum)

	// 将字母转换为大写并返回结果
	return strings.ToUpper(sha256Hex)
}

func MD5(s string) string {
	sum := md5.Sum([]byte(s))
	return hex.EncodeToString(sum[:])
}

// HmacSha1Encode 使用HmacSha1计算签名
func HmacSha1Encode(secret string, query string) string {
	secret = secret + "&"
	key := []byte(secret)
	mac := hmac.New(sha1.New, key)
	mac.Write([]byte(query))
	query = base64.StdEncoding.EncodeToString(mac.Sum(nil))
	return query
}

func Hash(str string) (uint32, error) {
	// 创建一个新的FNV哈希实例
	hasher := fnv.New32a()
	// 写入字符串到哈希
	_, err := hasher.Write([]byte(str))
	if err != nil {
		fmt.Println("哈希写入错误:", err)
		return 0, err
	}
	// 计算哈希值
	hashed := hasher.Sum32()
	return hashed, nil
}
