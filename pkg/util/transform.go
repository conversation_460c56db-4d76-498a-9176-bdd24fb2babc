package util

import (
	"encoding/json"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/structpb"
	"reflect"
)

func ReplyAny(data interface{}) (*structpb.Struct, error) {

	resJson, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}
	response := &structpb.Struct{}
	err = protojson.Unmarshal(resJson, response)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func ConvertInterface(input interface{}, output interface{}) error {
	inputBytes, err := json.Marshal(input)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(inputBytes, &output); err != nil {
		return err
	}

	return nil
}

func StructToMap(input interface{}) map[string]string {
	result := make(map[string]string)

	val := reflect.ValueOf(input)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	typ := val.Type()

	for i := 0; i < val.NumField(); i++ {
		field := val.Field(i)
		fieldType := typ.Field(i)
		jsonTag := fieldType.Tag.Get("json")
		result[jsonTag] = field.String()
	}

	return result
}
