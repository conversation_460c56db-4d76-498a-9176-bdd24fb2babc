# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
paths:
    /v1/lui/add-llm-memory-context:
        post:
            tags:
                - LuiContentApi
            operationId: LuiContentApi_AddLLMMemoryContext
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/AddLLMMemoryContextRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/AddLLMMemoryContextReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/lui/ai-eye-image-judge:
        post:
            tags:
                - LuiContentApi
            description: 用户大模型Tails
            operationId: LuiContentApi_AIEyeFingerImageJudge
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/AIEyeFingerImageJudgeRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/lui/choice:
        post:
            tags:
                - LuiContentApi
            description: LUI资源 用户选择的版本 from学习系统
            operationId: LuiContentApi_LuiUserChoice
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/LuiUserChoiceRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LuiUserChoiceReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/lui/exit-lui-scene:
        post:
            tags:
                - LuiContentApi
            description: 用户主动退出某个场景
            operationId: LuiContentApi_ExitLuiScene
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ExitLuiSceneRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ExitLuiSceneReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/lui/full-view-context:
        post:
            tags:
                - LuiContentApi
            description: 全屏对话页上下文
            operationId: LuiContentApi_FullViewContext
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/FullViewContextRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/FullViewContextReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/lui/llm-memory-context:
        post:
            tags:
                - LuiContentApi
            operationId: LuiContentApi_GetLLMMemoryContext
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/LLMMemoryContextRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LLMMemoryContextReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/lui/memory-context:
        post:
            tags:
                - LuiContentApi
            description: 拒识接口上下文呢
            operationId: LuiContentApi_MemoryContext
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/MemoryContextRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/MemoryContextReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/lui/nlu:
        post:
            tags:
                - LuiContentApi
            description: nlu数据
            operationId: LuiContentApi_LuiNluResult
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/LuiNluRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/lui/nlu-lui:
        post:
            tags:
                - LuiContentApi
            description: 二次确认调用
            operationId: LuiContentApi_LuiNlu4Lui
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/LuiNluRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/lui/nlu-reject:
        post:
            tags:
                - LuiContentApi
            operationId: LuiContentApi_RejectNLU
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/LuiRejectRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/lui/simulate-query:
        post:
            tags:
                - LuiContentApi
            description: LuiSimulateQuery
            operationId: LuiContentApi_LuiSimulateQuery
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/LuiSimulateQueryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/lui/sug-lui:
        post:
            tags:
                - LuiContentApi
            description: sug点击调用
            operationId: LuiContentApi_LuiSug2Lui
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/LuiSugRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/lui/tools/choice:
        post:
            tags:
                - LuiContentApi
            description: LUI资源 用户选择的版本 from工具
            operationId: LuiContentApi_LuiToolsUserChoice
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/LuiToolsUserChoiceRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LuiToolsUserChoiceReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/lui/trace/feedback:
        post:
            tags:
                - LuiContentApi
            operationId: LuiContentApi_QueryTraceFeedback
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/QueryTraceFeedbackRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/QueryTraceFeedbackReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/lui/updateMemoryContextLlmResponse:
        post:
            tags:
                - LuiContentApi
            operationId: LuiContentApi_UpdateMemoryContextLlmResponse
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/LlmResponse'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/lui/user-info:
        get:
            tags:
                - UCenterApi
            description: 用户信息
            operationId: UCenterApi_LuiUserInfo
            parameters:
                - name: token
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LuiUserInfoReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/lui/user-llm-sug:
        post:
            tags:
                - LuiContentApi
            description: 用户大模型SUG
            operationId: LuiContentApi_LuiUserLlmSug
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/LuiUserLlmSugRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LuiUserLlmSugReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/lui/user-llm-tails:
        post:
            tags:
                - LuiContentApi
            description: 用户大模型Tails
            operationId: LuiContentApi_LuiUserLlmTails
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/LuiUserLlmTailsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/lui/zh-word-confirm:
        post:
            tags:
                - LuiContentApi
            description: 中文字词确认
            operationId: LuiContentApi_ZhWordConfirm
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/LuiNluConfirmRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        AIEyeFingerImageJudgeRequest:
            type: object
            properties:
                bizType:
                    type: integer
                    format: int32
                imageUrl:
                    type: string
        AddLLMMemoryContextReply:
            type: object
            properties: {}
        AddLLMMemoryContextRequest:
            type: object
            properties:
                talId:
                    type: string
                sessionId:
                    type: string
                messages:
                    type: array
                    items:
                        $ref: '#/components/schemas/AddLLMMemoryContextRequest_ContextItem'
                bizType:
                    type: string
        AddLLMMemoryContextRequest_ContextItem:
            type: object
            properties:
                role:
                    type: string
                content:
                    type: string
                dialogueId:
                    type: string
        ExitLuiSceneReply:
            type: object
            properties: {}
        ExitLuiSceneRequest:
            type: object
            properties:
                bizType:
                    type: string
                talId:
                    type: string
        FullViewContextReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/FullViewContextReply_FullViewContextItem'
        FullViewContextReply_CommandApp:
            type: object
            properties:
                name:
                    type: string
                icon:
                    type: string
        FullViewContextReply_FullViewContextItem:
            type: object
            properties:
                sessionId:
                    type: string
                asrInfo:
                    type: string
                response:
                    type: string
                ttsInfo:
                    type: string
                source:
                    type: string
                imageUrl:
                    type: string
                videoUrl:
                    type: string
                isLlm:
                    type: boolean
                cardType:
                    type: integer
                    format: int32
                media:
                    $ref: '#/components/schemas/FullViewContextReply_Media'
                xiaosiCommandList:
                    type: array
                    items:
                        $ref: '#/components/schemas/FullViewContextReply_XiaosiCommands'
                rnResponse:
                    type: object
                reasoningContent:
                    type: string
                ttsNorm:
                    type: string
        FullViewContextReply_Media:
            type: object
            properties:
                type:
                    type: integer
                    format: int32
                cover:
                    type: string
                url:
                    type: string
                coverTitle:
                    type: string
        FullViewContextReply_XiaosiCommands:
            type: object
            properties:
                title:
                    type: string
                query:
                    type: string
                apps:
                    type: array
                    items:
                        $ref: '#/components/schemas/FullViewContextReply_CommandApp'
        FullViewContextRequest:
            type: object
            properties:
                talId:
                    type: string
                bizType:
                    type: string
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        LLMMemoryContextReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/LLMMemoryContextReply_ContextItem'
        LLMMemoryContextReply_ContextItem:
            type: object
            properties:
                role:
                    type: string
                content:
                    type: string
                dialogueId:
                    type: string
        LLMMemoryContextRequest:
            type: object
            properties:
                bizType:
                    type: string
                currentSessionId:
                    type: string
                talId:
                    type: string
                needLlmSkill:
                    type: array
                    items:
                        type: string
                version:
                    type: string
        LlmAgent:
            type: object
            properties:
                name:
                    type: string
                status:
                    type: integer
                    format: int32
        LlmResponse:
            type: object
            properties:
                llmSkill:
                    type: string
                llmModel:
                    type: string
                response:
                    type: string
                imageUrl:
                    type: string
                videoUrl:
                    type: string
                llmResponse:
                    type: string
                dialogueId:
                    type: string
                sessionId:
                    type: string
                type:
                    type: integer
                    format: int32
                cover:
                    type: string
                coverTitle:
                    type: string
                url:
                    type: string
                ttsInfo:
                    type: string
                mixedModalQuery:
                    type: string
                mixedModalResponse:
                    type: object
                ttsNorm:
                    type: string
        LuiNluConfirmRequest:
            type: object
            properties:
                userId:
                    type: string
                requestId:
                    type: string
                sessionId:
                    type: string
                version:
                    type: string
                asrInput:
                    $ref: '#/components/schemas/LuiNluConfirmRequest_AsrInput'
                userSystem:
                    $ref: '#/components/schemas/LuiNluConfirmRequest_UserSystem'
                ocrInput:
                    $ref: '#/components/schemas/LuiNluConfirmRequest_OcrInput'
                handleInput:
                    $ref: '#/components/schemas/LuiNluConfirmRequest_HandleInput'
            description: 从paas-service的/api/ai/v1/nlu接口迁移过来的，参考paas-service的NluPadReq
        LuiNluConfirmRequest_AsrInput:
            type: object
            properties:
                asrLen:
                    type: integer
                    format: int32
                asrInfo:
                    type: string
                asrPinyin:
                    type: string
        LuiNluConfirmRequest_ConfirmRequest:
            type: object
            properties:
                confirmRequestIntent:
                    type: string
                confirmRequestInput:
                    type: string
                confirmRequestFinger:
                    type: string
                confirmRequestPinyin:
                    type: string
        LuiNluConfirmRequest_HandleInput:
            type: object
            properties:
                handleTaskType:
                    type: string
                isEngCorrect:
                    type: boolean
                confirmRequest:
                    $ref: '#/components/schemas/LuiNluConfirmRequest_ConfirmRequest'
        LuiNluConfirmRequest_OcrInput:
            type: object
            properties:
                ocrAlldocLen:
                    type: integer
                    format: int32
                ocrKeyLen:
                    type: integer
                    format: int32
                ocrAlldocInfo:
                    type: string
                ocrKeyInfo:
                    type: array
                    items:
                        type: string
                ocrKeywordInfo:
                    type: array
                    items:
                        type: string
                ocrKeyInfoOffset:
                    type: array
                    items:
                        type: integer
                        format: int32
        LuiNluConfirmRequest_UserSystem:
            type: object
            properties:
                location:
                    type: string
                grade:
                    type: integer
                    format: int32
                gender:
                    type: string
                nickname:
                    type: string
                semester:
                    type: string
                textbookVersion:
                    type: string
        LuiNluRequest:
            type: object
            properties:
                requestId:
                    type: string
                asrInfo:
                    type: string
                asrPinyin:
                    type: string
                location:
                    type: string
                grade:
                    type: string
                talId:
                    type: string
                slotFillList:
                    type: array
                    items:
                        $ref: '#/components/schemas/SlotFill'
                gradeId:
                    type: string
                bizType:
                    type: string
                continuous:
                    type: boolean
                sentenceId:
                    type: integer
                    format: int32
                wakeUpType:
                    type: string
                pagePrompt:
                    type: string
                sceneMode:
                    type: integer
                    format: int32
                llmAgent:
                    $ref: '#/components/schemas/LlmAgent'
                rnVersion:
                    type: string
                screenMode:
                    type: integer
                    format: int32
                imagePrompt:
                    type: array
                    items:
                        type: object
                bizExtra:
                    type: string
                from:
                    type: string
                asrStartTimestamp:
                    type: string
                asrEndTimestamp:
                    type: string
        LuiRejectRequest:
            type: object
            properties:
                requestId:
                    type: string
                deviceId:
                    type: string
                version:
                    type: string
                asrInfo:
                    type: string
                wakeupType:
                    type: string
                rejectResult:
                    type: integer
                    format: int32
                continuous:
                    type: boolean
                talId:
                    type: string
                bizType:
                    type: string
        LuiSimulateQueryRequest:
            type: object
            properties:
                asrInfo:
                    type: string
                asrPinyin:
                    type: string
                location:
                    type: string
                grade:
                    type: string
                gradeId:
                    type: string
                talId:
                    type: string
                bizType:
                    type: string
                slotFillList:
                    type: array
                    items:
                        $ref: '#/components/schemas/SlotFill'
        LuiSugRequest:
            type: object
            properties:
                userId:
                    type: string
                requestId:
                    type: string
                sessionId:
                    type: string
                timestamp:
                    type: string
                source:
                    type: string
                version:
                    type: string
                asrInput:
                    $ref: '#/components/schemas/LuiSugRequest_AsrInput'
                userSystem:
                    $ref: '#/components/schemas/LuiSugRequest_UserSystem'
                bizType:
                    type: string
                sceneMode:
                    type: integer
                    format: int32
                rnVersion:
                    type: string
                screenState:
                    type: string
                bizExtra:
                    type: string
                from:
                    type: string
                exerciseOcr:
                    type: string
        LuiSugRequest_AsrInput:
            type: object
            properties:
                asrLen:
                    type: integer
                    format: int32
                asrInfo:
                    type: string
                asrPinyin:
                    type: string
        LuiSugRequest_UserSystem:
            type: object
            properties:
                location:
                    type: string
                grade:
                    type: integer
                    format: int32
        LuiToolsUserChoiceReply:
            type: object
            properties:
                versionName:
                    type: string
        LuiToolsUserChoiceRequest:
            type: object
            properties:
                talId:
                    type: string
                subject:
                    type: integer
                    format: int32
        LuiUserChoiceReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/LuiUserChoiceReplyItem'
        LuiUserChoiceReplyItem:
            type: object
            properties:
                courseSystem:
                    type: integer
                    format: int32
                courseSystemAlias:
                    type: string
                version:
                    type: integer
                    format: int32
                versionName:
                    type: string
                subject:
                    type: integer
                    format: int32
                subjectName:
                    type: string
        LuiUserChoiceRequest:
            type: object
            properties:
                talId:
                    type: string
                courseSystem:
                    type: integer
                    format: int32
                subject:
                    type: integer
                    format: int32
                grade:
                    type: integer
                    format: int32
                deviceId:
                    type: string
        LuiUserInfoReply:
            type: object
            properties:
                talId:
                    type: string
                grade:
                    type: integer
                    format: int32
                gradeName:
                    type: string
                nickname:
                    type: string
        LuiUserLlmSugReply:
            type: object
            properties:
                userId:
                    type: string
                sug:
                    type: string
        LuiUserLlmSugRequest:
            type: object
            properties:
                userId:
                    type: string
        LuiUserLlmTailsRequest:
            type: object
            properties:
                userId:
                    type: string
                screenMode:
                    type: integer
                    format: int32
                bizType:
                    type: string
        MemoryContextReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/MemoryContextReply_MemoryContextItem'
        MemoryContextReply_MemoryContextItem:
            type: object
            properties:
                asrInfo:
                    type: string
                intent:
                    type: string
                response:
                    type: string
                ttsInfo:
                    type: string
                rewriteQuery:
                    type: string
                timeStamp:
                    type: string
                ttsNorm:
                    type: string
        MemoryContextRequest:
            type: object
            properties:
                talId:
                    type: string
                bizType:
                    type: string
        QueryTraceFeedbackReply:
            type: object
            properties: {}
        QueryTraceFeedbackRequest:
            type: object
            properties:
                sessionId:
                    type: string
                feedbackType:
                    type: integer
                    format: int32
                feedback:
                    type: string
                feedbackTypes:
                    type: array
                    items:
                        type: integer
                        format: int32
        SlotFill:
            type: object
            properties:
                key:
                    type: string
                id:
                    type: string
                name:
                    type: string
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
tags:
    - name: LuiContentApi
      description: The greeting service definition.
    - name: UCenterApi
