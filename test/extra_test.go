package test

import (
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	"github.com/google/uuid"
	"lui-api/internal/data/dto"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"testing"
	"time"
)

func TestA(t *testing.T) {

	resp, err := resty.New().OnBeforeRequest(func(client *resty.Client, r *resty.Request) error {
		client.SetRetryCount(1).
			SetTimeout(time.Second * time.Duration(4))
		return nil
	}).
		R().
		SetHeaders(map[string]string{}).
		SetBody(map[string]interface{}{}).
		Post("http://39.96.39.238:3000/mock/192/exactly/api/lui/id-list")

	if err != nil {
		fmt.Println(err)
	}
	if resp.IsError() {
		fmt.Println(resp.String())
	}

	var br dto.BaseResponse
	err = json.Unmarshal(resp.Body(), &br)

	jsonBytes, err := json.Marshal(br.Data)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	var data dto.SubjectClassesIdQueryResp
	err = json.Unmarshal(jsonBytes, &data)

	var dataExtra SubjectClassesIdQueryExtraResp
	courseExtras := make([]CourseExtra, 0)
	for _, course := range data.CourseList {

		courseBytes, err := json.Marshal(course)
		if err != nil {
			fmt.Println("Error:", err)
			return
		}

		var courseExtra CourseExtra
		err = json.Unmarshal(courseBytes, &courseExtra)

		courseExtra.Course = course
		courseExtra.Sort = 12
		courseExtras = append(courseExtras, courseExtra)
	}

	dataExtra.CourseList = courseExtras
	fmt.Println("dataExtra::::", dataExtra)
	//if v, ok := br.Data.(*dto.SubjectClassesIdQueryResp); ok {
	//
	//	fmt.Println("=====", v)
	//}

	fmt.Println("#####", data)
}

type SubjectClassesIdQueryResp struct {
	CourseList      []CourseExtra         `json:"course_list"`
	KnowledgePoints []KnowledgePointExtra `json:"knowledge_points"`
	LessonList      []LessonExtra         `json:"lesson_list"`
	PlanList        []PlanExtra           `json:"plan_list"`
	UnitList        []UnitExtra           `json:"unit_list"`
}

type Course interface{}
type KnowledgePoint interface{}
type Lesson interface{}
type Plan interface{}
type Unit interface{}

type CourseExtra struct {
	Course
	Sort int `json:"sort"`
}
type KnowledgePointExtra struct {
	KnowledgePoint

	Sort int `json:"sort"`
}
type LessonExtra struct {
	Lesson
	Sort int `json:"sort"`
}
type PlanExtra struct {
	Plan
	Sort int `json:"sort"`
}
type UnitExtra struct {
	Unit
	Sort int `json:"sort"`
}

type SubjectClassesIdQueryExtraResp struct {
	CourseList      []CourseExtra         `json:"course_list"`
	KnowledgePoints []KnowledgePointExtra `json:"knowledge_points"`
	LessonList      []LessonExtra         `json:"lesson_list"`
	PlanList        []PlanExtra           `json:"plan_list"`
	UnitList        []UnitExtra           `json:"unit_list"`
}

func TestB(t *testing.T) {
	courseMap := make(map[string]int)
	fmt.Println(courseMap["11"])
}

func TestC(t *testing.T) {
	s1 := dto.DefaultSlot{
		GradeName:    "s111",
		SemesterName: "s11111",
	}

	s2 := dto.SlotDict{
		Category:     "Science",
		GradeId:      0, // Zero value for int
		SchoolName:   "",
		SemesterName: "Fall",
		GradeName:    "",
		//ResourceName: "SSS",
	}

	// 合并 s1 到 s2
	result := mergeStructs(s1, s2)
	fmt.Printf("%+v\n", result)
}

func mergeStructs[T any](s1 interface{}, s2 T) T {
	s3 := s2 // 创建一个新的结构体 s3，初始值为 s2

	// 使用反射遍历 DefaultSlot 结构体字段
	s1Value := reflect.ValueOf(s1)
	s3Value := reflect.ValueOf(&s3).Elem()

	for i := 0; i < s1Value.NumField(); i++ {
		fieldName := s1Value.Type().Field(i).Name
		s1FieldValue := s1Value.Field(i)
		s3FieldValue := s3Value.FieldByName(fieldName)

		// 如果 s3 中的字段为零值且 s1 中的字段非零值，则将 s1 中的值赋给 s3
		if s3FieldValue.IsValid() && s3FieldValue.Interface() == reflect.Zero(s3FieldValue.Type()).Interface() && !s1FieldValue.IsZero() {
			s3FieldValue.Set(s1FieldValue)
		}
	}

	return s3
}

func TestName(t *testing.T) {
	b := []byte{123, 34, 99, 97, 116, 101, 103, 111, 114, 121, 34, 58, 49, 44, 34, 99, 111, 117, 114, 115, 101, 95, 105, 100, 115, 34, 58, 91, 123, 34, 116, 121, 112, 101, 34, 58, 34, 99, 111, 117, 114, 115, 101, 34, 44, 34, 105, 100, 34, 58, 34, 99, 111, 117, 114, 115, 101, 95, 54, 49, 97, 51, 99, 49, 50, 49, 54, 51, 102, 98, 51, 98, 102, 55, 54, 102, 97, 97, 100, 48, 50, 49, 48, 48, 97, 57, 98, 52, 49, 34, 125, 44, 123, 34, 116, 121, 112, 101, 34, 58, 34, 99, 111, 117, 114, 115, 101, 34, 44, 34, 105, 100, 34, 58, 34, 99, 111, 117, 114, 115, 101, 95, 48, 52, 52, 57, 51, 56, 52, 53, 53, 49, 52, 101, 52, 56, 101, 53, 101, 54, 49, 54, 99, 99, 98, 99, 48, 54, 101, 100, 100, 101, 54, 102, 34, 125, 44, 123, 34, 116, 121, 112, 101, 34, 58, 34, 99, 111, 117, 114, 115, 101, 34, 44, 34, 105, 100, 34, 58, 34, 99, 111, 117, 114, 115, 101, 95, 56, 98, 97, 53, 101, 98, 100, 100, 97, 49, 97, 101, 52, 54, 53, 51, 56, 102, 97, 52, 56, 49, 55, 98, 53, 48, 51, 49, 97, 52, 97, 34, 125, 44, 123, 34, 116, 121, 112, 101, 34, 58, 34, 99, 111, 117, 114, 115, 101, 34, 44, 34, 105, 100, 34, 58, 34, 99, 111, 117, 114, 115, 101, 95, 101, 99, 48, 100, 53, 56, 101, 99, 98, 100, 48, 54, 98, 48, 56, 57, 49, 99, 56, 102, 53, 53, 54, 56, 55, 50, 52, 56, 98, 55, 98, 34, 125, 44, 123, 34, 116, 121, 112, 101, 34, 58, 34, 99, 111, 117, 114, 115, 101, 34, 44, 34, 105, 100, 34, 58, 34, 99, 111, 117, 114, 115, 101, 95, 57, 97, 54, 54, 53, 53, 57, 101, 56, 98, 102, 52, 100, 53, 52, 52, 57, 55, 99, 55, 99, 53, 56, 51, 50, 48, 52, 52, 100, 50, 99, 34, 125, 44, 123, 34, 116, 121, 112, 101, 34, 58, 34, 99, 111, 117, 114, 115, 101, 34, 44, 34, 105, 100, 34, 58, 34, 99, 111, 117, 114, 115, 101, 95, 49, 55, 52, 50, 99, 102, 98, 100, 57, 98, 98, 56, 54, 50, 98, 57, 102, 101, 57, 102, 54, 54, 102, 98, 48, 102, 54, 51, 52, 97, 99, 34, 125, 44, 123, 34, 116, 121, 112, 101, 34, 58, 34, 99, 111, 117, 114, 115, 101, 34, 44, 34, 105, 100, 34, 58, 34, 99, 111, 117, 114, 115, 101, 95, 57, 97, 55, 51, 100, 48, 54, 97, 54, 97, 53, 48, 100, 55, 101, 97, 101, 100, 99, 48, 100, 53, 102, 53, 51, 97, 54, 97, 48, 54, 102, 34, 125, 44, 123, 34, 116, 121, 112, 101, 34, 58, 34, 99, 111, 117, 114, 115, 101, 34, 44, 34, 105, 100, 34, 58, 34, 99, 111, 117, 114, 115, 101, 95, 56, 101, 56, 51, 97, 57, 100, 55, 100, 100, 48, 99, 102, 52, 48, 101, 54, 51, 54, 100, 98, 51, 55, 98, 101, 97, 53, 100, 101, 101, 56, 34, 125, 44, 123, 34, 116, 121, 112, 101, 34, 58, 34, 99, 111, 117, 114, 115, 101, 34, 44, 34, 105, 100, 34, 58, 34, 99, 111, 117, 114, 115, 101, 95, 51, 54, 101, 51, 100, 49, 98, 99, 50, 56, 56, 49, 50, 51, 56, 102, 52, 48, 101, 56, 101, 98, 54, 56, 50, 56, 57, 49, 99, 55, 54, 34, 125, 44, 123, 34, 116, 121, 112, 101, 34, 58, 34, 99, 111, 117, 114, 115, 101, 34, 44, 34, 105, 100, 34, 58, 34, 99, 111, 117, 114, 115, 101, 95, 98, 52, 50, 52, 51, 100, 54, 52, 97, 97, 52, 50, 101, 97, 51, 48, 101, 54, 49, 99, 101, 52, 48, 53, 97, 98, 49, 101, 57, 56, 101, 34, 125, 44, 123, 34, 116, 121, 112, 101, 34, 58, 34, 99, 111, 117, 114, 115, 101, 34, 44, 34, 105, 100, 34, 58, 34, 99, 111, 117, 114, 115, 101, 95, 101, 49, 49, 56, 50, 101, 97, 52, 54, 101, 57, 55, 55, 49, 49, 54, 99, 48, 56, 57, 53, 100, 53, 49, 101, 55, 50, 99, 55, 51, 52, 34, 125, 44, 123, 34, 116, 121, 112, 101, 34, 58, 34, 99, 111, 117, 114, 115, 101, 34, 44, 34, 105, 100, 34, 58, 34, 99, 111, 117, 114, 115, 101, 95, 102, 51, 100, 99, 49, 52, 100, 57, 50, 56, 52, 53, 54, 102, 51, 102, 102, 55, 102, 54, 55, 50, 102, 57, 56, 56, 50, 53, 101, 53, 54, 34, 125, 44, 123, 34, 116, 121, 112, 101, 34, 58, 34, 99, 111, 117, 114, 115, 101, 34, 44, 34, 105, 100, 34, 58, 34, 99, 111, 117, 114, 115, 101, 95, 97, 53, 57, 48, 99, 49, 102, 48, 57, 53, 49, 52, 99, 98, 97, 56, 50, 51, 57, 98, 54, 49, 56, 100, 97, 52, 49, 98, 53, 56, 48, 34, 125, 93, 44, 34, 115, 117, 98, 106, 101, 99, 116, 95, 99, 108, 97, 115, 115, 101, 115, 95, 105, 100, 115, 34, 58, 91, 93, 44, 34, 103, 114, 97, 100, 101, 34, 58, 48, 44, 34, 115, 101, 109, 101, 115, 116, 101, 114, 34, 58, 48, 44, 34, 115, 117, 98, 106, 101, 99, 116, 34, 58, 48, 44, 34, 118, 101, 114, 115, 105, 111, 110, 34, 58, 48, 44, 34, 116, 105, 109, 101, 95, 115, 116, 97, 109, 112, 34, 58, 49, 54, 57, 51, 57, 56, 54, 55, 49, 51, 44, 34, 115, 105, 103, 110, 34, 58, 34, 54, 51, 56, 48, 98, 48, 56, 101, 54, 101, 102, 56, 97, 97, 100, 49, 100, 99, 100, 102, 98, 99, 57, 57, 57, 50, 57, 49, 48, 51, 99, 102, 34, 125}
	fmt.Println(string(b))
}

func TestUUID(t *testing.T) {
	go func() {
		fmt.Println(uuid.New().String())
	}()
	go func() {
		fmt.Println(uuid.New().String())
	}()
	time.Sleep(time.Second)

	id, _ := uuid.NewUUID()
	fmt.Println(id)
}

func TestResty(t *testing.T) {
	resty.New().OnBeforeRequest(func(client *resty.Client, r *resty.Request) error {
		client.SetRetryCount(1).
			SetTimeout(time.Second * time.Duration(10)).
			SetHeaders(map[string]string{
				"Content-Type": "application/json",
			})
		return nil
	})
}

func TestUnicodeString(t *testing.T) {
	text := "\\u5927\\u5bb6\\u90fd\\u53eb\\u6211\\u5c0f\\u601d\\u3002"
	uniText, _ := strconv.Unquote(strings.Replace(strconv.Quote(text), `\\u`, `\u`, -1))

	fmt.Println(uniText)
}

func TestStringBuilder(t *testing.T) {
	ssb := strings.Builder{}
	ssb.WriteString("hehehehe")
	ssb.WriteString("\t")
	ssb.WriteString("hahahaha")
	fmt.Println(ssb.String())

	fmt.Println(splitChineseText("今天天气"))
}

func splitChineseText(text string) []string {
	// 使用正则表达式匹配中文句子结束的标点符号以及换行符
	re := regexp.MustCompile(`。|？|！|；|，|：|“|”|‘|’|、|（|）|【|】|……|\n`)
	// 找到所有匹配的标点符号位置
	indexes := re.FindAllStringIndex(text, -1)

	lastStart := 0
	var sentences []string
	for _, loc := range indexes {
		// loc[1] 是匹配到的标点符号的结束位置，我们需要包括这个标点符号
		sentence := text[lastStart:loc[1]]
		if sentence != "" {
			sentences = append(sentences, sentence)
		}
		lastStart = loc[1]
	}
	// 检查是否有剩余的文本没有结束的标点符号
	if lastStart < len(text) {
		sentences = append(sentences, text[lastStart:])
	}
	return sentences
}

func TestJson2NLUQueryPad2Resp(t *testing.T) {
	resp := dto.NLUQueryPad2Resp{}
	str := `{"scene_result":{}}`
	json.Unmarshal([]byte(str), &resp)
	fmt.Println(resp)
}
