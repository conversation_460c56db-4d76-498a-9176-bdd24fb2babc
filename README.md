# Kratos Project Template

# min golang version >= 1.18.0

## Install Kratos

```
go get -u git.100tal.com/aielearning_hmi_golang/kratos/cmd/kratos/v2@latest
```

## Create a service

```
# 创建项目模板
kratos new helloworld

cd helloworld
# 拉取项目依赖
go mod download
# 下载wire cmd
go get github.com/google/wire/cmd/wire@v0.5.0
# 修改configs 
config.yaml内
mysql和redis链接地址
# 生成proto模板
kratos proto add api/helloworld/helloworld.proto
# 生成proto源码
kratos proto client api/helloworld/helloworld.proto
# 生成server模板
kratos proto server api/helloworld/helloworld.proto -t internal/service

# 生成所有proto源码、wire等等
go generate ./...

# 运行程序
kratos run(不使用nacos配置中心,使用本地配置文件)
# name: 可忽略，默认取main.go内的Name字段，请不要随意修改，会影响服务发现和服务注册,
# env: local/develop/test/gray/online
go run cmd/server/main.go cmd/server/wire_gen.go -env=xx -name=xx(可忽略，默认取main内的Name)
```

## 客户端error msg处理方式

```
修改configs/config.yaml error相关即可覆盖，只需要bff层处理error,service层无需关注

error: #强制更改api级别error错误到客户端(仅BFF修改即可，service服务无需更改)
  default: "服务错误" #通用错误术语
  handle:
    "GET#/api/v1/demo": #特殊api 特殊error_reason错误术语提示，只需添加配置即可实时生效
      error_messages:
        - error_reason: "error_01"
          message: "哎呀1"
        - error_reason: "error_11"
          message: "嗨1"
    "POST#/api/v1/demo2":
      error_messages:
        - error_reason: "error_02"
          message: "changed"
        - error_reason: "error_1"
          message: "嗨"
```

## Generate other auxiliary files by Makefile

```
# Download and update dependencies
make init
# Generate API files (include: pb.go, http, grpc, validate, swagger) by proto file
make api
# Generate all files
make all
```

## Automated Initialization (wire)

```
# install wire
go get github.com/google/wire/cmd/wire

# generate wire
cd cmd/server
wire
```

## Docker

```bash
# build
docker build -t <your-docker-image-name> .

# run
docker run --rm -p 8000:8000 -p 9000:9000 -v </path/to/your/configs>:/data/conf <your-docker-image-name>
```

## OpenTelemetry [阿里云trace](https://sls.console.aliyun.com/lognext/trace)

[手动埋点refer](https://help.aliyun.com/document_detail/208902.htm?spm=a2c4g.11186623.0.0.5bb228f7qKTVIA#task-2058210)

## 配置管理

> 切换项目组：集团前台-智能学习产品二部-工程研发后端组，找@段超 @赵敏 @孙卓标 @邓超 @朱战平开通相应权限

* 本地环境： 默认取configs目录
* [开发环境 dev](https://cloud-test.tal.com/tcm-fe#/configlist)
* [测试环境 test](https://cloud-test.tal.com/tcm-fe#/configlist)
* [灰度环境 beta](https://cloud.tal.com/tcm-fe#/configlist)
* [线上环境 prod](https://cloud.tal.com/tcm-fe#/configlist)

## 服务发现 && 注册

* [本地环境 local](http://**************:8848/nacos/#/subscriberList?dataId=&group=&appName=&namespace=genie-service-local&namespaceShowName=genie-service-local)
* [开发环境 develop](http://**************:8848/nacos/#/serviceManagement?dataId=&group=&appName=&namespace=dev-tipaipai&namespaceShowName=dev)
* [测试环境 test](http://**************:8848/nacos/#/serviceManagement?dataId=&group=&appName=&namespace=genie-service-test&namespaceShowName=genie-service-test)
* [灰度环境 gray](http://*************:8848/nacos/#/serviceManagement?serverId=center&group=&dataId=&namespace=genie-service-gray&appName=&namespaceShowName=genie-service-gray)
* [线上环境 online](http://*************:8848/nacos/#/serviceManagement?serverId=center&group=&dataId=&namespace=genie-service&appName=&namespaceShowName=genie-service)

// Mock
if strings.Contains(req.AsrInfo, "课程测试") {
nlu4lui.Skill = common.SearchCourse.ToString()

mockData := `{ "module_name": "resource_retrival", "is_valid_widget": true, "result_confidence": 1, "need_confirm": false, "skill": "search_course", "category": 1, "slot_dict": { "subject_id": "2", "subject_name": "数学", "semester_id": "2", "semester_name": "下学期", "version_id": "2", "version_name": "沪教版", "grade_id": "5", "grade_name": "五年级", "course_system_id": "11111", "course_system_name": "提升A" }, "word": [ "命中1", "命中2" ], "resource_list": [ { "resource_type": "course", "resource_id": "course_baa558ef29d387500e961ba3ac7beb14" }, { "resource_type": "course", "resource_id": "course_d2977feb7963d0546a8e702faf3cad2f" }, { "resource_type": "jzx_course", "resource_id": "7:6:43:5" }, { "resource_type": "jzx_course", "resource_id": "7:2:43:6" }, { "resource_type": "course", "resource_id": "course_5618e345b42d86842c090e84e78f37a8" }, { "resource_type": "course", "resource_id": "course_a2cad47e01c02e6fbe878a003236abb1" }, { "resource_type": "course", "resource_id": "course_9bf7217d892d5f9ac29ac8bc2d260e5" }, { "resource_type": "course", "resource_id": "course_d642de2f3e9ea91c153512f0fd68a1cb" }, { "resource_type": "knowledge", "course_id": "course_26763e17f22d23a4503523ab170fd8c2", "resource_id": "know_5b3284dafde38b4cccf23d3133396566" }, { "resource_type": "sub_knowledge", "course_id": "course_26763e17f22d23a4503523ab170fd8c2", "resource_id": "know_81a9fb53a3de7d116074a0a232f360e0" }, { "resource_type": "plan", "course_id": "course_683cf5739e6eafa787affa6b6931c43", "resource_id": "plan_6684ddbb864634a3c4645b1801ceb544" }, { "resource_type": "lesson", "course_id": "course_202fe9b5bfd2824e2c9a4bb28c341b16", "resource_id": "lesson_43b4be20d6d0262b3900e27aec089b5b" }, { "resource_type": "unit", "course_id": "course_ae6301e79d261ad89738b8f84ac17487", "resource_id": "unit_f4992f1dc010e6f6237d2bced0fb1d37" }, { "resource_type": "jzx", "course_id": "7:6:55:6", "resource_id": "2351" }, { "resource_type": "jzx", "course_id": "7:6:55:6", "resource_id": "2352" } ] }`
_ = json.Unmarshal([]byte(mockData), &nlu4lui)

} else if strings.Contains(req.AsrInfo, "试卷测试") {
mockData := `{ "module_name": "resource_retrival", "is_valid_widget": true, "result_confidence": 1, "need_confirm": false, "skill": "search_course", "category": 1, "slot_dict": { "subject_id": "2", "subject_name": "数学", "semester_id": "2", "semester_name": "下学期", "version_id": "2", "version_name": "沪教版", "grade_id": "5", "grade_name": "五年级", "course_system_id": "11111", "course_system_name": "提升A" }, "word": [ "命中1", "命中2" ], "resource_list": [ { "resource_type": "paper", "resource_id": "paper_cbf0401ebc1b43dc30514c50eae65e7c" }, { "resource_type": "paper", "resource_id": "paper_85c172631e55757066f195918d644363" } ] }`
_ = json.Unmarshal([]byte(mockData), &nlu4lui)
nlu4lui.Skill = common.SearchPaper.ToString()
} else if strings.Contains(req.AsrInfo, "确认") {
mockData := `{ "module_name": "resource_retrival", "is_valid_widget": true, "result_confidence": 1, "need_confirm": false, "skill": "search_course", "category": 1, "slot_dict": { "subject_id": "2", "subject_name": "数学", "semester_id": "2", "semester_name": "下学期", "version_id": "2", "version_name": "沪教版", "grade_id": "5", "grade_name": "五年级", "course_system_id": "11111", "course_system_name": "提升A" }, "word": [ "命中1", "命中2" ], "resource_list": [ { "resource_type": "course", "resource_id": "course_baa558ef29d387500e961ba3ac7beb14" } ] }`
_ = json.Unmarshal([]byte(mockData), &nlu4lui)
nlu4lui.Skill = common.PaperSubjectConfirm.ToString()
}
