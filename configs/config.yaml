server:
  http:
    addr: 0.0.0.0:8100
    timeout: 180s
  grpc:
    addr: 0.0.0.0:9100
    timeout: 10s
data:
  database:
    driver: mysql
    source: lamp_rw:p6jYjyoOEsJXhOBp@tcp(rm-2zec97l8f8vf9hl2l.mysql.rds.aliyuncs.com)/xpp_growth?timeout=5s&readTimeout=2s&writeTimeout=2s&parseTime=true&loc=Local&charset=utf8mb4,utf8
  database2:
    driver: mysql
    source: lamp_rw:p6jYjyoOEsJXhOBp@tcp(rm-2zec97l8f8vf9hl2lco.mysql.rds.aliyuncs.com)/xpp_growth?timeout=5s&readTimeout=2s&writeTimeout=2s&parseTime=true&loc=Local&charset=utf8mb4,utf8
  redis:
    network: tcp
    addr: r-2zeg988izwyy63jzhx.redis.rds.aliyuncs.com:6379
    password: 820PurringMinutes
    db: 0
    dial_timeout: 1s
    read_timeout: 1s
    write_timeout: 0.9s
  embedding:
    db:
      database: "lui"
    tencent:
      url: "http://**********:80"
      token: "Bearer account=root&api_key=Zb9HUG6qgbip1tLxmHFiaiu8xU5pGS8z3XDxNUBF"
    db_faq:
      database: "xiaosi"
      collection: "faq"
      faq_enable: true
      faq_threshold: 0.93
      url: "http://**********:80"
      token: "Bearer account=root&api_key=Zb9HUG6qgbip1tLxmHFiaiu8xU5pGS8z3XDxNUBF"
    db_hotfix:
      database: "xiaosi"
      collection: "hotfix_qa_test"
      enable: true
      threshold: 0.99
      faq_threshold: 0.93
      url: "http://**********:80"
      token: "Bearer account=root&api_key=Zb9HUG6qgbip1tLxmHFiaiu8xU5pGS8z3XDxNUBF"
  query_kafka:
    topic: "ai-query-log"
    group_id: "analysis"
    bootstrap_servers: "alikafka-pre-cn-7pp2i1wgu005-1-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-7pp2i1wgu005-2-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-7pp2i1wgu005-3-vpc.alikafka.aliyuncs.com:9092"
    security_protocol: "plaintext"
    ssl_ca_location: ""
    sasl_mechanism: "PLAIN"
    sasl_username: "alikafka_pre-cn-7pp2i1wgu005"
    sasl_password: "uR75oEBKQz38Az3EabqOQe3tIlftMadf"
    auto_ack: 1
    max_message_bytes: *********
  query_kafka_list:
    - topic: "ai-query-log"
      bootstrap_servers: "alikafka-pre-cn-7pp2i1wgu005-1-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-7pp2i1wgu005-2-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-7pp2i1wgu005-3-vpc.alikafka.aliyuncs.com:9092"
      security_protocol: "plaintext"
      ssl_ca_location: ""
      sasl_mechanism: "PLAIN"
      sasl_username: "alikafka_pre-cn-7pp2i1wgu005"
      sasl_password: "uR75oEBKQz38Az3EabqOQe3tIlftMadf"
      auto_ack: 1
      max_message_bytes: *********
    - topic: "ai-query-log"
      bootstrap_servers: "alikafka-pre-cn-0hz3pq4ev001-1-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-0hz3pq4ev001-2-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-0hz3pq4ev001-3-vpc.alikafka.aliyuncs.com:9092"
      security_protocol: "plaintext"
      ssl_ca_location: ""
      sasl_mechanism: "PLAIN"
      sasl_username: "alikafka_pre-cn-st21yi4pf00e"
      sasl_password: "1Ata53cazHxlEMjrHvm20w8PTRFynXbe"
      auto_ack: 1
      max_message_bytes: *********

log:
  filename: "/home/<USER>/xeslog/lui-api.log"
  max_size: 1
  max_backup: 5
  max_age: 10
  compress: true

error: #强制更改api级别error错误到客户端(仅BFF修改即可，service服务无需更改)
  default: "服务错误" #通用错误术语
  handle:
    "GET#/api/v1/demo": #特殊api 特殊error_reason错误术语提示，只需添加配置即可实时生效
      error_messages:
        - error_reason: "error_01"
          message: "哎呀1"
        - error_reason: "error_11"
          message: "嗨1"
    "POST#/api/v1/demo2":
      error_messages:
        - error_reason: "error_02"
          message: "changed"
        - error_reason: "error_1"
          message: "嗨"
#接口验签
sign:
  sign_secrets:
    100021: "d625392693a3a09def783bf55af2459c"
    200038: "bd12b658d35e4d9b9e1fb4de12c84ae1"
    200067: "f843e94eccca49fa9b2e2d4c4a7d365c" #平板lite
    200073: "d82be6c9c0d74596ad39504f6caaeed5" #一代平板2.0
    200076: "22dc1850eae44fa883abbef7ff997ee5"
    200078: "2f6218c93ff045ee90a7cd3dc1596537"
    200083: "116374258d1148b09c2d410a898a288e" #学练机25
    200007: "d111794531364408ab649cee09e13f37" #SpeakerDemo
    200087: "b0d8296469ba4a1590c24ffc06461e8c" #旗舰25
    200088: "bc79719ba11c4aacbf673e526c032c71" #经典25
    200089: "dc65981265f44b1190a126b5ba56ce96" #平价25

white:
  status: 0
  list: "71DC0522A2000071,"

white_os_version:
  status: 1
  list: "D231103,T231103,D231104,T231104,D231105,T231105,D231106,T231106,T231024,D110402,"
  sn_list : "7DD1092422300386,76DD0823A2400065,76DE0823B1400081,78DD0823A2100175,76DD0823A2400066,78PE0823C0100007,76DD0823A2400045,78DD0823A2100169,76DD082391300046,76DD0823A2400067,76DD0823A2400066,78DD0823A2100098,78DD082391400116,78DD0823A2100179,78DD0823A2100154,78DD0823A2100112,78PE0823C0100007,78DD082391400024,78DD082391500023,78PE0823C0100014,78PE0823C0100004,78DD0823A2100106,76DD0823A2400049,76DD0823A2400046,76DD0823A2400044,76DD082391300085,76DD0823A2400052,76DD0823A2400048,78DD0823A2100188,76DD0823A2400052,78PE0823C0100014,78DD0823A2100242,76DE0823B1600011,78DE0623B1300017,78DD0823A2100247,76DE0823B1600077,76DE0823B1600024,78DD0823A2100221,78DD0823A2100158,76DE0823B1600005,76DE0823B1600003,76DE0823B1500090,76DE0823B1500075,76DE0823B1500111,76DE0823B1500064,76DE0823B1500078,76DE0823B1500066,76DE0823B1500065,76DE0823B1500091,76DE0823B1500084,76DE0823B1500058,76DE0823B1500112,76DE0823B1500094,76DE0823B1500076,76DE0823B1500098,76DE0823B1500095,76DE0823B1500052,76PE0823C0400092,76PE0823C0400083,76PE0823C0400108,76PE0823C0400087,76PE0823C0400077,76PE0823C0400095,76PE0823C0400086,76PE0823C0400106,76PE0823C0400078,76PE0823C0400085,76PE0823C0400093,76PE0823C0400096,76PE0823C0400076,76PE0823C0400099,76PE0823C0400088,76PE0823C0400098,76PE0823C0400094,76PE0823C0400089,76PE0823C0400074,76PE0823C0400075,76PE0823C0400071,76PE0823C0400091,76PE0823C0400079,76PE0823C0400081,76PE0823C0400097,76PE0823C0400082,76PE0823C0400084,76PE0823C0400107,76PE0823C0400080,78DE0823B1100058,78DE0823B1100042,78DE0823B1100048,78DE0823B1100063,78DE0823B1100019,78DE0823B1100073,78DE0823B1100046,78DE0823B1100066,78DE0823B1100056,78DE0823B1100003,78DE0823B1100051,78DE0823B1100087,78DE0823B1100037,78DE0823B1100071,78DE0823B1100031,78DE0823B1100028,78DE0823B1100034,78DE0823B1100076,78DE0823B1100022,78DE0823B1100095,78DE0823B1100008,78DE0823B1100004,78DE0823B1100012,78DE0823B1100043,78DE0823B1100050,78DE0823B1100061,78DE0823B1100038,78DE0823B1100010,78DE0823B1100090,78DE0823B1100013,78DE0823B1100062,78DE0823B1100057,78DE0823B1100011,78DE0823B1100085,78DE0823B1100074,78DE0823B1100091,78DE0823B1100025,78DE0823B1100036,78DE0823B1100072,78DE0823B1100035,78DE0823B1100027,78DE0823B1100082,78DE0823B1100002,78DE0823B1100065,78DE0823B1100081,78DE0823B1100084,78DE0823B1100029,78DE0823B1100015,78DE0823B1100097,78DE0823B1100049,78DE0823B1100099,78DE0823B1100069,78DE0823B1100033,78DE0823B1100041,78DE0823B1100032,78DE0823B1100075,78DE0823B1100055,78DE0823B1100064,78DE0823B1100007,78DE0823B1100024,78DE0823B1100059,78DE0823B1100020,78DE0823B1100086,78DE0823B1100068,78DE0823B1100098,78DE0823B1100014,78DE0823B1100094,78DE0823B1100026,78DE0823B1100039,78DE0823B1100018,78DE0823B1100070,78DE0823B1100030,78DE0823B1100092,78DE0823B1100080,78PE0823B2900025,78PE0823B2900033,78PE0823B2900035,78PE0823B2800154,78PE0823B2800249,78PE0823B2800234,78PE0823B2900043,78PE0823B2900041,78PE0823B2900042,78PE0823B2800157,78PE0823B2800012,78PE0823B2800246,78PE0823B2800262,78PE0823B2800256,78PE0823B2800001,78PE0823B2900003,78PE0823B2800144,78PE0823B2800167,78PE0823B2800129,78PE0823B2900022,78PE0823B2900040,78PE0823B2900039,78PE0823B2800059,78PE0823B2900027,78PE0823B2800244,78PE0823B2800286,78PE0823B2800141,78PE0823B2800194,78PE0823B2800025,78PE0823B2800004,78PE0823B2800252,78PE0823B2900017,78PE0823B2800260,78PE0823B2800269,78PE0823B2800242,78PE0823B2800010,78PE0823B2800253,78PE0823B2900032,78PE0823B2900044,78PE0823B2800143,78PE0823B2800178,78PE0823B2800197,78PE0823B2800266,78PE0823B2800161,78PE0823B2800221,78PE0823B2800218,78PE0823B2800201,78PE0823B2800267,78PE0823B2800275,78PE0823B2900028,78PE0823B2900002,78PE0823B2800243,78PE0823B2800232,78PE0823B2800205,78PE0823B2900011,78PE0823B2900014,78PE0823B2800285,78PE0823B2900038,78PE0823B2800149,78PE0823B2900036,78PE0823B2900006,78PE0823B2900004,78PE0823B2800261,78PE0823B2800240,78PE0823B2900037,78PE0823B2900024,78PE0823B2800170,78PE0823B2800185,78PE0823B2800052,78PE0823B2800214,78PE0823B3000016,78PE0823B3000031,78PE0823B3000030,78PE0823B3000034,78PE0823B3000058,78PE0823B3000052,78PE0823B3000061,78PE0823B3000057,78PE0823B3000007,78PE0823B3000062,78PE0823B3000019,78PE0823B3000043,78PE0823B3000044,78PE0823B3000042,78PE0823B3000060,78PE0823B3000054,78PE0823B3000008,78PE0823B3000050,78PE0823B3000025,78PE0823B3000024,78PE0823B3000053,78PE0823B3000037,78PE0823B3000036,78PE0823B3000065,78PE0823B3000009,78PE0823B3000018,78PE0823B3000041,78PE0823B3000045,78PE0823B3000015,78PE0823B2900009,76DE0823B1500080,76DE0823B1500081,"
  zx_sn_list : "78DD082391500010,78PE0823B3000019,7DD1092422300343,78DD082391500021,76DD0823A2400065"
third:
  app_id_map:
    xueLianJiAppId: "200076" #学练机
    xueLianJi25AppId: "200083" #学练机25
    xueLianJiAppIds: "200076,200083"
    xiaosi2AppId: "200038,200076,200067,200078,200083,200087,200088,200089" #小思对话2.0应用ID
    xinruiAPPId: "200078"
    xPad2AppId_qijian: "200038"
    xPad2LiteAppId: "200067"
    xPad1V2AppId: "200073"
    xPad2AppId_qijian25: "200087" #旗舰25
    xPad2AppId_classic25: "200088" #经典25
    xPad2AppId_pingjia25: "200089" #平价25
    xPad25AppIds: "200087,200088"
    filtersAppIds : "200038,200089,200088,200087"
  home_work_doudi:
    - "我们要先完成今天的作业哦～"
    - "我可以帮你一起解决作业中的问题哦～"
    - "你可以问我“小思小思，这个字什么意思”"
    - "你可以问我“小思小思，帮我检查作业”"
    - "让我们先完成今天的作业吧～"
  proxy_url: "http://hmi.chengjiukehu.com/paas-proxy/v1/paas/api"
  #  proxy_url: "https://genie.vdyoo.net/paas-proxy/v1/paas/api "
  nlu_origin: "NLU4Skill"
  nlu4lui_origin: "NLU4LUI"
  nlu4lui_list_origin: "NLU4SkillList"
  ocr_origin: "GroupOcr"
  math_gpt_ask_classify_origin: "MathGPTAskClassify"
  resource_ids_url: "https://test-baodian.tal.com/exactly/api/v1/lui/id-list"
  resource_slot_url: "https://test-baodian.tal.com/exactly/api/v1/lui/scheme"
  resource_choice_url: "https://test-baodian.tal.com/exactly/api/v1/lui/user/chose"
  token_parse_url: "https://internal-test-api.edstars.com.cn/api/v1/user/token/parse"
  user_profile_url: "https://internal-test-api.edstars.com.cn/api/v1/user/profile"
  tools_url: "http://data-service-test-svc.jituan-genie-server/api/v1/book/list"
  study_log_url: "https://test-baodian.tal.com/exactly/api/v1/lui/continuing/learning"
  resource_conf_url: "https://test-baodian.tal.com/exactly/api/v1/lui/resource/conf"
  content_app_id: "2N5A8F6S0J1M4D3H7R9Q0N6L8A0E0M"
  tools_user_version_url: "https://hmi.chengjiukehu.com/tool-bff-test/dictation/v1/version-name"
  readbook_scheme_url : "http://internal-test-api.edstars.com.cn/readbook/internal/v1/resource/landing-page"
  readbook_note_scheme_url : "http://internal-test-api.edstars.com.cn/readbook-note/internal/v1/resource/landing-page"
  readbook_default_cover : "https://qz-tools.oss-cn-beijing.aliyuncs.com/xiaosi/lui/S.png"
  legal_check:
    check_enable: true
    service_id: "300010"
    host: "http://hmi.chengjiukehu.com/censor-service/v1/check/shumei"
    check_intent: ["百科问答"]
  xPad1AppId: "100021"
  xPad1V2AppId: "200073"
  xPad1:
    resource_ids_url: "https://test-baodian.tal.com/exactly/api/v1/lui/id-list"
    resource_slot_url: "https://test-baodian.tal.com/exactly/api/v1/lui/scheme"
    resource_choice_url: "https://test-baodian.tal.com/exactly/api/v1/lui/user/chose"
    token_parse_url: "https://internal-test-api.edstars.com.cn/api/v1/user/token/parse"
    user_profile_url: "https://internal-test-api.edstars.com.cn/api/v1/user/profile"
    tools_url: "http://data-service-test-svc.jituan-genie-server/api/v1/book/list"
    study_log_url: "https://test-baodian.tal.com/exactly/api/v1/lui/continuing/learning"
    resource_conf_url: "https://test-baodian.tal.com/exactly/api/v1/lui/resource/conf"
    content_app_id: "2N5A8F6S0J1M4D3H7R9Q0N6L8A0E0M"
    tools_user_version_url: "https://hmi.chengjiukehu.com/tool-bff-test/dictation/v1/version-name"
  xPad2AppId: "200038,200067,200076,200078,200083,200087,200088,200089" #走二代代码的逻辑
  xPad2AppId_qijian : "200038"
  xPad2LiteAppId: "200067"
  xPad2:
    resource_ids_url: "http://test-api.edstars.com.cn/xpad2/lpath/v1/lui/id-list"
    resource_slot_url: "http://test-api.edstars.com.cn/xpad2/lpath/v1/lui/scheme"
    resource_choice_url: "http://test-api.edstars.com.cn/xpad2/lpath/v1/lui/user/chose"
    token_parse_url: "https://internal-test-api.edstars.com.cn/api/v1/user/token/parse"
    user_profile_url: "https://internal-test-api.edstars.com.cn/api/v1/user/profile"
    tools_url: "http://data-service-test-svc.jituan-genie-server/api/v1/book/list"
    study_log_url: "http://test-api.edstars.com.cn/xpad2/lpath/v1/lui/continuing/learning"
    resource_conf_url: "http://test-api.edstars.com.cn/xpad2/lpath/v1/lui/resource/conf"
    content_app_id: "2N5A8F6S0J1M4D3H7R9Q0N6L8A0E0M"
    tools_user_version_url: "https://hmi.chengjiukehu.com/tool-bff-test/dictation/v1/version-name"
  ip_analysis:
    url: "https://hmi.chengjiukehu.com/paas-proxy/v1/paas/api"
    retry: 3
    timeout: 3
  reject_check: "https://qz.chengjiukehu.com/alv/gw/nlp-center/v1/nlu/reject-check"
  # 单字默认回复
  single_word_tts: ["查字可以这样说：春天的春怎么写"]
  # 无意义默认回复
  nonsense_tts: ["这个小思还在学习呢，试着对我说：你有什么技能，先了解我的能力吧", "诶呀，这句没理解，但小思会很多知识，不信你问我：世界上最高的山在哪里", "这句听起来有点复杂，但我可以回答很多问题。你可以考考我：李白的诗有哪些", "这个小思还在学习，不过我们可以一起探索数学的世界！你可以对我说，打开数学同步学"]
  # 润色话术
  polish_tts: ["学习是成长的阶梯，加油！","每天进步一点点。","知识是最好的礼物。","努力会发光，相信自己。","坚持就是胜利。","梦想从学习开始。","你是最棒的，继续努力！","智慧源于勤奋，继续加油！","每一次努力，都会闪耀未来。","学无止境，享受过程。"]
  # 小尾巴
  tail_tts: ["今天过得开心吗？","今天过得怎么样？","明天有什么安排吗？","有没有什么开心的事？","最近有没有新发现？","最近学习感觉如何？","有没有什么新发现想分享？","最近读了哪些好书呢？"]

  white_exam_judge:
    status: 1
    sn_list: "76DD082391300097,78DD0823A2100170,78DD0823A2100188,76DD0823A2400045,76DD0823A2400044,78DD08239140001,76DD0823A2400049,76DD0823A2400049,78DD0823A2100249,78PE0823C0100010"

  llm_prompt:
    enable: true
    sn_list: []
    app_id: "200001"
    full_view_enable: true
    filterChatPunctuation: "：，。？！～"
    full_view_baike_tmp_id: "ca884b862d5f3f372e913a7545352831"
    full_view_chat_tmp_id: "4fc7a43dd226c7bbda6064261aa026b7"
    xs2_chat_plus_tmp_id: "df2f306592879b309dd7ab90e6c81b0c"
    xs2_baike_plus_tmp_id: "ca884b862d5f3f372e913a7545352831"
    xs_deepseek_tmp_id: "356c0e28af73734e548941b2fd4d8c82"
    xs_deepseek_exercises_tmp_id: "137e431fd4bfa884156c798458332ae1"
    baike_tmp_id: "65a572a2db49aa72050f0cfa6a2e240e"
    chat_tmp_id: "bd6db71006b26662d6777302afc93ad1"
    understand_question_template_id: "33ebaf0571d508fe391eae9cd4253336"
    exam_question_template_id: "17ab87faa7c58718445745107957fdf6"
    baike_default_tts: ["哎呀，这个问题真是难倒我了，但我会努力去学习的！", "嘿，你的问题真有深度，看来我得多加练习了。", "哇塞, 这个问题超出了我的知识范围, 我要好好学习一下!", "看来你的好奇心比我的知识库还要大呢！我会尽快补充相关知识的。", "这个问题对我来说有点复杂，不过别担心，我会尽快找到答案的！", "唉呀, 这个问题把我难倒了! 不过没关系, 我会去查阅资料的", "嘿小朋友, 你问得太专业了! 看来我得加强学习才行", "被你问住了，看来要更努力学习了", "恭喜您成功地难住一个人工智能伙伴！", "您问得太棒啦！让人工智能伙伴也感觉挫败。", "厄……那是什么？就算是人工智能也会有些迷惑。
恭喜你成功地难住一个AI伙伴!", "哇！你的问题真有挑战性，我现在还不会，需要恶补一下。", "你问的问题超出了我的知识范围, 但是我会尽快补充相关知识的!", "噢, 这个问题真是一个大挑战, 我需要时间去查阅资料"]
    legal_check_tts: ["哎呀，这个问题对我来说有点棘手呢，我们换个话题聊聊怎么样？", "哇塞! 这个问题超出了我的能力范围!", "哦豁，你问到了我的盲区！让我们换一个话题吧。", "看来我的知识库需要更新了! 对于你提出的这个问题, 我现在还不能给出答案。"]
    sug_tmp_id: "29c0b3cfb4235c1b3fc8be12128fa1b0"
    tails_tmp_id: "a288bf554ff23681152ee4dfa4038143"
  llm:
    intent: ["百科问答","查中文字词成语","查英文字词","查句子","查诗歌","中英翻译","计算","非计算题目","模糊意图","查字","查词语","查单词","翻译句子","查时间","搜文字"]
    llm_sug_switch: 1
  cloud_control_host: "http://xiaosi-cloud-control-svc"

  lui_controller_url: "http://hmi-in.chengjiukehu.com/xpod-controller"
  xs_skill_hub_url: "http://hmi.chengjiukehu.com/xiaosi-skill-hub/v1/call"
  xiaosi_rag_url: "http://hmi.chengjiukehu.com/lui-pipeline/lui/chat-rag"
  baike_rag_url: "http://hmi.chengjiukehu.com/xiaosi-skill-hub/rag/baike"
  dialogue_profile_url: "https://hmi.chengjiukehu.com/xiaosi-voice-data/user/memory/get"
  memory_service_url: "https://hmi.chengjiukehu.com/xiaosi-voice-data/user/eventmemory/search"
  all_skills_web_url: "https://test-tools-v2.xuepaipai.com/#/xes/allSkill"
  xue_lian_ji_all_skills_web_url: "https://xiaosi-fe-test.xuepaipai.com/learning-skill/#/allSkill"
  update_profile_subject_version_url: "http://hmi.chengjiukehu.com/xiaosi-voice-data/v1/UpdateUserGradeSubjectVersion"
  chat_context_feed_url: "" # http://xiaosi-dialog-service-svc.znxx-xpp/v1/api/chat-context/feed

  xPodAppId: "200070"
  mobby_scheme:
    "fixed_mobby_2": "tal://onlineclass/enlighten_learn?opt=3&enable_tts=0&tab_id=2"
    "fixed_mobby_2_1": "tal://leap/mob_detail?course_id=gc_047afb0b9ad34"
    "fixed_mobby_2_2": "tal://leap/mob_detail?course_id=gc_ec0da6e97fedf"
    "fixed_mobby_2_3": "tal://leap/mob_detail?course_id=gc_9ab585ad80f36"
    "fixed_mobby_1": "tal://onlineclass/enlighten_learn?opt=3&enable_tts=0&tab_id=1"
    "fixed_mobby_1_1": "tal://leap/mob_detail?course_id=gc_6231e011fd1d6"
    "fixed_mobby_1_2": "tal://leap/mob_detail?course_id=gc_29c9f365cfa8c"
    "fixed_harvard_3": "tal://onlineclass/enlighten_learn?opt=3&enable_tts=0&tab_id=3"
    "fixed_harvard_3_1": "tal://leap/chapter?key_level_id=gc_96f1a93793f1c&key_title=L1.字母启蒙&key_selected_index=0"
    "fixed_harvard_3_2": "tal://leap/chapter?key_level_id=gc_7decbe925389e&key_title=L2.单词拼读&key_selected_index=0"
    "fixed_harvard_3_3": "tal://leap/chapter?key_level_id=gc_fbe9de21ed8a6&key_title=L3.主题句型&key_selected_index=0"
    "fixed_harvard_3_4": "tal://leap/chapter?key_level_id=gc_ec3a6ca9172b0&key_title=L4.流利对话&key_selected_index=0"
    "fixed_xxgc_0": "tal://onlineclass/enlighten_learn?opt=3&enable_tts=0&tab_id=2"
    "fixed_swxl_2": "tal://onlineclass/enlighten_learn?opt=3&enable_tts=0&tab_id=2"
    "fixed_kyqm_3": "tal://onlineclass/enlighten_learn?opt=3&enable_tts=0&tab_id=3"
    "fixed_bdqm_1": "tal://onlineclass/enlighten_learn?opt=3&enable_tts=0&tab_id=1"
  xs_contact_url: "https://hmi.chengjiukehu.com/xiaosi-voice-data/contact/list/get"

  math_agent:
    pai_image_url: "https://qz-tools.oss-cn-beijing.aliyuncs.com/xiaosi/pic/jiangti3x.png"
    math_gpt_tmp_id: "86219ea78d6b5c794a12b8301ccebe76"
    # math_agent_tmp_id: "91192cc2987e610e1b47c6a6c048a407"
    math_agent_tmp_id: "b9aa9d588f3e996629516b1a397a4748"
    suishiwen_h5_url: "https://static0-test.xesimg.com/math-fe/automath-container/index.html#/?param_data=%s"
    downward_msg_url: "http://ws-voice-consumer-test-svc.znxx-xpp:80/message/downward"
    auto_math_url: "http://ailearn.chengjiukehu.com/alv-automath-service-web/v2/automath/check"
    auto_math_ak: "pad-kspg"
    auto_math_sk: "BpLnfgDsc2WD8F2qNfHK5a84jjJkwzDk"

  nlp_func:
    controller: "http://hmi-in.chengjiukehu.com/xpad-controller"
    controller_timeout: 3
    function_call: "http://hmi.chengjiukehu.com/xiaosi-skill-hub/v2/call"
    function_call_timeout: 5
    dialog_api: "http://hmi.chengjiukehu.com/xiaosi-dialog-api"
    dialog_api_timeout: 10
    dou_di_map:
      xiaosi:
        dou_di_intent_map:
          计算:
            items: ["嗨，小朋友，手动计算才是王道哦！试试看你能解出这个题目吧！", "亲爱的小朋友，数学就像一块巧克力，自己品尝才会知道其中的甜味。试着自己解答这个题目吧。", "猜猜看, 这个答案是什么？我相信你可以做到的!", "记住哦, 数学就像健身一样, 不努力锻炼怎么能有肌肉呢? 加油做题啦!", "让我们一起探索数学的世界吧! 你先试着解决这个问题哦", "数学就像一个宝箱，只有用钥匙打开它才能找到里面的宝藏。那把钥匙就是你自己去尝试和实践。加油！", "别忘了小朋友，练习使得完美！来挑战下这道题目吧!", "虽然我很乐意帮助您求解问题……但每次当您亲手完成一个难题时，那种成就感简直比任何东西都要美妙。", "我知道您有能力解决这个问题，所以我会在这里等着，看着您发挥出自己的实力。", "把答案告诉你就像是剧透电影结局一样没意思哦~ 试试自己做做看吧！", "小朋友，你知道吗？数学就像一个游戏，我们需要自己去探索和挑战。", "嘿, 小伙伴! 计算机可以帮助我们计算, 但真正理解问题和找到答案需要我们自己动脑筋哦", "嘻嘻，这个题目对你来说小菜一碟！先试试看吧。", "数学就像一个迷宫，只有通过亲身尝试才能找到出口。加油！", "记得哦, 真正的智慧来源于实践。让我们开始做题吧!", "亲爱的小朋友, 数学是一门语言, 我们需要通过练习来掌握它", "呼啦啦~ 这个问题好棒! 我相信你可以独立完成它!", "哈哈哈! 这个问题对你来说应该不在话下! 加油!", "魔法师从不泄露他的秘密同样地我也不能直接告诉你答案哟～", "只有当你亲手打开那扇门时才能见到新世界的曙光所以，请尝试去解决这道题目。", "想要变成数学大侠？那就先从解决这道题开始吧！", "别忘了：每一个伟大的科学家都是从一点点实践中开始他们的旅程 所以请努力去完成这道题目！", "唷！数字正在向您招手呢！快用您聪明的大脑去握住他们吧～", "虽然我很愿意帮助您求解……但请相信我——当您独立完成后那种喜悦感会让人欲罢不能的" ]
          找作文:
            items: [ "哎呀，真抱歉，小思还不会作文查找，正在努力学习这个能力呢～", "很遗憾，我目前还不支持作文查找，但我在其他方面可以尽力帮助你哦～", "不好意思哦，作文查找超出了我的能力范围，很乐意为你解答其他问题哦～", "这个功能我还没有学会，但我可以尝试帮你解决其他问题。", "你好，关于作文的搜索，我可能帮不上忙，但其他问题我都可以尽力而为。", "真是不好意思，作文搜索不是我的强项，但我很乐意帮你解决其他问题。", "真抱歉，我目前还不能提供作文搜索服务，但我可以尝试为你提供其他帮助。", "哎呀，作文查找这个功能我还在学习中，希望不久后能帮到你。", "不好意思，我还在努力学习中，作文查找暂时还不行，但我很乐意帮你解答其他问题。" ]
            #找电子书:
            #items: [ "很抱歉，我目前还不支持查找电子书的功能呢。", "对不起，查找电子书这个功能我还没有学会，希望不久后能帮到你。", "抱歉，我现在还不会找电子书，但我可以为你提供其他帮助。", "很抱歉，我不能帮你找电子书，不过如果有其他需要，我很乐意助你一臂之力。", "哎呀，查找电子书这事我还帮不上忙，不过你有其他问题我可以尽力解答。", "对不起，目前我还不具备查找电子书的功能，但我可以尝试为你提供其他帮助。", "非常遗憾，我目前还不能帮你查找电子书，但是我在努力学习了，希望不久后能帮到你。" ]
          找学习音频:
            items: [ "恩，这个问题好像有点难倒我了，我还在努力学习中。", "这个内容听起太酷了！但我还不支持点播它，让我再学习学习。", "你的请求让我有点手足无措，因为我还没这个功能。", "真不好意思，你说的功能我还做不到，希望不久后能帮到你。" ]
          找非学习音频:
            items: [ "你想听的小思无法播放哦，但是你可以开听听电台看看有没有你想听的内容哦", "你想听的内容我没办法提供，不过“听听电台“里可能会有你喜欢的内容，你可以打开试试～", "虽然我不能播音乐，但‘打开听听电台’，精彩内容等你来发现！", "音乐功能不在服务区，不过你可以说‘打开听听电台’，精彩内容随时待命！", "哼唱不了今天的旋律，但‘打开听听电台’，精彩内容听听就知道！" ]
          找动画片:
            items: [ "动画片我放不了，但是素养视频我懂得！对我说‘我要看素养课程’，一起去涨知识吧！", "我不能播放动画片，但是可以帮你打开素养视频，你会发现另一个有趣的世界哦！", "动画片虽好，素养视频更能带你涨知识，快来试试看！", "虽然我不可以播放动画片，但素养视频可以让你收获另一份快乐哦~" ]
            #找故事:
            #items: [ "故事我没找到，但素养视频我懂得！对我说‘我要看素养课程’，一起去涨知识吧！", "这个故事我没找到，但是可以帮你打开素养视频，你会发现另一个有趣的世界哦！", "故事虽好，素养视频更能带你涨知识，快来试试看！", "虽然我没找到故事，但素养视频可以让你收获另一份快乐哦~" ]
          无意义:
            items: [ "诶呀，这个问题好像有点复杂，要不换个方式再问问我呀", "这句话似乎有点小难度呢，换个说法再来一遍吧", "呀，这次没听明白呢，能再完整的和小思说一遍吗", "哎呀，刚才的话让我有点摸不着头脑。来，再和小思讲一次吧", "哎呀，小思差一点点就听懂啦，换个说法再说一遍吧", "这句话小思竟然没听明白，再完整的对我说一遍吧", "哎呀，好像漏听了些什么，可以再完整的说一遍嘛" ]
            # 找课本:
            # items: [ "哎呀，真不巧，我翻遍了我的资源库也没找到这本书。", "哎呀，这本书似乎躲起来了，我找不到呢。非常抱歉！", "唉，我尽力了，但还是没能找到你要的书，真抱歉。", "真不好意思，你要找的那本书我这里没有。" ]
          找图片:
            items: [ "很抱歉，我目前还不支持查找图片的功能呢。","对不起，查找图片这个功能我还没有学会，希望不久后能帮到你。","抱歉，我现在还不会找图片，但我可以为你提供其他帮助。","很抱歉，我不能帮你找图片，不过如果有其他需要，我很乐意助你一臂之力。","很抱歉，我不能帮你找图片，不过如果有其他需要，我很乐意助你一臂之力。","哎呀，查找图片这事我还帮不上忙，不过你有其他问题我可以尽力解答。","对不起，目前我还不具备查找图片的功能，但我可以尝试为你提供其他帮助。","非常遗憾，我目前还不能帮你查找图片，但是我在努力学习了，希望不久后能帮到你。"]
      bella:
        dou_di_intent_map:
          无意义:
            items: [ "诶呀，这个问题好像有点复杂，要不换个方式再问问我呀", "这句话似乎有点小难度呢，换个说法再来一遍吧", "这次没听明白呢，能再完整的说一遍吗", "你的话我好像漏听了些什么，可以再完整的说一遍嘛"]
          百科:
            items: [ "这个问题我暂时没办法回答你，等我回去查阅资料后再来回复你吧。","看来你的好奇心比我的知识库还要大呢！我会尽快补充相关知识的。","唉呀, 这个问题把我难倒了! 不过没关系, 我会去查阅资料的，我们一起进步好吗。","噢, 这个问题真是一个大挑战, 我需要时间去查阅资料" ]
          闲聊:
            items: [ "这个问题我暂时没办法回答你，等我回去查阅资料后再来回复你吧。","看来你的好奇心比我的知识库还要大呢！我会尽快补充相关知识的。","唉呀, 这个问题把我难倒了! 不过没关系, 我会去查阅资料的，我们一起进步好吗。","噢, 这个问题真是一个大挑战, 我需要时间去查阅资料","这个问题对我来说有点棘手呢，我们换个话题聊聊怎么样？"]

    query_nlp_switch: 1
    #query_nlp_switch  0:关闭 1:开启
    query_nlp_v2: "https://qz.chengjiukehu.com/alv/gw/nlp-center/v1/nlu/query-nlp-v2"
    bai_ke: "http://hmi-in.chengjiukehu.com/lui-baike-agent"
  nlu_suggest:
    url: "http://qz.chengjiukehu.com/alv/gw/nlp-center/v1/nlu/recommend"
    xpp:
      - content: "打开护眼模式"
        pinyin: "da3 kai1 hu4 yan3 mo2 shi4"
      - content: "打开拍教辅"
        pinyin: "da3 kai1 pai1 jiao4 fu3"
      - content: "打开学一学"
        pinyin: "da3 kai1 xue2 yi1 xue2"
      - content: "打开听一听"
        pinyin: "da3 kai1 ting1 yi1 ting1"
      - content: "我要打电话"
        pinyin: "wo3 yao4 da3 dian4 hua4"
      - content: "我要投屏"
        pinyin: "wo3 yao4 tou2 ping2"
      - content: "学一学支持什么教材"
        pinyin: "xue2 yi1 xue2 zhi1 chi2 shen2 me5 jiao4 cai2"
      - content: "哪些教辅支持讲解"
        pinyin: "na3 xie1 jiao4 fu3 zhi1 chi2 jiang3 jie3"
      - content: "调低音量"
        pinyin: "tiao2 di1 yin1 liang4"
      - content: "调低亮度"
        pinyin: "tiao2 di1 liang4 du4"
  filters:
    filters_intent_white_list: ["百科问答", "闲聊", "找非学习音频", "找动画片", "找故事", "找图片", "查天气", "查时间", "应用下载卸载", "应用打开关闭", "设置闹钟", "设置计时器", "设置提醒日程", "打电话", "应用快捷操作", "系统控制", "模糊意图", "学习机使用答疑"]
    filters_role_white_list: ["哪吒", "太乙真人"]
    filters_under_tts_xiaozha: ["变哪吒！你想学哪吒那种无畏面对挑战的勇气吗？",
                                "哪吒可是无畏的英雄，相信你也可以在学习上勇往直前，勇敢面对每个难题！",
                                "哪吒不怕困难，你也可以像他一样，在生活中勇敢追求梦想噢！",
                                "哪吒保护百姓，你在学校里有没有做过帮助别人、传递正能量的事呢？快跟小思讲讲吧",
                                "哪吒永远坚持正义，你在生活中遇到过哪些坚守正义与善良的事情吗？",
                                "哪吒虽然和父亲有过矛盾，但他始终爱着父亲，你最近跟爸爸妈妈聊天了吗？",
                                "变哪吒，勇敢无畏，你有没有发现自己也能成为一名领袖呢？",
                                "哪吒的牺牲精神值得学习，你有没有为他人做过不求回报的好事？",
                                "哪吒敢于挑战强敌，最近你是怎样战胜学习上的难关呢？",
                                "哪吒拥有坚韧不拔的意志力，小思相信，你也不会轻易放弃的，对不对？",
                                "哪吒与敖丙并肩作战，团结协作让他们战无不胜，你有没有跟别人合作完成什么事呢？"]
    filters_under_tts_xiaoyi: ["太乙真人驾到！今天想传你一招专治不会写作业？",
                               "嗯？你要我变太乙真人？那我得先炼个专注法宝，你想学吗？",
                               "虽然我不是变太乙真人，但我可以陪你一起修炼学习力！你最想提升哪一科？",
                               "太乙真人爱观察、爱思考，你平时遇到难题，会怎么思考解决呢？",
                               "太乙真人常帮弟子渡过难关，你最近有没有遇到什么需要我支招的事？",
                               "虽然我没仙术，但我有陪伴之术！你今天在学校过得怎么样？",
                               "如果你也有一位像太乙真人那样的师父，你最希望他教你什么？",
                               "想变太乙真人？那你得先学会静心术，你平时怎么让自己专注下来？",
                               "太乙真人的智慧来自不断学习，你最近有没有学到特别有意思的知识？",
                               "如果我变成太乙真人，能送你一个成长锦囊，你最希望它里面有什么？",
                               "变太乙真人的话，我第一件事就是送你一句鼓励的话：你比想象的更厉害！"]
    filters_name_map_videoid:
      "哪吒": "xiaozha"
      "太乙真人": "xiaoyi"
    filters_intent_white_list_to_skill: ["找非学习音频", "找动画片", "找图片", "查天气", "查时间", "应用下载卸载", "应用打开关闭", "设置闹钟", "设置计时器", "设置提醒日程", "打电话", "应用快捷操作", "系统控制", "模糊意图", "学习机使用答疑"]

api:
  mapping:
    "lui_llm":
      name: "LUI-LLM"
      function: "proxy_lui_llm_caht"
      version: "1.0.0"
      #url: "http://hmi.chengjiukehu.com/gpt-service/sse/lui-llm/chat"
      url: "http://hmi.chengjiukehu.com/gpt-service/sse/common/chat"
      timeout: 180
      retry: 0
      service: "gpt-service"
    "dialogue_llm":
      name: "Dialogue-LLM"
      function: "proxy_dialogue_llm_chat"
      version: "1.0.0"
      url: "https://hmi.chengjiukehu.com/llm-service/sse/common/chat"
      timeout: 180
      retry: 0
      service: "llm-service"



subject_config:
  course_system_cover_crop:
    11: "crop,w_720,h_408"
    8: "crop,w_720,h_408"
    9: "crop,w_720,h_408"
  subject_font_color:
    1: "#CC851912"
    2: "#764D1C"
    3: "#CC361D7B"
    4: "#0969B0"
    5: "#277360"
    6: "#486C15"
    21: "#3E861F"
    7: "#CC361D7B"
    9: "#994823"
    8: "#935C0F"
  subject_overlay_color:
    1: "#FCAD9F"
    2: "#FFEC92"
    3: "#CAD0FF"
    4: "#A0D1FF"
    5: "#9FF4E3"
    6: "#DBF5AF"
    21: "#A6F385"
    7: "#C298F5"
    9: "#EBA181"
    8: "#EEB25D"
  subject_jzx_cover:
    1: "https://qz-test.oss-cn-beijing.aliyuncs.com/tools/lui/jzx_1.png"
    2: "https://qz-test.oss-cn-beijing.aliyuncs.com/tools/lui/jzx_2.png"
    3: "https://qz-test.oss-cn-beijing.aliyuncs.com/tools/lui/jzx_3.png"
    4: "https://qz-test.oss-cn-beijing.aliyuncs.com/tools/lui/jzx_4.png"
    5: "https://qz-test.oss-cn-beijing.aliyuncs.com/tools/lui/jzx_5.png"
    6: "https://qz-test.oss-cn-beijing.aliyuncs.com/tools/lui/jzx_6.png"
    999999: "https://qz-test.oss-cn-beijing.aliyuncs.com/tools/lui/jzx_default.png"
  jzx_v15_scheme: "tal://onlineclass/xs_ai_exact_practice"
  jzx_v15_questions_cover: "https://qz-test.oss-cn-beijing.aliyuncs.com/tools/lui/xuexifengmian.png"
