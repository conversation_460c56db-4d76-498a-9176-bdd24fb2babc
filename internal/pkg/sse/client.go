package sse

import (
	"bufio"
	"context"
	"fmt"
	"lui-api/pkg/zlog"
	"net/http"
	"runtime/debug"
	"strings"
	"time"
)

type Method string

func (m Method) String() string {
	return string(m)
}

const (
	OptionsMethod Method = "OPTIONS"
	GetMethod     Method = "GET"
	HeadMethod    Method = "HEAD"
	PostMethod    Method = "POST"
	PutMethod     Method = "PUT"
	DeleteMethod  Method = "DELETE"
)

type Client struct {
	url     string
	timeout time.Duration
}

type Event struct {
	Data string
}

func NewClient(url string, timeout time.Duration) *Client {
	return &Client{
		url:     url,
		timeout: timeout,
	}
}

func (c *Client) Send(ctx context.Context, method Method, header map[string]string, body []byte) (<-chan *Event, error) {
	eventChan := make(chan *Event, 1024)

	request, err := http.NewRequestWithContext(ctx, method.String(), c.url, strings.NewReader(string(body)))
	if err != nil {
		return nil, err
	}
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("Accept", "text/event-stream")
	for k, v := range header {
		request.Header.Add(k, v)
	}

	httpClient := http.Client{Timeout: c.timeout}
	response, err := httpClient.Do(request)
	if err != nil {
		return nil, err
	}
	if response.StatusCode != 200 {
		response.Body.Close()
		return nil, fmt.Errorf("sse: http code error: %d", response.StatusCode)
	}

	go func() {
		defer func() {
			if err := recover(); err != nil {
				errorMsg := fmt.Sprintf("go routine panic错误：%v\n %s", err, debug.Stack())
				zlog.STDInstance().Error(errorMsg)
				return
			}
			response.Body.Close()
			close(eventChan)
		}()

		scanner := bufio.NewScanner(response.Body)
		for {
			select {
			case <-ctx.Done():
				fmt.Println("sse client ctx done")
				return
			default:
				if !scanner.Scan() {
					return
				}
				line := scanner.Text()
				if line == "" {
					continue
				}
				eventChan <- &Event{Data: line}
			}
		}
	}()

	return eventChan, nil
}
