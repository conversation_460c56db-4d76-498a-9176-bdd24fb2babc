package sync

import (
	"context"
	"runtime/debug"
	"sync"

	"github.com/go-kratos/kratos/v2/log"
)

// Go start routine with recover
func Go(ctx context.Context, log *log.Helper, f func()) {
	go func() {
		defer func() {
			if err := recover(); err != nil {
				log.WithContext(ctx).Errorf("go routine panic错误：%v\n %s", err, debug.Stack())
				return
			}
		}()
		f()
	}()
}

func GoGroupWait(ctx context.Context, logger log.Logger, fnArr ...func() string) {
	var (
		logH *log.Helper
	)
	if logger != nil {
		logH = log.NewHelper(log.With(logger, "module", "sync/GoGroupWait"))
	}
	if len(fnArr) == 0 {
		return
	}
	var wg sync.WaitGroup
	goNum := len(fnArr)
	wg.Add(goNum)
	for _, fn := range fnArr {
		go func(doFn func() string) {
			defer func(ctx context.Context) {
				if err := recover(); err != nil {
					if ctx != nil {
						if logH != nil {
							logH.WithContext(ctx).Errorw(
								"msg", "GoGroupWait panic recovered",
								"panic_err", err,
								"stack", string(debug.Stack()),
							)
						}
					} else {
						if logH != nil {
							logH.Errorw(
								"msg", "GoGroupWait panic recovered",
								"panic_err", err,
								"stack", string(debug.Stack()),
							)
						}
					}
				}
			}(ctx)
			defer wg.Done()
			doFn()
		}(fn)
	}
	wg.Wait()
}
