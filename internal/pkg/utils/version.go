/*
* Author:  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
* Date:    2024/6/6 16:28
* Description:
 */

package utils

import "strings"

func GetVersionNum(version string) string {
	//去除version中的前几位的字母
	if len(version) > 0 {
		for i, v := range version {
			if v >= '0' && v <= '9' {
				version = version[i:]
				break
			}
		}
	}
	return version
}

func ReplaceURLDomain(url string) string {
	testNewDomain := "https://test-genie.edstars.com.cn"
	testOldDomain := "https://ss-test-genie.oss-cn-beijing-internal.aliyuncs.com"
	prodNewDomain := "https://ss-prod-genie.oss-cn-beijing.aliyuncs.com"
	prodOldDomain := "https://ss-prod-genie.oss-cn-beijing-internal.aliyuncs.com"

	if strings.Contains(url, testOldDomain) {
		return strings.Replace(url, testOldDomain, testNewDomain, 1)
	} else if strings.Contains(url, prodOldDomain) {
		return strings.Replace(url, prodOldDomain, prodNewDomain, 1)
	}
	return url
}
