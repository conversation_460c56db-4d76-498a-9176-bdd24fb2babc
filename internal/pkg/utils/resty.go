package utils

import (
	"context"
	"encoding/json"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"net/http"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
)

type CurlInstance struct {
	Ctx    context.Context
	Client *resty.Client
	Req    *resty.Request
	log    *log.Helper
}

func CurlInit(ctx context.Context, t time.Duration, retryCount int, log *log.Helper) *CurlInstance {
	s := resty.New().SetRetryCount(retryCount)
	if t > time.Second {
		s.SetTimeout(t)
	}
	return &CurlInstance{
		Ctx:    ctx,
		Client: s,
		Req:    s.R(),
		log:    log,
	}
}
func (ci *CurlInstance) ChClient(cli *resty.Client) *CurlInstance {
	ci.Client = cli
	ci.Req = cli.R()
	return ci
}

func (ci *CurlInstance) ChRequest(req *resty.Request) *CurlInstance {
	ci.Req = req
	return ci
}

func (ci *CurlInstance) ChRequestBody(headers map[string]string, body any) *CurlInstance {
	req := ci.Req.SetHeaders(headers).SetBody(body)
	ci.Req = req
	return ci
}

func (ci *CurlInstance) Get(url string) (*resty.Response, error) {

	if ci == nil || ci.Ctx == nil || ci.Req == nil {
		return nil, errors.New("Request client nil")
	}
	start := time.Now()
	resp, err := ci.Req.EnableTrace().Get(url)
	if err != nil {
		return nil, errors.Errorf("Get Error: %v", err)
	}
	if resp == nil {
		return nil, errors.New("Get Resp nil")
	}
	if resp.StatusCode() != http.StatusOK {
		return nil, errors.Errorf("Get Status NotOK, resp: %+v", resp)
	}
	defer ci.doLog(start, resp, err)
	return resp, nil
}

func (ci *CurlInstance) Post(url string) (*resty.Response, error) {

	if ci == nil || ci.Ctx == nil || ci.Req == nil {
		return nil, errors.New("Request client nil")
	}
	start := time.Now()
	resp, err := ci.Req.EnableTrace().Post(url)
	if err != nil {
		return nil, errors.Errorf("Post Error: %v", err)
	}
	if resp == nil {
		return nil, errors.New("Post Resp nil")
	}
	if resp.StatusCode() != http.StatusOK {
		return resp, nil
	}
	defer ci.doLog(start, resp, err)
	return resp, nil
}

func (ci *CurlInstance) doLog(start time.Time, response *resty.Response, err error) {
	if ci.Ctx == nil {
		return
	}

	var (
		query, action strings.Builder
	)
	query.WriteString(ci.Req.URL)
	action.WriteString("[")
	action.WriteString(ci.Req.Method)
	action.WriteString("]")
	action.WriteString(ci.Req.QueryParam.Encode())

	bodyByte, _ := json.Marshal(ci.Req.Body)
	fields := map[string]interface{}{
		"p_method":         ci.Req.Method,
		"p_url":            ci.Req.URL,
		"p_raw_query":      query.String(),
		"p_request_body":   string(bodyByte),
		"p_response_body":  string(response.Body()),
		"x_action":         action.String(),
		"p_status_code":    response.StatusCode(),
		"p_cost_ms":        float64(time.Now().Sub(start).Microseconds()) / 1e3,
		"p_request_header": ci.Req.Header,
	}

	if err == nil {
		ci.log.WithContext(ci.Ctx).Info(fields)
	} else {
		fields["error"] = err
		fields["response_err"] = response.Error()
		ci.log.WithContext(ci.Ctx).Error(err.Error(), fields)
	}
}
