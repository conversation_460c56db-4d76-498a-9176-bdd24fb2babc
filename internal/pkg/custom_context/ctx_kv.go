package custom_context

import (
	"context"
	"github.com/spf13/cast"
	"strings"
	"time"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/trace"
	"lui-api/internal/common"
)

func GetXAppId(ctx context.Context) (appId string) {
	if val := ctx.Value(common.XAppId); val != nil {
		return val.(string)
	}
	return ""
}

func SetXAppId(ctx context.Context, appId string) context.Context {
	return context.WithValue(ctx, common.XAppId, appId)
}

func GetRequestIp(ctx context.Context) (clientIP string) {
	if val := ctx.Value(common.RequestIp); val != nil {
		return val.(string)
	}
	return ""
}

func SetRequestIp(ctx context.Context, clientIp string) context.Context {
	return context.WithValue(ctx, common.RequestIp, clientIp)
}

func GetXNonce(ctx context.Context) (nonce string) {
	if val := ctx.Value(common.XNonce); val != nil {
		return val.(string)
	}
	return ""
}

func SetXNonce(ctx context.Context, nonce string) context.Context {
	return context.WithValue(ctx, common.XNonce, nonce)
}

func GetXDeviceId(ctx context.Context) (deviceId string) {
	if val := ctx.Value(common.XDeviceId); val != nil {
		return val.(string)
	}
	return ""
}

func SetXDeviceId(ctx context.Context, deviceId string) context.Context {
	return context.WithValue(ctx, common.XDeviceId, deviceId)
}

func GetXPlatform(ctx context.Context) (platForm string) {
	if val := ctx.Value(common.XPlatForm); val != nil {
		return val.(string)
	}
	return ""
}

func SetXPlatform(ctx context.Context, platform string) context.Context {
	return context.WithValue(ctx, common.XPlatForm, platform)
}

func GetXVersion(ctx context.Context) (origin string) {
	if val := ctx.Value(common.XVersion); val != nil {
		return val.(string)
	}
	return ""
}

func SetXVersion(ctx context.Context, version string) context.Context {
	return context.WithValue(ctx, common.XVersion, version)
}

func GetXOSVersion(ctx context.Context) (osVersion string) {
	if val := ctx.Value(common.XOSVersion); val != nil {
		return val.(string)
	}
	return ""
}

func SetXOSVersion(ctx context.Context, osVersion string) context.Context {
	return context.WithValue(ctx, common.XOSVersion, osVersion)
}

func GetTraceId(ctx context.Context) string {
	if val := ctx.Value(common.TraceId); val != nil {
		return val.(string)
	}

	if span := trace.SpanContextFromContext(ctx); span.HasTraceID() {
		return span.TraceID().String()
	}
	return ""
}

func SetTraceId(ctx context.Context, traceId string) context.Context {
	return context.WithValue(ctx, common.TraceId, traceId)
}

func GetAuthorization(ctx context.Context) (authorization string) {
	if val := ctx.Value(common.Authorization); val != nil {
		return val.(string)
	}
	return ""
}

func SetAuthorization(ctx context.Context, authorization string) context.Context {
	return context.WithValue(ctx, common.Authorization, authorization)
}

func GetVersion(ctx context.Context) (authorization string) {
	if val := ctx.Value(common.Platversionoriginal); val != nil {
		return val.(string)
	}
	return ""
}

func SetVersion(ctx context.Context, authorization string) context.Context {
	return context.WithValue(ctx, common.Platversionoriginal, authorization)
}

func SetStartTime(ctx context.Context) context.Context {
	return context.WithValue(ctx, common.StartTime, time.Now())
}

func NewTraceContext(ctx context.Context, traceIds ...string) context.Context {
	span := trace.SpanFromContext(ctx)
	if span.IsRecording() {
		return trace.ContextWithSpan(context.Background(), span)
	}

	var traceId trace.TraceID
	if len(traceIds) > 0 && len(traceIds[0]) == 32 {
		traceId, _ = trace.TraceIDFromHex(traceIds[0])
	} else {
		traceId, _ = trace.TraceIDFromHex(strings.ReplaceAll(uuid.New().String(), "-", ""))
	}

	spanCtx := trace.NewSpanContext(trace.SpanContextConfig{
		TraceID:    traceId,
		SpanID:     trace.SpanID{},
		TraceFlags: 0,
		TraceState: trace.TraceState{},
		Remote:     false,
	})
	return trace.ContextWithSpanContext(ctx, spanCtx)
}

func GetXOsPkgDate(ctx context.Context) (pkgTime int) {
	version := GetXOSVersion(ctx)
	if len(version) > 2 {
		pkgTime = cast.ToInt(version[1:])
	}

	return pkgTime
}
