package data

import (
	"github.com/go-kratos/kratos/contrib/registry/nacos/v2"
	"github.com/go-kratos/kratos/v2/registry"
	"github.com/nacos-group/nacos-sdk-go/clients"
	"github.com/nacos-group/nacos-sdk-go/clients/naming_client"
	"github.com/nacos-group/nacos-sdk-go/common/constant"
	"github.com/nacos-group/nacos-sdk-go/vo"
	"lui-api/internal/common"
)

func NewRegistrar(client naming_client.INamingClient) registry.Registrar {
	groupOption := nacos.WithGroup("genie-server")
	return nacos.New(client, groupOption)
}

func NewNacosClient(env common.Env) naming_client.INamingClient {
	sc := []constant.ServerConfig{
		*constant.NewServerConfig(env.Discovery().Address, env.Discovery().Port),
	}
	client, err := clients.NewNamingClient(
		vo.NacosClientParam{
			ClientConfig:  constant.NewClientConfig(constant.WithNamespaceId(env.Discovery().NamespaceId), constant.WithNotLoadCacheAtStart(true)),
			ServerConfigs: sc,
		},
	)
	if err != nil {
		panic(err)
	}
	return client
}
