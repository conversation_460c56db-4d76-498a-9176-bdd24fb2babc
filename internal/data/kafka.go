package data

import (
	"context"
	"git.100tal.com/jituan_genie_server/mqs/kafka"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cast"
	"lui-api/internal/conf"
)

func NewKafkaProducer(conf *conf.Data_Kafka, logger log.Logger) (*kafka.Producer, error) {
	var kafkaConnectOptions []kafka.ConnectOption
	securityProtocol := kafka.SecurityProtocolPlainText
	if conf.SecurityProtocol == "sasl_ssl" {
		kafkaConnectOptions = append(kafkaConnectOptions,
			kafka.WithSaslMechanism(kafka.MechanismTypePlain),
			kafka.WithSaslUsername(conf.SaslUsername),
			kafka.WithSaslPassword(conf.SaslPassword),
			kafka.WithSslCaLocation(conf.SslCaLocation),
		)
		securityProtocol = kafka.SecurityProtocolSaslSsl
	}
	kafkaConnectOptions = append(kafkaConnectOptions, kafka.WithProducerMessageMaxBytes(cast.ToInt(conf.MaxMessageBytes)))
	return kafka.NewProducer(
		context.Background(),
		conf.BootstrapServers,
		securityProtocol,
		logger,
		kafkaConnectOptions...,
	)
}
