package data

import (
	"context"
	"git.100tal.com/jituan_genie_server/mqs"
	"github.com/go-kratos/kratos/v2/log"
	"lui-api/internal/biz"
	"lui-api/internal/conf"
	"lui-api/pkg/util"
)

type kafkaProducerRepo struct {
	data      *Data
	log       *log.Helper
	kafkaConf []*conf.Data_Kafka
}

// SendMessage .
func (k kafkaProducerRepo) SendMessage(ctx context.Context, message *mqs.QueryMsg) error {

	keyHash, _ := util.Hash(message.Key)
	index := int(keyHash) % len(k.data.queryProducers)
	kafkaProducer := k.data.queryProducers[index]
	topic := k.kafkaConf[index].Topic
	return kafkaProducer.SendQueryMsg(ctx, topic, message)
}

// NewKafkaProducerRepo .
func NewKafkaProducerRepo(data *Data, logger log.Logger, kafkaConf []*conf.Data_Kafka) biz.KafkaProducerRepo {
	return &kafkaProducerRepo{
		data:      data,
		log:       log.NewHelper(logger),
		kafkaConf: kafkaConf,
	}
}
