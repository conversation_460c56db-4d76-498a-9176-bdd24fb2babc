package data

import (
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"lui-api/internal/biz"
	"strings"
)

type VisionRepo struct {
	data *Data
	log  *log.Helper
}

func NewVisionRepo(
	data *Data,
	logger log.Logger,
) biz.IVisionRepo {
	return &VisionRepo{
		data: data,
		log:  log.<PERSON>elper(log.With(logger, "module", "data/vision")),
	}
}

const (
	RedisKeyVisionImage = "vision:image:%v"
)

func (repo *VisionRepo) GetVisionImage(ctx context.Context, requestId string) (string, error) {
	redisKey := fmt.Sprintf(RedisKeyVisionImage, getSessionId(requestId))
	cmd := repo.data.rdb.Get(ctx, redisKey)
	return cmd.Result()
}

func (repo *VisionRepo) DelVisionImage(ctx context.Context, requestId string) error {

	redisKey := fmt.Sprintf(RedisKeyVisionImage, getSessionId(requestId))
	cmd := repo.data.rdb.Del(ctx, redisKey)
	return cmd.Err()
}

func getSessionId(requestId string) string {
	sessionId := requestId
	if strings.Index(sessionId, "-") > 0 {
		sessionId = sessionId[:strings.Index(sessionId, "-")]
	}
	return sessionId
}
