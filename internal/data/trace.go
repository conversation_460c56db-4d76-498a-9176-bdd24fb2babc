package data

import (
	"github.com/aliyun-sls/opentelemetry-go-provider-sls/provider"
	"github.com/pkg/errors"
)

const (
	OnlineEnv = "online" // 生产环境
	GrayEnv   = "gray"   // 灰度环境
)

func NewTrace(env, name, version string) (*provider.Config, error) {
	traceExporterEndpoint := "sls.cn-beijing-intranet.log.aliyuncs.com:10010"
	metricExporterEndpoint := "sls.cn-beijing-intranet.log.aliyuncs.com:10010"
	slsProject := "k8s-log-ccdeb0595ad904bf6ab510740b2fda922"
	logstore := "znxx-jtj-trace-test"
	slsAk := "LTAI5tBJSvZQUQ8bWbTjyx2k"
	slsSk := "******************************"
	if OnlineEnv == env || GrayEnv == env {
		traceExporterEndpoint = "sls.cn-beijing-intranet.log.aliyuncs.com:10010"
		metricExporterEndpoint = "sls.cn-beijing-intranet.log.aliyuncs.com:10010"
		slsProject = "k8s-log-cfa53097e3ff546168eca309a7e50ffb0"
		logstore = "znxx-jtj-trace-prod"
		slsAk = "LTAI5tDjGFYXRXaRa9Dx76eQ"
		slsSk = "******************************"
	}

	slsConfig, err := provider.NewConfig(provider.WithServiceName(name),
		provider.WithServiceNamespace("genie"),
		provider.WithServiceVersion(version),
		provider.WithTraceExporterEndpoint(traceExporterEndpoint),
		provider.WithMetricExporterEndpoint(metricExporterEndpoint),
		provider.WithSLSConfig(slsProject, logstore, slsAk, slsSk))
	if err != nil {
		return nil, errors.Wrapf(err, "data: NewTrace error, config: %+v", slsConfig)
	}
	if err := provider.Start(slsConfig); err != nil {
		return nil, errors.Wrapf(err, "data: NewTrace Start error, config: %+v", slsConfig)
	}
	return slsConfig, nil
}
