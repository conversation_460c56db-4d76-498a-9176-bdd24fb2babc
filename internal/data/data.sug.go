/*
* Author:  <PERSON><PERSON><PERSON><PERSON>iang
* Date:    2024/4/9 16:46
* Description:
 */

package data

import (
	"context"
	"fmt"
	"github.com/redis/go-redis/v9"
	"time"
)

const (
	SugPre                    = "lui:llm_sug:"
	TailsPre                  = "lui:llm_tails:"
	UserSearchCourseAllResPre = "lui:user_search_course_all_res:" //用户找课程全部课程页
	UserSearchPaperAllResPre  = "lui:user_search_paper_all_res:"  //用户找试卷全部试卷页
)

type SugRepo struct {
	data *Data
}

func NewSugRepo(data *Data) *SugRepo {
	return &SugRepo{
		data: data,
	}
}

func (d *SugRepo) RdbGetKey(ctx context.Context, key string) *redis.StringCmd {
	return d.data.rdb.Get(ctx, fmt.Sprintf("%s%s", SugPre, key))
}

func (d *SugRepo) RdbSetKey(ctx context.Context, key string, value interface{}, expiration int) *redis.StatusCmd {
	return d.data.rdb.Set(ctx, fmt.Sprintf("%s%s", SugPre, key), value, time.Second*time.Duration(expiration))
}

func (d *SugRepo) RdbDelKey(ctx context.Context, key string) *redis.IntCmd {
	return d.data.rdb.Del(ctx, fmt.Sprintf("%s%s", SugPre, key))
}

func (d *SugRepo) RdbGetTailsKey(ctx context.Context, key string) *redis.StringCmd {
	return d.data.rdb.Get(ctx, fmt.Sprintf("%s%s", TailsPre, key))
}

func (d *SugRepo) RdbSetTailsKey(ctx context.Context, key string, value interface{}, expiration int) *redis.StatusCmd {
	return d.data.rdb.Set(ctx, fmt.Sprintf("%s%s", TailsPre, key), value, time.Second*time.Duration(expiration))
}

func (d *SugRepo) RdbDelTailsKey(ctx context.Context, key string) *redis.IntCmd {
	return d.data.rdb.Del(ctx, fmt.Sprintf("%s%s", TailsPre, key))
}

func (d *SugRepo) RdbGetUserSearchCourseAllResKey(ctx context.Context, key string) *redis.StringCmd {
	return d.data.rdb.Get(ctx, fmt.Sprintf("%s%s", UserSearchCourseAllResPre, key))
}

func (d *SugRepo) RdbSetUserSearchCourseAllResKey(ctx context.Context, key string, value interface{}) *redis.StatusCmd {
	return d.data.rdb.Set(ctx, fmt.Sprintf("%s%s", UserSearchCourseAllResPre, key), value, time.Hour)
}

func (d *SugRepo) RdbDelUserSearchCourseAllResKey(ctx context.Context, key string) *redis.IntCmd {
	return d.data.rdb.Del(ctx, fmt.Sprintf("%s%s", UserSearchCourseAllResPre, key))
}

func (d *SugRepo) RdbGetUserSearchPaperAllResKey(ctx context.Context, key string) *redis.StringCmd {
	return d.data.rdb.Get(ctx, fmt.Sprintf("%s%s", UserSearchPaperAllResPre, key))
}

func (d *SugRepo) RdbSetUserSearchPaperAllResKey(ctx context.Context, key string, value interface{}) *redis.StatusCmd {
	return d.data.rdb.Set(ctx, fmt.Sprintf("%s%s", UserSearchPaperAllResPre, key), value, time.Hour)
}

func (d *SugRepo) RdbDelUserSearchPaperAllResKey(ctx context.Context, key string) *redis.IntCmd {
	return d.data.rdb.Del(ctx, fmt.Sprintf("%s%s", UserSearchPaperAllResPre, key))
}
