package data

import (
	"context"
	"encoding/json"
	"fmt"
	"lui-api/internal/biz"
	"lui-api/internal/common"
	"lui-api/internal/data/dto"
	"lui-api/pkg/util"
	"sort"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
)

type MemoryContext struct {
	data *Data
	log  *log.Helper
}

func NewMemoryContext(
	data *Data,
	logger log.Logger,
) biz.IMemoryContextRepo {
	return &MemoryContext{
		data: data,
		log:  log.NewHelper(log.With(logger, "module", "data/memory")),
	}
}

// GetMemoryContextBySessionID
func (d *MemoryContext) GetMemoryContextBySessionID(ctx context.Context, sessionID string) (*dto.MemoryContext, error) {
	if sessionID == "" {
		return nil, nil
	}
	hashKey := fmt.Sprintf(dto.MemoryContextHashPrefix, sessionID)
	cmd := d.data.rdb.HGetAll(ctx, hashKey)
	if cmd.Err() != nil {
		return nil, cmd.Err()
	}

	redisContext := &dto.RedisMemoryContext{}
	err := cmd.Scan(redisContext)
	if err != nil {
		return nil, err
	}

	var (
		funcList []*dto.ContextFuncList
		state    *dto.State
		response interface{}
	)
	err = json.Unmarshal([]byte(redisContext.FuncList), &funcList)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal([]byte(redisContext.State), &state)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal([]byte(redisContext.Response), &response)
	if err != nil {
		return nil, err
	}

	return &dto.MemoryContext{
		SessionId:       redisContext.SessionId,
		TimeStamp:       redisContext.TimeStamp,
		Intent:          redisContext.Intent,
		AsrInfo:         redisContext.AsrInfo,
		TtsInfo:         redisContext.TtsInfo,
		FuncList:        funcList,
		State:           state,
		Response:        response,
		IsLlm:           redisContext.IsLlm,
		RewriteQuery:    redisContext.RewriteQuery,
		Source:          redisContext.Source,
		ImageUrl:        redisContext.ImageUrl,
		VideoUrl:        redisContext.VideoUrl,
		LlmSkill:        redisContext.LlmSkill,
		LlmResponse:     redisContext.LlmResponse,
		DialogueId:      redisContext.DialogueId,
		MixedModalQuery: redisContext.MixedModalQuery,
	}, nil
}

// 添加上下文
func (d *MemoryContext) AddMemoryContext(ctx context.Context, userTalID, sessionID string, context dto.MemoryContext) error {
	if userTalID == "" || sessionID == "" {
		return nil
	}

	funcListByte, err := json.Marshal(context.FuncList)
	if err != nil {
		return err
	}

	if context.State == nil {
		context.State = &dto.State{}
	}
	stateByte, err := json.Marshal(context.State)
	if err != nil {
		return err
	}

	responseByte, err := json.Marshal(context.Response)
	if err != nil {
		return err
	}
	mediaByte, err := json.Marshal(context.Media)
	if err != nil {
		return err
	}

	xiaosiCommandByte, err := json.Marshal(context.XiaosiCommandList)
	if err != nil {
		return err
	}

	multiModalInfoByte, err := json.Marshal(context.MultiModalInfo)
	if err != nil {
		return err
	}

	redisContext := dto.RedisMemoryContext{
		SessionId:         sessionID,
		TimeStamp:         context.TimeStamp,
		Intent:            context.Intent,
		AsrInfo:           context.AsrInfo,
		TtsInfo:           context.TtsInfo,
		TtsNorm:           context.TtsNorm,
		FuncList:          string(funcListByte),
		State:             string(stateByte),
		Response:          string(responseByte),
		IsLlm:             context.IsLlm,
		RewriteQuery:      context.RewriteQuery,
		Source:            context.Source,
		ImageUrl:          context.ImageUrl,
		VideoUrl:          context.VideoUrl,
		LlmSkill:          context.LlmSkill,
		LlmResponse:       context.LlmResponse,
		DialogueId:        context.DialogueId,
		Media:             string(mediaByte),
		ScenarioType:      context.ScenarioType,
		XiaosiCommandList: string(xiaosiCommandByte),
		SceneCode:         context.SceneCode,
		MixedModalQuery:   context.MixedModalQuery,
		MultiModalInfo:    string(multiModalInfoByte),
	}

	// 过期时间
	expire := dto.SecondsNextDayFourAM()
	listKeyPre := dto.MemoryContextListKey(common.SceneCode(context.SceneCode))

	_, err = d.data.rdb.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		listKey := fmt.Sprintf(listKeyPre, userTalID)
		hashKey := fmt.Sprintf(dto.MemoryContextHashPrefix, sessionID)

		if err := pipe.LPush(ctx, listKey, sessionID).Err(); err != nil {
			return err
		}

		if err := pipe.LTrim(ctx, listKey, 0, dto.MemoryContextListMaxLength-1).Err(); err != nil {
			return err
		}

		if err := pipe.Expire(ctx, listKey, expire).Err(); err != nil {
			return err
		}

		if err := pipe.HMSet(ctx, hashKey, redisContext).Err(); err != nil {
			return err
		}

		if err := pipe.Expire(ctx, hashKey, expire).Err(); err != nil {
			return err
		}

		return nil
	})

	return err
}

// 获取上下文
func (d *MemoryContext) GetMemoryContext(ctx context.Context, userTalID string, limit int) ([]*dto.MemoryContext, error) {
	if limit < 1 {
		limit = dto.MemoryContextListMaxLength
	}

	var result []*dto.MemoryContext

	// 从list中获取sessionID
	listKeyPre := dto.MemoryContextListKey(dto.GetCtxSceneCode(ctx))
	listKey := fmt.Sprintf(listKeyPre, userTalID)
	sessionIDsCmd := d.data.rdb.LRange(ctx, listKey, 0, int64(limit-1))
	sessionIDs, err := sessionIDsCmd.Result()
	if err != nil || len(sessionIDs) == 0 {
		return result, err
	}

	sessionIDs = util.UniqueSlice(sessionIDs)
	cmds, err := d.data.rdb.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		for _, sessionID := range sessionIDs {
			hashKey := fmt.Sprintf(dto.MemoryContextHashPrefix, sessionID)
			pipe.HGetAll(ctx, hashKey)
		}
		return nil
	})
	if err != nil {
		return result, err
	}

	var redisContexts []*dto.RedisMemoryContext
	for _, cmd := range cmds {
		memoryContext := &dto.RedisMemoryContext{}
		err = cmd.(*redis.MapStringStringCmd).Scan(memoryContext)
		if err != nil {
			continue
		}
		redisContexts = append(redisContexts, memoryContext)

	}

	for _, redisContext := range redisContexts {
		var (
			funcList          []*dto.ContextFuncList
			state             *dto.State
			media             *dto.Media
			response          interface{}
			xiaosiCommandList []dto.XiaosiCommands
			multiModalInfo    *dto.MultiModalInfo
		)

		if redisContext.FuncList != "" {
			err = json.Unmarshal([]byte(redisContext.FuncList), &funcList)
			if err != nil {
				continue
			}
		}

		if redisContext.State != "" {
			err = json.Unmarshal([]byte(redisContext.State), &state)
			if err != nil {
				continue
			}
		}

		if redisContext.Response != "" {
			err = json.Unmarshal([]byte(redisContext.Response), &response)
			if err != nil {
				continue
			}
		}

		if redisContext.Media != "" {
			err = json.Unmarshal([]byte(redisContext.Media), &media)
			if err != nil {
				continue
			}
		}

		if redisContext.XiaosiCommandList != "" {
			err = json.Unmarshal([]byte(redisContext.XiaosiCommandList), &xiaosiCommandList)
			if err != nil {
				continue
			}
		}

		if redisContext.MultiModalInfo != "" {
			err = json.Unmarshal([]byte(redisContext.MultiModalInfo), &multiModalInfo)
			if err != nil {
				continue
			}
		}

		result = append(result, &dto.MemoryContext{
			SessionId:         redisContext.SessionId,
			TimeStamp:         redisContext.TimeStamp,
			Intent:            redisContext.Intent,
			AsrInfo:           redisContext.AsrInfo,
			TtsInfo:           redisContext.TtsInfo,
			TtsNorm:           redisContext.TtsNorm,
			FuncList:          funcList,
			State:             state,
			Response:          response,
			IsLlm:             redisContext.IsLlm,
			RewriteQuery:      redisContext.RewriteQuery,
			Source:            redisContext.Source,
			ImageUrl:          redisContext.ImageUrl,
			VideoUrl:          redisContext.VideoUrl,
			LlmSkill:          redisContext.LlmSkill,
			LlmResponse:       redisContext.LlmResponse,
			DialogueId:        redisContext.DialogueId,
			Media:             media,
			ScenarioType:      redisContext.ScenarioType,
			XiaosiCommandList: xiaosiCommandList,
			SceneCode:         redisContext.SceneCode,
			MixedModalQuery:   redisContext.MixedModalQuery,
			ReasoningContent:  redisContext.ReasoningContent,
			MultiModalInfo:    multiModalInfo,
		})
	}

	sort.Slice(result, func(i, j int) bool {
		return result[i].TimeStamp < result[j].TimeStamp
	})

	return result, nil
}

// 更新上下文
func (d *MemoryContext) UpdateMemoryContextResponse(ctx context.Context, sessionID string, context dto.MemoryContext) error {
	if sessionID == "" {
		return nil
	}
	responseByte, err := json.Marshal(context.Response)
	if err != nil {
		return err
	}

	mediaByte, err := json.Marshal(context.Media)
	if err != nil {
		return err
	}

	hashKey := fmt.Sprintf(dto.MemoryContextHashPrefix, sessionID)
	setMap := map[string]interface{}{
		"response":          string(responseByte),
		"is_llm":            context.IsLlm,
		"source":            context.Source,
		"image_url":         context.ImageUrl,
		"video_url":         context.VideoUrl,
		"llm_skill":         context.LlmSkill,
		"llm_response":      context.LlmResponse,
		"dialogue_id":       context.DialogueId,
		"media":             string(mediaByte),
		"tts_info":          context.TtsInfo,
		"mixed_modal_query": context.MixedModalQuery,
		"reasoning_content": context.ReasoningContent,
	}

	cmd := d.data.rdb.HSet(ctx, hashKey, setMap)

	expire := dto.SecondsNextDayFourAM()
	d.data.rdb.Expire(ctx, hashKey, expire)

	return cmd.Err()
}

func (d *MemoryContext) AddSugMemory(ctx context.Context, userTalID string, sug []string) error {
	if userTalID == "" || len(sug) == 0 {
		return nil
	}

	_, err := d.data.rdb.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		listKey := fmt.Sprintf(dto.MemorySugListKey, userTalID)

		if err := pipe.LPush(ctx, listKey, sug).Err(); err != nil {
			return err
		}

		if err := pipe.LTrim(ctx, listKey, 0, dto.MemorySugListMax-1).Err(); err != nil {
			return err
		}

		if err := pipe.Expire(ctx, listKey, dto.MemorySugListExp).Err(); err != nil {
			return err
		}

		return nil
	})

	return err
}

func (d *MemoryContext) GetSugMemory(ctx context.Context, userTalID string) ([]string, error) {
	listKey := fmt.Sprintf(dto.MemorySugListKey, userTalID)
	sugData, err := d.data.rdb.LRange(ctx, listKey, 0, -1).Result()
	if err != nil {
		return nil, err
	}

	return sugData, nil
}

func (d *MemoryContext) DelSugMemory(ctx context.Context, userTalID string) error {
	listKey := fmt.Sprintf(dto.MemorySugListKey, userTalID)
	cmd := d.data.rdb.Del(ctx, listKey)

	return cmd.Err()
}

func (d *MemoryContext) SetAddMemoryContextRejectFlag(ctx context.Context, sessionID string) error {
	key := fmt.Sprintf(dto.MemoryContextSetRejectFlag, sessionID)
	cmd := d.data.rdb.Set(ctx, key, 1, time.Minute*2)

	return cmd.Err()
}

func (d *MemoryContext) GetAddMemoryContextRejectFlag(ctx context.Context, sessionID string) (int64, error) {
	key := fmt.Sprintf(dto.MemoryContextSetRejectFlag, sessionID)
	cmd := d.data.rdb.Get(ctx, key)

	return cmd.Int64()
}

func (d *MemoryContext) SetMemoryContextMathAgentFlag(ctx context.Context, talId string, uuid string) (string, error) {
	key := fmt.Sprintf(dto.MemoryContextMathAgentFlag, talId)
	cmd := d.data.rdb.Set(ctx, key, uuid, time.Minute*20)

	return cmd.Result()
}

func (d *MemoryContext) GetMemoryContextMathAgentFlag(ctx context.Context, talId string) (string, error) {
	key := fmt.Sprintf(dto.MemoryContextMathAgentFlag, talId)
	cmd := d.data.rdb.Get(ctx, key)
	return cmd.Result()
}

func (d *MemoryContext) DelMemoryContextMathAgentFlag(ctx context.Context, talId string) (int64, error) {
	key := fmt.Sprintf(dto.MemoryContextMathAgentFlag, talId)
	cmd := d.data.rdb.Del(ctx, key)
	return cmd.Result()
}

func (d *MemoryContext) GetMemoryContextKETQAgentFlag(ctx context.Context, talId string) (string, error) {
	key := fmt.Sprintf(dto.MemoryContextKetQAgentFlag, talId)
	cmd := d.data.rdb.Get(ctx, key)
	return cmd.Result()
}

func (d *MemoryContext) SetMemoryContextMathAgentQuestion(ctx context.Context, talId string, question string, rag string) (int64, error) {
	key := fmt.Sprintf(dto.MemoryContextMathAgentQuestion, talId)
	cmd := d.data.rdb.HSet(ctx, key, "question", question, "rag", rag)

	d.data.rdb.Expire(ctx, key, time.Minute*20)
	return cmd.Result()
}

func (d *MemoryContext) GetMemoryContextMathAgentQuestion(ctx context.Context, talId string) (map[string]string, error) {
	key := fmt.Sprintf(dto.MemoryContextMathAgentQuestion, talId)
	cmd := d.data.rdb.HGetAll(ctx, key)
	return cmd.Result()
}

func (d *MemoryContext) DelMemoryContextMathAgentQuestion(ctx context.Context, talId string) (int64, error) {
	key := fmt.Sprintf(dto.MemoryContextMathAgentQuestion, talId)
	cmd := d.data.rdb.Del(ctx, key)
	return cmd.Result()
}

func (d *MemoryContext) SetNXXiaoSiCommand(ctx context.Context, talId string) (bool, error) {
	key := fmt.Sprintf(dto.MemoryContextXiaoSiCommand, talId)
	cmd := d.data.rdb.SetNX(ctx, key, 1, dto.SecondsNextDayFourAM())
	return cmd.Result()
}

func (d *MemoryContext) SetDeepSeekFlag(ctx context.Context, talId string, uuid string) error {
	key := fmt.Sprintf(dto.MemoryContextDeepSeekFlag, talId)
	cmd := d.data.rdb.Set(ctx, key, uuid, time.Hour*15)
	return cmd.Err()
}

func (d *MemoryContext) GetDeepSeekFlag(ctx context.Context, talId string) (string, error) {
	key := fmt.Sprintf(dto.MemoryContextDeepSeekFlag, talId)
	cmd := d.data.rdb.Get(ctx, key)
	return cmd.Result()
}

func (d *MemoryContext) DelDeepSeekFlag(ctx context.Context, talId string) error {
	key := fmt.Sprintf(dto.MemoryContextDeepSeekFlag, talId)
	cmd := d.data.rdb.Del(ctx, key)
	return cmd.Err()
}

func (d *MemoryContext) SetExerciseOcr(ctx context.Context, talId string, exerciseOcr string) error {
	key := fmt.Sprintf(dto.MemoryContextExerciseOcr, talId)
	cmd := d.data.rdb.Set(ctx, key, exerciseOcr, time.Hour*24)
	return cmd.Err()
}

func (d *MemoryContext) GetExerciseOcr(ctx context.Context, talId string) (string, error) {
	key := fmt.Sprintf(dto.MemoryContextExerciseOcr, talId)
	cmd := d.data.rdb.Get(ctx, key)
	return cmd.Result()
}

func (d *MemoryContext) SetWorkshopFlag(ctx context.Context, talId string, uuid string) error {
	key := fmt.Sprintf(dto.MemoryContextWorkshopFlag, talId)
	cmd := d.data.rdb.Set(ctx, key, uuid, time.Hour*24)
	return cmd.Err()
}

func (d *MemoryContext) GetWorkshopFlag(ctx context.Context, talId string) (string, error) {
	key := fmt.Sprintf(dto.MemoryContextWorkshopFlag, talId)
	cmd := d.data.rdb.Get(ctx, key)
	return cmd.Result()
}

func (d *MemoryContext) DelWorkshopFlag(ctx context.Context, talId string) error {
	key := fmt.Sprintf(dto.MemoryContextWorkshopFlag, talId)
	cmd := d.data.rdb.Del(ctx, key)
	return cmd.Err()
}

func (d *MemoryContext) GetAtmsExplainType(ctx context.Context, talId string) (int, error) {
	key := fmt.Sprintf(dto.AtmsExplainType, talId)
	cmd := d.data.rdb.Get(ctx, key)
	return cmd.Int()
}

func (d *MemoryContext) DelAtmsExplainType(ctx context.Context, talId string) error {
	key := fmt.Sprintf(dto.AtmsExplainType, talId)
	cmd := d.data.rdb.Del(ctx, key)
	return cmd.Err()
}

func (d *MemoryContext) SetFiltersFlag(ctx context.Context, talId string, filtersName string) error {
	key := fmt.Sprintf(dto.MemoryContextFilters, talId)
	cmd := d.data.rdb.Set(ctx, key, filtersName, time.Hour*15)
	return cmd.Err()
}

func (d *MemoryContext) GetFiltersFlag(ctx context.Context, talId string) (string, error) {
	key := fmt.Sprintf(dto.MemoryContextFilters, talId)
	cmd := d.data.rdb.Get(ctx, key)
	return cmd.Result()
}

func (d *MemoryContext) DelFiltersFlag(ctx context.Context, talId string) error {
	key := fmt.Sprintf(dto.MemoryContextFilters, talId)
	cmd := d.data.rdb.Del(ctx, key)
	return cmd.Err()
}
