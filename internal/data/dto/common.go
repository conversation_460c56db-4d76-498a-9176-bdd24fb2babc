package dto

import (
	"reflect"
	"time"
)

type Response struct {
	ErrorReason string            `json:"error_reason"`
	ErrorMsg    string            `json:"error_msg"`
	MetaData    map[string]string `json:"meta_data"`
	TraceId     string            `json:"trace_id"`
	ServerTime  int64             `json:"server_time"`
	Data        interface{}       `json:"data"`
}

type BaseResponse struct {
	Data    interface{} `json:"data"`
	Errcode int         `json:"errcode"`
	Errmsg  string      `json:"errmsg"`
	Code    int         `json:"code"`
}

type ExactlyHeader struct {
	XRequestId     string `json:"X-Request-Id"` // sn
	XTalSn         string `json:"X-Tal-Sn"`
	XTalVersion    string `json:"X-Tal-Version"`
	Traceparent    string `json:"Traceparent"`
	XTalDeviceCode string `json:"X-Tal-Devicecode"`
}

// 小学学部
var primarySubjects = []Option{
	{
		Id:   "2",
		Name: "数学",
	},
	{
		Id:   "1",
		Name: "语文",
	},
	{
		Id:   "3",
		Name: "英语",
	},
}

// 小学6年级 山东有生物
var primarySixSubjects = []Option{
	{
		Id:   "2",
		Name: "数学",
	},
	{
		Id:   "1",
		Name: "语文",
	},
	{
		Id:   "3",
		Name: "英语",
	},
	{
		Id:   "6",
		Name: "生物学",
	},
}

// 初中学部
var middleSubjects = []Option{
	{
		Id:   "2",
		Name: "数学",
	},
	{
		Id:   "1",
		Name: "语文",
	},
	{
		Id:   "3",
		Name: "英语",
	},
	{
		Id:   "4",
		Name: "物理",
	},
	{
		Id:   "5",
		Name: "化学",
	},
	{
		Id:   "6",
		Name: "生物学",
	},
	{
		Id:   "21",
		Name: "科学",
	},
	{
		Id:   "7",
		Name: "道德与法治",
	},
	{
		Id:   "9",
		Name: "历史",
	},
	{
		Id:   "8",
		Name: "地理",
	},
}

// 高中学部
var highSubjects = []Option{
	{
		Id:   "2",
		Name: "数学",
	},
	{
		Id:   "1",
		Name: "语文",
	},
	{
		Id:   "3",
		Name: "英语",
	},
	{
		Id:   "4",
		Name: "物理",
	},
	{
		Id:   "5",
		Name: "化学",
	},
	{
		Id:   "6",
		Name: "生物学",
	},
	//{
	//	Id:   "21",
	//	Name: "科学",
	//},
	{
		Id:   "7",
		Name: "道德与法治",
	},
	{
		Id:   "9",
		Name: "历史",
	},
	{
		Id:   "8",
		Name: "地理",
	},
}

// GetSubjects 年级支持的学科
func GetSubjects(grade int) []Option {
	if grade > 9 {
		return highSubjects
	}
	if grade > 6 {
		return middleSubjects
	}

	if grade == 6 {
		return primarySixSubjects
	}

	return primarySubjects
}

// GetSemester 学期
func GetSemester() string {

	// 获取当前时间
	currentTime := time.Now()
	// 构建7月1号的时间
	july01 := time.Date(currentTime.Year(), time.July, 1, 0, 0, 0, 0, time.UTC)

	if currentTime.Before(july01) {
		return "下学期"
	}
	return "上学期"
}

var HomeworkType = []Option{
	{
		Id:   "1028",
		Name: "口算",
	},
	{
		Id:   "1036",
		Name: "语文作文",
	},
	{
		Id:   "1036.1",
		Name: "英语作文",
	},
}

var DictateType = []Option{
	{
		Id:   "1",
		Name: "语文",
	},
	{
		Id:   "3",
		Name: "英语",
	},
}

// MergeStructs s1和s2都有某字段，则其值为s2的值，其余字段取并集
func MergeStructs(s1 DefaultSlot, s2 SlotDict) SlotDict {
	s3 := s2 // 创建一个新的结构体 s3，初始值为 s2

	// 使用反射遍历 DefaultSlot 结构体字段
	s1Value := reflect.ValueOf(s1)
	s3Value := reflect.ValueOf(&s3).Elem()

	for i := 0; i < s1Value.NumField(); i++ {
		fieldName := s1Value.Type().Field(i).Name
		s1FieldValue := s1Value.Field(i)
		s3FieldValue := s3Value.FieldByName(fieldName)

		// 如果 s3 中的字段为零值且 s1 中的字段非零值，则将 s1 中的值赋给 s3
		if s3FieldValue.IsValid() && s3FieldValue.Interface() == reflect.Zero(s3FieldValue.Type()).Interface() && !s1FieldValue.IsZero() {
			s3FieldValue.Set(s1FieldValue)
		}
	}

	return s3
}
