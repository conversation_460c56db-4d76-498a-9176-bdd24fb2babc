package dto

type AutoMathCheckRequest struct {
	Sum           string `json:"sum"`
	ComputeDot    bool   `json:"compute_dot"`
	DotPartMaxLen int    `json:"dot_part_max_len,omitempty"`
	RequestID     string `json:"request_id"`
}

type AutoMathCheckResp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		SolveType     string `json:"solve_type"`
		PreProcessRes string `json:"pre_process_res"`
		IsCalc        bool   `json:"is_calc"`
		OutPut        string `json:"out_put"`
		Result        string `json:"result"`
		VideoURL      string `json:"video_url"`
		KeID          string `json:"ke_id"`
		GID           string `json:"g_id"`
	} `json:"data"`
	TraceID string `json:"trace_id"`
}

type OutPut struct {
	Context string `json:"context"`
	OutType string `json:"out_type"`
	Result  string `json:"result"`
}

type MathSceneModeReq struct {
	RequestId string
	AsrInfo   string
	AsrPinyin string
	TalId     string
	Location  string
	GradeId   string
	DeviceId  string
	Version   string
}
