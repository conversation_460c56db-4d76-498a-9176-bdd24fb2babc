package dto

type ToolsResourceParam struct {
	Type  string `json:"type"`
	Id    int    `json:"id"`
	TalID string `json:"tal_id"`
}

type GetResourceListReq struct {
	List []*ToolsResourceParam `json:"list"`
}

type ToolsMetaInfo struct {
	ID          int    `json:"id"`
	CoverURL    string `json:"cover_url"`
	Name        string `json:"name"`
	BookVersion string `json:"book_version"`
	Grade       string `json:"grade"`
	Term        string `json:"term"`
	UnitName    string `json:"unit_name"`
	Scheme      string `json:"scheme"`
	Subject     string `json:"subject"`
	IsOutside   int    `json:"is_outside"`
}

type ToolsInfo struct {
	Cover        string `json:"cover"`
	ResourceName string `json:"resource_name"`
	VersionName  string `json:"version_name"`
	GradeName    string `json:"grade_name"`
	SemesterName string `json:"semester_name"`
	UnitName     string `json:"unit_name"`
	Scheme       string `json:"scheme"`
	SubjectName  string `json:"subject_name"`
	IsOutside    int    `json:"is_outside"`
}

type GetToolsResourceRes struct {
	ErrorReason string             `json:"error_reason"`
	ErrorMsg    string             `json:"error_msg"`
	MetaData    interface{}        `json:"meta_data"`
	TraceID     string             `json:"trace_id"`
	ServerTime  int                `json:"server_time"`
	Data        GetResourceListRes `json:"data"`
}

type GetResourceListRes struct {
	Recitation []*ToolsMetaInfo `json:"beisong_list"`
	Dictation  []*ToolsMetaInfo `json:"tingxie_list"`
}

type UniDictationItem struct {
	Type string     `json:"type"`
	Item *ToolsInfo `json:"item"`
}

type UserVersionReq struct {
	UserId  string `json:"user_id"`
	Subject int32  `json:"subject"`
}

type GetUserVersionApiRes struct {
	ErrorReason string         `json:"error_reason"`
	ErrorMsg    string         `json:"error_msg"`
	MetaData    interface{}    `json:"meta_data"`
	TraceID     string         `json:"trace_id"`
	ServerTime  int            `json:"server_time"`
	Data        UserVersionRes `json:"data"`
}

type UserVersionRes struct {
	VersionName string `json:"version_name"`
}
