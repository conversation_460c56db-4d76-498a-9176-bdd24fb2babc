package dto

import (
	"github.com/spf13/cast"
	"lui-api/internal/common"
	"strings"
)

const (
	HotfixTypeText         = 1
	HotfixTypeSkill        = 2
	HotfixTypeSimilarQuery = 3
)

const (
	HotfixSnTypeAll    = 1
	HotfixSnTypeAssign = 2
)

type HotfixQueryResponse struct {
	Code      int                     `json:"code"`
	Msg       string                  `json:"msg"`
	Documents [][]HotfixQueryDocument `json:"documents"`
}

type HotfixQueryDocument struct {
	ID           string  `json:"id"`
	SnType       int64   `json:"sn_type"`     // 1:全量 2:指定sn
	SnList       string  `json:"sn_list"`     // sn列表
	HotfixType   int64   `json:"hotfix_type"` // 快修类型:1指定文本 2指定技能 3指定相似query
	Score        float64 `json:"score"`
	Data         string  `json:"data"`
	Text         string  `json:"text"`
	Function     string  `json:"function"`
	Domain       string  `json:"domain"`
	Intent       string  `json:"intent"`
	Query        string  `json:"query"`
	QaID         int64   `json:"qa_id"`
	Operate      string  `json:"operate"`
	TtsShow      string  `json:"tts_show"`
	TtsNorm      string  `json:"tts_norm"`
	TextDataInfo string  `json:"text_data_info"` // TTS情绪、语速、语调、音量配置
	DeviceList   string  `json:"device_list"`    //设备 空串:全部/1：学习机旗舰款/2:学习机经典24/3:学习机经典23/4:学练机
	AgentList    string  `json:"agent_list"`     //智能体 空串:全部/1：小思/2:bella
	Url          string  `json:"url"`
}

type FaqVectorDo struct {
	ID        string  `json:"id"`
	Score     float64 `json:"score"`
	Answer    string  `json:"answer"`
	FaqAnswer string  `json:"faq_answer"`
	Question  string  `json:"question"`
	Text      string  `json:"text"`
}

type HotfixVectorDo struct {
	ID           string  `json:"id"`
	SnType       int64   `json:"sn_type"`     // 1:全量 2:指定sn
	SnList       string  `json:"sn_list"`     // sn列表
	HotfixType   int64   `json:"hotfix_type"` // 快修类型:1指定文本 2指定技能 3指定相似query
	Score        float64 `json:"score"`
	Data         string  `json:"data"`
	Text         string  `json:"text"`
	Function     string  `json:"function"`
	Domain       string  `json:"domain"`
	Intent       string  `json:"intent"`
	Query        string  `json:"query"`
	QaID         int64   `json:"qa_id"`
	Operate      string  `json:"operate"`
	TtsShow      string  `json:"tts_show"`
	TtsNorm      string  `json:"tts_norm"`
	TextDataInfo string  `json:"text_data_info"` // TTS情绪、语速、语调、音量配置
	DeviceList   string  `json:"device_list"`    //设备 空串:全部/1：学习机旗舰款/2:学习机经典24/3:学习机经典23/4:学练机5:25学练机-练习屏，5:25学练机7:新锐学习机8:旗舰25 (T)9:经典25 (S)10:平价25 (P)
	AgentList    string  `json:"agent_list"`     //智能体 空串:全部/1：小思/2:bella
	AsrInfo      string  `json:"asr_info"`       // ASR信息
	Url          string  `json:"url"`
}

func GetHotfixDeviceID(appId string, screenMode int32) string {
	switch appId {
	case "200038":
		fallthrough
	case "200011":
		return "1"
	case "200067":
		fallthrough
	case "200018":
		return "2"
	case "200073":
		fallthrough
	case "200025":
		return "3"
	case "200076":
		fallthrough
	case "200026":
		return "4-" + cast.ToString(screenMode)
	case "200083":
		fallthrough
	case "200031":
		return "5-" + cast.ToString(screenMode)
	case "200078":
		fallthrough
	case "200028":
		return "6"
	case "200087":
		fallthrough
	case "200032":
		return "7"
	case "200088":
		fallthrough
	case "200033":
		return "8"
	case "200089":
		fallthrough
	case "200035":
		return "9"
	default:
		return ""
	}
}

func GetHotfixAgentID(bizType string) string {
	if strings.HasPrefix(bizType, common.BizTypeAiTutor) {
		return "2"
	} else {
		return "1"
	}
}

type VdbTextDataInfoDO struct {
	Item  string `json:"item"`
	Value string `json:"value"`
}

// VdbPoetAuthorInfo 查诗人信息
type VdbPoetAuthorInfo struct {
	Author          string `json:"author"`           //诗人名字
	PoetName        string `json:"poet_name"`        //诗名
	AuthorDynasty   string `json:"author_dynasty"`   //诗人朝代
	PoetContent     string `json:"poet_content"`     //诗歌内容
	AuthorAttribute string `json:"author_attribute"` //诗人属性
}

// VdbPoeticLines 查某首/句诗
type VdbPoeticLines struct {
	Author        string `json:"author"`         //诗人名字
	PoetName      string `json:"poet_name"`      //诗名
	AuthorDynasty string `json:"author_dynasty"` //诗人朝代
	PoetTags      string `json:"poet_tags"`      //诗歌标签
	PoetKeyword   string `json:"poet_keyword"`   //诗歌关键词
	PoetForm      string `json:"poet_form"`      //诗歌形式
}

// VdbPoetInterpretationAppreciationSource 查诗歌释义/赏析/出处
type VdbPoetInterpretationAppreciationSource struct {
	Author        string `json:"author"`         //诗人名字
	PoetName      string `json:"poet_name"`      //诗名
	AuthorDynasty string `json:"author_dynasty"` //诗人朝代
	PoetContent   string `json:"poet_content"`   //诗歌内容
	AnalysisType  string `json:"analysis_type"`  //解析类型
}

// TranslateSentence 翻译句子
type TranslateSentence struct {
	TranslateType    string `json:"translate_type"`    //翻译方式
	TranslateContent string `json:"translate_content"` //需要翻译的语句
}

type HotfixVectorResourceDO struct {
	GradeIDs     []string `json:"grade_ids"`
	SemesterIDs  []string `json:"semester_ids"`
	ResourceType string   `json:"type"`
	ResourceId   string   `json:"resource_id"`
	ResourceName string   `json:"resource_name"`
	SubjectId    int      `json:"subject_id"`
	SubjectName  string   `json:"subject_name"`
	VersionId    int      `json:"version_id"`
	VersionName  string   `json:"version_name"`

	// 课程字段
	CourseSystemId int    `json:"course_system_id"`
	CourseId       string `json:"course_id"`

	// 素养字段
	AlbumIDs []string `json:"album_ids"`
}
