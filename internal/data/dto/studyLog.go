package dto

type StudyLogReq struct {
	TalID        string `json:"tal_id"`
	Subject      int    `json:"subject"` //学科类型的入参学科的subjectId
	CourseSystem int    `json:"course_system"`
	CntType      int    `json:"cnt_type"`
	Grade        int    `json:"grade"`
}

type StudyLog struct {
	JumpType      int                          `json:"jump_type"`
	SchemeURL     string                       `json:"scheme_url"`
	FindData      []*StudyLogDetail            `json:"find_data"`
	AlbumFindData []*StudyCultivationLogDetail `json:"album_find_data"`
}

type StudyLogRes struct {
	Errcode     int       `json:"errcode"`
	Errmsg      string    `json:"errmsg"`
	Data        *StudyLog `json:"data"`
	Trace       string    `json:"trace"`
	DataType    int       `json:"data_type"`
	OtelTraceID string    `json:"otel_trace_id"`
	OtelSpanID  string    `json:"otel_span_id"`
}

type StudyLogDetail struct {
	CourseID        string   `json:"course_id"`
	PlanID          string   `json:"plan_id"`
	UnitID          string   `json:"unit_id"`
	LessonID        string   `json:"lesson_id"`
	ModuleID        string   `json:"module_id"`
	ModuleName      string   `json:"module_name"`
	SubModuleID     string   `json:"sub_module_id"`
	CourseSystem    int      `json:"course_system"`
	CourseSystemStr string   `json:"course_system_str"`
	Grade           int      `json:"grade"`
	GradeStr        string   `json:"grade_str"`
	Subject         int      `json:"subject"`
	SubjectStr      string   `json:"subject_str"`
	Semester        int      `json:"semester"`
	SemesterStr     string   `json:"semester_str"`
	Version         int      `json:"version"`
	VideoTime       int      `json:"video_time"`
	VersionName     string   `json:"version_name"`
	Navigation      []string `json:"navigation"`
	SchemeURL       string   `json:"scheme_url"`
	IconUrl         string   `json:"icon_url"`
	Cover           string   `json:"cover"`
	IconType        int      `json:"icon_type"`
}

type UniLearnTrack struct {
	SubjectName      string   `json:"subject_name"`
	SemesterName     string   `json:"semester_name"`
	VersionName      string   `json:"version_name"`
	GradeName        string   `json:"grade_name"`
	CourseSystemName string   `json:"course_system_name"`
	ResourceName     string   `json:"resource_name"`
	VideoTime        int      `json:"video_time"`
	Navigation       []string `json:"navigation"`
	Scheme           string   `json:"scheme"`
	Cover            string   `json:"cover"`
	IconType         int      `json:"icon_type"`
	ModuleName       string   `json:"module_name"`
}

type UniLearnTrackItem struct {
	Type string         `json:"type"`
	Item *UniLearnTrack `json:"item"`
	Attr []Attr         `json:"attr"`
}

type UniCultivationTrack struct {
	Title        string `json:"title"`
	Cover        string `json:"cover"`
	CategoryName string `json:"category_name"`
	Scheme       string `json:"scheme"`
}

type StudyCultivationLogDetail struct {
	AlbumTitle      string `json:"album_title"`
	AlbumCover      string `json:"album_cover"`
	CategoryName    string `json:"category_name"`
	ResourceTotal   int    `json:"resource_total"`
	LatestStudyTime int    `json:"latest_study_time"`
	SchemeURL       string `json:"scheme_url"`
	VideoTitle      string `json:"video_title"`
}

type UniCultivationTrackItem struct {
	Type string               `json:"type"`
	Item *UniCultivationTrack `json:"item"`
}

type UserCourseChoiceReq struct {
	TalID        string `json:"tal_id"`
	Subject      int32  `json:"subject"`
	Grade        int32  `json:"grade"`
	CourseSystem int32  `json:"course_system"`
	DeviceId     string `json:"device_id"`
	BaodianSignParam
}

type UserCourseChoiceResp struct {
	CourseSystem      int32  `json:"course_system"`
	CourseSystemAlias string `json:"course_system_alias"`
	Version           int32  `json:"version"`
	VersionName       string `json:"version_name"`
	Subject           int32  `json:"subject"`
	SubjectAlias      string `json:"subject_alias"`
}
