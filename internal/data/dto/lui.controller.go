package dto

import "lui-api/internal/common"

type LuiControllerReq struct {
	RequestId   string                   `json:"request_id"`
	AsrInput    AsrInput                 `json:"asr_input"`
	UserSystem  UserSystemPad24LUI       `json:"user_system"`
	SceneCode   common.SceneCode         `json:"scene_code"`
	Contacts    []Contact                `json:"contacts"`
	SessionId   string                   `json:"session_id"`
	Source      string                   `json:"source"`
	PagePrompt  string                   `json:"page_prompt"`
	HistoryInfo []*XPodMemoryContextResp `json:"history_info"`
}

type Contact struct {
	ContactName string `json:"contact_name"`
	ContactID   string `json:"contact_id"`
}

type LuiFunction struct {
	FuncName       string      `json:"func_name"`
	FuncParamaters interface{} `json:"func_paramaters"`
	Intent         string      `json:"intent"`
	IsSee2say      string      `json:"is_see2say"`
}
type LuiFunctionReq struct {
	FuncName       string `json:"func_name"`
	FuncParamaters string `json:"func_paramaters"`
	Intent         string `json:"intent"`
	IsSee2say      string `json:"is_see2say"`
}
type FuncList struct {
	FuncList []*LuiFunction `json:"func_list"`
}
type LuiControllerRes struct {
	Code    int      `json:"code"`
	Data    FuncList `json:"data"`
	Msg     string   `json:"msg"`
	TraceID string   `json:"trace_id"`
}

type ContactInfo struct {
	ContactName string `json:"contactName"`
	ContactID   string `json:"contactId"`
}

type SkillHubUserInfo struct {
	Contacts []*ContactInfo `json:"contacts"`
}

type SkillCallReq struct {
	UserInfo  SkillHubUserInfo  `json:"user_info"`
	Functions []*LuiFunctionReq `json:"functions"`
}

type ContactListGetRes struct {
	Code int `json:"code"`
	Data struct {
		Contacts []*ContactInfo `json:"contactList"`
		UserId   string         `json:"userId"`
	} `json:"data"`
	Msg     string `json:"msg"`
	TraceID string `json:"trace_id"`
}
