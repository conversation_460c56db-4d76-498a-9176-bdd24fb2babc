package dto

import "lui-api/internal/common"

type NluReq struct {
	UserId     string     `json:"userId"`
	RequestId  string     `json:"requestId"`
	SessionId  string     `json:"sessionId"`
	TimeStamp  int64      `json:"timeStamp"`
	Source     string     `json:"source"`
	Version    string     `json:"version"`
	AsrInput   AsrInput   `json:"asr_input"`
	UserSystem UserSystem `json:"user_system"`
}

type Nlu4LUIReq struct {
	RequestId    string         `json:"requestId"`
	AsrInput     AsrInput       `json:"asr_input"`
	UserSystem   UserSystem4LUI `json:"user_system"`
	SlotFillList []Slot         `json:"slot_fill_list"`
}

type Slot struct {
	Key  string `json:"key"`
	Id   string `json:"id"`
	Name string `json:"name"`
}

type AsrInput struct {
	AsrLen    int    `json:"asr_len"`
	AsrInfo   string `json:"asr_info"`
	AsrPinyin string `json:"asr_pinyin"`
}

type UserSystem struct {
	Location string `json:"location"`
	Grade    int    `json:"grade"`
}

type UserSystem4LUI struct {
	Location string `json:"location"`
	Grade    string `json:"grade"`
	Semester string `json:"semester"`
	TalId    string `json:"tal_id"`
	DeviceId string `json:"device_id"`
}

type NluRes struct {
	Code    int     `json:"code"`
	Msg     string  `json:"msg"`
	TraceID string  `json:"trace_id"`
	NluData NluData `json:"data"`
}

type NluData struct {
	UserID        string      `json:"user_id"`
	RequestID     string      `json:"request_id"`
	Skill         string      `json:"skill"`
	Task          string      `json:"task"`
	TtsShow       string      `json:"tts_show"`
	TtsNorm       string      `json:"tts_norm"`
	Count         int         `json:"count"`
	ShouldConfirm bool        `json:"should_confirm"`
	ShowTypeList  []string    `json:"show_type_list"`
	ShowCarType   int         `json:"show_car_type"`
	IsResultValid bool        `json:"is_result_valid"`
	Command       interface{} `json:"command"`
	NearWord      interface{} `json:"near_word"`
	Widget        string      `json:"widget"`
	Source        string      `json:"source"`
	Version       string      `json:"version"`
	Input         string      `json:"input"`

	OriginNluResp  interface{}     `json:"origin_nlu_resp"`
	LegalCheckType string          `json:"legal_check_type"`
	FaqVector      *FaqVectorDo    `json:"faq_vector"`
	HotfixVector   *HotfixVectorDo `json:"hotfix_vector"`
}

type Nlu4LuiRes struct {
	Code    int         `json:"code"`
	Data    Nlu4LuiData `json:"data"`
	Msg     string      `json:"msg"`
	TraceID string      `json:"trace_id"`
}

type Nlu4LuiData struct {
	RequestID        string      `json:"request_id"`
	ModuleName       string      `json:"module_name"`
	IsValidWidget    bool        `json:"is_valid_widget"`
	ResultConfidence int         `json:"result_confidence"`
	NeedConfirm      bool        `json:"need_confirm"`
	Skill            string      `json:"skill"`
	SubIntent        int         `json:"sub_intent"`
	DefaultSlots     DefaultSlot `json:"default_slots"`
	SlotDict         SlotDict    `json:"slot_dict"`
	RawSlotDict      RawSlotDict `json:"raw_slot_dict"`
	AllSlots         SlotDict    `json:"all_slots"`
	Category         int32       `json:"category"`
	ResourceList     []Resource  `json:"resource_list"`
	Word             []string    `json:"word"`
	Hit              bool        `json:"hit"`
	SimScore         float64     `json:"sim_score"`
	SearchFlag       bool        `json:"search_flag"`
	OriginNluResp    interface{} `json:"origin_nlu_resp"`
}

// DefaultSlot 用户基础槽位
type DefaultSlot struct {
	GradeName    string `json:"grade_name,omitempty"`
	SemesterName string `json:"semester_name,omitempty"`
	VersionName  string `json:"version_name,omitempty"`
}

// SlotDict 用户槽位
type SlotDict struct {
	Category     string `json:"category,omitempty"`
	ResourceName string `json:"resource_name,omitempty"`

	SubjectId    int    `json:"subject_id"`
	SubjectName  string `json:"subject_name,omitempty"`
	SemesterId   int    `json:"semester_id"`
	SemesterName string `json:"semester_name,omitempty"`
	VersionId    int    `json:"version_id"`
	VersionName  string `json:"version_name,omitempty"`
	GradeId      int    `json:"grade_id"`
	GradeName    string `json:"grade_name,omitempty"`

	CourseSystemId   int    `json:"course_system_id,omitempty"`
	CourseSystemName string `json:"course_system_name,omitempty"`
	CourseId         int    `json:"course_id,omitempty"`
	CourseName       string `json:"course_name,omitempty"`

	CatalogId     int    `json:"catalog_id,omitempty"`
	CatalogName   string `json:"catalog_name,omitempty"`
	AlbumId       int    `json:"album_id,omitempty"`
	AlbumName     string `json:"album_name,omitempty"`
	ChildColumnId int    `json:"child_column_id,omitempty"`
	ColumnId      int    `json:"column_id,omitempty"`

	PaperTypeName  string `json:"paper_type_name,omitempty"`
	PaperType      int    `json:"paper_type,omitempty"`
	CupTypeName    string `json:"cup_type_name,omitempty"`
	CupType        int    `json:"cup_type,omitempty"`
	ProvinceName   string `json:"province_name,omitempty"`
	ProvinceId     int    `json:"province_id,omitempty"`
	CityName       string `json:"city_name,omitempty"`
	AreaName       string `json:"area_name,omitempty"`
	SchoolName     string `json:"school_name,omitempty"`
	YearName       int    `json:"year_name,omitempty"`
	DifficultyName string `json:"difficulty_name,omitempty"`
	Difficulty     int    `json:"difficulty,omitempty"`
	IsFamousSchool string `json:"is_famous_school,omitempty"`
	PaperScope     int    `json:"scope,omitempty"`
	ProblemCount   int    `json:"problem_count,omitempty"`
	SearchFlag     bool   `json:"search_flag,omitempty"`
	CollectionName string `json:"collection_name,omitempty"` //合集名，在找图书、找报刊、找纪录片时使用
	BookName       string `json:"book_name,omitempty"`       //书名，在找图书时使用
	Author         string `json:"author,omitempty"`          //作者，在找图书时使用
	ThemeLabel     string `json:"theme_label,omitempty"`     //主题标签，在找图书、找记录片时使用
	NewspaperName  string `json:"newspaper_name,omitempty"`  //报刊名，在找报刊时使用
	SeriesName     string `json:"series_name,omitempty"`     //系列名，在找纪录片时使用
	QuestionIndex  int    `json:"question_index,omitempty"`  //题目序号，在查题时使用
}

type RawSlotDict struct {
	SubjectName      string `json:"subject_name,omitempty"`
	SemesterName     string `json:"semester_name,omitempty"`
	VersionName      string `json:"version_name,omitempty"`
	GradeName        string `json:"grade_name,omitempty"`
	CourseSystemName string `json:"course_system_name,omitempty"`
	CourseName       string `json:"course_name,omitempty"`
	CatalogName      string `json:"catalog_name,omitempty"`
	AlbumName        string `json:"album_name,omitempty"`
	PaperTypeName    string `json:"paper_type_name,omitempty"`
	CupTypeName      string `json:"cup_type_name,omitempty"`
	ProvinceName     string `json:"province_name,omitempty"`
	CityName         string `json:"city_name,omitempty"`
	AreaName         string `json:"area_name,omitempty"`
	SchoolName       string `json:"school_name,omitempty"`
	YearName         string `json:"year_name,omitempty"`
	DifficultyName   string `json:"difficulty_name,omitempty"`
	IsFamousSchool   string `json:"is_famous_school,omitempty"`
	CollectionName   string `json:"collection_name,omitempty"`
	BookName         string `json:"book_name,omitempty"`
	Author           string `json:"author,omitempty"`
	ThemeLabel       string `json:"theme_label,omitempty"`
	NewspaperName    string `json:"newspaper_name,omitempty"`
	SeriesName       string `json:"series_name,omitempty"`
}

type Resource struct {
	// 以下为资源搜索命中字段
	ResourceType string `json:"resource_type"`
	ResourceId   string `json:"resource_id"`
	ResourceName string `json:"resource_name"`
	Category     int    `json:"category"`

	// 以下为基本属性字段，个资源大类按需所取
	GradeId      int    `json:"grade_id"`
	GradeName    string `json:"grade_name,omitempty"`
	SemesterId   int    `json:"semester_id"`
	SemesterName string `json:"semester_name,omitempty"`
	SubjectId    int    `json:"subject_id"`
	SubjectName  string `json:"subject_name,omitempty"`
	VersionId    int    `json:"version_id"`
	VersionName  string `json:"version_name,omitempty"`

	// 课程字段
	CourseSystemId int    `json:"course_system_id"`
	CourseId       string `json:"course_id,omitempty"`

	// 素养字段
	CatalogId int `json:"catalog_id,omitempty"`
	AlbumId   int `json:"album_id,omitempty"`

	// 试卷字段
	PaperType      int    `json:"paper_type,omitempty"`
	PaperTypeName  string `json:"paper_type_name,omitempty"`
	CupType        int    `json:"cup_type,omitempty"`
	CupTypeName    string `json:"cup_type_name,omitempty"`
	IsFamousSchool int    `json:"is_famous_school,omitempty"`
	ProvinceId     int    `json:"province_id,omitempty"`
	ProvinceName   string `json:"province_name,omitempty"`
	Year           int    `json:"year,omitempty,omitempty"`
	DifficultyId   int    `json:"difficulty_id,omitempty"`
	DifficultyName string `json:"difficulty_name,omitempty"`
	SchoolId       int    `json:"school_id,omitempty"`
	SchoolName     string `json:"school_name,omitempty"`

	//精准学1.5
	ParentNodeNames string `json:"parent_node_names" bson:"parent_node_names,omitempty"`

	Sort         int    `json:"sort,omitempty"`
	VipLevel     int    `json:"vip_level,omitempty"`
	Cover        string `json:"cover,omitempty"`
	Author       string `json:"author,omitempty"`
	Introduction string `json:"introduction,omitempty"`
}

type Nlu4LuiWidget struct {
	ModuleName       string      `json:"module_name"`
	IsValidWidget    bool        `json:"is_valid_widget"`
	ResultConfidence int         `json:"result_confidence"`
	NeedConfirm      bool        `json:"need_confirm"`
	Content          interface{} `json:"content"`
}

type Nlu4LuiWidgetContent struct {
	Category      int         `json:"category"`
	Scheme        string      `json:"scheme"`
	SlotDict      SlotDict    `json:"slot_dict"`
	Toast         string      `json:"toast"`
	ToastDuration int         `json:"toast_duration"`
	Title         string      `json:"title"`
	Word          []string    `json:"word"`
	Filters       []Filter    `json:"filters"`
	Confirm       *Confirm    `json:"confirm"`
	Data          interface{} `json:"data"`
}

type Nlu4LuiWidgetContentList struct {
	Scheme string `json:"scheme"`
}

// Filter 筛选条件
type Filter struct {
	Key     string   `json:"key"`
	Name    string   `json:"name"`
	Options []Option `json:"options"`
}

type Confirm struct {
	AsrInfo   string    `json:"asr_info"`
	AsrPinyin string    `json:"asr_pinyin"`
	Key       string    `json:"key"`
	Name      string    `json:"name"`
	Options   []Option  `json:"options"`
	Slots     *SlotDict `json:"slots"`
}

// Option 选项KV
type Option struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

type IPAnalysis struct {
	Accuracy  string `json:"accuracy"`
	Adcode    string `json:"adcode"`
	Areacode  string `json:"areacode"`
	Asnumber  string `json:"asnumber"`
	City      string `json:"city"`
	Continent string `json:"continent"`
	Country   string `json:"country"`
	Province  string `json:"province"`
}

type IPAnalysisRes struct {
	ErrorReason string      `json:"error_reason"`
	ErrorMsg    string      `json:"error_msg"`
	MetaData    interface{} `json:"meta_data"`
	TraceID     string      `json:"trace_id"`
	ServerTime  int         `json:"server_time"`
	Data        IPAnalysis  `json:"data"`
}

type IPRequest struct {
	IP string `json:"ip"`
}

type PadV2Nlu4LuiRes struct {
	Code    int              `json:"code"`
	Data    NLUQueryPad2Resp `json:"data"`
	Msg     string           `json:"msg"`
	TraceID string           `json:"trace_id"`
}

type NLUQueryPad2Resp struct {
	UserID            string                 `json:"user_id"`
	RequestID         string                 `json:"request_id"`
	Version           string                 `json:"version"`
	Input             string                 `json:"input"`
	SessionId         string                 `json:"session_id"`
	SkillList         []NLUSkillItem         `json:"skill_list"`
	ModelOutputIntent string                 `json:"model_output_intent"`
	SceneCode         int                    `json:"scene_code"`
	HotfixVector      *HotfixVectorDo        `json:"hotfix_vector"`
	LegalCheckType    string                 `json:"legal_check_type"`
	ControllerVersion string                 `json:"controller_version"`
	FunctionList      []Function             `json:"function_list"`
	Rewrite           NLUControllerRewrite   `json:"rewrite"`
	SceneResult       *SceneResult           `json:"scene_result"`
	RewriteInfo       *ControllerRewriteInfo `json:"rewrite_info"`
	MixedModalInfo    *MixedModalInfo        `json:"mixed_modal_info"`
	DeepSeekInfo      *DeepSeekInfo          `json:"deep_seek_info"`
	FiltersInfo       *FiltersInfo           `json:"filters_info"`
	VisionInput       *VisionInput           `json:"vision_input"` // 视觉输入
	BizType           string                 `json:"biz_type"`
	ExtraResp         *ExtraResp             `json:"extra_resp"`
}

type ExtraResp struct {
	UnderScreenExercises bool            `json:"under_screen_exercises"` // 是否是屏下查题意图
	MultiModalInfo       *MultiModalInfo `json:"multi_modal_info"`
}

type DeepSeekInfo struct {
	DeepSeekFlag   bool `json:"deep_seek_flag"`
	DeepSeekIntent bool `json:"deep_seek_intent"`
}

type FiltersInfo struct {
	FiltersFlag bool   `json:"filters_flag"`
	FiltersName string `json:"filters_name"`
	ToSkill     bool   `json:"to_skill"`
}

type MixedModalInfo struct {
	MixedModal bool                     `json:"mixed_modal"`
	OcrType    common.MixedModalOcrType `json:"ocr_type"`
}

type NLUSkillItem struct {
	FuncName           string           `json:"func_name"`
	Skill              string           `json:"skill"`
	Task               string           `json:"task"`
	TtsShow            string           `json:"tts_show"`
	TtsNorm            string           `json:"tts_norm"`
	Count              int              `json:"count"`
	ShowTypeList       []string         `json:"show_type_list"`
	ShowCarType        int              `json:"show_car_type"`
	IsResultValid      bool             `json:"is_result_valid"`
	Command            interface{}      `json:"command"`
	NearWord           interface{}      `json:"near_word"`
	Widget             string           `json:"widget"`
	DefaultSlots       DefaultSlot      `json:"default_slots"`
	SlotDict           SlotDict         `json:"slot_dict"`
	RawSlotDict        RawSlotDict      `json:"raw_slot_dict,omitempty"`
	AllSlots           SlotDict         `json:"all_slots"`
	Category           int32            `json:"category,omitempty"`
	ResourceList       []Resource       `json:"resource_list,omitempty"`
	Word               []string         `json:"word,omitempty"`
	Hit                bool             `json:"hit,omitempty"`
	SimScore           float64          `json:"sim_score"`
	SceneCode          int              `json:"scene_code"`
	AsrInfo            string           `json:"asr_info"`
	SessionId          string           `json:"session_id"`
	SentenceId         int32            `json:"sentence_id"`
	IsAccessingLLM     bool             `json:"is_accessing_llm"`
	AiAgent            string           `json:"ai_agent"`
	SceneMode          int              `json:"scene_mode"`
	IsSee2say          string           `json:"is_see2say"`
	NluTtsConfig       NluTtsConfig     `json:"nlu_tts_config"`
	MultiDialogSubject bool             `json:"multidialog_subject"`
	MultiDialogVersion bool             `json:"multidialog_version"`
	XiaosiCommandList  []XiaosiCommands `json:"xiaosi_command_list,omitempty"`
	SceneResult        *SceneResult     `json:"scene_result"`
	SearchFlag         bool             `json:"search_flag"`
	Dispatch           *Dispatch        `json:"dispatch"`
	ShowType           int              `json:"show_type"` // 0落地页 1卡片
	MixedModalData     interface{}      `json:"mixed_modal_data"`
	AsrStartTimestamp  int64            `json:"asr_start_timestamp,omitempty"`
	AsrEndTimestamp    int64            `json:"asr_end_timestamp,omitempty"`
	QuestionV2         *QuestionV2      `json:"question_v2,omitempty"`
	IsNoImage          bool             `json:"is_no_image"`
	AiApi              string           `json:"ai_api"`
}

type NLUSkillItemSimple struct {
	Skill          string      `json:"skill"`
	Task           string      `json:"task"`
	Widget         string      `json:"widget"`
	ShowTypeList   []string    `json:"show_type_list"`
	ShowCarType    int         `json:"show_car_type"`
	IsResultValid  bool        `json:"is_result_valid"`
	Dispatch       *Dispatch   `json:"dispatch"`
	Command        interface{} `json:"command"`
	MixedModalData interface{} `json:"mixed_modal_data"`
}

type NluTtsConfig struct {
	Volume       string `json:"volume"`        // 音量
	Rate         string `json:"rate"`          // 语速
	Pitch        string `json:"pitch"`         // 语调
	ExpressStyle string `json:"express_style"` // 表达风格
}
type TtsParam struct {
	VoiceId string `json:"voice_id"`
}

type QueryPad2Resp struct {
	TtsShow           string                 `json:"tts_show"`
	TtsNorm           string                 `json:"tts_norm"`
	TtsParam          *TtsParam              `json:"tts_param"`
	Data              []*Pad2SkillData       `json:"data"`
	ModelOutputIntent string                 `json:"model_output_intent"`
	ConfirmSkill      bool                   `json:"confirm_skill,omitempty"`
	LegalCheckType    string                 `json:"legal_check_type,omitempty"`
	OriginNluResp     interface{}            `json:"origin_nlu_resp,omitempty"`
	FaqVector         *FaqVectorDo           `json:"faq_vector,omitempty"`
	RejectRecType     int                    `json:"reject_rec_type,omitempty"`
	HotfixVector      *HotfixVectorDo        `json:"hotfix_vector"`
	ControllerVersion string                 `json:"controller_version"`
	IsSessionEnd      bool                   `json:"is_session_end"`
	EndReason         string                 `json:"end_reason"`
	Dispatch          *Dispatch              `json:"dispatch"`
	ShowType          int                    `json:"show_type"` // 0落地页 1卡片
	RewriteInfo       *ControllerRewriteInfo `json:"rewrite_info,omitempty"`

	// 多模数据
	MixedModal     common.MixedModal        `json:"mixed_modal,omitempty"`
	OcrType        common.MixedModalOcrType `json:"ocr_type,omitempty"`
	MixedModalData *MixedModalData          `json:"mixed_modal_data,omitempty"`
	VisionInput    *VisionInput             `json:"vision_input,omitempty"`
	BizType        string                   `json:"biz_type,omitempty"`
}

type MixedModalData struct {
	TalId        string                   `json:"tal_id"`
	SessionId    string                   `json:"session_id"`
	SentenceId   int32                    `json:"sentence_id"`
	GradeId      string                   `json:"grade_id"`
	AsrInfo      string                   `json:"asr_info"`
	Intent       string                   `json:"intent"`
	RnVersion    string                   `json:"rn_version"`
	FunctionCall *FunctionCall            `json:"function_call"`
	OcrType      common.MixedModalOcrType `json:"ocr_type,omitempty"`
}

type QueryPad2RespSimple struct {
	Data     []*Pad2SkillDataSimple `json:"data"`
	Dispatch *Dispatch              `json:"dispatch"`
	ShowType int                    `json:"show_type"` // 0落地页 1卡片
}

type Dispatch struct {
	OpenType int    `json:"open_type"` //打开方式 1-h5 2-rn 对应bundle字段 3-App(插件化) 对应weight 4-系统指令 5-App打开/关闭
	Router   Router `json:"router"`
}

type Router struct {
	Bundle []Bundle `json:"bundle"`
	Web    Web      `json:"web"`
}

type Bundle struct {
	BundleName    string `json:"bundle_name"`
	BundleVersion string `json:"bundle_version"`
	Force         int32  `json:"force"`
	Level         string `json:"level"`
	CheckSum      string `json:"check_sum"`
	DownloadUrl   string `json:"download_url"`
}
type Web struct {
	Url   string                 `json:"url"`
	Extra map[string]interface{} `json:"extra"`
}

type Pad2SkillData struct {
	Skill          string `json:"skill"`
	Sort           int
	VerticalDomain string        `json:"vertical_domain"`
	Data           *NLUSkillItem `json:"data"`
	ModuleId       int           `json:"module_id"`
	ParserId       int           `json:"parser_id"`
}

type Pad2SkillDataSimple struct {
	Skill          string `json:"skill"`
	Sort           int
	VerticalDomain string              `json:"vertical_domain"`
	ModuleId       int                 `json:"module_id"`
	ParserId       int                 `json:"parser_id"`
	Data           *NLUSkillItemSimple `json:"data"`
}

type NluPad24LUIReq struct {
	RequestId    string             `json:"requestId"`
	AsrInput     AsrInput           `json:"asr_input"`
	UserSystem   UserSystemPad24LUI `json:"user_system"`
	SlotFillList []Slot             `json:"slot_fill_list"`
	SceneCode    common.SceneCode   `json:"scene_code"`
	Source       string             `json:"source"`
	Version      string             `json:"version"`
	AppId        string             `json:"app_id"`
}

type UserSystemPad24LUI struct {
	Location string `json:"location"`
	Grade    int    `json:"grade"`
	Semester string `json:"semester"`
	TalId    string `json:"tal_id"`
	DeviceId string `json:"device_id"`
}

type WebSkillInfo struct {
	VerticalDomain string `json:"vertical_domain"`
	ModuleId       int    `json:"module_id"`
	ParserId       int    `json:"parser_id"`
}

type CommendTmp struct {
	API   string          `json:"api"`
	Param CommendParamTmp `json:"param"`
}

type CommendParamTmp struct {
	Appid       string `json:"appid" mapstructure:"appid"`
	Scheme      string `json:"scheme" mapstructure:"scheme"`
	Value       string `json:"value" mapstructure:"value"`
	Method      string `json:"method" mapstructure:"method"`
	Operate     string `json:"operate" mapstructure:"operate"`
	PackageName string `json:"package_name" mapstructure:"package_name"`
}

var WebSkillInfoMap = map[string]*WebSkillInfo{
	"zh_dic": {
		VerticalDomain: "字词翻译",
		ModuleId:       1,
		ParserId:       3,
	},
	"en_dic": {
		VerticalDomain: "字词翻译",
		ModuleId:       1,
		ParserId:       3,
	},
	"zh_to_en": {
		VerticalDomain: "字词翻译",
		ModuleId:       1,
		ParserId:       3,
	},
	"baike": {
		VerticalDomain: "百科问答",
		ModuleId:       1,
		ParserId:       3,
	},
	"baike.knowledge": {
		VerticalDomain: "知识问答",
		ModuleId:       1,
		ParserId:       3,
	},
	"poem": {
		VerticalDomain: "诗词学习",
		ModuleId:       1,
		ParserId:       3,
	},
	"search_course": {
		VerticalDomain: "课程学习",
		ModuleId:       1,
		ParserId:       1,
	},
	"material": {
		VerticalDomain: "作文素材",
		ModuleId:       1,
		ParserId:       3,
	},
	"search_paper": {
		VerticalDomain: "真题试卷",
		ModuleId:       1,
		ParserId:       1,
	},
	"paper_subject_confirm": {
		VerticalDomain: "真题试卷",
		ModuleId:       1,
		ParserId:       1,
	},
	"search_quality": {
		VerticalDomain: "素养视频",
		ModuleId:       1,
		ParserId:       1,
	},
	"listen_write": {
		VerticalDomain: "学习助手",
		ModuleId:       2,
		ParserId:       2,
	},
	"recite": {
		VerticalDomain: "学习助手",
		ModuleId:       2,
		ParserId:       2,
	},
	"click_read": {
		VerticalDomain: "学习助手",
		ModuleId:       2,
		ParserId:       2,
	},
	"continue_learn_course": {
		VerticalDomain: "课程学习",
		ModuleId:       1,
		ParserId:       1,
	},
	"continue_learn_quality": {
		VerticalDomain: "素养视频",
		ModuleId:       1,
		ParserId:       1,
	},
	"search_exercise": {
		VerticalDomain: "题目练习",
		ModuleId:       1,
		ParserId:       4,
	},
	"book": {
		VerticalDomain: "图书",
		ModuleId:       1,
		ParserId:       5,
	},
	"newspaper": {
		VerticalDomain: "报刊",
		ModuleId:       1,
		ParserId:       5,
	},
	"documentary": {
		VerticalDomain: "纪录片",
		ModuleId:       1,
		ParserId:       5,
	},
}

type PadV2RejectCheckRes struct {
	Code int `json:"code"`
	Data struct {
		IsReject bool `json:"is_reject"`
	} `json:"data"`
	Msg     string `json:"msg"`
	TraceID string `json:"trace_id"`
}

type NLUQuery struct {
	RequestID         string             `json:"requestId"`  //
	SessionID         string             `json:"session_id"` //
	Continuous        bool               `json:"continuous"`
	SentenceID        int32              `json:"sentence_id"`
	UserSystem        UserSystemV2       `json:"user_system"`
	AsrInput          AsrInput           `json:"asr_input"`
	Source            string             `json:"source"`
	Version           string             `json:"version"`
	SlotFillList      []NLUSlotDict      `json:"slot_fill_list"`
	SceneCode         common.SceneCode   `json:"scene_code"` // 场景code，为1表示作业模式
	ControllerResp    *NLUControllerResp `json:"controller_resp,omitempty"`
	ContextInfo       *ContextInfo       `json:"context_info,omitempty"`
	LlmAgent          LlmAgent           `json:"llm_agent"`
	AppId             string             `json:"app_id"`
	PagePrompt        string             `json:"page_prompt"`
	PageLayoutData    interface{}        `json:"page_layout_data"`
	MrIntent          int                `json:"mr_intent"`            // 多轮意图理解 1:多轮 0:单轮
	MultiModalNluFlag int                `json:"multi_modal_nlu_flag"` // 多模态标识 1:走多模nlu 0:走非多模nlu
	VisionInput       *VisionInput       `json:"vision_input"`         // 视觉输入
	RouteType         int                `json:"route_type"`
	IsHomeWorkFlag    int                `json:"is_home_work_flag"` //1代表作业模式，0表示非作业模式
}

type VisionInput struct {
	TraceImageInfo   *TraceImageInfoData   `json:"trace_image_info"`
	DesktopImageInfo *DesktopImageInfoData `json:"desktop_image_info"`
}

type DesktopImageInfoData struct {
	ImageURL       string `json:"image_url"`
	FingerPos2ma   []int  `json:"finger_pos_2ma"`
	TextPos2ma     []int  `json:"text_pos_2ma"`
	MainBox        []int  `json:"main_box"`
	SelectPagePose []int  `json:"select_page_pose"`
	SessionToken   string `json:"session_token"`
}

type TraceImageInfoData struct {
	ImageURL     string  `json:"image_url"`
	TraceList    []Point `json:"trace_list"`
	UserDrawBox  []Point `json:"user_draw_box"`
	SessionToken string  `json:"session_token"`
}

type Point struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
}

type PointInt struct {
	X int `json:"x"`
	Y int `json:"y"`
}

type LlmAgent struct {
	Name   string `json:"name"`
	Status int    `json:"status"`
}

type ContextInfo struct {
	HistoryInfo interface{}         `json:"history_info"`
	CurrentInfo *ContextCurrentInfo `json:"current_info"`
}

type ContextCurrentInfo struct {
	WakeUpType string `json:"wake_up_type"`
}

type UserSystemV2 struct {
	Location string `json:"location"`
	Grade    int    `json:"grade"`
	Gender   string `json:"gender"`
	Nickname string `json:"nickname"`
	Semester string `json:"semester"`
	TalId    string `json:"tal_id"`
	DeviceId string `json:"device_id"`
}

type OcrInput struct {
	OcrAlldocLen     int      `json:"ocr_alldoc_len"`
	OcrKeyLen        int      `json:"ocr_key_len"`
	OcrAlldocInfo    string   `json:"ocr_alldoc_info"`
	OcrKeyInfo       []string `json:"ocr_key_info"`
	OcrKeywordInfo   []string `json:"ocr_keyword_info"`
	OcrKeyInfoOffset []int    `json:"ocr_key_info_offset"`
}

type HandleInput struct {
	HandleTaskType string         `json:"handle_task_type"`
	ConfirmRequest ConfirmRequest `json:"confirm_request"`
	IsEngCorrect   bool           `json:"is_eng_correct"`
}

type ConfirmRequest struct {
	ConfirmRequestIntent string `json:"confirm_request_intent"`
	ConfirmRequestInput  string `json:"confirm_request_input"`
	ConfirmRequestFinger string `json:"confirm_request_finger"`
	ConfirmRequestPinyin string `json:"confirm_request_pinyin"`
}

type NLUSlotDict struct {
	Key  string `json:"key"`
	Id   string `json:"id"`
	Name string `json:"name"`
}

type NLUControllerResp struct {
	Code              int64  `json:"code"`
	Mag               string `json:"mag"`
	TraceId           string `json:"trace_id"`
	Chain             string `json:"chain"`
	ControllerVersion string `json:"controller_version"`
	Data              struct {
		Intent       string       `json:"intent"`
		FunctionCall FunctionCall `json:"function_call"`
		Baike        struct {
			Task          string `json:"task"`
			Text          string `json:"text"`
			TtsNormText   string `json:"tts_norm_text"`
			IsValidWidget bool   `json:"is_valid_widget"`
		} `json:"baike"`
		Action            int8                   `json:"action"`
		RequestType       string                 `json:"request_type"`
		SluResult         *SluResult             `json:"slu_result"`
		FaqResult         *Module                `json:"faq_result"`
		FingerIntent      *Intent                `json:"finger_intent"`
		ModuleList        interface{}            `json:"module_list"`
		ControllerVersion string                 `json:"controller_version"`
		Rewrite           NLUControllerRewrite   `json:"rewrite"`
		RewriteInfo       *ControllerRewriteInfo `json:"rewrite_info"`
		SceneResult       *SceneResult           `json:"scene_result"`
	} `json:"data"`
}

type MultiModalInfo struct {
	MultiModalFlag int     `json:"multi_modal_flag"`
	VisionType     string  `json:"vision_type"`
	ImageURL       string  `json:"image_url"`
	TraceBox       []Point `json:"trace_box"`
}

type SceneResult struct {
	Type      int         `json:"type"`
	Box       [][]int32   `json:"box"`
	Text      interface{} `json:"text"`
	ClickArea [][]int32   `json:"click_area"`
}

type FunctionCall struct {
	Task           string          `json:"task"`
	Functions      []Function      `json:"functions"`
	Method         string          `json:"method"`
	MultiModalInfo *MultiModalInfo `json:"multi_modal_info"`
}

type NLUControllerRewrite struct {
	RewriteQuery string `json:"rewrite_query"`
}

type ControllerRewriteInfo struct {
	RewriteQuery           string `json:"rewrite_query"`
	Method                 string `json:"method"`
	Status                 int    `json:"status"`
	Scenario               string `json:"scenario"`
	MultiModalRewriteQuery string `json:"multimodal_rewrite_query"`
}

type Function struct {
	FuncName       string      `json:"func_name"`
	FuncParamaters interface{} `json:"func_paramaters"`
}
type SearchQuestionReqParams struct {
	SearchQuestionSlot
}

type SearchQuestionSlot struct {
	Context    string `json:"context"`
	TargetText string `json:"target_text"`
	Info       string `json:"info"`
}
type SluResult struct {
	Query               string      `json:"query"`
	DomainResult        interface{} `json:"domain_result"`
	NerResult           interface{} `json:"ner_result"`
	QueryRepresentation interface{} `json:"query_representation"`
	CorrectResult       interface{} `json:"correct_result"`
	Intent              string      `json:"intent"`
}

type Module struct {
	StatusCode       string        `json:"status_code"`
	ModuleName       string        `json:"module_name"`
	DuiWidget        string        `json:"dui_widget"`
	Text             string        `json:"text"`
	TtsNormText      string        `json:"tts_norm_text"`
	Count            int           `json:"count"`
	DataSource       string        `json:"data_source"`
	IsValidWidget    bool          `json:"is_valid_widget"`
	ContentId        string        `json:"content_id"`
	ResultConfidence interface{}   `json:"result_confidence"`
	NeedConfirm      bool          `json:"need_confirm"`
	IsSessionEnd     bool          `json:"is_session_end"`
	Intent           *Intent       `json:"intent,omitempty"`
	Task             string        `json:"task"`
	Skill            string        `json:"skill,omitempty"`
	SubIntent        int           `json:"sub_intent,omitempty"`
	DefaultSlots     interface{}   `json:"default_slots,omitempty"`
	RawSlotDict      interface{}   `json:"raw_slot_dict,omitempty"`
	SlotDict         interface{}   `json:"slot_dict,omitempty"`
	Category         int32         `json:"category,omitempty"`
	ResourceList     []interface{} `json:"resource_list,omitempty"`
	Word             []string      `json:"word,omitempty"`
	Hit              bool          `json:"hit,omitempty"`
}

type Intent struct {
	Confidence  float64       `json:"confidence"`
	Skill       string        `json:"skill"`
	SkillId     string        `json:"skill_id"`
	Task        string        `json:"task"`
	Slots       []interface{} `json:"slots"`
	ExtraParams interface{}   `json:"extra_params"`
}

type FunctionCallReq struct {
	RequestId    string           `json:"request_id"`
	UserSystem   UserSystemV2     `json:"user_system"`
	AsrInput     AsrInput         `json:"asr_input"`
	SceneCode    common.SceneCode `json:"scene_code"`  // 场景code，为1表示作业模式
	ScreenMode   int32            `json:"screen_mode"` // 学练机屏幕模式
	Intent       string           `json:"intent"`
	FunctionCall struct {
		Task           string          `json:"task"`
		Functions      []Function      `json:"functions"`
		Method         string          `json:"method"`
		MultiModalInfo *MultiModalInfo `json:"multi_modal_info"`
	} `json:"function_call"`
	SlotFillList       []NLUSlotDict `json:"slot_fill_list"`
	BizType            string        `json:"biz_type"`
	AiTutorPlanBizCode string        `json:"ai_tutor_plan_biz_code"`
	AiTutorQAType      string        `json:"ai_tutor_qa_type"`
}

type FunctionCallRespData struct {
	SkillList         []NLUSkillItem `json:"skill_list"` //这块暂时用lui-api和nlp-center-service中有个别区别
	ModelOutputIntent string         `json:"model_output_intent"`
}

type FunctionCallResp struct {
	Code    int64                 `json:"code"`
	Data    *FunctionCallRespData `json:"data"`
	Mag     string                `json:"mag"`
	TraceId string                `json:"trace_id"`
}

type NLPQueryPad2Resp struct {
	Code    int64                 `json:"code"`
	Data    *NLPQueryPad2RespData `json:"data"`
	Msg     string                `json:"msg"`
	TraceId string                `json:"trace_id"`
}

type NLPQueryPad2RespData struct {
	UserID            string         `json:"user_id"`
	RequestID         string         `json:"request_id"`
	Source            string         `json:"source"`
	Version           string         `json:"version"`
	SuggestList       []Suggest      `json:"suggest_list,omitempty"`
	Input             string         `json:"input"`
	IsSessionEnd      bool           `json:"is_session_end"`
	SessionId         string         `json:"session_id"`
	SkillList         []NLUSkillItem `json:"skill_list"`
	ModelOutputIntent string         `json:"model_output_intent"`
	LegalCheckType    string         `json:"legal_check_type"`
	ControllerVersion string         `json:"controller_version"`
}

type Suggest struct {
	Content string `json:"content"`
	Pinyin  string `json:"pinyin"`
}

type BaiKeResp struct {
	Code int64 `json:"code"`
	Data struct {
		ModuleName       string `json:"module_name"`
		DuiWidget        string `json:"dui_widget"`
		Text             string `json:"text"`
		TtsNormText      string `json:"tts_norm_text"`
		Count            int    `json:"count"`
		DataSource       string `json:"data_source"`
		KPointBaike      string `json:"kpoint_baike"`
		IsValidWidget    bool   `json:"is_valid_widget"`
		ContentId        string `json:"content_id"`
		ResultConfidence int    `json:"result_confidence"`
		NeedConfirm      bool   `json:"need_confirm"`
		Intent           struct {
			Confidence  float64       `json:"confidence"`
			Skill       string        `json:"skill"`
			SkillId     string        `json:"skill_id"`
			Task        string        `json:"task"`
			Slots       []interface{} `json:"slots"`
			ExtraParams interface{}   `json:"extra_params"`
		}
	}
	Mag     string `json:"mag"`
	TraceId string `json:"trace_id"`
}

// NewNluRes 从paas-service迁移过来 NewNluResData,Command 兼容历史中文字词二次确认
type NewNluResData struct {
	UserID        string      `json:"user_id"`
	RequestID     string      `json:"request_id"`
	Skill         string      `json:"skill"`
	Task          string      `json:"task"`
	TtsShow       string      `json:"tts_show"`
	TtsNorm       string      `json:"tts_norm"`
	Count         int         `json:"count"`
	ShouldConfirm bool        `json:"should_confirm"`
	ShowTypeList  []string    `json:"show_type_list"`
	ShowCarType   int         `json:"show_car_type"`
	IsResultValid bool        `json:"is_result_valid"`
	Command       Command     `json:"command"`
	NearWord      interface{} `json:"near_word"`
	Widget        string      `json:"widget"`
	Source        string      `json:"source"`
	Version       string      `json:"version"`
	Input         string      `json:"input"`
}

type Command struct {
	Api   string `json:"api"`
	Param struct {
		AppId       interface{} `json:"appId"`
		Degree      interface{} `json:"degree"`
		Extra       interface{} `json:"extra"`
		Method      string      `json:"method"`
		Object      interface{} `json:"object"`
		Operate     string      `json:"operate"`
		PackageName string      `json:"package_name"`
		Scheme      string      `json:"scheme"`
		Score       interface{} `json:"score"`
		Value       string      `json:"value"`
	} `json:"param"`
}

type ZhWordSearchParams struct {
	SourceText string `json:"source_text"`
	TargetText string `json:"target_text"`
	Attribute  string `json:"attribute"`
}

type BizNlUExtra struct {
	AiTutorPlanBizCode string `json:"ai_tutor_plan_biz_code"`
	AiTutorQAType      string `json:"ai_tutor_qa_type"`
}

type RnResponse struct {
	Widget         string    `json:"widget"`
	ShowTypeList   []string  `json:"show_type_list"`
	Dispatch       *Dispatch `json:"dispatch"`
	DispatchInData *Dispatch `json:"dispatch_in_data"`
	ShowType       int       `json:"show_type"`
}

// QuestionV2 题目相关信息
type QuestionV2 struct {
	ImageURL string  `json:"image_url"`
	TraceBox []Point `json:"trace_box"`
	QusIndex int     `json:"qus_index"`
}

// ExtraParams 应用跳转参数
type ExtraParams struct {
	API   string `json:"api"`
	Param *Param `json:"param"`
}

// Param 应用跳转详细参数
type Param struct {
	AppID       string      `json:"appid"`
	Method      string      `json:"method"`
	Operate     string      `json:"operate"`
	PackageName string      `json:"package_name"`
	Value       string      `json:"value"`
	Scheme      string      `json:"scheme"`
	Degree      interface{} `json:"degree"`
}
