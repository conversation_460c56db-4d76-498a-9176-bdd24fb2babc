package dto

type MathGptAskClassifyRes struct {
	ErrorReason string             `json:"error_reason"`
	ErrorMsg    string             `json:"error_msg"`
	MetaData    interface{}        `json:"meta_data"`
	TraceID     string             `json:"trace_id"`
	ServerTime  int                `json:"server_time"`
	Data        MathGptAskClassify `json:"data"`
}

type MathGptAskClassify struct {
	Assistant      int     `json:"assistant"`
	AssistantScore float64 `json:"assistant_score"`
	Grade          int     `json:"grade"`
	GradeScore     float64 `json:"grade_score"`
	Subject        int     `json:"subject"`
	SubjectScore   float64 `json:"subject_score"`
	Viewpoint      int     `json:"viewpoint"`
	ViewpointScore float64 `json:"viewpoint_score"`
}

type OcrRes struct {
	ErrorReason string      `json:"error_reason"`
	ErrorMsg    string      `json:"error_msg"`
	MetaData    interface{} `json:"meta_data"`
	TraceID     string      `json:"trace_id"`
	ServerTime  int         `json:"server_time"`
	Data        OCRData     `json:"data"`
}

type OCRData struct {
	Result       []CharInfo     `json:"result"`
	HandText     []HandText     `json:"hand_text"`
	PrintText    []PrintText    `json:"print_text"`
	HandFormula  []HandFormula  `json:"hand_formula"`
	PrintFormula []PrintFormula `json:"print_formula"`
	Images       []*Images      `json:"images"`
	Sheets       []*Sheets      `json:"sheets"`
}

type Images struct {
	Poses []Pose `json:"poses"`
}
type Sheets struct {
	Poses []Pose `json:"poses"`
}
type PrintFormula struct {
	Poses []Pose `json:"poses"`
	Texts string `json:"texts"`
}
type HandFormula struct {
	Poses []Pose `json:"poses"`
	Text  string `json:"text"`
}
type PrintText struct {
	Poses []Pose `json:"poses"`
	Text  string `json:"text"`
}
type HandText struct {
	Poses []Pose   `json:"poses"`
	Text  []string `json:"text"`
}

type CharInfo struct {
	Text  string `json:"text"`
	Poses []Pose `json:"poses"`
}

type Pose struct {
	X int32 `json:"x"`
	Y int32 `json:"y"`
}
