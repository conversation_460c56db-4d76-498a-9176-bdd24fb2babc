package dto

type SseLuiReq struct {
	Message   string `json:"message"`
	SessionId string `json:"session_id"`
	Intent    string `json:"intent"`
	Uid       string `json:"uid"`
	BizType   string `json:"biz_type"`
}

type SseGptReq struct {
	Biz              int                 `form:"biz" json:"biz"`
	Message          string              `form:"message" json:"message"`
	History          []LlmHistoryMessage `form:"history" json:"history"`
	SessionId        string              `form:"session_id" json:"session_id"`
	TmpId            string              `form:"tmp_id" json:"tmp_id"`
	Args             []string            `form:"args" json:"args"`
	IsCheckText      int                 `form:"is_check_text" json:"is_check_text"`           // 0-检查, 1-不检查
	IsInputCheck     int                 `form:"is_input_check" json:"is_input_check"`         // 0-检查, 1-不检查
	NoForbiddenCheck int                 `form:"no_forbidden_check" json:"no_forbidden_check"` // 0-检查, 1-不检查
	IsStoreHistory   int                 `form:"is_store_history" json:"is_store_history"`     // 0-存储, 1-不存储
	IsChatSkill      int                 `form:"is_chat_skill" json:"is_chat_skill"`           // 0-闲聊不走大模型, 1-闲聊走大模型
}

type LlmServiceCommonChatReq struct {
	Biz           int                 `form:"biz" json:"biz"`
	TmpId         string              `form:"tmp_id" json:"tmp_id"`
	Args          []string            `form:"args" json:"args"`
	Message       string              `form:"message" json:"message"`
	SessionId     string              `form:"session_id" json:"session_id"`
	EnableSearch  bool                `form:"enable_search" json:"enable_search"`
	History       []LlmHistoryMessage `form:"history" json:"history"`
	MsgNoCheck    bool                `form:"msg_no_check" json:"msg_no_check"`
	OutPutNoCheck bool                `form:"output_no_check" json:"output_no_check"` // 输出不走风控
	Sn            string              `form:"sn" json:"sn,omitempty"`
	TalId         string              `form:"tal_id" json:"tal_id,omitempty"`
}

type LlmHistoryMessage struct {
	Role    string `json:"role" form:"role"`
	Content string `json:"content" form:"content"`
}

type SseRes struct {
	ErrorCode  int    `json:"error_code"`
	ErrorMsg   string `json:"error_msg"`
	TraceId    string `json:"trace_id"`
	Id         string `json:"id"`
	Object     string `json:"object"`
	Created    int    `json:"created"`
	SentenceId int    `json:"sentence_id"`
	IsDeepSeek bool   `json:"is_deep_seek"` // true 表示深度思考内容，false 为非深度思考内容
	IsEnd      bool   `json:"is_end"`
	IsStart    bool   `json:"is_start"`
	Result     string `json:"result"`
	Model      string `json:"model"`
	Source     string `json:"source"`
	Usage      Usage  `json:"usage"`
}

type DialogueSseRes struct {
	ErrorCode    int      `json:"error_code"`
	ErrorMsg     string   `json:"error_msg"`
	TraceId      string   `json:"trace_id"`
	Id           string   `json:"id"`
	Object       string   `json:"object"`
	Created      int      `json:"created"`
	SentenceId   int      `json:"sentence_id"`
	IsEnd        bool     `json:"is_end"`         // 大模型结束标识
	IsDeepSeek   bool     `json:"is_deep_seek"`   // true 表示深度思考内容，false 为非深度思考内容
	IsReplyStart bool     `json:"is_reply_start"` //回复开始标识
	IsReplyEnd   bool     `json:"is_reply_end"`   //回复结束标识
	Result       string   `json:"result"`         //回复内容
	Emotion      string   `json:"emotion"`        //表达
	Sug          []string `json:"sug"`            //sug
	Model        string   `json:"model"`
	Source       string   `json:"source"`
	Usage        Usage    `json:"usage"`
}

type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

type CommonSseOutputRes struct {
	ErrorCode    int      `json:"error_code"`
	ErrorMsg     string   `json:"error_msg"`
	TraceId      string   `json:"trace_id"`
	Id           string   `json:"id"`
	Created      int      `json:"created"`
	SentenceId   int      `json:"sentence_id"`
	IsEnd        bool     `json:"is_end"`         // 大模型结束标识
	IsReplyStart bool     `json:"is_reply_start"` //回复开始标识
	IsReplyEnd   bool     `json:"is_reply_end"`   //回复结束标识
	Result       string   `json:"result"`         //回复内容
	Media        Media    `json:"media,omitempty"`
	ResType      int      `json:"res_type"`
	Emotion      string   `json:"emotion,omitempty"` //表达
	Sug          []string `json:"sug,omitempty"`     //sug
	Model        string   `json:"model,omitempty"`
	Source       string   `json:"source,omitempty"`
	RawResult    string   `json:"raw_result"`
}

type Media struct {
	Type       int    `json:"type"`
	Cover      string `json:"cover"`
	CoverTitle string `json:"cover_title"`
	Url        string `json:"url"`
}

type LlmTracePayload struct {
	SessionId        string `json:"session_id"`
	Result           string `json:"result"`
	ReasoningContent string `json:"reasoning_content"`
	IllegalType      string `json:"illegalType"`
	LLMModel         string `json:"llmModel"`
	Emotion          string `json:"emotion"`
	TmpId            string `json:"tmpId"`
}
