package dto

import (
	"fmt"
	"github.com/spf13/cast"
	"lui-api/internal/common"
	"reflect"
	"strings"
)

// CourseIdsQueryReq 课程ID
type CourseIdsQueryReq struct {
	Category  common.LuiContentCategory `json:"category"`
	CourseIds []struct {
		ResourceType string `json:"type"`
		Id           string `json:"id"`
	} `json:"course_ids"`
}

// SubjectClassesIdQueryReq 课ID
type SubjectClassesIdQueryReq struct {
	Category          common.LuiContentCategory `json:"category"`
	CourseIds         []CourseId                `json:"course_ids"`
	SubjectClassesIds []SubjectClassesId        `json:"subject_classes_ids"`
	Grade             int                       `json:"grade"`
	Semester          int                       `json:"semester"`
	Subject           int                       `json:"subject"`
	Version           int                       `json:"version"`
	ShowXpad2Cover    int                       `json:"show_xpad2_cover"`
	BaodianSignParam
}

type CourseId struct {
	Type string `json:"type"`
	Id   string `json:"id"`
}

type SubjectClassesId struct {
	Type     string `json:"type"`
	CourseId string `json:"course_id"`
	NodeId   string `json:"node_id"`
	Limit    int    `json:"limit"`
}

type SubjectClassesIdQueryResp struct {
	CourseList      []Course         `json:"course_list"`
	KnowledgePoints []KnowledgePoint `json:"knowledge_points"`
	LessonList      []Lesson         `json:"lesson_list"`
	PlanList        []Plan           `json:"plan_list"`
	UnitList        []Unit           `json:"unit_list"`
	BookReadingList []*BookReading   `json:"book_reading_list"`
}

type Course interface{}
type KnowledgePoint interface{}
type Lesson interface{}
type Plan interface{}
type Unit interface{}
type BookReading struct {
	BookID        string `json:"book_id"`
	BookName      string `json:"book_name"`
	BookCover     string `json:"book_cover"`
	GradeID       int    `json:"grade_id"`
	GradeName     string `json:"grade_name"`
	SubjectID     int    `json:"subject_id"`
	SubjectName   string `json:"subject_name"`
	Semester      int    `json:"semester"`
	SemesterName  string `json:"semester_name"`
	CatalogueID   int    `json:"catalogue_id"`
	CatalogueName string `json:"catalogue_name"`
	UnitName      string `json:"unit_name"`
	StartPageNo   int    `json:"start_page_no"`
	PageID        int    `json:"page_id"`
	Scheme        string `json:"scheme"`
}

type BookReadingInfo struct {
	Cover        string `json:"cover"`
	ResourceName string `json:"resource_name"`
	VersionName  string `json:"version_name"`
	GradeName    string `json:"grade_name"`
	SemesterName string `json:"semester_name"`
	UnitName     string `json:"unit_name"`
	Scheme       string `json:"scheme"`
	SubjectName  string `json:"subject_name"`
}
type UniBookReadingItem struct {
	Type string           `json:"type"`
	Item *BookReadingInfo `json:"item"`
}
type CourseExtra struct {
	Course
	CourseId     string `json:"id"`
	CourseSystem int    `json:"course_system"`
	Xpad2Cover   string `json:"xpad2_cover"`
	Subject      int    `json:"subject"`
	Semester     int    `json:"semester"`
	Grade        int    `json:"grade"`
}
type KnowledgePointExtra struct {
	KnowledgePoint
	ModuleId     string `json:"id"`
	IconType     int    `json:"icon_type"`
	CourseSystem int    `json:"course_system"`
	Xpad2Cover   string `json:"xpad2_cover"`
	Subject      int    `json:"subject"`
	Semester     int    `json:"semester"`
	Grade        int    `json:"grade"`
}
type LessonExtra struct {
	Lesson
	LessonId     string `json:"id"`
	CourseSystem int    `json:"course_system"`
	Subject      int    `json:"subject"`
	Semester     int    `json:"semester"`
	Xpad2Cover   string `json:"xpad2_cover"`
	Grade        int    `json:"grade"`
}
type PlanExtra struct {
	Plan
	PlanId       string `json:"id"`
	CourseSystem int    `json:"course_system"`
	Xpad2Cover   string `json:"xpad2_cover"`
	Subject      int    `json:"subject"`
	Semester     int    `json:"semester"`
	Grade        int    `json:"grade"`
}
type UnitExtra struct {
	Unit
	UnitId       string `json:"id"`
	CourseSystem int    `json:"course_system"`
	Xpad2Cover   string `json:"xpad2_cover"`
	Subject      int    `json:"subject"`
	Semester     int    `json:"semester"`
	Grade        int    `json:"grade"`
}

// AttainmentAlbumQueryReq 素养专辑ID + 素养ID
type AttainmentAlbumQueryReq struct {
	Category           common.LuiContentCategory `json:"category"`
	AttainmentAlbumIds []int                     `json:"attainment_album_ids"`
	AttainmentIds      []AttainmentId            `json:"attainment_ids"`
	Grade              int                       `json:"grade"`
	Semester           int                       `json:"semester"`
	Subject            int                       `json:"subject"`
	Version            int                       `json:"version"`
}

type AttainmentId struct {
	AttainmentAlbumId int `json:"attainment_album_id"`
	AttainmentId      int `json:"attainment_id"`
}

type AlbumAttainmentIdQueryResp struct {
	AttainmentAlbumList []AttainmentAlbum `json:"attainment_album_list"`
	AttainmentList      []Attainment      `json:"attainment_list"`
}

type AttainmentAlbum struct {
	ID                int    `json:"id"`
	BusinessID        int    `json:"business_id"`
	BusinessName      string `json:"business_name"`
	AlbumID           int    `json:"album_id"`
	Cover             string `json:"cover"`
	Title             string `json:"title"`
	Desc              string `json:"desc"`
	ResourceCount     int    `json:"resource_count"`
	CategoryID        int    `json:"category_id"`
	CategoryName      string `json:"category_name"`
	ChildCategoryID   int    `json:"child_category_id"`
	ChildCategoryName string `json:"child_category_name"`
	Scheme            string `json:"scheme"`
	Grade             int    `json:"grade"`
}
type Attainment struct {
	Cover             string `json:"cover"`
	ID                int    `json:"id"`
	BusinessID        int    `json:"business_id"`
	BusinessName      string `json:"business_name"`
	Title             string `json:"title"`
	VideoID           string `json:"video_id"`
	CategoryID        int    `json:"category_id"`
	AlbumID           int    `json:"album_id"`
	CategoryName      string `json:"category_name"`
	AlbumName         string `json:"album_name"`
	ChildCategoryID   int    `json:"child_category_id"`
	ChildCategoryName string `json:"child_category_name"`
	VideoTime         int    `json:"video_time"`
	Scheme            string `json:"scheme"`
	Grade             int    `json:"grade"`
}

type AttainmentAlbumExtra struct {
	AttainmentAlbum
	AlbumId    int    `json:"id"`
	Xpad2Cover string `json:"xpad2_cover"`
	Semester   int    `json:"semester"`
}
type AttainmentExtra struct {
	Attainment
	AttainmentId int    `json:"id"`
	Xpad2Cover   string `json:"xpad2_cover"`
	Semester     int    `json:"semester"`
}

// PaperIdsQueryReq 试卷ID
type PaperIdsQueryReq struct {
	Category common.LuiContentCategory `json:"category"`
	PaperIds []string                  `json:"paper_ids"`
	Grade    int                       `json:"grade"`
	Semester int                       `json:"semester"`
	Subject  int                       `json:"subject"`
	Version  int                       `json:"version"`
}

type PaperIdQueryResp struct {
	Papers []Paper `json:"papers"`
}
type Paper interface{}

type PaperExtra struct {
	Paper
	PaperId  string `json:"id"`
	Semester int    `json:"semester"`
}

type BySort []*ResourceSortRespItem

func (s BySort) Len() int           { return len(s) }
func (s BySort) Less(i, j int) bool { return s[i].Sort < s[j].Sort }
func (s BySort) Swap(i, j int)      { s[i], s[j] = s[j], s[i] }

type ResourceSortRespItem struct {
	Type         string      `json:"type"`
	Item         interface{} `json:"item"`
	IconType     int         `json:"icon_type"`
	Attr         []Attr      `json:"attr"`
	Sort         int         `json:"sort"`
	CourseSystem int         `json:"course_system"`
	Xpad2Cover   string      `json:"xpad_2_cover"`
	FontColor    string      `json:"font_color"`
	OverlayColor string      `json:"overlay_color"`
}

type EbookResource struct {
	VipLevel     int    `json:"vip_level"`
	Cover        string `json:"cover"`
	Author       string `json:"author"`
	Introduction string `json:"introduction"`
	ResourceType string `json:"resource_type"`
	ResourceId   string `json:"resource_id"`
	ResourceName string `json:"resource_name"`
	Scheme       string `json:"scheme"`
	Sort         int    `json:"sort"`
}
type EbookResourceReq struct {
	List []EbookResourceReqItem `json:"resource"`
}
type EbookResourceInfo struct {
	ResourceId  string `json:"resource_id"`
	LandingPage string `json:"landing_page"`
}
type EbookResourceRes struct {
	List []EbookResourceInfo `json:"list"`
}
type EbookResourceReqItem struct {
	ResourceType string `json:"resource_type"`
	ResourceId   string `json:"resource_id"`
}

type Attr struct {
	Key string `json:"key"`
	Id  string `json:"id"`
}

type BaodianSignParam struct {
	TimeStamp int64  `json:"time_stamp"`
	Sign      string `json:"sign"`
}

type CourseChildrenQueryReq struct {
	PlanId       string `json:"plan_id"`
	LessonId     string `json:"lesson_id"`
	CourseSystem string `json:"course_system"`
	BaodianSignParam
}

type SlotSchemeReq struct {
	SchemeType     int `json:"scheme_type"`
	ChannelId      int `json:"channel_id"`
	ColumnId       int `json:"column_id"`
	Grade          int `json:"grade"`
	CourseType     int `json:"course_type"`
	SemesterId     int `json:"semester_id"`
	VersionId      int `json:"version_id"`
	PlayAfterVoice int `json:"playAfterVoice"`
	ChildColumnId  int `json:"child_column_id"`
	BaodianSignParam
	Text PaperText `json:"text"`
}

type SlotSchemeResp struct {
	SchemeUrl string `json:"scheme_url"`
}

type BookReadingIdQueryReq struct {
	Category       common.LuiContentCategory `json:"category"`
	BookReadingIds []LuiBookReadingID        `json:"book_reading_ids"`
}

type LuiBookReadingID struct {
	BookID      string `json:"book_id"`
	CatalogueID int    `json:"catalogue_id"`
}

// SlotCombineCheckReq 槽位组合是否是真实存在的
type SlotCombineCheckReq struct {
	Category common.LuiContentCategory `json:"category"`

	GradeId        int `json:"grade_id"`
	SemesterId     int `json:"semester_id"`
	SubjectId      int `json:"subject_id"`
	VersionId      int `json:"version_id"`
	CourseSystemId int `json:"course_system_id"`
	CatalogId      int `json:"catalog_id"`
}

// GetFilters 获取过滤条件
func (slot SlotCombineCheckReq) GetFilters() string {
	var pairs []string
	v := reflect.ValueOf(slot)
	t := v.Type()

	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		value := v.Field(i).Interface()
		if reflect.DeepEqual(value, reflect.Zero(field.Type).Interface()) {
			continue
		}
		jsonTag := field.Tag.Get("json")
		jsonKey := jsonTag
		if jsonKey == "category" {
			continue
		}
		if jsonKey == "grade_id" {
			grade := cast.ToInt(value)
			if grade < 0 {
				continue
			}
		}
		//if jsonKey == "subject_id" {
		//	pairs = append(pairs, fmt.Sprintf("(%s=%d)", jsonKey, value))
		//} else {
		// 有些资源id默认为0为全部
		pairs = append(pairs, fmt.Sprintf("(%s=%d or %s=0)", jsonKey, value, jsonKey))
		//}
	}
	return strings.Join(pairs, " and ")
}

type PaperText struct {
	Entrance   string `json:"entrance" `   //金刚位入口
	Province   int    `json:"province" `   //省份
	PaperType  int    `json:"paper_type" ` //试卷类型
	PaperScope int    `json:"scope" `      //试卷范围
	Difficulty int    `json:"difficulty" ` //试卷难度
	Years      int    `json:"years" `      //试卷年份
	CupType    int    `json:"cup_type" `   //杯赛类型
}

type PoemInfo struct {
	Content []struct {
		ShowType string `json:"showType"`
	} `json:"content"`
	ContentID string `json:"content_id"`
}

type JzxV15QuestionExtra struct {
	Cover           string   `json:"cover"`
	GradeName       string   `json:"grade_name"`
	ParentNodeNames []string `json:"parent_node_names"`
	Scheme          string   `json:"scheme"`
	SemesterName    string   `json:"semester_name"`
	SubjectName     string   `json:"subject_name"`
	VersionName     string   `json:"version_name"`
	QuesNum         int      `json:"ques_num"`
	ResourceName    string   `json:"resource_name"`
	ResourceId      string   `json:"resource_id"`
}
