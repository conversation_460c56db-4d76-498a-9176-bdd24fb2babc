package dto

import (
	"math/rand"
)

type ScreeMode int

const (
	MODE_Default           ScreeMode = -1
	MODE_LEARNING_PRACTICE ScreeMode = 0
	MODE_READING           ScreeMode = 1
	MODE_TUTOR             ScreeMode = 2
)

func (mode ScreeMode) ToString() string {
	switch mode {
	case MODE_LEARNING_PRACTICE:
		return "学练"
	case MODE_READING:
		return "阅读"
	case MODE_TUTOR:
		return "学习机"
	default:
		return ""
	}
}

// 学练机支持模式
func (mode ScreeMode) IsXueLianJiSupport(intent string) (bool, string) {
	screeMode, ok := IntentScreeModeMap[intent]
	if !ok { // 如果未配置到继续执行
		return true, ""
	}

	if len(screeMode) == 0 {
		return false, "学练机上暂时不支持这个功能哦～"
	}

	for _, modeItem := range screeMode {
		if modeItem == mode {
			return true, ""
		}
	}

	modeStr := "学习机"
	if len(screeMode) > 0 {
		modeStr = screeMode[rand.Intn(len(screeMode))].ToString()
	}

	return false, "需要切换到" + modeStr + "模式才能使用哦"
}

var IntentScreeModeMap = map[string][]ScreeMode{
	"查题":      {MODE_TUTOR},
	"找练习":     {MODE_TUTOR},
	"找试卷":     {MODE_LEARNING_PRACTICE},
	"查字":      {MODE_TUTOR},
	"查词语":     {MODE_TUTOR},
	"查单词":     {MODE_TUTOR},
	"查句子":     {MODE_TUTOR},
	"查诗歌":     {MODE_TUTOR},
	"查学科知识":   {MODE_TUTOR},
	"找动画片":    {MODE_TUTOR},
	"找非学习音频":  {MODE_TUTOR},
	"找故事":     {MODE_TUTOR},
	"问答页":     {},
	"打电话":     {},
	"模糊意图":    {},
	"学习机使用答疑": {},
}

func (mode ScreeMode) Is25XueLianJiSupport(intent string) (bool, string) {
	screeMode, ok := Intent25ScreeModeMap[intent]
	if !ok { // 如果未配置到继续执行
		return true, ""
	}

	if len(screeMode) == 0 {
		return false, "学练机上暂时不支持这个功能哦～"
	}

	for _, modeItem := range screeMode {
		if modeItem == mode {
			return true, ""
		}
	}

	modeStr := "学习机"
	if len(screeMode) > 0 {
		modeStr = screeMode[rand.Intn(len(screeMode))].ToString()
	}

	return false, "需要切换到" + modeStr + "模式才能使用哦"
}

var Intent25ScreeModeMap = map[string][]ScreeMode{
	"找动画片":    {MODE_TUTOR, MODE_READING},
	"找非学习音频":  {MODE_TUTOR, MODE_READING},
	"找故事":     {MODE_TUTOR, MODE_READING},
	"学习机使用答疑": {MODE_TUTOR, MODE_READING, MODE_LEARNING_PRACTICE},
	"模糊意图":    {},
}
