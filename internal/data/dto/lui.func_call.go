package dto

type XpodContactMatchRzt struct {
	ContactName string `json:"contact_name"` // 提取的名字
	MsgContent  string `json:"msg_content"`
	IsFuzzy     string `json:"is_fuzzy"` // 0-精确匹配 1-模糊匹配
	IsSee2say   string `json:"is_see2say"`
	Matched     []struct {
		ContactName string  `json:"contact_name"` // 通讯录名字
		ContactId   string  `json:"contact_id"`   // 通讯录id
		IsFuzzy     string  `json:"is_fuzzy"`     // 0-精确匹配 1-模糊匹配
		Score       float64 `json:"score"`        // 匹配分数
	} `json:"matched"`
}
type XpodFunction interface{}
type XpodSee2SayRzt struct {
	XpodFunction
	IsSee2say string `json:"is_see2say"`
}

// search_author_information
type SearchAuthorInformationFunc struct {
	Author   string `json:"author"`   //诗人的名字
	Title    string `json:"title"`    //诗的名字
	Dynasty  string `json:"dynasty"`  //诗人的朝代
	Sentence string `json:"sentence"` //诗句的内容
	//要查询的诗人属性 enum ["字", "别号", "朝代", "代表作", "简介", "派系"]
	SearchAuthorInformationType string `json:"search_author_information_type"`
}

// SearchPoetryFormsFunc 查某首/句诗
type SearchPoetryFormsFunc struct {
	Author  string `json:"author"`  //诗人的名字
	Title   string `json:"title"`   //诗的名字
	Dynasty string `json:"dynasty"` //诗人的朝代
	Tags    string `json:"tags"`    //诗句的内容
	Keyword string `json:"keyword"`
	//要查询的诗的形式。 enum["现代诗", "古诗词", "绝句", "古文（文言文）"]
	Types string `json:"types"`
}

type AnalysisOfPoemFunc struct {
	Author   string `json:"author"`   //诗人的名字
	Title    string `json:"title"`    //诗的名字
	Dynasty  string `json:"dynasty"`  //诗人的朝代
	Sentence string `json:"sentence"` //诗句的内容
	//要查询的具体解析类型 enum ["写法", "释义", "赏析", "作者", "诗名", "中心句", "最后一句", "首句", "三四句", "一二句", "上一句", "下一句"]
	SearchAnalysisType string `json:"search_analysis_type"`
}

// TranslationLookupParametersFunc
type TranslationLookupParametersFunc struct {
	SourceText        string `json:"source_text"`
	TranslationMethod string `json:"translation_method"`
}
