package dto

import (
	"context"
	"lui-api/internal/pkg/custom_context"
)

type DeviceCode string

const (
	DeviceCodeTALIHPD1  DeviceCode = "TALIH-PD1"  // 平板一代
	DeviceCodeTALIHPD2  DeviceCode = "TALIH-PD2"  // 平板二代12寸
	DeviceCodeTALIHPD2P DeviceCode = "TALIH-PD2P" // 平板二代14寸
	DeviceCodeTALIHPD2A DeviceCode = "TALIH-PD2A" // 平板二代11寸
	DeviceCodeTALIHBK1  DeviceCode = "TALIH-BK1"  // 学练机
	DeviceCodeTALIHPDE1 DeviceCode = "TALIH-PDE1" // 新锐学习机
)

func (d DeviceCode) String() string {
	return string(d)
}

func GetXTalDeviceCode(ctx context.Context, appIdMap map[string]string) DeviceCode {
	appId := custom_context.GetXAppId(ctx)
	if appId == "" {
		return DeviceCodeTALIHPD2 // 默认平板二代12寸
	}

	switch appId {
	case appIdMap["xueLianJiAppId"]:
		return DeviceCodeTALIHBK1
	case appIdMap["xueLianJi25AppId"]:
		return DeviceCodeTALIHBK1
	case appIdMap["xinruiAPPId"]:
		return DeviceCodeTALIHPDE1
	case appIdMap["xPad2AppId_qijian"]:
		return DeviceCodeTALIHPD2
	case appIdMap["xPad2LiteAppId"]:
		return DeviceCodeTALIHPD2A
	case appIdMap["xPad1V2AppId"]:
		return DeviceCodeTALIHPD1
	default:
		return DeviceCodeTALIHPD2
	}
}
