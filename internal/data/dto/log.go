package dto

type QuestionExplainLog struct {
	Timestamp       int64       `json:"timestamp"`
	BizPrimaryKey   string      `json:"biz_primarykey"`
	BizPrimaryValue string      `json:"biz_primaryvalue"`
	Biz             string      `json:"biz"`
	BizFunc         string      `json:"biz_func"`
	Message         interface{} `json:"message"`
}

const (
	QuestionExplainLogPrimaryKeyTraceId = "trace_id"

	QuestionQueryLogBiz     = "xiaosi_question_query"
	QuestionQueryLogBizFunc = "dw_xiaosi_question_query"
)
