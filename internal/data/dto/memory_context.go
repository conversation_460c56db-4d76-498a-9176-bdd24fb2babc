package dto

import (
	"context"
	"lui-api/internal/common"
	"math/rand"
	"strings"
	"time"
)

const (
	CtxSceneCode                       = "X-Genie-SceneCode"
	LuiPad2MaxLen                      = 30
	AgentMemoryContextListMaxLength    = 10
	MemoryContextListMaxLength         = 100
	Expiration                         = 20 * time.Minute
	ExpirationMsInt64                  = 20 * 60 * 1000
	MemoryContextListPrefix            = "lui:memory-context-list:%v"
	MemoryContextListKetTutorPrefix    = "lui:memory-context-list-ket-tutor:%v"
	MemoryContextHashPrefix            = "lui:memory-context-hash:%v"
	MemoryContextSetRejectFlag         = "lui:memory-context-reject-flag:%v"
	MemoryContextMathAgentFlag         = "lui:memory-context:math-agent:%v"
	MemoryContextMathAgentQuestion     = "lui:memory-context:math-agent-question:%v"
	MemoryContextXiaoSiCommand         = "lui:memory-context:xiaosi-command_list:%v"
	MemoryContextXiaoSiCommandLLmSkill = "xiaosi-command-list"
	MemoryContextKetQAgentFlag         = "agent:memory-context:ket-1-agent:%v"
	MemoryContextRnCardType            = "rn_card"
	MemoryContextDeepSeekFlag          = "lui:memory-context:deep-seek-flag:%v" // 用户深度思考标记
	MemoryContextExerciseOcr           = "lui:exercise_ocr:%v"                  // 用户题目ocr
	MemoryContextWorkshopFlag          = "lui:workshop-flag:%v"                 // 用户小思工作坊标记
	AtmsExplainType                    = "atms:explain_type:%v"
	MemoryContextFilters               = "lui:filters:%v" // 用户小思滤镜
)

const (
	MemorySugListMax = 9
	MemorySugListExp = 24 * time.Hour
	MemorySugListKey = "lui:memory-sug-list:%v"
)

func SetCtxSceneCode(ctx context.Context, bizType string) context.Context {
	var sceneCode = common.SceneXiaoSi
	if strings.HasPrefix(bizType, common.BizTypeAiTutor) {
		sceneCode = common.SceneAiTutor
	}
	//if strings.HasPrefix(bizType, common.BizTypeHomeWork) && custom_context.GetVersion(ctx) == common.PlatVersionOriginalT {
	//	sceneCode = common.SceneHomeWork
	//}

	return context.WithValue(ctx, CtxSceneCode, sceneCode)
}

func GetCtxSceneCode(ctx context.Context) common.SceneCode {
	if sceneCode, ok := ctx.Value(CtxSceneCode).(common.SceneCode); ok {
		return sceneCode
	}

	return common.SceneXiaoSi
}

func MemoryContextListKey(sceneCode common.SceneCode) string {
	if sceneCode == common.SceneAiTutor {
		return MemoryContextListKetTutorPrefix
	}

	return MemoryContextListPrefix
}

func SecondsNextDayFourAM() time.Duration {
	now := time.Now()
	endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
	duration := endOfDay.Sub(now)
	seconds := int(duration.Seconds())

	rand.Seed(time.Now().UnixNano())
	randNum := rand.Intn(600) + 1 // 随机10分钟

	return time.Duration(seconds+4*60*60+randNum) * time.Second
}

type ContextSource int64

const (
	ContextSourceUnknown   ContextSource = iota // iota = 0
	ContextSourceBaiChuan                       // iota = 1
	ContextSourceErnie                          // iota = 2
	ContextSourceQWen                           // iota = 3
	ContextSourceGPT4                           // iota = 4
	ContextSourceGPT4Turbo                      // iota = 5
	ContextSourceKnowledge                      // iota = 6
	ContextSourceDeepseek
)

func (source ContextSource) ToString() string {
	switch source {
	case ContextSourceBaiChuan:
		return "百川"
	case ContextSourceErnie:
		return "文心一言"
	case ContextSourceQWen:
		return "通义千问"
	case ContextSourceGPT4:
		return "GPT4"
	case ContextSourceKnowledge:
		return "学科知识"
	default:
		return ""
	}
}

func (source ContextSource) ToInt64() int64 {
	return int64(source)
}

// GetLLMSource 获取llm来源
func GetLLMSource(llmModel string) ContextSource {
	if strings.Contains(llmModel, "Baichuan") {
		return ContextSourceBaiChuan
	}

	if strings.Contains(llmModel, "baidu") {
		return ContextSourceErnie
	}

	if strings.Contains(llmModel, "ernie") {
		return ContextSourceErnie
	}

	if strings.Contains(llmModel, "qwen") {
		return ContextSourceQWen
	}

	if strings.Contains(llmModel, "gpt-4") {
		return ContextSourceGPT4
	}
	if strings.Contains(llmModel, "gPT4Turbo") {
		return ContextSourceGPT4Turbo
	}
	if strings.Contains(llmModel, "deepseek") {
		return ContextSourceDeepseek
	}

	return ContextSourceUnknown
}

func GetFullViewCardType(llmSkill string) int32 {
	switch llmSkill {
	case common.SkillSearchQuestion.ToString():
		return 5 // 解题大模型
	case common.SkillChat.ToString():
		return 3 // 闲聊大模型
	case common.Baike.ToString():
		return 3 // 闲聊大模型
	case common.SkillTutorQA.ToString():
		return 3 // 听不懂
	case MemoryContextXiaoSiCommandLLmSkill:
		return 6 // 小思指令
	case common.SkillKetTutorChat.ToString():
		return 3 // 闲聊大模型
	case common.SkillKetTutorBaike.ToString():
		return 3 // 闲聊大模型
	case common.SkillMixedModelBaike.ToString():
		return 3 // 闲聊大模型
	case MemoryContextRnCardType:
		return 7
	case common.SkillDeepseek.ToString():
		return 8 // deepseek
	default:
		return 1 // 纯文本
	}
}

type MemoryContextReq struct {
	UserTalID   string          `json:"user_tal_id"`
	SessionId   string          `json:"session_id"`
	AsrInfo     string          `json:"asr_info"`
	IllegalType string          `json:"illegal_type"`
	Hotfix      *HotfixVectorDo `json:"hotfix"`
	SceneCode   int             `json:"scene_code"`
}

type XPadMemoryContextResp struct {
	TimeStamp       int64                  `json:"time_stamp"`
	Intent          string                 `json:"intent"`
	AsrInfo         string                 `json:"asr_info"`
	TtsInfo         string                 `json:"tts_info"`
	TtsNorm         string                 `json:"tts_norm"`
	FuncList        []*XPadContextFuncList `json:"func_list"`
	Response        string                 `json:"response"`
	RewriteQuery    string                 `json:"rewrite_query"`
	ScenarioType    string                 `json:"scenario_type"`
	MixedModalQuery string                 `json:"mixed_modal_query"`
	MultiModalInfo  *MultiModalInfo        `json:"multi_modal_info,omitempty"`
}

type XPadContextFuncList struct {
	FuncName       string      `json:"func_name"`
	FuncParameters interface{} `json:"func_parameters"`
}

type LlmMemoryContextResp struct {
	// user/assistant
	Role       string `json:"role"`
	Content    string `json:"content"`
	DialogueId string `json:"dialogue_id"`
}

type XPodMemoryContextResp struct {
	TimeStamp int64              `json:"time_stamp"`
	Intent    []string           `json:"intent"`
	AsrInfo   string             `json:"asr_info"`
	TtsInfo   string             `json:"tts_info"`
	FuncList  []*ContextFuncList `json:"func_list"`
}

// MemoryContext 每个上下文语境定义成一个名为MemoryContext的对象
type MemoryContext struct {
	SessionId         string             `json:"session_id"`
	TimeStamp         int64              `json:"time_stamp"`
	Intent            string             `json:"intent"`
	AsrInfo           string             `json:"asr_info"`
	TtsInfo           string             `json:"tts_info"`
	TtsNorm           string             `json:"tts_norm"`
	TtsParam          *TtsParam          `json:"tts_param"`
	FuncList          []*ContextFuncList `json:"func_list"`
	State             *State             `json:"state,omitempty"`
	Response          interface{}        `json:"response,omitempty"`
	IsLlm             bool               `json:"is_llm"`
	RewriteQuery      string             `json:"rewrite_query,omitempty"`
	Source            int64              `json:"source,omitempty"`
	ImageUrl          string             `json:"image_url,omitempty"`
	VideoUrl          string             `json:"video_url,omitempty"`
	LlmSkill          string             `json:"llm_skill,omitempty"`
	LlmResponse       string             `json:"llm_response,omitempty"`
	DialogueId        string             `json:"dialogue_id,omitempty"`
	Media             *Media             `json:"media,omitempty"`
	ScenarioType      string             `json:"scenario_type,omitempty"`
	XiaosiCommandList []XiaosiCommands   `json:"xiaosi_command_list,omitempty"`
	SceneCode         int                `json:"scene_code"`
	MixedModalQuery   string             `json:"mixed_modal_query,omitempty"`
	ReasoningContent  string             `json:"reasoning_content,omitempty"`
	MultiModalInfo    *MultiModalInfo    `json:"multi_modal_info,omitempty"`
}

type ContextFuncList struct {
	FuncName       string      `json:"func_name"`
	FuncParameters interface{} `json:"func_parameters"`
	Intent         string      `json:"intent"`
}

// 对话状态
type State struct {
	NumAsk int `json:"num_ask"`
}

type RedisMemoryContext struct {
	SessionId         string `json:"session_id" redis:"session_id"`
	TimeStamp         int64  `json:"time_stamp" redis:"time_stamp"`
	Intent            string `json:"intent" redis:"intent"`
	AsrInfo           string `json:"asr_info" redis:"asr_info"`
	TtsInfo           string `json:"tts_info" redis:"tts_info"`
	TtsNorm           string `json:"tts_norm" redis:"tts_norm"`
	FuncList          string `json:"func_list" redis:"func_list"`
	State             string `json:"state" redis:"state"`
	Response          string `json:"response" redis:"response"`
	IsLlm             bool   `json:"is_llm" redis:"is_llm"`
	RewriteQuery      string `json:"rewrite_query" redis:"rewrite_query"`
	Source            int64  `json:"source" redis:"source"`
	ImageUrl          string `json:"image_url" redis:"image_url"`
	VideoUrl          string `json:"video_url" redis:"video_url"`
	LlmSkill          string `json:"llm_skill" redis:"llm_skill"`
	LlmResponse       string `json:"llm_response" redis:"llm_response"`
	DialogueId        string `json:"dialogue_id" redis:"dialogue_id"`
	Media             string `json:"media" redis:"media"`
	ScenarioType      string `json:"scenario_type" redis:"scenario_type"`
	XiaosiCommandList string `json:"xiaosi_command_list" redis:"xiaosi_command_list"`
	SceneCode         int    `json:"scene_code"`
	MixedModalQuery   string `json:"mixed_modal_query" redis:"mixed_modal_query"`
	ReasoningContent  string `json:"reasoning_content" redis:"reasoning_content"`
	MultiModalInfo    string `json:"multi_modal_info" redis:"multi_modal_info"`
}

type MemoryContextApi struct {
	SessionId         string           `json:"session_id"`
	TimeStamp         int64            `json:"time_stamp"`
	Intent            string           `json:"intent"`
	AsrInfo           string           `json:"asr_info"`
	TtsInfo           string           `json:"tts_info"`
	TtsNorm           string           `json:"tts_norm"`
	Response          interface{}      `json:"response"`
	RewriteQuery      string           `json:"rewrite_query"`
	Source            ContextSource    `json:"source"`
	IsLlm             bool             `json:"is_llm"`
	ImageUrl          string           `json:"image_url"`
	VideoUrl          string           `json:"video_url"`
	LlmSkill          string           `json:"llm_skill"`
	LlmResponse       string           `json:"llm_response"`
	Media             *Media           `json:"media"`
	XiaosiCommandList []XiaosiCommands `json:"xiaosi_command_list"`
	ReasoningContent  string           `json:"reasoning_content"`
}

type MemoryContextApiResp struct {
	List []*MemoryContextApi `json:"list"`
}

type NluDataWidget struct {
	Content struct {
		Category     int                     `json:"category"`
		CategoryName string                  `json:"category_name"`
		Data         []NluDataWidgetDataItem `json:"data"`
	} `json:"content"`
}

type NluDataWidgetDataItem struct {
	Type string `json:"type"`
	Item struct {
		CourseName       string `json:"course_name"`
		CourseSystemName string `json:"course_system_name"`
		LessonName       string `json:"lesson_name"`
		CategoryName     string `json:"category_name"`
		ResourceName     string `json:"resource_name"`
	} `json:"item"`
}

type ContextWidgetInfo struct {
	Content []struct {
		Title    string `json:"title"`
		Subtitle string `json:"subtitle"`
		ShowType string `json:"showType"`
		Task     string `json:"task"`
	} `json:"content"`
}

type UpdateMemoryContextLlmResponse struct {
	LlmSkill           string      `json:"llm_skill,omitempty"`
	LlmModel           string      `json:"llm_model,omitempty"`
	Response           string      `json:"response,omitempty"`
	ImageUrl           string      `json:"image_url,omitempty"`
	VideoUrl           string      `json:"video_url,omitempty"`
	LlmResponse        string      `json:"llm_response,omitempty"`
	DialogueId         string      `json:"dialogue_id,omitempty"`
	Media              *Media      `json:"media,omitempty"`
	TtsInfo            string      `json:"tts_info,omitempty"`
	MixedModalQuery    string      `json:"mixed_modal_query,omitempty"`
	MixedModalResponse interface{} `json:"mixed_modal_response,omitempty"`
	ReasoningContent   string      `json:"reasoning_content,omitempty"`
}

type XiaosiCommands struct {
	Title string       `json:"title"`
	Query string       `json:"query"`
	Apps  []CommandApp `json:"apps"`
}

type CommandApp struct {
	Name string `json:"name"`
	Icon string `json:"icon"`
}
