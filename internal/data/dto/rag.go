package dto

type RagReq struct {
	Query     string `json:"query"`
	RequestId string `json:"request_id"`
}

type RagResp struct {
	Code    int     `json:"code"`
	Data    RagData `json:"data"`
	TraceID string  `json:"trace_id"`
}

type RagData struct {
	Recall []*RagRecallItem `json:"recall"`
	Task   string           `json:"task"`
	Text   string           `json:"text"`
}

type RagRecallItem struct {
	Question string `json:"question"`
	Answer   string `json:"answer"`
}
