package dto

type ContentConfRes struct {
	Errcode     int         `json:"errcode"`
	Errmsg      string      `json:"errmsg"`
	Data        ContentConf `json:"data"`
	Trace       string      `json:"trace"`
	DataType    int         `json:"data_type"`
	OtelTraceID string      `json:"otel_trace_id"`
	OtelSpanID  string      `json:"otel_span_id"`
}

type ContentConf struct {
	SemesterInfo []GradeInfo `json:"semester_info"`
	GradeInfo    []struct {
		ID    int    `json:"id"`
		Value string `json:"value"`
	} `json:"grade_info"`
	Attainment []struct {
		ID    int    `json:"id"`
		Value string `json:"value"`
	} `json:"attainment"`
	SubjectCategories []struct {
		ID            int    `json:"id"`
		Value         string `json:"value"`
		VisibleGrades []int  `json:"visible_grades"`
	} `json:"subject_categories"`
	CourseSubjects    []CourseSubject `json:"course_subjects"`
	BCourseSubjectMap map[string]int  `json:"b_course_subject_map"`
	CourseSystemMap   map[int]string  `json:"course_system_map"`
	BCourseSystemMap  map[string]int  `json:"b_course_system_map"`
	VersionMap        map[int]string  `json:"version_map"`
	PaperProvinces    []struct {
		TagID   int    `json:"tag_id"`
		TagName string `json:"tag_name"`
		Idx     string `json:"idx"`
	} `json:"paper_provinces"`
	PaperDifficult    map[int]string `json:"paper_difficult"`
	PaperType         map[int]string `json:"paper_type"`
	PaperScopeMap     map[int]string `json:"paper_scope_map"`
	PaperCupTypeMap   map[int]string `json:"paper_cup_type_map"`
	PreschoolLearnBar []struct {
		BusinessID  int    `json:"business_id"`
		ColumnID    int    `json:"column_id"`
		ColumnName  string `json:"column_name"`
		SelectedBgm string `json:"selected_bgm"`
		UnSelectBgm string `json:"un_select_bgm"`
		ChildData   []struct {
			BgImg           string `json:"bg_img"`
			ChildColumnID   int    `json:"child_column_id"`
			ChildColumnName string `json:"child_column_name"`
			ColumnID        int    `json:"column_id"`
		} `json:"child_data"`
	} `json:"preschool_learn_bar"`
	PreschoolLookBar []struct {
		BusinessID  int    `json:"business_id"`
		ColumnID    int    `json:"column_id"`
		ColumnName  string `json:"column_name"`
		SelectedBgm string `json:"selected_bgm"`
		UnSelectBgm string `json:"un_select_bgm"`
		ChildData   []struct {
			BgImg           string `json:"bg_img"`
			ChildColumnID   int    `json:"child_column_id"`
			ChildColumnName string `json:"child_column_name"`
			ColumnID        int    `json:"column_id"`
		} `json:"child_data"`
	} `json:"preschool_look_bar"`
	PreschoolGrades         []GradeInfo     `json:"preschool_grades"`
	PreschoolCourseSubjects []CourseSubject `json:"preschool_course_subjects"`
}

type GradeInfo struct {
	ID    int    `json:"id"`
	Value string `json:"value"`
}

type CourseSubject struct {
	ID            int    `json:"id"`
	Value         string `json:"value"`
	VisibleGrades []int  `json:"visible_grades"`
}
