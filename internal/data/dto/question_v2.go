package dto

// GetImageDetectInfoReq 获取图像检测信息请求结构
type GetImageDetectInfoReq struct {
	SessionID      string    `json:"session_id,omitempty"`
	TalId          string    `json:"tal_id,omitempty"`
	QueryType      int       `json:"query_type"`                // 1 序号 2 指尖或圈选
	ImageUrl       string    `json:"image_url,omitempty"`       // 原图 指尖或圈选时用
	PointPosition  []float64 `json:"point_position,omitempty"`  // 指尖位置 指尖或圈选时用
	MainBox        []float64 `json:"main_box,omitempty"`        // 主体坐标 指尖或圈选时用
	QuestionNumber int       `json:"question_number,omitempty"` // 题目序号 序号时用
}

// PageMatchDto 页面匹配结果响应结构
type PageMatchDto struct {
	MatchFlag bool      `json:"match_flag"`
	QusIndex  int       `json:"qus_index"`
	QusBox    []float64 `json:"qus_box"`
	ImageUrl  string    `json:"image_url"`
}

// ImageDetectInfoResp 图像检测信息响应结构
type ImageDetectInfoResp struct {
	Code     int          `json:"code"`
	ErrorMsg string       `json:"error_msg"`
	Data     PageMatchDto `json:"data"`
}

// AtmsQuestionSearchReq ATMS多模态问题检索请求
type AtmsQuestionSearchReq struct {
	TalId    string `json:"talId"`                       // 用户ID
	ImageUrl string `json:"imageUrl" binding:"required"` // 图片URL
}

// AtmsQuestionSearchResp ATMS多模态问题检索响应
type AtmsQuestionSearchResp struct {
	ExplainStatus int    `json:"explainStatus"` // 讲解状态
	ExplainType   int    `json:"explainType"`   // 讲解类型
	Question      string `json:"question"`      // 问题文本
	QuestionId    string `json:"questionId"`    // 问题ID
	Subject       int    `json:"subject"`       // 学科
	SessionId     string `json:"sessionId"`     // 会话ID
}

// AtmsQuestionSearchResponse 接口返回的完整响应
type AtmsQuestionSearchResponse struct {
	Code     int                    `json:"code"`
	ErrorMsg string                 `json:"error_msg"`
	Data     AtmsQuestionSearchResp `json:"data"`
}
