package dto

type ActionMapDeviceType string

const (
	ActionMapDeviceTypeDefault   ActionMapDeviceType = "xPad"
	ActionMapDeviceTypeXueLianJi ActionMapDeviceType = "xuelianji"
	ActionMapDeviceTypeXinRui    ActionMapDeviceType = "xinrui"
)

var ActionMap = map[ActionMapDeviceType]map[ScreeMode]map[string][]Function{
	ActionMapDeviceTypeDefault: {
		MODE_Default: {
			"search_ebook": {
				{
					FuncName: "search_book",
				},
				{
					FuncName: "search_periodical",
				},
			},
			"search_cultivation": {
				{
					FuncName: "search_documentary",
				},
				{
					FuncName: "search_cultivation",
				},
			},
		},
	},
	ActionMapDeviceTypeXueLianJi: {
		MODE_READING: {
			"search_ebook": {
				{
					FuncName: "search_book",
				},
				{
					FuncName: "search_periodical",
				},
			},
			"search_cultivation": {
				{
					FuncName: "search_documentary",
				},
			},
			"search_course": {
				{
					FuncName: "search_course",
				},
			},
		},
		MODE_TUTOR: {
			"search_ebook": {
				{
					FuncName: "search_ebook",
				},
			},
			"search_cultivation": {
				{
					FuncName: "search_cultivation",
				},
			},
		},
	},
}
