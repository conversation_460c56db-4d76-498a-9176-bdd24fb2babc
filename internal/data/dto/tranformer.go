package dto

import (
	"github.com/spf13/cast"
	"sort"
)

// TransSubjectResource2Filter 学科过滤条件
func TransSubjectResource2Filter(resourceList []Resource) []Filter {

	subjectOption := make([]Option, 0)
	gradeOption := make([]Option, 0)
	semesterOption := make([]Option, 0)
	// TODO 去除没有在查询结果中的数据
	for _, resource := range resourceList {
		subjectOption = append(subjectOption, Option{
			Id:   cast.ToString(resource.SubjectId),
			Name: resource.SubjectName,
		})
		gradeOption = append(gradeOption, Option{
			Id:   cast.ToString(resource.GradeId),
			Name: resource.GradeName,
		})
		semesterOption = append(semesterOption, Option{
			Id:   cast.ToString(resource.SemesterId),
			Name: resource.SemesterName,
		})
	}
	return []Filter{
		{
			Key:     "subject",
			Name:    "学科",
			Options: UniqueNoneEmptyAndSortOptions(subjectOption),
		},
		{
			Key:     "grade",
			Name:    "年级",
			Options: UniqueNoneEmptyAndSortOptions(gradeOption),
		},
		{
			Key:     "semester",
			Name:    "学期",
			Options: UniqueNoneEmptyAndSortOptions(semesterOption),
		},
	}
}

func TransPaperResource2Filter(resourceList []Resource) []Filter {

	gradeOption := make([]Option, 0)
	semesterOption := make([]Option, 0)
	paperTypeOption := make([]Option, 0)
	provinceOption := make([]Option, 0)
	famousSchoolOption := make([]Option, 0)
	yearOption := make([]Option, 0)
	difficultyOption := make([]Option, 0)
	famousSchoolMap := map[int]string{
		2: "只看名校",
	}
	// TODO 去除没有在查询结果中的数据
	for _, resource := range resourceList {
		gradeOption = append(gradeOption, Option{
			Id:   cast.ToString(resource.GradeId),
			Name: resource.GradeName,
		})
		semesterOption = append(semesterOption, Option{
			Id:   cast.ToString(resource.SemesterId),
			Name: resource.SemesterName,
		})
		paperTypeOption = append(paperTypeOption, Option{
			Id:   cast.ToString(resource.PaperType),
			Name: resource.PaperTypeName,
		})
		provinceOption = append(provinceOption, Option{
			Id:   cast.ToString(resource.ProvinceId),
			Name: resource.ProvinceName,
		})
		famousSchoolOption = append(famousSchoolOption, Option{
			Id:   cast.ToString(resource.IsFamousSchool),
			Name: famousSchoolMap[resource.IsFamousSchool],
		})
		yearOption = append(yearOption, Option{
			Id:   cast.ToString(resource.Year),
			Name: cast.ToString(resource.Year),
		})
		difficultyOption = append(difficultyOption, Option{
			Id:   cast.ToString(resource.DifficultyId),
			Name: cast.ToString(resource.DifficultyName),
		})
	}
	return []Filter{
		{
			Key:     "grade",
			Name:    "年级",
			Options: UniqueNoneEmptyAndSortOptions(gradeOption),
		},
		{
			Key:     "semester",
			Name:    "学期",
			Options: UniqueNoneEmptyAndSortOptions(semesterOption),
		},
		{
			Key:     "paperType",
			Name:    "试卷类型",
			Options: UniqueNoneEmptyAndSortOptions(paperTypeOption),
		},
		{
			Key:     "province",
			Name:    "地区",
			Options: UniqueNoneEmptyAndSortOptions(provinceOption),
		},
		{
			Key:     "isFamousSchool",
			Name:    "试卷范围",
			Options: UniqueNoneEmptyAndSortOptions(famousSchoolOption),
		},
		{
			Key:     "year",
			Name:    "年份",
			Options: UniqueNoneEmptyAndSortOptions(yearOption),
		},
		{
			Key:     "difficulty",
			Name:    "难度",
			Options: UniqueNoneEmptyAndSortOptions(difficultyOption),
		},
	}
}

func UniqueNoneEmptyAndSortOptions(options []Option) []Option {

	// 创建一个 map 用于去重
	optionMap := make(map[string]Option)

	// 遍历原始 options 切片，将每个元素添加到 map 中
	for _, opt := range options {
		if len(opt.Name) > 0 {
			optionMap[opt.Id] = opt
		}
	}

	// 创建一个切片存储去重后的元素
	uniqueOptions := make([]Option, 0, len(optionMap))

	// 遍历 map 中的值，按 Id 从小到大的顺序添加到新切片中
	var keys []string
	for k := range optionMap {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	for _, key := range keys {
		uniqueOptions = append(uniqueOptions, optionMap[key])
	}

	return uniqueOptions
}

func TransSubjectStudyLog2Filter(studyLogList []*StudyLogDetail) []Filter {

	subjectOption := make([]Option, 0)
	gradeOption := make([]Option, 0)
	semesterOption := make([]Option, 0)
	// TODO 去除没有在查询结果中的数据
	for _, resource := range studyLogList {
		subjectOption = append(subjectOption, Option{
			Id:   cast.ToString(resource.Subject),
			Name: resource.SubjectStr,
		})
		gradeOption = append(gradeOption, Option{
			Id:   cast.ToString(resource.Grade),
			Name: resource.GradeStr,
		})
		semesterOption = append(semesterOption, Option{
			Id:   cast.ToString(resource.Semester),
			Name: resource.SemesterStr,
		})
	}
	return []Filter{
		{
			Key:     "subject",
			Name:    "学科",
			Options: UniqueNoneEmptyAndSortOptions(subjectOption),
		},
		{
			Key:     "grade",
			Name:    "年级",
			Options: UniqueNoneEmptyAndSortOptions(gradeOption),
		},
		{
			Key:     "semester",
			Name:    "学期",
			Options: UniqueNoneEmptyAndSortOptions(semesterOption),
		},
	}
}
