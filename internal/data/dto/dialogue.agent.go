package dto

const (
	SceneModeLightweight   = 0 //轻量模式
	SceneModeFullView      = 2 //全屏模式
	SceneModeNotUnderstand = 3 // 没听懂
)

type FetchMemoryReq struct {
	TalId string `json:"tal_id"`
	Query string `json:"query"`
}

type LuiMemoryServiceResp struct {
	ErrorReason string      `json:"error_reason"`
	ErrorMsg    string      `json:"error_msg"`
	MetaData    interface{} `json:"meta_data"`
	TraceID     string      `json:"trace_id"`
	ServerTime  int         `json:"server_time"`
	Data        struct {
		Replies []struct {
			ID          string `json:"id"`
			EventDate   string `json:"event_date"`
			Description string `json:"description"`
			TalID       string `json:"tal_id"`
			DeviceID    string `json:"device_id"`
		} `json:"replies"`
	} `json:"data"`
}

type DialogueProfile struct {
	Recent    string `json:"recent"`    // 近况
	Interests string `json:"interests"` // 爱好
	Task      string `json:"task"`      // 目标
}

type DialogueProfileResp struct {
	ErrorReason string      `json:"error_reason"`
	ErrorMsg    string      `json:"error_msg"`
	MetaData    interface{} `json:"meta_data"`
	TraceID     string      `json:"trace_id"`
	ServerTime  int         `json:"server_time"`
	Data        struct {
		Emotion []struct {
			QueryTime string `json:"query_time"`
			Details   string `json:"details"`
			Score     int    `json:"score"`
		} `json:"emotion"`
		Hobby []struct {
			Details string `json:"details"`
			Score   int    `json:"score"`
		} `json:"hobby"`
		UserName            string `json:"user_name"`
		UserNameTrustScores int    `json:"user_name_trust_scores"`
	} `json:"data"`
}
