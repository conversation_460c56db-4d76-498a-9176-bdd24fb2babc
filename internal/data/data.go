package data

import (
	"git.100tal.com/jituan_genie_server/mqs/kafka"
	"lui-api/internal/conf"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/extra/redisotel/v9"
	"github.com/redis/go-redis/v9"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(NewData, NewRegistrar, NewGreeterRepo, NewUCenterRepo, NewEmbeddingRepo, NewIPAnalysisRepo, NewKafkaProducerRepo, NewContactRepo, NewMemoryContext, NewSugRepo, NewVisionRepo)

// Data .
type Data struct {
	rdb            *redis.Client
	kafkaProducer  *kafka.Producer
	queryProducers []*kafka.Producer
}

// NewData .
func NewData(conf *conf.Data, logger log.Logger) (*Data, func(), error) {

	rdb := redis.NewClient(&redis.Options{
		Addr:         conf.Redis.Addr,
		Password:     conf.Redis.Password,
		DB:           int(conf.Redis.Db),
		DialTimeout:  conf.Redis.DialTimeout.AsDuration(),
		WriteTimeout: conf.Redis.WriteTimeout.AsDuration(),
		ReadTimeout:  conf.Redis.ReadTimeout.AsDuration(),
	})

	if err := redisotel.InstrumentTracing(rdb); err != nil {
		return nil, nil, errors.Wrap(err, "data: redisotel.InstrumentTracing error")
	}
	if err := redisotel.InstrumentMetrics(rdb); err != nil {
		return nil, nil, errors.Wrap(err, "data: redisotel.InstrumentMetrics error")
	}

	producer, err := NewKafkaProducer(conf.QueryKafka, logger)
	if err != nil {
		return nil, nil, errors.Wrap(err, "data: NewKafkaProducer error")
	}

	queryProducers := make([]*kafka.Producer, 0)
	for _, kafkaConf := range conf.QueryKafkaList {
		producerAsr, err := NewKafkaProducer(kafkaConf, logger)
		if err != nil {
			return nil, nil, errors.Wrapf(err, "data: NewKafkaProducer error: %+v", kafkaConf)
		}
		queryProducers = append(queryProducers, producerAsr)
	}

	d := &Data{
		rdb:            rdb,
		kafkaProducer:  producer,
		queryProducers: queryProducers,
	}
	return d, func() {
		d.rdb.Close()
		log.NewHelper(logger).Info("closing the redis")
		d.kafkaProducer.Close()
		log.NewHelper(logger).Info("closing the kafka producer")

		for index, producer := range d.queryProducers {
			err := producer.Close()
			log.NewHelper(logger).Info("closing the kafka, index", index, err)
		}
	}, nil
}
