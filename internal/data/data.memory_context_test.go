package data

import (
	context2 "context"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"lui-api/internal/conf"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/dayu_trace"
	"lui-api/internal/pkg/header_logger"
	"lui-api/pkg/util"
	"lui-api/pkg/zlog"
	"testing"
	"time"
)

func TestMemoryContext_AddMemoryContext(t *testing.T) {
	memoryContext := newMemoryContext(t)
	ctx := context2.Background()

	deviceID := "test_device_id1"
	sessionID := "test_session_id2"
	context := dto.MemoryContext{
		TimeStamp: time.Now().Unix(),
		Intent:    "test_intent",
		AsrInfo:   "test_asr_info",
		TtsInfo:   "test_tts_info2",
		FuncList:  nil,
		State: &dto.State{
			NumAsk: 1,
		},
		Response: "test_response2",
	}

	if err := memoryContext.AddMemoryContext(ctx, deviceID, sessionID, context); err != nil {
		t.Error(err)
	}
}

func TestMemoryContext_GetMemoryContext(t *testing.T) {
	memoryContext := newMemoryContext(t)
	ctx := context2.Background()

	deviceID := "test_device_id1"

	res, err := memoryContext.GetMemoryContext(ctx, deviceID)
	if err != nil {
		t.Error(err)
	}
	t.Logf("res: %v", util.Marshal(res))

	for _, v := range res {
		t.Log(util.Marshal(v))
	}
}

func TestMemoryContext_UpdateMemoryContext(t *testing.T) {
	memoryContext := newMemoryContext(t)
	ctx := context2.Background()

	sessionID := "test_session_id1"

	response := "test_response_update"

	context := dto.MemoryContext{
		Response: response,
	}

	err := memoryContext.UpdateMemoryContextResponse(ctx, sessionID, context)
	if err != nil {
		t.Error(err)
	}
}

func newMemoryContext(t *testing.T) *MemoryContext {
	name := "lui-api-test"

	c := config.New(
		config.WithSource(
			file.NewSource("./../../configs/config.yaml"),
		),
	)
	defer c.Close()

	if err := c.Load(); err != nil {
		panic(err)
	}

	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}

	zlog.Init(name, "/tmp/log.log", 1, 5, 10, true)
	defer zlog.Sync()
	logger := log.With(zlog.NewZapLogger(zlog.STDInstance()),
		"ts", log.Timestamp(time.RFC3339Nano),
		"caller", log.DefaultCaller,
		"trace_id", tracing.TraceID(),
		"span_id", tracing.SpanID(),
		"dayu_trace_id", dayu_trace.TraceID(),
		"header", header_logger.Header(),
	)

	dataBase, _, err := NewData(bc.Data, logger)
	if err != nil {
		t.Error(err)
	}

	return &MemoryContext{
		log:  log.NewHelper(logger),
		data: dataBase,
	}
}
