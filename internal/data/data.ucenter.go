package data

import (
	"context"
	"github.com/redis/go-redis/v9"
	"lui-api/internal/biz"
	"time"
)

type uCenterRepo struct {
	data *Data
}

func NewUCenterRepo(data *Data) biz.UCenterRepo {
	return &uCenterRepo{
		data: data,
	}
}

func (d *uCenterRepo) RdbGetKey(ctx context.Context, key string) *redis.StringCmd {
	return d.data.rdb.Get(ctx, key)
}

func (d *uCenterRepo) RdbSetKey(ctx context.Context, key string, value interface{}, expiration int) *redis.StatusCmd {
	return d.data.rdb.Set(ctx, key, value, time.Second*time.Duration(expiration))
}
