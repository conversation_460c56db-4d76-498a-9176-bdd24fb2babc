package data

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"go.opentelemetry.io/otel/trace"
	"lui-api/internal/biz"
	"lui-api/internal/conf"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	"lui-api/internal/pkg/utils"
	"lui-api/pkg/util"
)

type ContactRepo struct {
	data  *Data
	log   *log.Helper
	third *conf.Third
}

func NewContactRepo(data *Data, logger log.Logger, third *conf.Third) biz.ContactRepo {
	return &ContactRepo{
		data:  data,
		log:   log.NewHelper(logger),
		third: third,
	}
}

func (d *ContactRepo) GetContact(ctx context.Context, talID string) []*dto.ContactInfo {

	//contactStr := "[{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100148\",\"contact_name\":\"盛宏\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100122\",\"contact_name\":\"悦悦\",\"hide_phone\":\"15600825217\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/nainai.jpg\",\"contact_id\":\"100102\",\"contact_name\":\"外婆\",\"hide_phone\":\"13269081567\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/mama.jpg\",\"contact_id\":\"100101\",\"contact_name\":\"妈妈\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100103\",\"contact_name\":\"小明\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy1.jpg\",\"contact_id\":\"100104\",\"contact_name\":\"Alex\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy3.jpg\",\"contact_id\":\"100105\",\"contact_name\":\"佳佳\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/xiaoyi.jpg\",\"contact_id\":\"100106\",\"contact_name\":\"大姑\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/xiaoyi.jpg\",\"contact_id\":\"100107\",\"contact_name\":\"Amy\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy3.jpg\",\"contact_id\":\"100108\",\"contact_name\":\"橙子姐姐\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy1.jpg\",\"contact_id\":\"100109\",\"contact_name\":\"储易辰\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy3.jpg\",\"contact_id\":\"100110\",\"contact_name\":\"朵儿\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100111\",\"contact_name\":\"何天怡\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100112\",\"contact_name\":\"何梓涵\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100113\",\"contact_name\":\"季易泽\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100114\",\"contact_name\":\"李雨希\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100115\",\"contact_name\":\"梁安邦\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100116\",\"contact_name\":\"刘乖乖\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100117\",\"contact_name\":\"刘孟儿\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100118\",\"contact_name\":\"钱伊含\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100119\",\"contact_name\":\"邵暖桃\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100120\",\"contact_name\":\"王杰瑞\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100121\",\"contact_name\":\"肖润哲\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100123\",\"contact_name\":\"张彦博\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100124\",\"contact_name\":\"赵一诺\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100125\",\"contact_name\":\"王子轩\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100126\",\"contact_name\":\"张梓轩\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100127\",\"contact_name\":\"刘晨曦\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100128\",\"contact_name\":\"陈欣怡\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100129\",\"contact_name\":\"杨天怡\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100130\",\"contact_name\":\"黄沐宸\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100131\",\"contact_name\":\"吴依林\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100132\",\"contact_name\":\"郑海蓝\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100133\",\"contact_name\":\"徐小豪\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100134\",\"contact_name\":\"孙语嫣\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100135\",\"contact_name\":\"周梦瑶\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100136\",\"contact_name\":\"吕博文\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100137\",\"contact_name\":\"高新月\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100138\",\"contact_name\":\"马晟睿\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100139\",\"contact_name\":\"朱乐彤\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100140\",\"contact_name\":\"林嘉欣\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100141\",\"contact_name\":\"胡瑾萱\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100142\",\"contact_name\":\"钟熙然\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100143\",\"contact_name\":\"韩梓楠\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100144\",\"contact_name\":\"彭天宇\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100145\",\"contact_name\":\"程梓涵\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100146\",\"contact_name\":\"曹雨辰\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100147\",\"contact_name\":\"贾诗涵\",\"hide_phone\":\"18510249630\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100175\",\"contact_name\":\"姥爷\",\"hide_phone\":\"15581968393\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100149\",\"contact_name\":\"姥爷爷\",\"hide_phone\":\"18201599502\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100150\",\"contact_name\":\"姥爷1\",\"hide_phone\":\"15581968393\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100151\",\"contact_name\":\"姥爷2\",\"hide_phone\":\"15581968393\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100152\",\"contact_name\":\"老家姥爷\",\"hide_phone\":\"15581968393\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/mama.jpg\",\"contact_id\":\"100153\",\"contact_name\":\"妈妈\",\"hide_phone\":\"15581968393\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/mama.jpg\",\"contact_id\":\"100154\",\"contact_name\":\"妈妈\",\"hide_phone\":\"18201599502\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/mama.jpg\",\"contact_id\":\"100155\",\"contact_name\":\"妈咪\",\"hide_phone\":\"15581968393\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/mama.jpg\",\"contact_id\":\"100156\",\"contact_name\":\"妈妈sdjf\",\"hide_phone\":\"15581968393\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/mama.jpg\",\"contact_id\":\"100157\",\"contact_name\":\"妈妈电话\",\"hide_phone\":\"15581968393\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/mama.jpg\",\"contact_id\":\"100158\",\"contact_name\":\"母亲大人\",\"hide_phone\":\"15581968393\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/mama.jpg\",\"contact_id\":\"100159\",\"contact_name\":\"妈\",\"hide_phone\":\"15581968393\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100160\",\"contact_name\":\"爸爸1\",\"hide_phone\":\"15581968393\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100161\",\"contact_name\":\"大爸\",\"hide_phone\":\"15581968393\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100162\",\"contact_name\":\"老爸\",\"hide_phone\":\"15581968393\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100163\",\"contact_name\":\"二爸\",\"hide_phone\":\"15581968393\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/nainai.jpg\",\"contact_id\":\"100164\",\"contact_name\":\"奶奶1\",\"hide_phone\":\"15581968393\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/nainai.jpg\",\"contact_id\":\"100165\",\"contact_name\":\"奶奶2\",\"hide_phone\":\"18201599502\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/nainai.jpg\",\"contact_id\":\"100166\",\"contact_name\":\"奶奶电话\",\"hide_phone\":\"15581968393\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/nainai.jpg\",\"contact_id\":\"100167\",\"contact_name\":\"奶奶4\",\"hide_phone\":\"15581968393\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/nainai.jpg\",\"contact_id\":\"100168\",\"contact_name\":\"老家奶奶\",\"hide_phone\":\"15581968393\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100169\",\"contact_name\":\"舅\",\"hide_phone\":\"18201599502\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100170\",\"contact_name\":\"舅舅\",\"hide_phone\":\"18201599502\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100171\",\"contact_name\":\"老舅\",\"hide_phone\":\"18201599502\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100172\",\"contact_name\":\"大舅\",\"hide_phone\":\"18201599502\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100173\",\"contact_name\":\"二舅\",\"hide_phone\":\"18201599502\"},{\"avatar\":\"https://audio-1302207384.cos.ap-beijing.myqcloud.com/boy2.jpg\",\"contact_id\":\"100174\",\"contact_name\":\"舅爷\",\"hide_phone\":\"18201599502\"}]"
	//Contacts := make([]*dto.ContactInfo, 0)
	//
	//err := json.Unmarshal([]byte(contactStr), &Contacts)
	//if err != nil {
	//	d.log.WithContext(ctx).Errorf("GetContact json.Unmarshal error: %s", err)
	//	return nil
	//}
	//return Contacts
	nluReq := make(map[string]interface{})
	nluReq["userId"] = talID
	header := &dto.ExactlyHeader{
		XRequestId:  custom_context.GetTraceId(ctx),
		Traceparent: fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
	}
	headers := util.StructToMap(header)
	headers["Content-Type"] = "application/json"
	ch := utils.CurlInit(ctx, time.Second*3, 0, d.log)
	resp, err := ch.ChRequest(ch.Req.SetHeaders(headers).SetBody(nluReq)).Post(d.third.XsContactUrl)
	if err != nil {
		d.log.Infof("GetContact err: %+v", err)
		return nil
	}
	d.log.WithContext(ctx).Infof("GetContact resp: %s; params: %+v", string(resp.Body()), nluReq)
	if resp.IsError() {
		d.log.Infof("GetContact resp.IsError err: %+v", err)
		return nil
	}

	var controllerRes dto.ContactListGetRes
	d.log.WithContext(ctx).Infof("GetContact resp:%s", string(resp.Body()))
	err = json.Unmarshal(resp.Body(), &controllerRes)
	if err != nil {
		d.log.Infof("GetContact Unmarshal err: %+v", err)
		return nil
	}

	return controllerRes.Data.Contacts
}
