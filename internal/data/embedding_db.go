package data

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"lui-api/internal/biz"
	"lui-api/internal/conf"
	"lui-api/internal/data/dto"
)

type embeddingDBRepo struct {
	data              *Data
	log               *log.Helper
	embeddingDB       *resty.Client
	embeddingFaqDB    *resty.Client
	embeddingHotfixDB *resty.Client
	conf              *conf.Data
}

type CommonResponse struct {
	Code  int    `json:"code"`
	Msg   string `json:"msg"`
	Count int    `json:"count"`
}

type FaqQueryResponse struct {
	Code      int          `json:"code"`
	Msg       string       `json:"msg"`
	Documents [][]Document `json:"documents"`
}

// 定义文档结构
type Document struct {
	ID       string  `json:"id"`
	Score    float64 `json:"score"`
	Question string  `json:"question"`
	Answer   string  `json:"answer"`
	Text     string  `json:"text"`
}

// Hint 查询
func (e embeddingDBRepo) Hint(ctx context.Context, database, collection string, filter dto.SlotCombineCheckReq) (hintStatus bool, err error) {
	var resp CommonResponse
	filters := filter.GetFilters()
	response, err := e.embeddingDB.R().
		SetBody(map[string]interface{}{
			"database":   database,
			"collection": collection,
			"query": map[string]interface{}{
				"filter":         filters,
				"limit":          1,
				"retrieveVector": false,
			},
		}).
		SetResult(&resp).
		Post(e.conf.Embedding.Tencent.Url + "/document/query")
	e.log.WithContext(ctx).Infof("embeddingDBRepo: Hint, database=%s, collection=%s, filter: %v", database, collection, filter)
	if err != nil {
		return false, errors.Wrapf(err, "embeddingDBRepo: Hint error, database=%s, collection=%s, filter: %+v, filters: %s", database, collection, filter, filters)
	}
	if response.StatusCode() != 200 || resp.Code != 0 {
		return false, fmt.Errorf("embeddingDBRepo: Hint status error, database=%s, collection=%s, filter: %+v, filters: %s, statusCode=%d, code=%d, msg=%s", database, collection, filter, filters, response.StatusCode(), resp.Code, resp.Msg)
	}
	e.log.WithContext(ctx).Infof("embeddingDBRepo: %+d; %s; %+v;", resp.Code, resp.Msg, resp)
	return resp.Count > 0, nil
}

// faq查询
func (e embeddingDBRepo) FaqQuery(ctx context.Context, database, collection, query string) (*dto.FaqVectorDo, error) {
	resp := &FaqQueryResponse{}

	response, err := e.embeddingFaqDB.SetTimeout(time.Second * time.Duration(2)).
		R().
		SetBody(map[string]interface{}{
			"database":   database,
			"collection": collection,
			"search": map[string]interface{}{
				"limit":          1,
				"params":         map[string]int{"ef": 200},
				"embeddingItems": []string{query},
				"retrieveVector": false,
			},
		}).SetResult(resp).
		Post(e.conf.Embedding.DbFaq.Url + "/document/search")
	if err != nil {
		return nil, errors.Wrapf(err, "embeddingDBRepo: FaqQuery error, database=%s, collection=%s, query=%s", database, collection, query)
	}

	if response.StatusCode() != 200 || resp.Code != 0 {
		return nil, fmt.Errorf("embeddingDBRepo: FaqQuery status error, database=%s, collection=%s, query=%s, statusCode=%d, code=%d, msg=%s", database, collection, query, response.StatusCode(), resp.Code, resp.Msg)
	}
	e.log.WithContext(ctx).Infof("embeddingDBRepo: FaqQuery success, database=%s, collection=%s, query=%s, response=%+v", database, collection, query, resp)

	if len(resp.Documents) == 0 {
		return nil, nil
	}
	if len(resp.Documents[0]) == 0 {
		return nil, nil
	}

	// 相似度判断
	if resp.Documents[0][0].Score < cast.ToFloat64(e.conf.Embedding.DbFaq.FaqThreshold) {
		return nil, nil
	}

	var answer []string
	_ = json.Unmarshal([]byte(resp.Documents[0][0].Answer), &answer)

	doAnswer := ""
	if len(answer) > 0 {
		rand.Seed(time.Now().UnixNano())
		randomIndex := rand.Intn(len(answer))
		doAnswer = answer[randomIndex]
	}

	return &dto.FaqVectorDo{
		ID:        resp.Documents[0][0].ID,
		Answer:    resp.Documents[0][0].Answer,
		FaqAnswer: doAnswer,
		Question:  resp.Documents[0][0].Question,
		Score:     resp.Documents[0][0].Score,
		Text:      resp.Documents[0][0].Text,
	}, nil
}

// hotfix查询
func (e embeddingDBRepo) HotfixQuery(ctx context.Context, query string) (*dto.HotfixVectorDo, error) {
	database := e.conf.Embedding.DbHotfix.Database
	collection := e.conf.Embedding.DbHotfix.Collection

	resp := &dto.HotfixQueryResponse{}

	response, err := e.embeddingHotfixDB.SetTimeout(time.Second * time.Duration(5)).
		R().
		SetBody(map[string]interface{}{
			"database":   database,
			"collection": collection,
			"search": map[string]interface{}{
				"limit":          1,
				"params":         map[string]int{"ef": 200},
				"embeddingItems": []string{query},
				"retrieveVector": false,
			},
		}).SetResult(resp).
		Post(e.conf.Embedding.DbHotfix.Url + "/document/search")
	if err != nil {
		e.log.WithContext(ctx).Errorf("HotfixQuery embeddingDBRepo err: %+v", err)
		return nil, errors.Wrapf(err, "embeddingDBRepo: HotfixQuery error, database=%s, collection=%s, query=%s", database, collection, query)
	}

	if response.StatusCode() != 200 || resp.Code != 0 {
		e.log.WithContext(ctx).Errorf("HotfixQuery embeddingDBRepo response: %+v, resp.Code: %v", response, resp.Code)
		return nil, fmt.Errorf("embeddingDBRepo: HotfixQuery status error, database=%s, collection=%s, query=%s, statusCode=%d, code=%d, msg=%s", database, collection, query, response.StatusCode(), resp.Code, resp.Msg)
	}

	if len(resp.Documents) == 0 {
		return nil, nil
	}
	if len(resp.Documents[0]) == 0 {
		return nil, nil
	}

	document := resp.Documents[0][0]

	return &dto.HotfixVectorDo{
		ID:           document.ID,
		SnType:       document.SnType,
		HotfixType:   document.HotfixType,
		SnList:       document.SnList,
		Score:        document.Score,
		Text:         document.Text,
		Data:         document.Data,
		Function:     document.Function,
		Domain:       document.Domain,
		Intent:       document.Intent,
		Query:        document.Query,
		QaID:         document.QaID,
		Operate:      document.Operate,
		TtsShow:      document.TtsShow,
		TtsNorm:      document.TtsNorm,
		TextDataInfo: document.TextDataInfo,
		DeviceList:   document.DeviceList,
		AgentList:    document.AgentList,
		Url:          document.Url,
	}, nil
}

// NewEmbeddingRepo .
func NewEmbeddingRepo(data *Data, logger log.Logger, conf *conf.Data) biz.EmbeddingDBRepo {
	return &embeddingDBRepo{
		data:              data,
		log:               log.NewHelper(logger),
		conf:              conf,
		embeddingDB:       resty.New().SetHeader("Authorization", conf.Embedding.Tencent.Token),
		embeddingFaqDB:    resty.New().SetHeader("Authorization", conf.Embedding.DbFaq.Token),
		embeddingHotfixDB: resty.New().SetHeader("Authorization", conf.Embedding.DbHotfix.Token),
	}
}
