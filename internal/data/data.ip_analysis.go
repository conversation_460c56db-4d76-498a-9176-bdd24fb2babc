package data

import (
	"context"
	"fmt"
	"lui-api/internal/biz"
	"time"

	"github.com/redis/go-redis/v9"
)

type ipAnalysisRepo struct {
	data *Data
}

func NewIPAnalysisRepo(data *Data) biz.IPAnalysisRepo {
	return &ipAnalysisRepo{
		data: data,
	}
}

func (d *ipAnalysisRepo) GetIPAnalysisByDeviceID(ctx context.Context, deviceID string) *redis.StringCmd {
	return d.data.rdb.Get(ctx, fmt.Sprintf("lui:ip-analysis:%s", deviceID))
}

func (d *ipAnalysisRepo) SetIPAnalysisByDeviceID(ctx context.Context, deviceID string, value interface{}, expiration int) *redis.StatusCmd {
	return d.data.rdb.Set(ctx, fmt.Sprintf("lui:ip-analysis:%s", deviceID), value, time.Second*time.Duration(expiration))
}
