package common

import (
	"context"

	"github.com/pkg/errors"
)

const (
	TraceId             = "trace-id"
	RequestIp           = "request-ip"
	Authorization       = "Authorization"
	Platversionoriginal = "X-Genie-Platversionoriginal"
	XAppId              = "X-Genie-AppId"
	XDeviceId           = "X-Genie-DeviceId"
	XVersion            = "X-Genie-Version"
	XOSVersion          = "X-Genie-Osversion"
	XPlatForm           = "X-Genie-Platform"
	XNonce              = "X-Genie-Nonce"
	StartTime           = "startTime"

	XTalDeviceCode     = "X-Tal-Devicecode"
	XWithOutMultiModal = "X-WithOut-MultiModal"
)

func GetXWithOutMultiModal(ctx context.Context) bool {
	if value, ok := ctx.Value(XWithOutMultiModal).(int); ok {
		if value == 1 {
			return true
		}
	}
	return false
}

type LuiContentCategory int

const (
	Subject LuiContentCategory = iota + 1
	Cultivation
	ExamPaper
	PointReading
	Dictation
	Recitation
	HomeworkType
	DictateType
	JzxV15Question
)

func (lcc LuiContentCategory) Collection() (string, error) {
	var collection string
	switch lcc {
	case Subject:
		collection = "subject"
	case Cultivation:
		collection = "cultivation"
	case ExamPaper:
		collection = "examPaper"
	default:
		return "", errors.New("invalid category")
	}
	return collection + CurrentEnv.EmbeddingTableSuffix(), nil
}

func (lcc LuiContentCategory) Int() int {
	return int(lcc)
}

type Skill string

const (
	// SearchCourse 视频课程
	SearchCourse Skill = "search_course"
	// ContinueLearnSubject 继续学学科课程
	ContinueLearnSubject Skill = "continue_learn_course"
	// ContinueLearnQuality 继续学素养课程
	ContinueLearnQuality Skill = "continue_learn_quality"
	// SearchQuality 找素养课程
	SearchQuality Skill = "search_quality"
	// PaperSubjectConfirm 找试卷二次确认
	PaperSubjectConfirm Skill = "paper_subject_confirm"
	// SearchPaper 查试卷
	SearchPaper Skill = "search_paper"
	// ListenWrite 听写课文
	ListenWrite Skill = "listen_write"
	// ClickRead 点读课文
	ClickRead Skill = "click_read"
	// Recite 背诵课文
	Recite Skill = "recite"
	// Poem 诗词
	Poem Skill = "poem"
	// Baike 百科
	Baike Skill = "baike"
	// CheckTypeConfirm 作业类型
	CheckTypeConfirm Skill = "check_type_confirm"
	// CheckDictateTypeConfirm 听写类型
	CheckDictateTypeConfirm Skill = "dictate_type_confirm"
	// ConfirmInvalid 二次确认无效
	ConfirmInvalid Skill = "confirm_invalid"
	// JzxV15QuestionSkill 精准学V1.5知识点找题目
	JzxV15QuestionSkill Skill = "search_exercise"
	// SearchCourseBook 找课本
	SearchCourseBook     Skill = "search_course_book"
	SkillChat            Skill = "chat"
	SkillSearchQuestion  Skill = "search_question"
	SkillTutorQA         Skill = "tutor_qa"
	SearchBook           Skill = "book"
	SkillNewspaper       Skill = "newspaper"
	SkillDocumentary     Skill = "documentary"
	SkillKetTutorChat    Skill = "ket_tutor_chat"
	SkillKetTutorBaike   Skill = "ket_tutor_baike"
	SkillSee2Say         Skill = "see_2_say"
	SkillMixedModelBaike Skill = "mixed_model_baike"
	SkillDeepseek        Skill = "deepseek"
	SkillExercisesTool   Skill = "exercises_tool"
	SkillExamQuestion    Skill = "exam_question"
	SkillReadQuestion    Skill = "question_understanding"
	SkillWorkshop        Skill = "workshop_tool"
	SkillFilters         Skill = "filters"
)

func (skill Skill) ToString() string {
	return string(skill)
}

func (skill Skill) Trans2Category() {

}

type ResourceType string

const (
	//学科课程

	Course          ResourceType = "course"
	JzxCourse       ResourceType = "jzx_course"
	Jzx             ResourceType = "jzx"
	Unit            ResourceType = "unit"
	Plan            ResourceType = "plan"
	Lesson          ResourceType = "lesson"
	Knowledge       ResourceType = "knowledge"
	SubKnowledge    ResourceType = "sub_knowledge"
	JzxV15          ResourceType = "jzx_v15"
	JzxV15Course    ResourceType = "jzx_v15_course"
	RAZ             ResourceType = "raz"
	JzxV15Knowledge ResourceType = "jzx_v15_knowledge"
	JzxV15Plan      ResourceType = "jzx_v15_plan"
	JzxV15Kd        ResourceType = "jzx_v15_kd"
	JzxV15Lesson    ResourceType = "jzx_v15_lesson"
	// 素养课程

	Album      ResourceType = "album"
	Attainment ResourceType = "attainment"

	// 试卷

	Paper      ResourceType = "paper"
	SchoolName ResourceType = "school_name"
)

func (resourceType ResourceType) ToString() string {
	return string(resourceType)
}

type CourseCardType string

const (
	CourseCard          CourseCardType = "course"
	UnitCard            CourseCardType = "unit"
	PlanCard            CourseCardType = "plan"
	LessonCard          CourseCardType = "lesson"
	KnowledgeCourseCard CourseCardType = "knowledge_point"

	AttainmentAlbumCard CourseCardType = "attainment_album"
	AttainmentCard      CourseCardType = "attainment"

	PaperCard CourseCardType = "paper"

	SearchExercise CourseCardType = "search_exercise"

	BookCard        CourseCardType = "search_book"
	NewspaperCard   CourseCardType = "newspaper"
	DocumentaryCard CourseCardType = "documentary"
)

func (courseCardType CourseCardType) ToString() string {
	return string(courseCardType)
}

const (
	// TtsTpl111 已打开《【资源名称】》，即将开始播放。
	TtsTpl111 = "已打开《%s》，即将开始播放。"
	// TtsTpl112 已打开《【资源名称】》。
	TtsTpl112 = "已打开《%s》。"
	// TtsTpl113 已打开“【槽位】+【资源类型】”。
	TtsTpl113 = "已打开“%s%s”。"
	// TtsTpl121 找到以下“【槽位list】”资源。
	TtsTpl121 = "找到以下“%s”资源。"
	// TtsTpl131 未找到相关资源，再多给我一些提示吧~
	TtsTpl131 = "未找到相关资源，再多给我一些提示吧~"
	// TtsTpl132 未找到相关资源，已打开《【槽位】+【资源类型】》。
	TtsTpl132 = "未找到相关资源，已打开“%s%s”。"
	// TtsTpl211 已打开上次学到的【资源名称】，3 秒后开始播放。
	TtsTpl211 = "已打开上次学到的《%s》，即将开始播放。"
	// TtsTpl221 找到以下学过的“【槽位list】”资源。
	TtsTpl221 = "找到以下学过的“%s”资源。"
	// TtsTpl231 未找到相关资源，已打开《【槽位】+【资源类型】》。
	TtsTpl231 = "未找到相关资源，已打开“%s%s”。"
	// TtsTpl311 哪个【槽位】呢？
	TtsTpl311 = "哪个%s呢？"
	// TtsTpl411 已打开《【资源名称】》【APP名称】。
	TtsTpl411 = "已打开《%s》%s。"
	// TtsTpl421 找到以下“【槽位list】”资源。
	TtsTpl421 = "找到以下资源。"
	// TtsTpl431 未找到相关资源，再多给我一些提示吧~
	TtsTpl431                 = "未找到相关资源，再多给我一些提示吧~"
	TtsTplIntercept           = "本学段无相关资源，请加上合适的年级后再问我吧~"
	TtsTplInterceptCnhtenment = "暂不支持查找启蒙资源，敬请期待~"
	// 混排文案
	TtsTplT121           = "找到以下资源。"
	TtsInterceptApp      = "%s没有%s哦~"
	TtsInterceptAppTmp   = "没有该应用哦~"
	TtsTplTAppType       = "请告诉我作业类型。"
	TtsTplTDictateType   = "请告诉我听写类型。"
	TtsTPLConfirmInvalid = "我还不会这个技能，我会像你一样努力学习哦~"
	TTSDefault           = "小思不明白你的意思，换个方式问我吧。"
	TTSBANXUENEICE       = "非常遗憾，我现在还不支持作业模式，希望不久后我能帮到你。"
	TTSNullSubject       = "小思未找到相关课程，已为你打开全部课程资源~"
	TTSNullPaper         = "哎呀，这个试卷我没找到哇，不过看看这些，也许有更好的等你选择～"
	TTSNullDianDu        = "很抱歉，小思没找到相关书籍。"
	TTSNullJiXuXue       = "小思未找到学习记录，已为你打开全部课程资源~"
	TTSBZiXiNEICE        = "功能内测中，敬请期待哦～"
	TTSTouchScreen       = "小思还在学习中，后续将逐渐支持屏幕点击～"
	TTSUninstall         = "小思还在学习中，长按应用可卸载对应应用～"
	TTSHuYanMoShi        = "可在护眼模式页面右下角开启护眼模式～"
)

const StudyLogCourse = "study_log_course"
const StudyLogCultivation = "study_log_cultivation"
const ToolsDictation = "dictation"
const ToolsRecitation = "recitation"

const ContentResourceConfKey = "resource_conf_key"
const ContentResourceConfPad2Key = "resource_conf_key_pad2"

const (
	ToastDuration = 1
)

const BaiKeKnowledge = "baike.knowledge"
const CnDictationAppName = "字词听写"
const EnDictationAppName = "课文听写"
const RecitationAppName = "课文背诵"
const PoemShowTypeAuthor = "author"
const PaiVersionFlag = 231123
const SearchExerciseVersionFlag = 240125
const BizTypeCompanionLearn = "com.tal.pad.companionlearning"
const BizTypeHomeWork = "com.tal.pad.homeworkmode"
const BizTypeVoice = "com.tal.pad.voice"
const BizTypeAiTutor = "com.tal.pad.ai_tutor"
const BizTypeTopicDialogue = "com.tal.pad.lui.topic_dialogue"

const BizTypeAiTutorLearn = "com.tal.pad.ai_tutor.learn"
const BizTypeAiTutorNormal = "com.tal.pad.ai_tutor.normal"
const BizTypeWorkshop = "com.tal.pad.lui.laboratory"
const AtmsExplainBizType = "com.tal.pad.lui.teach_question"

const (
	PlatVersionOriginalS = "s"
	PlatVersionOriginalT = "TALIH-PD3U" //T系列东山U
	PlatVersionOriginalP = "p"
)

type SceneCode int

const (
	// SceneXiaoSi 小思默认场景
	SceneXiaoSi SceneCode = 0
	// SceneCompanionLearn 作业模式
	SceneCompanionLearn SceneCode = 1
	// SceneAiTutor AITutor
	SceneAiTutor  SceneCode = 2
	SceneHomeWork SceneCode = 3
)

const SystemTongBu = 5

const SystemJZX = 2
const SystemJZXV15 = 23
const SystemSuYang = 100
const SystemSiWeiTuoZhan = 8
const SystemZuoWenJingJiang = 11
const SystemZhuanXiangXueXi = 9
const SystemNengLiTiSheng = 7

const (
	HotfixIntentAPP                  = "APP操作"
	HotfixIntentAPPOpenClose         = "应用打开关闭"
	HotfixIntentAPPDownloadUninstall = "应用下载卸载"
	HotfixIntentSearchCourse         = "找课程"
	HotfixIntentSearchQuality        = "找素养"

	HotfixDomainStudyResources  = "学习资源"
	HotfixDomainAppOperation    = "应用控制"
	HotfixDomainSystemOperation = "系统控制"
	HotfixDomainStudyTools      = "学习工具"

	// HotfixFunction
	HotfixFunctionPoemAuthor   = "poet_author_info"                        // 查诗人信息
	HotfixFunctionPoemContent  = "poetic_lines"                            // 查某首/句诗
	HotfixFunctionPoemAnalysis = "poet_interpretation_appreciation_source" // 查诗歌释义/赏析/出处
	HotfixFunctionTranslate    = "translate_sentence"                      // 翻译句子

	HotfixSkillSearchCourse  = "search_course"
	HotfixSkillSearchQuality = "search_quality"
)

const (
	XPad2AppId                 = "200038,200011"
	CheckIsLLMVersion          = 240125
	CheckIsFullViewVersion     = 240512
	CheckLiteIsFullViewVersion = 240721
	LlmChatIntent              = "chat"
	SuggestExercisesTool       = "suggest_exercises"
)
const LegalCheckRiskPolitics = "politics"

const (
	Pad2LiteSource = "pad2_lite"
)

const (
	XsdhFuncCallSnList         = "xsdh_function_call_sn_list"
	XsdhXiaosi20AgentSnList    = "xsdh_xiaosi2.0_agent_sn_list"
	XsdhOralAppSnList          = "xsdh_oral_app_sn_list"
	XsdhMathExplainSnList      = "xsdh_math_explain_sn_list"
	XszixishiSnList            = "xiaosi_zixishi_sn_list"
	XsdhXiaosi2ChatPlusSnList  = "xsdh_xiaosi2_chat_plus_sn_list"
	XsdhXiaosi2BaiKePlusSnList = "xsdh_xiaosi2_baike_plus_sn_list"
	XsxiezuoyindaoSnList       = "xiaosi_xiezuoyindao_sn_list"
	XsMrIntentSnList           = "xsdh_mr_intent_sn_list" // 多轮意图理解
	XsYueDuLiJieSnList         = "xiaosi_yuedulijie_sn_list"
	XsAtEnglishWordSnList      = "xiaosi_at_english_word_sn_list"
	XsUnclearIntentUpgrade     = "xiaosi_unclear_intent_upgrade"
	XsDeepseek                 = "xiaosi_deepseek"
	XsExplainQuestionAgentList = "explain_question_agent_list"
)

const (
	IntentChat          = "闲聊"
	IntentBaiKeWenDa    = "百科问答"
	IntentChaTi         = "查题"
	IntentKnowledge     = "查学科知识"
	IntentSearchStory   = "找故事"
	IntentSearchPoem    = "查诗歌"
	IntentTranslation   = "翻译句子"
	IntentClarification = "没听懂"
)

const (
	AiAgentXiaoSi2              = "DialogueAgent"
	AiAgentChaTi                = "MathExplainAgent"
	AiAgentKetTutorWiki         = "KetTutorWikiAgent"
	AiAgentKetTutorQA           = "AIKETQA"
	AiAgentDeepseek             = "DeepseekAgent"
	AiAgentWorkshop             = "LuiGameProgramSSE"
	AiAgentMixedModal           = "MultiModalBaikeAgent"
	AiAgentDeepseekExercises    = "DeepseekExercisesAgent"
	AiAgentExplainQuestionTopic = "QuestionExplainDialogue"
	AiExamQuestion              = "ExamQuestion"
	AiReadQuestion              = "ReadQuestion"
	AiAgentExplainSocrates      = "XiaosiExplainSocrates"
	AiAgentExplainScripts       = "XiaosiExplainScripts"
	AiAgentFilters           = "FiltersAgent"
)

const (
	AtmsExplainTypeSocrates = 1
	AtmsExplainTypeScripts  = 2
)

const (
	TTSFollowUp        = "follow_up"
	TTSFollowUpSubject = "你想要哪个学科呢？"
	TTSFollowUpVersion = "你想要哪个版本呢？"
	//TTSNextStepGuidePre = "还可以继续让我帮你"
	TTSNextStepGuide = "如需更换学科、年级等，可以继续和我说哦！"
	TTSOtherIntent   = "咦~你要开启新的对话嘛，请再对小思说一遍吧。"
)

const (
	XinRuiAppIdKey      = "xinruiAPPId"
	XinRuiDeviceCodeVal = "TALIH-PDE1"
)

const (
	MixedModalVersion = 241216
	PlatformXiaoSi3   = "3.0"
)

type MixedModal string

const (
	MixedModalUnderScreen         MixedModal = "under_screen" // 屏下识别
	MixedModalOcrTypeDefaultTts              = "请指出来要查询的内容，我会帮你解答哦～"
	MixedModalOcrTypeNoSupportTts            = "小思还在学习中，暂不支持该功能哦～"
)

type MixedModalOcrType int

const (
	MixedModalOcrTypeDefault   MixedModalOcrType = iota
	MixedModalOcrTypeChar                        // 字
	MixedModalOcrTypeSentence                    // 句子
	MixedModalOcrTypeParagraph                   // 段落
	MixedModalOcrTypeQuestion                    // 题目
	MixedModalOcrTypePage                        // 整页
	MixedModalOcrTypePageBaike                   // 整页百科
	MixedModalOcrTypeWord                        // 词
)

func MixedModalOcrTypeToInt(slotType string) MixedModalOcrType {
	switch slotType {
	case "char":
		return MixedModalOcrTypeChar
	case "word":
		return MixedModalOcrTypeWord
	case "sentence":
		return MixedModalOcrTypeSentence
	case "paragraph":
		return MixedModalOcrTypeParagraph
	case "question":
		return MixedModalOcrTypeQuestion
	case "page":
		return MixedModalOcrTypePage
	default:
		return MixedModalOcrTypeSentence
	}
}
