package middleware

import (
	"context"
	"encoding/json"
	"net/http"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/gorilla/mux"
	"github.com/spf13/cast"
	"go.opentelemetry.io/otel"
	"lui-api/internal/common"
	"lui-api/internal/pkg/custom_context"
)

func AddTraceToRequest() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			var traceId string
			if header, ok := transport.FromServerContext(ctx); ok {
				if header.RequestHeader().Get("X-Genie-TraceId") != "" {
					traceId = header.RequestHeader().Get("X-Genie-TraceId")
				} else if trace, ok := tracing.TraceID()(ctx).(string); ok {
					traceId = trace
				}
				header.RequestHeader().Set(common.TraceId, traceId)
				reqData, _ := json.Marshal(req)
				header.RequestHeader().Set("req", string(reqData))
				header.RequestHeader().Set("time", cast.ToString(time.Now().UnixMilli()-10))

				ctx = HeaderContext(ctx, header.RequestHeader())
			}
			return handler(ctx, req)
		}
	}
}

func AddTraceToRequestMux() mux.MiddlewareFunc {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			var traceId string
			// 1. 从header中获取traceId
			if header, ok := transport.FromServerContext(ctx); ok {
				if header.RequestHeader().Get("X-Genie-TraceId") != "" {
					traceId = header.RequestHeader().Get("X-Genie-TraceId")
				} else if trace, ok := tracing.TraceID()(ctx).(string); ok && trace != "" {
					traceId = trace
				}
				header.RequestHeader().Set(common.TraceId, traceId)
				header.RequestHeader().Set("time", cast.ToString(time.Now().UnixMilli()-10))
				ctx = HeaderContext(ctx, header.RequestHeader())
				ctx = custom_context.NewTraceContext(ctx, traceId)
			}
			// 2. 如果header中没有traceId, 则生成一个新的traceId
			if traceId == "" {
				tracer := otel.Tracer("middleware")
				ctx, _ = tracer.Start(ctx, "AddTraceToRequestMux")
			}
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

func HeaderContext(ctx context.Context, header transport.Header) context.Context {

	if header == nil {
		return ctx
	}

	appId := header.Get(common.XAppId)
	ctx = custom_context.SetXAppId(ctx, appId)
	clientIP := header.Get("X-Forwarded-For")
	if clientIP == "" {
		clientIP = header.Get("X-Real-IP")
	} else {
		ips := strings.Split(clientIP, ",")
		clientIP = strings.TrimSpace(ips[0])
	}
	ctx = custom_context.SetRequestIp(ctx, clientIP)
	nonce := header.Get(common.XNonce)
	ctx = custom_context.SetXNonce(ctx, nonce)
	deviceId := header.Get(common.XDeviceId)
	ctx = custom_context.SetXDeviceId(ctx, deviceId)
	platform := header.Get(common.XPlatForm)
	ctx = custom_context.SetXPlatform(ctx, platform)
	version := header.Get(common.XVersion)
	ctx = custom_context.SetXVersion(ctx, version)
	osVersion := header.Get(common.XOSVersion)
	ctx = custom_context.SetXOSVersion(ctx, osVersion)
	authorization := header.Get(common.Authorization)
	ctx = custom_context.SetAuthorization(ctx, authorization)
	traceId := header.Get(common.TraceId)
	ctx = custom_context.SetTraceId(ctx, traceId)
	platVersion := header.Get(common.Platversionoriginal)
	ctx = custom_context.SetVersion(ctx, platVersion)
	ctx = custom_context.SetStartTime(ctx)
	return ctx
}
