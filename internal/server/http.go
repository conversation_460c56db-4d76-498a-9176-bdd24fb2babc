package server

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/middleware/validate"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/gorilla/mux"
	v1 "lui-api/api/helloworld/v1"
	luiV1 "lui-api/api/lui/v1"
	skillV1 "lui-api/api/skill/v1"
	"lui-api/internal/conf"
	"lui-api/internal/server/middleware"
	"lui-api/internal/service"
)

var errorHandle *conf.ErrorHandle

// NewHTTPServer new a HTTP server.
func NewHTTPServer(c *conf.Server, errorHandler *conf.ErrorHandle, greeter *service.GreeterService, lui *service.LuiService, logger log.Logger, uCenter *service.UCenterService, skill *service.SkillService) *http.Server {
	errorHandle = errorHandler

	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			tracing.Server(),
			middleware.AddTraceToRequest(),
			logging.Server(logger),
			validate.Validator(),
			metrics.Server(),
			metadata.Server(),
		),
	}
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	}
	opts = append(opts, http.ErrorEncoder(MyErrorEncoder))
	opts = append(opts, http.ResponseEncoder(MyResponseEncoder))
	srv := http.NewServer(opts...)

	router := mux.NewRouter()
	router.HandleFunc("/v1/lui/llm-faq", lui.SseLuiFaq)
	srv.HandlePrefix("/v1/lui/llm-faq", router)

	routerAgent := mux.NewRouter()
	routerAgent.Use(middleware.AddTraceToRequestMux())
	routerAgent.HandleFunc("/v1/lui/agent/llm-faq-sse", lui.SseLuiFaq)
	routerAgent.HandleFunc("/v1/lui/agent/math-explain-sse", lui.MathExplainAgent)
	routerAgent.HandleFunc("/v1/lui/agent/dialogue-sse", lui.DialogueAgentSse)
	routerAgent.HandleFunc("/v1/lui/agent/deepseek-sse", lui.DeepSeekSse)
	routerAgent.HandleFunc("/v1/lui/agent/deepseek-exercises-sse", lui.DeepSeekExercisesSse)
	srv.HandlePrefix("/v1/lui/agent", routerAgent)

	v1.RegisterGreeterHTTPServer(srv, greeter)
	luiV1.RegisterLuiContentApiHTTPServer(srv, lui)
	luiV1.RegisterUCenterApiHTTPServer(srv, uCenter)
	skillV1.RegisterSkillApiHTTPServer(srv, skill)
	return srv
}
