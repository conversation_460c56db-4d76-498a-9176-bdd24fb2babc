syntax = "proto3";
package kratos.api;

option go_package = "lui-api/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
  ErrorHandle error = 3;
  Log log = 4;
  Third third = 5;
  Sign sign = 6;
  WhiteOsVersion white_os_version = 8;
  Api api = 9;
  Skill skill = 20;
  SkillDetail skill_detail = 21;
  SubjectConfig subject_config = 22;

  map<string, Skills> skills = 25;
  map<string, VoiceInteractionCommands> voice_interaction_tips = 26;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
}

message Data {
  message Database {
    string driver = 1;
    string source = 2;
  }
  message Redis {
    string network = 1;
    string addr = 2;
    string password = 3;
    int32 db = 4;
    google.protobuf.Duration dial_timeout = 5;
    google.protobuf.Duration read_timeout = 6;
    google.protobuf.Duration write_timeout = 7;
  }
  message Embedding {
    message DB {
      string database = 1;
    }
    message Tencent {
      string url = 1;
      string token = 2;
    }
    message DbFaq {
      string database = 1;
      string collection = 2;
      bool faq_enable = 3;
      float faq_threshold = 4;
      string url = 5;
      string token = 6;
    }

    message DbHotfix {
      string database = 1;
      string collection = 2;
      bool enable = 3;
      float threshold = 4;
      string url = 5;
      string token = 6;
      float faq_threshold = 7;
    }

    DB db = 1;
    Tencent tencent = 2;
    DbFaq db_faq = 3;
    DbHotfix db_hotfix = 4;
  }

  message Kafka {
    string topic = 1;
    string group_id = 2;
    string bootstrap_servers = 3;
    string security_protocol = 4;
    string ssl_ca_location = 5;
    string sasl_mechanism = 6;
    string sasl_username = 7;
    string sasl_password = 8;
    int32 auto_ack = 9;
    int32 max_message_bytes = 10;
  }

  Database database = 1;
  Redis redis = 2;
  Embedding embedding = 3;
  Kafka query_kafka = 4;
  repeated Kafka query_kafka_list = 5;
}

message ErrorHandle {
  message ErrorMessages {
    repeated ErrorMessage error_messages = 1;
  }
  message ErrorMessage {
    string error_reason = 1;
    string message = 2;
  }
  map<string, ErrorMessages> handle = 1;
  string default = 2;
}

message Log {
  string filename = 1;
  int32 max_size = 2;
  int32 max_backup = 3;
  int32 max_age = 4;
  bool compress = 5;
}

message Third {
  message IPAnalysis {
    string url = 1;
    int32 retry = 2;
    int32 timeout = 3;
  }

  message LlmPrompt {
    bool enable = 1;
    repeated string sn_list = 2;
    string baike_tmp_id = 3;
    string chat_tmp_id = 4;
    repeated string legal_check_tts = 5;
    string app_id = 6;
    repeated string baike_default_tts = 7;
    string sug_tmp_id = 8;
    string tails_tmp_id = 9;
    bool full_view_enable = 10;
    string full_view_baike_tmp_id = 11;
    string full_view_chat_tmp_id = 12;
    string filterChatPunctuation = 13;
    string xs2_chat_plus_tmp_id = 14;
    string xs2_baike_plus_tmp_id = 15;
    string xs_deepseek_tmp_id = 16;
    string xs_deepseek_exercises_tmp_id = 17;
  }

  message Llm{
    repeated string intent = 1;
    int32 llm_sug_switch = 2;
  }

  string proxy_url = 1;
  string nlu_origin = 2;
  string nlu4lui_origin = 3;
  LegalCheck legal_check = 4;
  LlmPrompt llm_prompt = 5;

  string xPad1AppId = 14;
  ThirdContentUrl xPad1 = 6;
  string xPad2AppId = 7;
  ThirdContentUrl xPad2 = 8;
  string nlu4lui_list_origin = 9;
  IPAnalysis ip_analysis = 10;
  repeated string single_word_tts = 11;
  repeated string nonsense_tts = 12;

  WhiteExamJudge white_exam_judge = 13;
  string lui_controller_url = 15;
  string xs_skill_hub_url = 16;
  string xPodAppId = 17;
  string xPad2LiteAppId = 18;
  string xiaosi_rag_url = 19;
  map<string, string> mobby_scheme = 20;
  string xPad1V2AppId = 22;
  NlpFunc nlp_func = 21;
  string xs_contact_url = 23;
  string baike_rag_url = 24;
  Llm llm = 25;
  string dialogue_profile_url = 26;
  string memory_service_url = 27;
  string ocr_origin = 28;
  string math_gpt_ask_classify_origin = 29;
  string cloud_control_host = 30;
  MathAgent math_agent = 31;
  string xPad2AppId_qijian = 32;
  string all_skills_web_url = 33;
  repeated string polish_tts = 34;
  string update_profile_subject_version_url = 35;
  map<string, string> app_id_map = 36;
  string xue_lian_ji_all_skills_web_url = 37;
  string readbook_scheme_url = 38;
  string readbook_note_scheme_url = 39;
  string readbook_default_cover = 40;
  repeated string tail_tts = 41;
  string chat_context_feed_url = 42;
  repeated string home_work_doudi = 43;
  Filters filters = 44;
}

message Filters{
  repeated string filters_intent_white_list = 1;
  repeated string filters_role_white_list = 2;
  repeated string filters_under_tts_xiaozha = 3;
  repeated string filters_under_tts_xiaoyi = 4;
  map<string, string> filters_name_map_videoid = 5;
  repeated string filters_intent_white_list_to_skill = 6;
}

message MathAgent {
  string pai_image_url = 1;
  string math_gpt_tmp_id = 2;
  string math_agent_tmp_id = 3;
  string suishiwen_h5_url = 4;
  string downward_msg_url = 5;
  string auto_math_url = 6;
  string auto_math_ak = 7;
  string auto_math_sk = 8;
}

message LegalCheck {
  bool check_enable = 1;
  string service_id = 2;
  string host = 3;
  repeated string check_intent = 4;
}

message ThirdContentUrl {
  string resource_ids_url = 4;
  string resource_slot_url = 5;
  string resource_choice_url = 6;
  string token_parse_url = 8;
  string user_profile_url = 9;
  string tools_url = 10;
  string study_log_url = 11;
  string resource_conf_url = 12;
  string content_app_id = 13;
  string tools_user_version_url = 14;
}

message Sign {
  map<string, string> sign_secrets = 1;
}

message WhiteOsVersion {
  int32 status = 1;
  string list = 2;
  string sn_list = 3;
  string zx_sn_list = 4;
}

message WhiteExamJudge {
  int32 status = 1;
  string sn_list = 2;
}

message NlpFunc {
  string controller = 1;
  int32 controller_timeout = 2;
  string function_call = 3;
  int32 function_call_timeout = 4;
  map<string, DouDiIntentMap> dou_di_map = 5;
  int32  query_nlp_switch = 6;
  string query_nlp_v2 = 7;
  string dialog_api = 8;
  int32 dialog_api_timeout = 9;
}

message DouDiIntentMap {
  map<string, DouDiList> dou_di_intent_map = 1;
}

message DouDiList {
  repeated string items = 1;
}

message Skill {
  message SkillTag {
    message SkillData {
      string tag_id = 1;
      string skill_id = 2;
      string skill_name = 3;
      string skill_icon = 4;
    }

    string tag_id = 1;
    string tag_name = 2;
    repeated SkillData skill_data = 3;
  }

  repeated SkillTag list = 1;
}

message SkillDetail {
  message SkillDetailList {
    message SkillDetailInfo {
      message CommandData {
        string skill_tag_id = 1;
        string skill_tag = 2;
        string command_id = 3;
        string command = 4;
      }

      string skill_id = 1;
      string skill_name = 2;
      int32 status = 3;
      repeated CommandData command_data = 4;
    }

    repeated SkillDetailInfo detail_list = 1;
  }

  map<string, SkillDetailList> skill_map = 1;
}

message Api {
  message TargetApi {
    string url = 1;
    string name = 2;
    string function = 3;
    string version = 4;
    int32  timeout = 5;
    int32  retry = 6;
    string service = 7;
  }
  map<string, TargetApi> mapping = 1;
}
message SubjectConfig {
  map<int32, string> subject_font_color = 1;
  map<int32, string> subject_jzx_cover = 2;
  string jzx_v15_scheme = 4;
  string jzx_v15_questions_cover = 5;
  map<int32, string> subject_overlay_color = 6;
}

message Skills {
  Skill skill = 1;
  SkillDetail skill_detail = 2;
}

message VoiceInteractionCommands {
  message VoiceCommand {
    string type = 1;
    repeated string query = 2;
    string name = 3;
    string icon = 4;
  }
  string more_image = 2;
  repeated VoiceCommand commands = 1;

}