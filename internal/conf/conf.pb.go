// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v5.29.3
// source: internal/conf/conf.proto

package conf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Bootstrap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Server               *Server                              `protobuf:"bytes,1,opt,name=server,proto3" json:"server,omitempty"`
	Data                 *Data                                `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Error                *ErrorHandle                         `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Log                  *Log                                 `protobuf:"bytes,4,opt,name=log,proto3" json:"log,omitempty"`
	Third                *Third                               `protobuf:"bytes,5,opt,name=third,proto3" json:"third,omitempty"`
	Sign                 *Sign                                `protobuf:"bytes,6,opt,name=sign,proto3" json:"sign,omitempty"`
	WhiteOsVersion       *WhiteOsVersion                      `protobuf:"bytes,8,opt,name=white_os_version,json=whiteOsVersion,proto3" json:"white_os_version,omitempty"`
	Api                  *Api                                 `protobuf:"bytes,9,opt,name=api,proto3" json:"api,omitempty"`
	Skill                *Skill                               `protobuf:"bytes,20,opt,name=skill,proto3" json:"skill,omitempty"`
	SkillDetail          *SkillDetail                         `protobuf:"bytes,21,opt,name=skill_detail,json=skillDetail,proto3" json:"skill_detail,omitempty"`
	SubjectConfig        *SubjectConfig                       `protobuf:"bytes,22,opt,name=subject_config,json=subjectConfig,proto3" json:"subject_config,omitempty"`
	Skills               map[string]*Skills                   `protobuf:"bytes,25,rep,name=skills,proto3" json:"skills,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	VoiceInteractionTips map[string]*VoiceInteractionCommands `protobuf:"bytes,26,rep,name=voice_interaction_tips,json=voiceInteractionTips,proto3" json:"voice_interaction_tips,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Bootstrap) Reset() {
	*x = Bootstrap{}
	mi := &file_internal_conf_conf_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Bootstrap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bootstrap) ProtoMessage() {}

func (x *Bootstrap) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bootstrap.ProtoReflect.Descriptor instead.
func (*Bootstrap) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{0}
}

func (x *Bootstrap) GetServer() *Server {
	if x != nil {
		return x.Server
	}
	return nil
}

func (x *Bootstrap) GetData() *Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Bootstrap) GetError() *ErrorHandle {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *Bootstrap) GetLog() *Log {
	if x != nil {
		return x.Log
	}
	return nil
}

func (x *Bootstrap) GetThird() *Third {
	if x != nil {
		return x.Third
	}
	return nil
}

func (x *Bootstrap) GetSign() *Sign {
	if x != nil {
		return x.Sign
	}
	return nil
}

func (x *Bootstrap) GetWhiteOsVersion() *WhiteOsVersion {
	if x != nil {
		return x.WhiteOsVersion
	}
	return nil
}

func (x *Bootstrap) GetApi() *Api {
	if x != nil {
		return x.Api
	}
	return nil
}

func (x *Bootstrap) GetSkill() *Skill {
	if x != nil {
		return x.Skill
	}
	return nil
}

func (x *Bootstrap) GetSkillDetail() *SkillDetail {
	if x != nil {
		return x.SkillDetail
	}
	return nil
}

func (x *Bootstrap) GetSubjectConfig() *SubjectConfig {
	if x != nil {
		return x.SubjectConfig
	}
	return nil
}

func (x *Bootstrap) GetSkills() map[string]*Skills {
	if x != nil {
		return x.Skills
	}
	return nil
}

func (x *Bootstrap) GetVoiceInteractionTips() map[string]*VoiceInteractionCommands {
	if x != nil {
		return x.VoiceInteractionTips
	}
	return nil
}

type Server struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Http *Server_HTTP `protobuf:"bytes,1,opt,name=http,proto3" json:"http,omitempty"`
	Grpc *Server_GRPC `protobuf:"bytes,2,opt,name=grpc,proto3" json:"grpc,omitempty"`
}

func (x *Server) Reset() {
	*x = Server{}
	mi := &file_internal_conf_conf_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server) ProtoMessage() {}

func (x *Server) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server.ProtoReflect.Descriptor instead.
func (*Server) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1}
}

func (x *Server) GetHttp() *Server_HTTP {
	if x != nil {
		return x.Http
	}
	return nil
}

func (x *Server) GetGrpc() *Server_GRPC {
	if x != nil {
		return x.Grpc
	}
	return nil
}

type Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Database       *Data_Database  `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
	Redis          *Data_Redis     `protobuf:"bytes,2,opt,name=redis,proto3" json:"redis,omitempty"`
	Embedding      *Data_Embedding `protobuf:"bytes,3,opt,name=embedding,proto3" json:"embedding,omitempty"`
	QueryKafka     *Data_Kafka     `protobuf:"bytes,4,opt,name=query_kafka,json=queryKafka,proto3" json:"query_kafka,omitempty"`
	QueryKafkaList []*Data_Kafka   `protobuf:"bytes,5,rep,name=query_kafka_list,json=queryKafkaList,proto3" json:"query_kafka_list,omitempty"`
}

func (x *Data) Reset() {
	*x = Data{}
	mi := &file_internal_conf_conf_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data) ProtoMessage() {}

func (x *Data) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data.ProtoReflect.Descriptor instead.
func (*Data) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2}
}

func (x *Data) GetDatabase() *Data_Database {
	if x != nil {
		return x.Database
	}
	return nil
}

func (x *Data) GetRedis() *Data_Redis {
	if x != nil {
		return x.Redis
	}
	return nil
}

func (x *Data) GetEmbedding() *Data_Embedding {
	if x != nil {
		return x.Embedding
	}
	return nil
}

func (x *Data) GetQueryKafka() *Data_Kafka {
	if x != nil {
		return x.QueryKafka
	}
	return nil
}

func (x *Data) GetQueryKafkaList() []*Data_Kafka {
	if x != nil {
		return x.QueryKafkaList
	}
	return nil
}

type ErrorHandle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Handle  map[string]*ErrorHandle_ErrorMessages `protobuf:"bytes,1,rep,name=handle,proto3" json:"handle,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Default string                                `protobuf:"bytes,2,opt,name=default,proto3" json:"default,omitempty"`
}

func (x *ErrorHandle) Reset() {
	*x = ErrorHandle{}
	mi := &file_internal_conf_conf_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorHandle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorHandle) ProtoMessage() {}

func (x *ErrorHandle) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorHandle.ProtoReflect.Descriptor instead.
func (*ErrorHandle) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{3}
}

func (x *ErrorHandle) GetHandle() map[string]*ErrorHandle_ErrorMessages {
	if x != nil {
		return x.Handle
	}
	return nil
}

func (x *ErrorHandle) GetDefault() string {
	if x != nil {
		return x.Default
	}
	return ""
}

type Log struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filename  string `protobuf:"bytes,1,opt,name=filename,proto3" json:"filename,omitempty"`
	MaxSize   int32  `protobuf:"varint,2,opt,name=max_size,json=maxSize,proto3" json:"max_size,omitempty"`
	MaxBackup int32  `protobuf:"varint,3,opt,name=max_backup,json=maxBackup,proto3" json:"max_backup,omitempty"`
	MaxAge    int32  `protobuf:"varint,4,opt,name=max_age,json=maxAge,proto3" json:"max_age,omitempty"`
	Compress  bool   `protobuf:"varint,5,opt,name=compress,proto3" json:"compress,omitempty"`
}

func (x *Log) Reset() {
	*x = Log{}
	mi := &file_internal_conf_conf_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Log) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Log) ProtoMessage() {}

func (x *Log) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Log.ProtoReflect.Descriptor instead.
func (*Log) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{4}
}

func (x *Log) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *Log) GetMaxSize() int32 {
	if x != nil {
		return x.MaxSize
	}
	return 0
}

func (x *Log) GetMaxBackup() int32 {
	if x != nil {
		return x.MaxBackup
	}
	return 0
}

func (x *Log) GetMaxAge() int32 {
	if x != nil {
		return x.MaxAge
	}
	return 0
}

func (x *Log) GetCompress() bool {
	if x != nil {
		return x.Compress
	}
	return false
}

type Third struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProxyUrl                       string            `protobuf:"bytes,1,opt,name=proxy_url,json=proxyUrl,proto3" json:"proxy_url,omitempty"`
	NluOrigin                      string            `protobuf:"bytes,2,opt,name=nlu_origin,json=nluOrigin,proto3" json:"nlu_origin,omitempty"`
	Nlu4LuiOrigin                  string            `protobuf:"bytes,3,opt,name=nlu4lui_origin,json=nlu4luiOrigin,proto3" json:"nlu4lui_origin,omitempty"`
	LegalCheck                     *LegalCheck       `protobuf:"bytes,4,opt,name=legal_check,json=legalCheck,proto3" json:"legal_check,omitempty"`
	LlmPrompt                      *Third_LlmPrompt  `protobuf:"bytes,5,opt,name=llm_prompt,json=llmPrompt,proto3" json:"llm_prompt,omitempty"`
	XPad1AppId                     string            `protobuf:"bytes,14,opt,name=xPad1AppId,proto3" json:"xPad1AppId,omitempty"`
	XPad1                          *ThirdContentUrl  `protobuf:"bytes,6,opt,name=xPad1,proto3" json:"xPad1,omitempty"`
	XPad2AppId                     string            `protobuf:"bytes,7,opt,name=xPad2AppId,proto3" json:"xPad2AppId,omitempty"`
	XPad2                          *ThirdContentUrl  `protobuf:"bytes,8,opt,name=xPad2,proto3" json:"xPad2,omitempty"`
	Nlu4LuiListOrigin              string            `protobuf:"bytes,9,opt,name=nlu4lui_list_origin,json=nlu4luiListOrigin,proto3" json:"nlu4lui_list_origin,omitempty"`
	IpAnalysis                     *Third_IPAnalysis `protobuf:"bytes,10,opt,name=ip_analysis,json=ipAnalysis,proto3" json:"ip_analysis,omitempty"`
	SingleWordTts                  []string          `protobuf:"bytes,11,rep,name=single_word_tts,json=singleWordTts,proto3" json:"single_word_tts,omitempty"`
	NonsenseTts                    []string          `protobuf:"bytes,12,rep,name=nonsense_tts,json=nonsenseTts,proto3" json:"nonsense_tts,omitempty"`
	WhiteExamJudge                 *WhiteExamJudge   `protobuf:"bytes,13,opt,name=white_exam_judge,json=whiteExamJudge,proto3" json:"white_exam_judge,omitempty"`
	LuiControllerUrl               string            `protobuf:"bytes,15,opt,name=lui_controller_url,json=luiControllerUrl,proto3" json:"lui_controller_url,omitempty"`
	XsSkillHubUrl                  string            `protobuf:"bytes,16,opt,name=xs_skill_hub_url,json=xsSkillHubUrl,proto3" json:"xs_skill_hub_url,omitempty"`
	XPodAppId                      string            `protobuf:"bytes,17,opt,name=xPodAppId,proto3" json:"xPodAppId,omitempty"`
	XPad2LiteAppId                 string            `protobuf:"bytes,18,opt,name=xPad2LiteAppId,proto3" json:"xPad2LiteAppId,omitempty"`
	XiaosiRagUrl                   string            `protobuf:"bytes,19,opt,name=xiaosi_rag_url,json=xiaosiRagUrl,proto3" json:"xiaosi_rag_url,omitempty"`
	MobbyScheme                    map[string]string `protobuf:"bytes,20,rep,name=mobby_scheme,json=mobbyScheme,proto3" json:"mobby_scheme,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XPad1V2AppId                   string            `protobuf:"bytes,22,opt,name=xPad1V2AppId,proto3" json:"xPad1V2AppId,omitempty"`
	NlpFunc                        *NlpFunc          `protobuf:"bytes,21,opt,name=nlp_func,json=nlpFunc,proto3" json:"nlp_func,omitempty"`
	XsContactUrl                   string            `protobuf:"bytes,23,opt,name=xs_contact_url,json=xsContactUrl,proto3" json:"xs_contact_url,omitempty"`
	BaikeRagUrl                    string            `protobuf:"bytes,24,opt,name=baike_rag_url,json=baikeRagUrl,proto3" json:"baike_rag_url,omitempty"`
	Llm                            *Third_Llm        `protobuf:"bytes,25,opt,name=llm,proto3" json:"llm,omitempty"`
	DialogueProfileUrl             string            `protobuf:"bytes,26,opt,name=dialogue_profile_url,json=dialogueProfileUrl,proto3" json:"dialogue_profile_url,omitempty"`
	MemoryServiceUrl               string            `protobuf:"bytes,27,opt,name=memory_service_url,json=memoryServiceUrl,proto3" json:"memory_service_url,omitempty"`
	OcrOrigin                      string            `protobuf:"bytes,28,opt,name=ocr_origin,json=ocrOrigin,proto3" json:"ocr_origin,omitempty"`
	MathGptAskClassifyOrigin       string            `protobuf:"bytes,29,opt,name=math_gpt_ask_classify_origin,json=mathGptAskClassifyOrigin,proto3" json:"math_gpt_ask_classify_origin,omitempty"`
	CloudControlHost               string            `protobuf:"bytes,30,opt,name=cloud_control_host,json=cloudControlHost,proto3" json:"cloud_control_host,omitempty"`
	MathAgent                      *MathAgent        `protobuf:"bytes,31,opt,name=math_agent,json=mathAgent,proto3" json:"math_agent,omitempty"`
	XPad2AppIdQijian               string            `protobuf:"bytes,32,opt,name=xPad2AppId_qijian,json=xPad2AppIdQijian,proto3" json:"xPad2AppId_qijian,omitempty"`
	AllSkillsWebUrl                string            `protobuf:"bytes,33,opt,name=all_skills_web_url,json=allSkillsWebUrl,proto3" json:"all_skills_web_url,omitempty"`
	PolishTts                      []string          `protobuf:"bytes,34,rep,name=polish_tts,json=polishTts,proto3" json:"polish_tts,omitempty"`
	UpdateProfileSubjectVersionUrl string            `protobuf:"bytes,35,opt,name=update_profile_subject_version_url,json=updateProfileSubjectVersionUrl,proto3" json:"update_profile_subject_version_url,omitempty"`
	AppIdMap                       map[string]string `protobuf:"bytes,36,rep,name=app_id_map,json=appIdMap,proto3" json:"app_id_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XueLianJiAllSkillsWebUrl       string            `protobuf:"bytes,37,opt,name=xue_lian_ji_all_skills_web_url,json=xueLianJiAllSkillsWebUrl,proto3" json:"xue_lian_ji_all_skills_web_url,omitempty"`
	ReadbookSchemeUrl              string            `protobuf:"bytes,38,opt,name=readbook_scheme_url,json=readbookSchemeUrl,proto3" json:"readbook_scheme_url,omitempty"`
	ReadbookNoteSchemeUrl          string            `protobuf:"bytes,39,opt,name=readbook_note_scheme_url,json=readbookNoteSchemeUrl,proto3" json:"readbook_note_scheme_url,omitempty"`
	ReadbookDefaultCover           string            `protobuf:"bytes,40,opt,name=readbook_default_cover,json=readbookDefaultCover,proto3" json:"readbook_default_cover,omitempty"`
	TailTts                        []string          `protobuf:"bytes,41,rep,name=tail_tts,json=tailTts,proto3" json:"tail_tts,omitempty"`
	ChatContextFeedUrl             string            `protobuf:"bytes,42,opt,name=chat_context_feed_url,json=chatContextFeedUrl,proto3" json:"chat_context_feed_url,omitempty"`
	HomeWorkDoudi                  []string          `protobuf:"bytes,43,rep,name=home_work_doudi,json=homeWorkDoudi,proto3" json:"home_work_doudi,omitempty"`
	Filters                        *Filters          `protobuf:"bytes,44,opt,name=filters,proto3" json:"filters,omitempty"`
}

func (x *Third) Reset() {
	*x = Third{}
	mi := &file_internal_conf_conf_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Third) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Third) ProtoMessage() {}

func (x *Third) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Third.ProtoReflect.Descriptor instead.
func (*Third) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{5}
}

func (x *Third) GetProxyUrl() string {
	if x != nil {
		return x.ProxyUrl
	}
	return ""
}

func (x *Third) GetNluOrigin() string {
	if x != nil {
		return x.NluOrigin
	}
	return ""
}

func (x *Third) GetNlu4LuiOrigin() string {
	if x != nil {
		return x.Nlu4LuiOrigin
	}
	return ""
}

func (x *Third) GetLegalCheck() *LegalCheck {
	if x != nil {
		return x.LegalCheck
	}
	return nil
}

func (x *Third) GetLlmPrompt() *Third_LlmPrompt {
	if x != nil {
		return x.LlmPrompt
	}
	return nil
}

func (x *Third) GetXPad1AppId() string {
	if x != nil {
		return x.XPad1AppId
	}
	return ""
}

func (x *Third) GetXPad1() *ThirdContentUrl {
	if x != nil {
		return x.XPad1
	}
	return nil
}

func (x *Third) GetXPad2AppId() string {
	if x != nil {
		return x.XPad2AppId
	}
	return ""
}

func (x *Third) GetXPad2() *ThirdContentUrl {
	if x != nil {
		return x.XPad2
	}
	return nil
}

func (x *Third) GetNlu4LuiListOrigin() string {
	if x != nil {
		return x.Nlu4LuiListOrigin
	}
	return ""
}

func (x *Third) GetIpAnalysis() *Third_IPAnalysis {
	if x != nil {
		return x.IpAnalysis
	}
	return nil
}

func (x *Third) GetSingleWordTts() []string {
	if x != nil {
		return x.SingleWordTts
	}
	return nil
}

func (x *Third) GetNonsenseTts() []string {
	if x != nil {
		return x.NonsenseTts
	}
	return nil
}

func (x *Third) GetWhiteExamJudge() *WhiteExamJudge {
	if x != nil {
		return x.WhiteExamJudge
	}
	return nil
}

func (x *Third) GetLuiControllerUrl() string {
	if x != nil {
		return x.LuiControllerUrl
	}
	return ""
}

func (x *Third) GetXsSkillHubUrl() string {
	if x != nil {
		return x.XsSkillHubUrl
	}
	return ""
}

func (x *Third) GetXPodAppId() string {
	if x != nil {
		return x.XPodAppId
	}
	return ""
}

func (x *Third) GetXPad2LiteAppId() string {
	if x != nil {
		return x.XPad2LiteAppId
	}
	return ""
}

func (x *Third) GetXiaosiRagUrl() string {
	if x != nil {
		return x.XiaosiRagUrl
	}
	return ""
}

func (x *Third) GetMobbyScheme() map[string]string {
	if x != nil {
		return x.MobbyScheme
	}
	return nil
}

func (x *Third) GetXPad1V2AppId() string {
	if x != nil {
		return x.XPad1V2AppId
	}
	return ""
}

func (x *Third) GetNlpFunc() *NlpFunc {
	if x != nil {
		return x.NlpFunc
	}
	return nil
}

func (x *Third) GetXsContactUrl() string {
	if x != nil {
		return x.XsContactUrl
	}
	return ""
}

func (x *Third) GetBaikeRagUrl() string {
	if x != nil {
		return x.BaikeRagUrl
	}
	return ""
}

func (x *Third) GetLlm() *Third_Llm {
	if x != nil {
		return x.Llm
	}
	return nil
}

func (x *Third) GetDialogueProfileUrl() string {
	if x != nil {
		return x.DialogueProfileUrl
	}
	return ""
}

func (x *Third) GetMemoryServiceUrl() string {
	if x != nil {
		return x.MemoryServiceUrl
	}
	return ""
}

func (x *Third) GetOcrOrigin() string {
	if x != nil {
		return x.OcrOrigin
	}
	return ""
}

func (x *Third) GetMathGptAskClassifyOrigin() string {
	if x != nil {
		return x.MathGptAskClassifyOrigin
	}
	return ""
}

func (x *Third) GetCloudControlHost() string {
	if x != nil {
		return x.CloudControlHost
	}
	return ""
}

func (x *Third) GetMathAgent() *MathAgent {
	if x != nil {
		return x.MathAgent
	}
	return nil
}

func (x *Third) GetXPad2AppIdQijian() string {
	if x != nil {
		return x.XPad2AppIdQijian
	}
	return ""
}

func (x *Third) GetAllSkillsWebUrl() string {
	if x != nil {
		return x.AllSkillsWebUrl
	}
	return ""
}

func (x *Third) GetPolishTts() []string {
	if x != nil {
		return x.PolishTts
	}
	return nil
}

func (x *Third) GetUpdateProfileSubjectVersionUrl() string {
	if x != nil {
		return x.UpdateProfileSubjectVersionUrl
	}
	return ""
}

func (x *Third) GetAppIdMap() map[string]string {
	if x != nil {
		return x.AppIdMap
	}
	return nil
}

func (x *Third) GetXueLianJiAllSkillsWebUrl() string {
	if x != nil {
		return x.XueLianJiAllSkillsWebUrl
	}
	return ""
}

func (x *Third) GetReadbookSchemeUrl() string {
	if x != nil {
		return x.ReadbookSchemeUrl
	}
	return ""
}

func (x *Third) GetReadbookNoteSchemeUrl() string {
	if x != nil {
		return x.ReadbookNoteSchemeUrl
	}
	return ""
}

func (x *Third) GetReadbookDefaultCover() string {
	if x != nil {
		return x.ReadbookDefaultCover
	}
	return ""
}

func (x *Third) GetTailTts() []string {
	if x != nil {
		return x.TailTts
	}
	return nil
}

func (x *Third) GetChatContextFeedUrl() string {
	if x != nil {
		return x.ChatContextFeedUrl
	}
	return ""
}

func (x *Third) GetHomeWorkDoudi() []string {
	if x != nil {
		return x.HomeWorkDoudi
	}
	return nil
}

func (x *Third) GetFilters() *Filters {
	if x != nil {
		return x.Filters
	}
	return nil
}

type Filters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FiltersIntentWhiteList        []string          `protobuf:"bytes,1,rep,name=filters_intent_white_list,json=filtersIntentWhiteList,proto3" json:"filters_intent_white_list,omitempty"`
	FiltersRoleWhiteList          []string          `protobuf:"bytes,2,rep,name=filters_role_white_list,json=filtersRoleWhiteList,proto3" json:"filters_role_white_list,omitempty"`
	FiltersUnderTtsXiaozha        []string          `protobuf:"bytes,3,rep,name=filters_under_tts_xiaozha,json=filtersUnderTtsXiaozha,proto3" json:"filters_under_tts_xiaozha,omitempty"`
	FiltersUnderTtsXiaoyi         []string          `protobuf:"bytes,4,rep,name=filters_under_tts_xiaoyi,json=filtersUnderTtsXiaoyi,proto3" json:"filters_under_tts_xiaoyi,omitempty"`
	FiltersNameMapVideoid         map[string]string `protobuf:"bytes,5,rep,name=filters_name_map_videoid,json=filtersNameMapVideoid,proto3" json:"filters_name_map_videoid,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	FiltersIntentWhiteListToSkill []string          `protobuf:"bytes,6,rep,name=filters_intent_white_list_to_skill,json=filtersIntentWhiteListToSkill,proto3" json:"filters_intent_white_list_to_skill,omitempty"`
}

func (x *Filters) Reset() {
	*x = Filters{}
	mi := &file_internal_conf_conf_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Filters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Filters) ProtoMessage() {}

func (x *Filters) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Filters.ProtoReflect.Descriptor instead.
func (*Filters) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{6}
}

func (x *Filters) GetFiltersIntentWhiteList() []string {
	if x != nil {
		return x.FiltersIntentWhiteList
	}
	return nil
}

func (x *Filters) GetFiltersRoleWhiteList() []string {
	if x != nil {
		return x.FiltersRoleWhiteList
	}
	return nil
}

func (x *Filters) GetFiltersUnderTtsXiaozha() []string {
	if x != nil {
		return x.FiltersUnderTtsXiaozha
	}
	return nil
}

func (x *Filters) GetFiltersUnderTtsXiaoyi() []string {
	if x != nil {
		return x.FiltersUnderTtsXiaoyi
	}
	return nil
}

func (x *Filters) GetFiltersNameMapVideoid() map[string]string {
	if x != nil {
		return x.FiltersNameMapVideoid
	}
	return nil
}

func (x *Filters) GetFiltersIntentWhiteListToSkill() []string {
	if x != nil {
		return x.FiltersIntentWhiteListToSkill
	}
	return nil
}

type MathAgent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PaiImageUrl    string `protobuf:"bytes,1,opt,name=pai_image_url,json=paiImageUrl,proto3" json:"pai_image_url,omitempty"`
	MathGptTmpId   string `protobuf:"bytes,2,opt,name=math_gpt_tmp_id,json=mathGptTmpId,proto3" json:"math_gpt_tmp_id,omitempty"`
	MathAgentTmpId string `protobuf:"bytes,3,opt,name=math_agent_tmp_id,json=mathAgentTmpId,proto3" json:"math_agent_tmp_id,omitempty"`
	SuishiwenH5Url string `protobuf:"bytes,4,opt,name=suishiwen_h5_url,json=suishiwenH5Url,proto3" json:"suishiwen_h5_url,omitempty"`
	DownwardMsgUrl string `protobuf:"bytes,5,opt,name=downward_msg_url,json=downwardMsgUrl,proto3" json:"downward_msg_url,omitempty"`
	AutoMathUrl    string `protobuf:"bytes,6,opt,name=auto_math_url,json=autoMathUrl,proto3" json:"auto_math_url,omitempty"`
	AutoMathAk     string `protobuf:"bytes,7,opt,name=auto_math_ak,json=autoMathAk,proto3" json:"auto_math_ak,omitempty"`
	AutoMathSk     string `protobuf:"bytes,8,opt,name=auto_math_sk,json=autoMathSk,proto3" json:"auto_math_sk,omitempty"`
}

func (x *MathAgent) Reset() {
	*x = MathAgent{}
	mi := &file_internal_conf_conf_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MathAgent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MathAgent) ProtoMessage() {}

func (x *MathAgent) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MathAgent.ProtoReflect.Descriptor instead.
func (*MathAgent) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{7}
}

func (x *MathAgent) GetPaiImageUrl() string {
	if x != nil {
		return x.PaiImageUrl
	}
	return ""
}

func (x *MathAgent) GetMathGptTmpId() string {
	if x != nil {
		return x.MathGptTmpId
	}
	return ""
}

func (x *MathAgent) GetMathAgentTmpId() string {
	if x != nil {
		return x.MathAgentTmpId
	}
	return ""
}

func (x *MathAgent) GetSuishiwenH5Url() string {
	if x != nil {
		return x.SuishiwenH5Url
	}
	return ""
}

func (x *MathAgent) GetDownwardMsgUrl() string {
	if x != nil {
		return x.DownwardMsgUrl
	}
	return ""
}

func (x *MathAgent) GetAutoMathUrl() string {
	if x != nil {
		return x.AutoMathUrl
	}
	return ""
}

func (x *MathAgent) GetAutoMathAk() string {
	if x != nil {
		return x.AutoMathAk
	}
	return ""
}

func (x *MathAgent) GetAutoMathSk() string {
	if x != nil {
		return x.AutoMathSk
	}
	return ""
}

type LegalCheck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CheckEnable bool     `protobuf:"varint,1,opt,name=check_enable,json=checkEnable,proto3" json:"check_enable,omitempty"`
	ServiceId   string   `protobuf:"bytes,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	Host        string   `protobuf:"bytes,3,opt,name=host,proto3" json:"host,omitempty"`
	CheckIntent []string `protobuf:"bytes,4,rep,name=check_intent,json=checkIntent,proto3" json:"check_intent,omitempty"`
}

func (x *LegalCheck) Reset() {
	*x = LegalCheck{}
	mi := &file_internal_conf_conf_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LegalCheck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LegalCheck) ProtoMessage() {}

func (x *LegalCheck) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LegalCheck.ProtoReflect.Descriptor instead.
func (*LegalCheck) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{8}
}

func (x *LegalCheck) GetCheckEnable() bool {
	if x != nil {
		return x.CheckEnable
	}
	return false
}

func (x *LegalCheck) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *LegalCheck) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *LegalCheck) GetCheckIntent() []string {
	if x != nil {
		return x.CheckIntent
	}
	return nil
}

type ThirdContentUrl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceIdsUrl      string `protobuf:"bytes,4,opt,name=resource_ids_url,json=resourceIdsUrl,proto3" json:"resource_ids_url,omitempty"`
	ResourceSlotUrl     string `protobuf:"bytes,5,opt,name=resource_slot_url,json=resourceSlotUrl,proto3" json:"resource_slot_url,omitempty"`
	ResourceChoiceUrl   string `protobuf:"bytes,6,opt,name=resource_choice_url,json=resourceChoiceUrl,proto3" json:"resource_choice_url,omitempty"`
	TokenParseUrl       string `protobuf:"bytes,8,opt,name=token_parse_url,json=tokenParseUrl,proto3" json:"token_parse_url,omitempty"`
	UserProfileUrl      string `protobuf:"bytes,9,opt,name=user_profile_url,json=userProfileUrl,proto3" json:"user_profile_url,omitempty"`
	ToolsUrl            string `protobuf:"bytes,10,opt,name=tools_url,json=toolsUrl,proto3" json:"tools_url,omitempty"`
	StudyLogUrl         string `protobuf:"bytes,11,opt,name=study_log_url,json=studyLogUrl,proto3" json:"study_log_url,omitempty"`
	ResourceConfUrl     string `protobuf:"bytes,12,opt,name=resource_conf_url,json=resourceConfUrl,proto3" json:"resource_conf_url,omitempty"`
	ContentAppId        string `protobuf:"bytes,13,opt,name=content_app_id,json=contentAppId,proto3" json:"content_app_id,omitempty"`
	ToolsUserVersionUrl string `protobuf:"bytes,14,opt,name=tools_user_version_url,json=toolsUserVersionUrl,proto3" json:"tools_user_version_url,omitempty"`
}

func (x *ThirdContentUrl) Reset() {
	*x = ThirdContentUrl{}
	mi := &file_internal_conf_conf_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThirdContentUrl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThirdContentUrl) ProtoMessage() {}

func (x *ThirdContentUrl) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThirdContentUrl.ProtoReflect.Descriptor instead.
func (*ThirdContentUrl) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{9}
}

func (x *ThirdContentUrl) GetResourceIdsUrl() string {
	if x != nil {
		return x.ResourceIdsUrl
	}
	return ""
}

func (x *ThirdContentUrl) GetResourceSlotUrl() string {
	if x != nil {
		return x.ResourceSlotUrl
	}
	return ""
}

func (x *ThirdContentUrl) GetResourceChoiceUrl() string {
	if x != nil {
		return x.ResourceChoiceUrl
	}
	return ""
}

func (x *ThirdContentUrl) GetTokenParseUrl() string {
	if x != nil {
		return x.TokenParseUrl
	}
	return ""
}

func (x *ThirdContentUrl) GetUserProfileUrl() string {
	if x != nil {
		return x.UserProfileUrl
	}
	return ""
}

func (x *ThirdContentUrl) GetToolsUrl() string {
	if x != nil {
		return x.ToolsUrl
	}
	return ""
}

func (x *ThirdContentUrl) GetStudyLogUrl() string {
	if x != nil {
		return x.StudyLogUrl
	}
	return ""
}

func (x *ThirdContentUrl) GetResourceConfUrl() string {
	if x != nil {
		return x.ResourceConfUrl
	}
	return ""
}

func (x *ThirdContentUrl) GetContentAppId() string {
	if x != nil {
		return x.ContentAppId
	}
	return ""
}

func (x *ThirdContentUrl) GetToolsUserVersionUrl() string {
	if x != nil {
		return x.ToolsUserVersionUrl
	}
	return ""
}

type Sign struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SignSecrets map[string]string `protobuf:"bytes,1,rep,name=sign_secrets,json=signSecrets,proto3" json:"sign_secrets,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Sign) Reset() {
	*x = Sign{}
	mi := &file_internal_conf_conf_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Sign) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Sign) ProtoMessage() {}

func (x *Sign) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Sign.ProtoReflect.Descriptor instead.
func (*Sign) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{10}
}

func (x *Sign) GetSignSecrets() map[string]string {
	if x != nil {
		return x.SignSecrets
	}
	return nil
}

type WhiteOsVersion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   int32  `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	List     string `protobuf:"bytes,2,opt,name=list,proto3" json:"list,omitempty"`
	SnList   string `protobuf:"bytes,3,opt,name=sn_list,json=snList,proto3" json:"sn_list,omitempty"`
	ZxSnList string `protobuf:"bytes,4,opt,name=zx_sn_list,json=zxSnList,proto3" json:"zx_sn_list,omitempty"`
}

func (x *WhiteOsVersion) Reset() {
	*x = WhiteOsVersion{}
	mi := &file_internal_conf_conf_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WhiteOsVersion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WhiteOsVersion) ProtoMessage() {}

func (x *WhiteOsVersion) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WhiteOsVersion.ProtoReflect.Descriptor instead.
func (*WhiteOsVersion) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{11}
}

func (x *WhiteOsVersion) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *WhiteOsVersion) GetList() string {
	if x != nil {
		return x.List
	}
	return ""
}

func (x *WhiteOsVersion) GetSnList() string {
	if x != nil {
		return x.SnList
	}
	return ""
}

func (x *WhiteOsVersion) GetZxSnList() string {
	if x != nil {
		return x.ZxSnList
	}
	return ""
}

type WhiteExamJudge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status int32  `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	SnList string `protobuf:"bytes,2,opt,name=sn_list,json=snList,proto3" json:"sn_list,omitempty"`
}

func (x *WhiteExamJudge) Reset() {
	*x = WhiteExamJudge{}
	mi := &file_internal_conf_conf_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WhiteExamJudge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WhiteExamJudge) ProtoMessage() {}

func (x *WhiteExamJudge) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WhiteExamJudge.ProtoReflect.Descriptor instead.
func (*WhiteExamJudge) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{12}
}

func (x *WhiteExamJudge) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *WhiteExamJudge) GetSnList() string {
	if x != nil {
		return x.SnList
	}
	return ""
}

type NlpFunc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Controller          string                     `protobuf:"bytes,1,opt,name=controller,proto3" json:"controller,omitempty"`
	ControllerTimeout   int32                      `protobuf:"varint,2,opt,name=controller_timeout,json=controllerTimeout,proto3" json:"controller_timeout,omitempty"`
	FunctionCall        string                     `protobuf:"bytes,3,opt,name=function_call,json=functionCall,proto3" json:"function_call,omitempty"`
	FunctionCallTimeout int32                      `protobuf:"varint,4,opt,name=function_call_timeout,json=functionCallTimeout,proto3" json:"function_call_timeout,omitempty"`
	DouDiMap            map[string]*DouDiIntentMap `protobuf:"bytes,5,rep,name=dou_di_map,json=douDiMap,proto3" json:"dou_di_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	QueryNlpSwitch      int32                      `protobuf:"varint,6,opt,name=query_nlp_switch,json=queryNlpSwitch,proto3" json:"query_nlp_switch,omitempty"`
	QueryNlpV2          string                     `protobuf:"bytes,7,opt,name=query_nlp_v2,json=queryNlpV2,proto3" json:"query_nlp_v2,omitempty"`
	DialogApi           string                     `protobuf:"bytes,8,opt,name=dialog_api,json=dialogApi,proto3" json:"dialog_api,omitempty"`
	DialogApiTimeout    int32                      `protobuf:"varint,9,opt,name=dialog_api_timeout,json=dialogApiTimeout,proto3" json:"dialog_api_timeout,omitempty"`
}

func (x *NlpFunc) Reset() {
	*x = NlpFunc{}
	mi := &file_internal_conf_conf_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NlpFunc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NlpFunc) ProtoMessage() {}

func (x *NlpFunc) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NlpFunc.ProtoReflect.Descriptor instead.
func (*NlpFunc) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{13}
}

func (x *NlpFunc) GetController() string {
	if x != nil {
		return x.Controller
	}
	return ""
}

func (x *NlpFunc) GetControllerTimeout() int32 {
	if x != nil {
		return x.ControllerTimeout
	}
	return 0
}

func (x *NlpFunc) GetFunctionCall() string {
	if x != nil {
		return x.FunctionCall
	}
	return ""
}

func (x *NlpFunc) GetFunctionCallTimeout() int32 {
	if x != nil {
		return x.FunctionCallTimeout
	}
	return 0
}

func (x *NlpFunc) GetDouDiMap() map[string]*DouDiIntentMap {
	if x != nil {
		return x.DouDiMap
	}
	return nil
}

func (x *NlpFunc) GetQueryNlpSwitch() int32 {
	if x != nil {
		return x.QueryNlpSwitch
	}
	return 0
}

func (x *NlpFunc) GetQueryNlpV2() string {
	if x != nil {
		return x.QueryNlpV2
	}
	return ""
}

func (x *NlpFunc) GetDialogApi() string {
	if x != nil {
		return x.DialogApi
	}
	return ""
}

func (x *NlpFunc) GetDialogApiTimeout() int32 {
	if x != nil {
		return x.DialogApiTimeout
	}
	return 0
}

type DouDiIntentMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DouDiIntentMap map[string]*DouDiList `protobuf:"bytes,1,rep,name=dou_di_intent_map,json=douDiIntentMap,proto3" json:"dou_di_intent_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *DouDiIntentMap) Reset() {
	*x = DouDiIntentMap{}
	mi := &file_internal_conf_conf_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DouDiIntentMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DouDiIntentMap) ProtoMessage() {}

func (x *DouDiIntentMap) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DouDiIntentMap.ProtoReflect.Descriptor instead.
func (*DouDiIntentMap) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{14}
}

func (x *DouDiIntentMap) GetDouDiIntentMap() map[string]*DouDiList {
	if x != nil {
		return x.DouDiIntentMap
	}
	return nil
}

type DouDiList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []string `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *DouDiList) Reset() {
	*x = DouDiList{}
	mi := &file_internal_conf_conf_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DouDiList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DouDiList) ProtoMessage() {}

func (x *DouDiList) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DouDiList.ProtoReflect.Descriptor instead.
func (*DouDiList) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{15}
}

func (x *DouDiList) GetItems() []string {
	if x != nil {
		return x.Items
	}
	return nil
}

type Skill struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*Skill_SkillTag `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *Skill) Reset() {
	*x = Skill{}
	mi := &file_internal_conf_conf_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Skill) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Skill) ProtoMessage() {}

func (x *Skill) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Skill.ProtoReflect.Descriptor instead.
func (*Skill) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{16}
}

func (x *Skill) GetList() []*Skill_SkillTag {
	if x != nil {
		return x.List
	}
	return nil
}

type SkillDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SkillMap map[string]*SkillDetail_SkillDetailList `protobuf:"bytes,1,rep,name=skill_map,json=skillMap,proto3" json:"skill_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SkillDetail) Reset() {
	*x = SkillDetail{}
	mi := &file_internal_conf_conf_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SkillDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkillDetail) ProtoMessage() {}

func (x *SkillDetail) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkillDetail.ProtoReflect.Descriptor instead.
func (*SkillDetail) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{17}
}

func (x *SkillDetail) GetSkillMap() map[string]*SkillDetail_SkillDetailList {
	if x != nil {
		return x.SkillMap
	}
	return nil
}

type Api struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mapping map[string]*Api_TargetApi `protobuf:"bytes,1,rep,name=mapping,proto3" json:"mapping,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Api) Reset() {
	*x = Api{}
	mi := &file_internal_conf_conf_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Api) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Api) ProtoMessage() {}

func (x *Api) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Api.ProtoReflect.Descriptor instead.
func (*Api) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{18}
}

func (x *Api) GetMapping() map[string]*Api_TargetApi {
	if x != nil {
		return x.Mapping
	}
	return nil
}

type SubjectConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubjectFontColor     map[int32]string `protobuf:"bytes,1,rep,name=subject_font_color,json=subjectFontColor,proto3" json:"subject_font_color,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	SubjectJzxCover      map[int32]string `protobuf:"bytes,2,rep,name=subject_jzx_cover,json=subjectJzxCover,proto3" json:"subject_jzx_cover,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	JzxV15Scheme         string           `protobuf:"bytes,4,opt,name=jzx_v15_scheme,json=jzxV15Scheme,proto3" json:"jzx_v15_scheme,omitempty"`
	JzxV15QuestionsCover string           `protobuf:"bytes,5,opt,name=jzx_v15_questions_cover,json=jzxV15QuestionsCover,proto3" json:"jzx_v15_questions_cover,omitempty"`
	SubjectOverlayColor  map[int32]string `protobuf:"bytes,6,rep,name=subject_overlay_color,json=subjectOverlayColor,proto3" json:"subject_overlay_color,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SubjectConfig) Reset() {
	*x = SubjectConfig{}
	mi := &file_internal_conf_conf_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubjectConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubjectConfig) ProtoMessage() {}

func (x *SubjectConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubjectConfig.ProtoReflect.Descriptor instead.
func (*SubjectConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{19}
}

func (x *SubjectConfig) GetSubjectFontColor() map[int32]string {
	if x != nil {
		return x.SubjectFontColor
	}
	return nil
}

func (x *SubjectConfig) GetSubjectJzxCover() map[int32]string {
	if x != nil {
		return x.SubjectJzxCover
	}
	return nil
}

func (x *SubjectConfig) GetJzxV15Scheme() string {
	if x != nil {
		return x.JzxV15Scheme
	}
	return ""
}

func (x *SubjectConfig) GetJzxV15QuestionsCover() string {
	if x != nil {
		return x.JzxV15QuestionsCover
	}
	return ""
}

func (x *SubjectConfig) GetSubjectOverlayColor() map[int32]string {
	if x != nil {
		return x.SubjectOverlayColor
	}
	return nil
}

type Skills struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Skill       *Skill       `protobuf:"bytes,1,opt,name=skill,proto3" json:"skill,omitempty"`
	SkillDetail *SkillDetail `protobuf:"bytes,2,opt,name=skill_detail,json=skillDetail,proto3" json:"skill_detail,omitempty"`
}

func (x *Skills) Reset() {
	*x = Skills{}
	mi := &file_internal_conf_conf_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Skills) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Skills) ProtoMessage() {}

func (x *Skills) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Skills.ProtoReflect.Descriptor instead.
func (*Skills) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{20}
}

func (x *Skills) GetSkill() *Skill {
	if x != nil {
		return x.Skill
	}
	return nil
}

func (x *Skills) GetSkillDetail() *SkillDetail {
	if x != nil {
		return x.SkillDetail
	}
	return nil
}

type VoiceInteractionCommands struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MoreImage string                                   `protobuf:"bytes,2,opt,name=more_image,json=moreImage,proto3" json:"more_image,omitempty"`
	Commands  []*VoiceInteractionCommands_VoiceCommand `protobuf:"bytes,1,rep,name=commands,proto3" json:"commands,omitempty"`
}

func (x *VoiceInteractionCommands) Reset() {
	*x = VoiceInteractionCommands{}
	mi := &file_internal_conf_conf_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VoiceInteractionCommands) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoiceInteractionCommands) ProtoMessage() {}

func (x *VoiceInteractionCommands) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoiceInteractionCommands.ProtoReflect.Descriptor instead.
func (*VoiceInteractionCommands) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{21}
}

func (x *VoiceInteractionCommands) GetMoreImage() string {
	if x != nil {
		return x.MoreImage
	}
	return ""
}

func (x *VoiceInteractionCommands) GetCommands() []*VoiceInteractionCommands_VoiceCommand {
	if x != nil {
		return x.Commands
	}
	return nil
}

type Server_HTTP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Network string               `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr    string               `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout *durationpb.Duration `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
}

func (x *Server_HTTP) Reset() {
	*x = Server_HTTP{}
	mi := &file_internal_conf_conf_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server_HTTP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_HTTP) ProtoMessage() {}

func (x *Server_HTTP) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_HTTP.ProtoReflect.Descriptor instead.
func (*Server_HTTP) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1, 0}
}

func (x *Server_HTTP) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_HTTP) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_HTTP) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Server_GRPC struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Network string               `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr    string               `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout *durationpb.Duration `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
}

func (x *Server_GRPC) Reset() {
	*x = Server_GRPC{}
	mi := &file_internal_conf_conf_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server_GRPC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_GRPC) ProtoMessage() {}

func (x *Server_GRPC) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_GRPC.ProtoReflect.Descriptor instead.
func (*Server_GRPC) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1, 1}
}

func (x *Server_GRPC) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_GRPC) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_GRPC) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Data_Database struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Driver string `protobuf:"bytes,1,opt,name=driver,proto3" json:"driver,omitempty"`
	Source string `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
}

func (x *Data_Database) Reset() {
	*x = Data_Database{}
	mi := &file_internal_conf_conf_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Database) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Database) ProtoMessage() {}

func (x *Data_Database) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Database.ProtoReflect.Descriptor instead.
func (*Data_Database) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 0}
}

func (x *Data_Database) GetDriver() string {
	if x != nil {
		return x.Driver
	}
	return ""
}

func (x *Data_Database) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

type Data_Redis struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Network      string               `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr         string               `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Password     string               `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	Db           int32                `protobuf:"varint,4,opt,name=db,proto3" json:"db,omitempty"`
	DialTimeout  *durationpb.Duration `protobuf:"bytes,5,opt,name=dial_timeout,json=dialTimeout,proto3" json:"dial_timeout,omitempty"`
	ReadTimeout  *durationpb.Duration `protobuf:"bytes,6,opt,name=read_timeout,json=readTimeout,proto3" json:"read_timeout,omitempty"`
	WriteTimeout *durationpb.Duration `protobuf:"bytes,7,opt,name=write_timeout,json=writeTimeout,proto3" json:"write_timeout,omitempty"`
}

func (x *Data_Redis) Reset() {
	*x = Data_Redis{}
	mi := &file_internal_conf_conf_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Redis) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Redis) ProtoMessage() {}

func (x *Data_Redis) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Redis.ProtoReflect.Descriptor instead.
func (*Data_Redis) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 1}
}

func (x *Data_Redis) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Data_Redis) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Data_Redis) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *Data_Redis) GetDb() int32 {
	if x != nil {
		return x.Db
	}
	return 0
}

func (x *Data_Redis) GetDialTimeout() *durationpb.Duration {
	if x != nil {
		return x.DialTimeout
	}
	return nil
}

func (x *Data_Redis) GetReadTimeout() *durationpb.Duration {
	if x != nil {
		return x.ReadTimeout
	}
	return nil
}

func (x *Data_Redis) GetWriteTimeout() *durationpb.Duration {
	if x != nil {
		return x.WriteTimeout
	}
	return nil
}

type Data_Embedding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Db       *Data_Embedding_DB       `protobuf:"bytes,1,opt,name=db,proto3" json:"db,omitempty"`
	Tencent  *Data_Embedding_Tencent  `protobuf:"bytes,2,opt,name=tencent,proto3" json:"tencent,omitempty"`
	DbFaq    *Data_Embedding_DbFaq    `protobuf:"bytes,3,opt,name=db_faq,json=dbFaq,proto3" json:"db_faq,omitempty"`
	DbHotfix *Data_Embedding_DbHotfix `protobuf:"bytes,4,opt,name=db_hotfix,json=dbHotfix,proto3" json:"db_hotfix,omitempty"`
}

func (x *Data_Embedding) Reset() {
	*x = Data_Embedding{}
	mi := &file_internal_conf_conf_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Embedding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Embedding) ProtoMessage() {}

func (x *Data_Embedding) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Embedding.ProtoReflect.Descriptor instead.
func (*Data_Embedding) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 2}
}

func (x *Data_Embedding) GetDb() *Data_Embedding_DB {
	if x != nil {
		return x.Db
	}
	return nil
}

func (x *Data_Embedding) GetTencent() *Data_Embedding_Tencent {
	if x != nil {
		return x.Tencent
	}
	return nil
}

func (x *Data_Embedding) GetDbFaq() *Data_Embedding_DbFaq {
	if x != nil {
		return x.DbFaq
	}
	return nil
}

func (x *Data_Embedding) GetDbHotfix() *Data_Embedding_DbHotfix {
	if x != nil {
		return x.DbHotfix
	}
	return nil
}

type Data_Kafka struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Topic            string `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
	GroupId          string `protobuf:"bytes,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	BootstrapServers string `protobuf:"bytes,3,opt,name=bootstrap_servers,json=bootstrapServers,proto3" json:"bootstrap_servers,omitempty"`
	SecurityProtocol string `protobuf:"bytes,4,opt,name=security_protocol,json=securityProtocol,proto3" json:"security_protocol,omitempty"`
	SslCaLocation    string `protobuf:"bytes,5,opt,name=ssl_ca_location,json=sslCaLocation,proto3" json:"ssl_ca_location,omitempty"`
	SaslMechanism    string `protobuf:"bytes,6,opt,name=sasl_mechanism,json=saslMechanism,proto3" json:"sasl_mechanism,omitempty"`
	SaslUsername     string `protobuf:"bytes,7,opt,name=sasl_username,json=saslUsername,proto3" json:"sasl_username,omitempty"`
	SaslPassword     string `protobuf:"bytes,8,opt,name=sasl_password,json=saslPassword,proto3" json:"sasl_password,omitempty"`
	AutoAck          int32  `protobuf:"varint,9,opt,name=auto_ack,json=autoAck,proto3" json:"auto_ack,omitempty"`
	MaxMessageBytes  int32  `protobuf:"varint,10,opt,name=max_message_bytes,json=maxMessageBytes,proto3" json:"max_message_bytes,omitempty"`
}

func (x *Data_Kafka) Reset() {
	*x = Data_Kafka{}
	mi := &file_internal_conf_conf_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Kafka) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Kafka) ProtoMessage() {}

func (x *Data_Kafka) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Kafka.ProtoReflect.Descriptor instead.
func (*Data_Kafka) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 3}
}

func (x *Data_Kafka) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *Data_Kafka) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *Data_Kafka) GetBootstrapServers() string {
	if x != nil {
		return x.BootstrapServers
	}
	return ""
}

func (x *Data_Kafka) GetSecurityProtocol() string {
	if x != nil {
		return x.SecurityProtocol
	}
	return ""
}

func (x *Data_Kafka) GetSslCaLocation() string {
	if x != nil {
		return x.SslCaLocation
	}
	return ""
}

func (x *Data_Kafka) GetSaslMechanism() string {
	if x != nil {
		return x.SaslMechanism
	}
	return ""
}

func (x *Data_Kafka) GetSaslUsername() string {
	if x != nil {
		return x.SaslUsername
	}
	return ""
}

func (x *Data_Kafka) GetSaslPassword() string {
	if x != nil {
		return x.SaslPassword
	}
	return ""
}

func (x *Data_Kafka) GetAutoAck() int32 {
	if x != nil {
		return x.AutoAck
	}
	return 0
}

func (x *Data_Kafka) GetMaxMessageBytes() int32 {
	if x != nil {
		return x.MaxMessageBytes
	}
	return 0
}

type Data_Embedding_DB struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Database string `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
}

func (x *Data_Embedding_DB) Reset() {
	*x = Data_Embedding_DB{}
	mi := &file_internal_conf_conf_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Embedding_DB) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Embedding_DB) ProtoMessage() {}

func (x *Data_Embedding_DB) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Embedding_DB.ProtoReflect.Descriptor instead.
func (*Data_Embedding_DB) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 2, 0}
}

func (x *Data_Embedding_DB) GetDatabase() string {
	if x != nil {
		return x.Database
	}
	return ""
}

type Data_Embedding_Tencent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url   string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *Data_Embedding_Tencent) Reset() {
	*x = Data_Embedding_Tencent{}
	mi := &file_internal_conf_conf_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Embedding_Tencent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Embedding_Tencent) ProtoMessage() {}

func (x *Data_Embedding_Tencent) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Embedding_Tencent.ProtoReflect.Descriptor instead.
func (*Data_Embedding_Tencent) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 2, 1}
}

func (x *Data_Embedding_Tencent) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Data_Embedding_Tencent) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type Data_Embedding_DbFaq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Database     string  `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
	Collection   string  `protobuf:"bytes,2,opt,name=collection,proto3" json:"collection,omitempty"`
	FaqEnable    bool    `protobuf:"varint,3,opt,name=faq_enable,json=faqEnable,proto3" json:"faq_enable,omitempty"`
	FaqThreshold float32 `protobuf:"fixed32,4,opt,name=faq_threshold,json=faqThreshold,proto3" json:"faq_threshold,omitempty"`
	Url          string  `protobuf:"bytes,5,opt,name=url,proto3" json:"url,omitempty"`
	Token        string  `protobuf:"bytes,6,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *Data_Embedding_DbFaq) Reset() {
	*x = Data_Embedding_DbFaq{}
	mi := &file_internal_conf_conf_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Embedding_DbFaq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Embedding_DbFaq) ProtoMessage() {}

func (x *Data_Embedding_DbFaq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Embedding_DbFaq.ProtoReflect.Descriptor instead.
func (*Data_Embedding_DbFaq) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 2, 2}
}

func (x *Data_Embedding_DbFaq) GetDatabase() string {
	if x != nil {
		return x.Database
	}
	return ""
}

func (x *Data_Embedding_DbFaq) GetCollection() string {
	if x != nil {
		return x.Collection
	}
	return ""
}

func (x *Data_Embedding_DbFaq) GetFaqEnable() bool {
	if x != nil {
		return x.FaqEnable
	}
	return false
}

func (x *Data_Embedding_DbFaq) GetFaqThreshold() float32 {
	if x != nil {
		return x.FaqThreshold
	}
	return 0
}

func (x *Data_Embedding_DbFaq) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Data_Embedding_DbFaq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type Data_Embedding_DbHotfix struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Database     string  `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
	Collection   string  `protobuf:"bytes,2,opt,name=collection,proto3" json:"collection,omitempty"`
	Enable       bool    `protobuf:"varint,3,opt,name=enable,proto3" json:"enable,omitempty"`
	Threshold    float32 `protobuf:"fixed32,4,opt,name=threshold,proto3" json:"threshold,omitempty"`
	Url          string  `protobuf:"bytes,5,opt,name=url,proto3" json:"url,omitempty"`
	Token        string  `protobuf:"bytes,6,opt,name=token,proto3" json:"token,omitempty"`
	FaqThreshold float32 `protobuf:"fixed32,7,opt,name=faq_threshold,json=faqThreshold,proto3" json:"faq_threshold,omitempty"`
}

func (x *Data_Embedding_DbHotfix) Reset() {
	*x = Data_Embedding_DbHotfix{}
	mi := &file_internal_conf_conf_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Embedding_DbHotfix) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Embedding_DbHotfix) ProtoMessage() {}

func (x *Data_Embedding_DbHotfix) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Embedding_DbHotfix.ProtoReflect.Descriptor instead.
func (*Data_Embedding_DbHotfix) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 2, 3}
}

func (x *Data_Embedding_DbHotfix) GetDatabase() string {
	if x != nil {
		return x.Database
	}
	return ""
}

func (x *Data_Embedding_DbHotfix) GetCollection() string {
	if x != nil {
		return x.Collection
	}
	return ""
}

func (x *Data_Embedding_DbHotfix) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *Data_Embedding_DbHotfix) GetThreshold() float32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *Data_Embedding_DbHotfix) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Data_Embedding_DbHotfix) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *Data_Embedding_DbHotfix) GetFaqThreshold() float32 {
	if x != nil {
		return x.FaqThreshold
	}
	return 0
}

type ErrorHandle_ErrorMessages struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorMessages []*ErrorHandle_ErrorMessage `protobuf:"bytes,1,rep,name=error_messages,json=errorMessages,proto3" json:"error_messages,omitempty"`
}

func (x *ErrorHandle_ErrorMessages) Reset() {
	*x = ErrorHandle_ErrorMessages{}
	mi := &file_internal_conf_conf_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorHandle_ErrorMessages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorHandle_ErrorMessages) ProtoMessage() {}

func (x *ErrorHandle_ErrorMessages) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorHandle_ErrorMessages.ProtoReflect.Descriptor instead.
func (*ErrorHandle_ErrorMessages) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{3, 0}
}

func (x *ErrorHandle_ErrorMessages) GetErrorMessages() []*ErrorHandle_ErrorMessage {
	if x != nil {
		return x.ErrorMessages
	}
	return nil
}

type ErrorHandle_ErrorMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorReason string `protobuf:"bytes,1,opt,name=error_reason,json=errorReason,proto3" json:"error_reason,omitempty"`
	Message     string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *ErrorHandle_ErrorMessage) Reset() {
	*x = ErrorHandle_ErrorMessage{}
	mi := &file_internal_conf_conf_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorHandle_ErrorMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorHandle_ErrorMessage) ProtoMessage() {}

func (x *ErrorHandle_ErrorMessage) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorHandle_ErrorMessage.ProtoReflect.Descriptor instead.
func (*ErrorHandle_ErrorMessage) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{3, 1}
}

func (x *ErrorHandle_ErrorMessage) GetErrorReason() string {
	if x != nil {
		return x.ErrorReason
	}
	return ""
}

func (x *ErrorHandle_ErrorMessage) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type Third_IPAnalysis struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url     string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Retry   int32  `protobuf:"varint,2,opt,name=retry,proto3" json:"retry,omitempty"`
	Timeout int32  `protobuf:"varint,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
}

func (x *Third_IPAnalysis) Reset() {
	*x = Third_IPAnalysis{}
	mi := &file_internal_conf_conf_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Third_IPAnalysis) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Third_IPAnalysis) ProtoMessage() {}

func (x *Third_IPAnalysis) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Third_IPAnalysis.ProtoReflect.Descriptor instead.
func (*Third_IPAnalysis) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{5, 0}
}

func (x *Third_IPAnalysis) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Third_IPAnalysis) GetRetry() int32 {
	if x != nil {
		return x.Retry
	}
	return 0
}

func (x *Third_IPAnalysis) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

type Third_LlmPrompt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable                   bool     `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	SnList                   []string `protobuf:"bytes,2,rep,name=sn_list,json=snList,proto3" json:"sn_list,omitempty"`
	BaikeTmpId               string   `protobuf:"bytes,3,opt,name=baike_tmp_id,json=baikeTmpId,proto3" json:"baike_tmp_id,omitempty"`
	ChatTmpId                string   `protobuf:"bytes,4,opt,name=chat_tmp_id,json=chatTmpId,proto3" json:"chat_tmp_id,omitempty"`
	LegalCheckTts            []string `protobuf:"bytes,5,rep,name=legal_check_tts,json=legalCheckTts,proto3" json:"legal_check_tts,omitempty"`
	AppId                    string   `protobuf:"bytes,6,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	BaikeDefaultTts          []string `protobuf:"bytes,7,rep,name=baike_default_tts,json=baikeDefaultTts,proto3" json:"baike_default_tts,omitempty"`
	SugTmpId                 string   `protobuf:"bytes,8,opt,name=sug_tmp_id,json=sugTmpId,proto3" json:"sug_tmp_id,omitempty"`
	TailsTmpId               string   `protobuf:"bytes,9,opt,name=tails_tmp_id,json=tailsTmpId,proto3" json:"tails_tmp_id,omitempty"`
	FullViewEnable           bool     `protobuf:"varint,10,opt,name=full_view_enable,json=fullViewEnable,proto3" json:"full_view_enable,omitempty"`
	FullViewBaikeTmpId       string   `protobuf:"bytes,11,opt,name=full_view_baike_tmp_id,json=fullViewBaikeTmpId,proto3" json:"full_view_baike_tmp_id,omitempty"`
	FullViewChatTmpId        string   `protobuf:"bytes,12,opt,name=full_view_chat_tmp_id,json=fullViewChatTmpId,proto3" json:"full_view_chat_tmp_id,omitempty"`
	FilterChatPunctuation    string   `protobuf:"bytes,13,opt,name=filterChatPunctuation,proto3" json:"filterChatPunctuation,omitempty"`
	Xs2ChatPlusTmpId         string   `protobuf:"bytes,14,opt,name=xs2_chat_plus_tmp_id,json=xs2ChatPlusTmpId,proto3" json:"xs2_chat_plus_tmp_id,omitempty"`
	Xs2BaikePlusTmpId        string   `protobuf:"bytes,15,opt,name=xs2_baike_plus_tmp_id,json=xs2BaikePlusTmpId,proto3" json:"xs2_baike_plus_tmp_id,omitempty"`
	XsDeepseekTmpId          string   `protobuf:"bytes,16,opt,name=xs_deepseek_tmp_id,json=xsDeepseekTmpId,proto3" json:"xs_deepseek_tmp_id,omitempty"`
	XsDeepseekExercisesTmpId string   `protobuf:"bytes,17,opt,name=xs_deepseek_exercises_tmp_id,json=xsDeepseekExercisesTmpId,proto3" json:"xs_deepseek_exercises_tmp_id,omitempty"`
}

func (x *Third_LlmPrompt) Reset() {
	*x = Third_LlmPrompt{}
	mi := &file_internal_conf_conf_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Third_LlmPrompt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Third_LlmPrompt) ProtoMessage() {}

func (x *Third_LlmPrompt) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Third_LlmPrompt.ProtoReflect.Descriptor instead.
func (*Third_LlmPrompt) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{5, 1}
}

func (x *Third_LlmPrompt) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *Third_LlmPrompt) GetSnList() []string {
	if x != nil {
		return x.SnList
	}
	return nil
}

func (x *Third_LlmPrompt) GetBaikeTmpId() string {
	if x != nil {
		return x.BaikeTmpId
	}
	return ""
}

func (x *Third_LlmPrompt) GetChatTmpId() string {
	if x != nil {
		return x.ChatTmpId
	}
	return ""
}

func (x *Third_LlmPrompt) GetLegalCheckTts() []string {
	if x != nil {
		return x.LegalCheckTts
	}
	return nil
}

func (x *Third_LlmPrompt) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *Third_LlmPrompt) GetBaikeDefaultTts() []string {
	if x != nil {
		return x.BaikeDefaultTts
	}
	return nil
}

func (x *Third_LlmPrompt) GetSugTmpId() string {
	if x != nil {
		return x.SugTmpId
	}
	return ""
}

func (x *Third_LlmPrompt) GetTailsTmpId() string {
	if x != nil {
		return x.TailsTmpId
	}
	return ""
}

func (x *Third_LlmPrompt) GetFullViewEnable() bool {
	if x != nil {
		return x.FullViewEnable
	}
	return false
}

func (x *Third_LlmPrompt) GetFullViewBaikeTmpId() string {
	if x != nil {
		return x.FullViewBaikeTmpId
	}
	return ""
}

func (x *Third_LlmPrompt) GetFullViewChatTmpId() string {
	if x != nil {
		return x.FullViewChatTmpId
	}
	return ""
}

func (x *Third_LlmPrompt) GetFilterChatPunctuation() string {
	if x != nil {
		return x.FilterChatPunctuation
	}
	return ""
}

func (x *Third_LlmPrompt) GetXs2ChatPlusTmpId() string {
	if x != nil {
		return x.Xs2ChatPlusTmpId
	}
	return ""
}

func (x *Third_LlmPrompt) GetXs2BaikePlusTmpId() string {
	if x != nil {
		return x.Xs2BaikePlusTmpId
	}
	return ""
}

func (x *Third_LlmPrompt) GetXsDeepseekTmpId() string {
	if x != nil {
		return x.XsDeepseekTmpId
	}
	return ""
}

func (x *Third_LlmPrompt) GetXsDeepseekExercisesTmpId() string {
	if x != nil {
		return x.XsDeepseekExercisesTmpId
	}
	return ""
}

type Third_Llm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Intent       []string `protobuf:"bytes,1,rep,name=intent,proto3" json:"intent,omitempty"`
	LlmSugSwitch int32    `protobuf:"varint,2,opt,name=llm_sug_switch,json=llmSugSwitch,proto3" json:"llm_sug_switch,omitempty"`
}

func (x *Third_Llm) Reset() {
	*x = Third_Llm{}
	mi := &file_internal_conf_conf_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Third_Llm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Third_Llm) ProtoMessage() {}

func (x *Third_Llm) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Third_Llm.ProtoReflect.Descriptor instead.
func (*Third_Llm) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{5, 2}
}

func (x *Third_Llm) GetIntent() []string {
	if x != nil {
		return x.Intent
	}
	return nil
}

func (x *Third_Llm) GetLlmSugSwitch() int32 {
	if x != nil {
		return x.LlmSugSwitch
	}
	return 0
}

type Skill_SkillTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagId     string                      `protobuf:"bytes,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TagName   string                      `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	SkillData []*Skill_SkillTag_SkillData `protobuf:"bytes,3,rep,name=skill_data,json=skillData,proto3" json:"skill_data,omitempty"`
}

func (x *Skill_SkillTag) Reset() {
	*x = Skill_SkillTag{}
	mi := &file_internal_conf_conf_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Skill_SkillTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Skill_SkillTag) ProtoMessage() {}

func (x *Skill_SkillTag) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Skill_SkillTag.ProtoReflect.Descriptor instead.
func (*Skill_SkillTag) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{16, 0}
}

func (x *Skill_SkillTag) GetTagId() string {
	if x != nil {
		return x.TagId
	}
	return ""
}

func (x *Skill_SkillTag) GetTagName() string {
	if x != nil {
		return x.TagName
	}
	return ""
}

func (x *Skill_SkillTag) GetSkillData() []*Skill_SkillTag_SkillData {
	if x != nil {
		return x.SkillData
	}
	return nil
}

type Skill_SkillTag_SkillData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagId     string `protobuf:"bytes,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	SkillId   string `protobuf:"bytes,2,opt,name=skill_id,json=skillId,proto3" json:"skill_id,omitempty"`
	SkillName string `protobuf:"bytes,3,opt,name=skill_name,json=skillName,proto3" json:"skill_name,omitempty"`
	SkillIcon string `protobuf:"bytes,4,opt,name=skill_icon,json=skillIcon,proto3" json:"skill_icon,omitempty"`
}

func (x *Skill_SkillTag_SkillData) Reset() {
	*x = Skill_SkillTag_SkillData{}
	mi := &file_internal_conf_conf_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Skill_SkillTag_SkillData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Skill_SkillTag_SkillData) ProtoMessage() {}

func (x *Skill_SkillTag_SkillData) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Skill_SkillTag_SkillData.ProtoReflect.Descriptor instead.
func (*Skill_SkillTag_SkillData) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{16, 0, 0}
}

func (x *Skill_SkillTag_SkillData) GetTagId() string {
	if x != nil {
		return x.TagId
	}
	return ""
}

func (x *Skill_SkillTag_SkillData) GetSkillId() string {
	if x != nil {
		return x.SkillId
	}
	return ""
}

func (x *Skill_SkillTag_SkillData) GetSkillName() string {
	if x != nil {
		return x.SkillName
	}
	return ""
}

func (x *Skill_SkillTag_SkillData) GetSkillIcon() string {
	if x != nil {
		return x.SkillIcon
	}
	return ""
}

type SkillDetail_SkillDetailList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DetailList []*SkillDetail_SkillDetailList_SkillDetailInfo `protobuf:"bytes,1,rep,name=detail_list,json=detailList,proto3" json:"detail_list,omitempty"`
}

func (x *SkillDetail_SkillDetailList) Reset() {
	*x = SkillDetail_SkillDetailList{}
	mi := &file_internal_conf_conf_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SkillDetail_SkillDetailList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkillDetail_SkillDetailList) ProtoMessage() {}

func (x *SkillDetail_SkillDetailList) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkillDetail_SkillDetailList.ProtoReflect.Descriptor instead.
func (*SkillDetail_SkillDetailList) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{17, 0}
}

func (x *SkillDetail_SkillDetailList) GetDetailList() []*SkillDetail_SkillDetailList_SkillDetailInfo {
	if x != nil {
		return x.DetailList
	}
	return nil
}

type SkillDetail_SkillDetailList_SkillDetailInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SkillId     string                                                     `protobuf:"bytes,1,opt,name=skill_id,json=skillId,proto3" json:"skill_id,omitempty"`
	SkillName   string                                                     `protobuf:"bytes,2,opt,name=skill_name,json=skillName,proto3" json:"skill_name,omitempty"`
	Status      int32                                                      `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	CommandData []*SkillDetail_SkillDetailList_SkillDetailInfo_CommandData `protobuf:"bytes,4,rep,name=command_data,json=commandData,proto3" json:"command_data,omitempty"`
}

func (x *SkillDetail_SkillDetailList_SkillDetailInfo) Reset() {
	*x = SkillDetail_SkillDetailList_SkillDetailInfo{}
	mi := &file_internal_conf_conf_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SkillDetail_SkillDetailList_SkillDetailInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkillDetail_SkillDetailList_SkillDetailInfo) ProtoMessage() {}

func (x *SkillDetail_SkillDetailList_SkillDetailInfo) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkillDetail_SkillDetailList_SkillDetailInfo.ProtoReflect.Descriptor instead.
func (*SkillDetail_SkillDetailList_SkillDetailInfo) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{17, 0, 0}
}

func (x *SkillDetail_SkillDetailList_SkillDetailInfo) GetSkillId() string {
	if x != nil {
		return x.SkillId
	}
	return ""
}

func (x *SkillDetail_SkillDetailList_SkillDetailInfo) GetSkillName() string {
	if x != nil {
		return x.SkillName
	}
	return ""
}

func (x *SkillDetail_SkillDetailList_SkillDetailInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *SkillDetail_SkillDetailList_SkillDetailInfo) GetCommandData() []*SkillDetail_SkillDetailList_SkillDetailInfo_CommandData {
	if x != nil {
		return x.CommandData
	}
	return nil
}

type SkillDetail_SkillDetailList_SkillDetailInfo_CommandData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SkillTagId string `protobuf:"bytes,1,opt,name=skill_tag_id,json=skillTagId,proto3" json:"skill_tag_id,omitempty"`
	SkillTag   string `protobuf:"bytes,2,opt,name=skill_tag,json=skillTag,proto3" json:"skill_tag,omitempty"`
	CommandId  string `protobuf:"bytes,3,opt,name=command_id,json=commandId,proto3" json:"command_id,omitempty"`
	Command    string `protobuf:"bytes,4,opt,name=command,proto3" json:"command,omitempty"`
}

func (x *SkillDetail_SkillDetailList_SkillDetailInfo_CommandData) Reset() {
	*x = SkillDetail_SkillDetailList_SkillDetailInfo_CommandData{}
	mi := &file_internal_conf_conf_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SkillDetail_SkillDetailList_SkillDetailInfo_CommandData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkillDetail_SkillDetailList_SkillDetailInfo_CommandData) ProtoMessage() {}

func (x *SkillDetail_SkillDetailList_SkillDetailInfo_CommandData) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkillDetail_SkillDetailList_SkillDetailInfo_CommandData.ProtoReflect.Descriptor instead.
func (*SkillDetail_SkillDetailList_SkillDetailInfo_CommandData) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{17, 0, 0, 0}
}

func (x *SkillDetail_SkillDetailList_SkillDetailInfo_CommandData) GetSkillTagId() string {
	if x != nil {
		return x.SkillTagId
	}
	return ""
}

func (x *SkillDetail_SkillDetailList_SkillDetailInfo_CommandData) GetSkillTag() string {
	if x != nil {
		return x.SkillTag
	}
	return ""
}

func (x *SkillDetail_SkillDetailList_SkillDetailInfo_CommandData) GetCommandId() string {
	if x != nil {
		return x.CommandId
	}
	return ""
}

func (x *SkillDetail_SkillDetailList_SkillDetailInfo_CommandData) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

type Api_TargetApi struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url      string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Name     string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Function string `protobuf:"bytes,3,opt,name=function,proto3" json:"function,omitempty"`
	Version  string `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	Timeout  int32  `protobuf:"varint,5,opt,name=timeout,proto3" json:"timeout,omitempty"`
	Retry    int32  `protobuf:"varint,6,opt,name=retry,proto3" json:"retry,omitempty"`
	Service  string `protobuf:"bytes,7,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *Api_TargetApi) Reset() {
	*x = Api_TargetApi{}
	mi := &file_internal_conf_conf_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Api_TargetApi) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Api_TargetApi) ProtoMessage() {}

func (x *Api_TargetApi) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Api_TargetApi.ProtoReflect.Descriptor instead.
func (*Api_TargetApi) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{18, 0}
}

func (x *Api_TargetApi) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Api_TargetApi) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Api_TargetApi) GetFunction() string {
	if x != nil {
		return x.Function
	}
	return ""
}

func (x *Api_TargetApi) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Api_TargetApi) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *Api_TargetApi) GetRetry() int32 {
	if x != nil {
		return x.Retry
	}
	return 0
}

func (x *Api_TargetApi) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

type VoiceInteractionCommands_VoiceCommand struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  string   `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Query []string `protobuf:"bytes,2,rep,name=query,proto3" json:"query,omitempty"`
	Name  string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Icon  string   `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
}

func (x *VoiceInteractionCommands_VoiceCommand) Reset() {
	*x = VoiceInteractionCommands_VoiceCommand{}
	mi := &file_internal_conf_conf_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VoiceInteractionCommands_VoiceCommand) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoiceInteractionCommands_VoiceCommand) ProtoMessage() {}

func (x *VoiceInteractionCommands_VoiceCommand) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoiceInteractionCommands_VoiceCommand.ProtoReflect.Descriptor instead.
func (*VoiceInteractionCommands_VoiceCommand) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{21, 0}
}

func (x *VoiceInteractionCommands_VoiceCommand) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *VoiceInteractionCommands_VoiceCommand) GetQuery() []string {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *VoiceInteractionCommands_VoiceCommand) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VoiceInteractionCommands_VoiceCommand) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

var File_internal_conf_conf_proto protoreflect.FileDescriptor

var file_internal_conf_conf_proto_rawDesc = []byte{
	0x0a, 0x18, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x2f,
	0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xee, 0x06, 0x0a, 0x09, 0x42, 0x6f, 0x6f, 0x74, 0x73,
	0x74, 0x72, 0x61, 0x70, 0x12, 0x2a, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x12, 0x24, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2d, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x21, 0x0a, 0x03, 0x6c, 0x6f, 0x67, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x4c, 0x6f, 0x67, 0x52, 0x03, 0x6c, 0x6f, 0x67, 0x12, 0x27, 0x0a, 0x05, 0x74, 0x68, 0x69, 0x72,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x68, 0x69, 0x72, 0x64, 0x52, 0x05, 0x74, 0x68, 0x69, 0x72,
	0x64, 0x12, 0x24, 0x0a, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x69, 0x67,
	0x6e, 0x52, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x44, 0x0a, 0x10, 0x77, 0x68, 0x69, 0x74, 0x65,
	0x5f, 0x6f, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x57,
	0x68, 0x69, 0x74, 0x65, 0x4f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x77,
	0x68, 0x69, 0x74, 0x65, 0x4f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a,
	0x03, 0x61, 0x70, 0x69, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6b, 0x72, 0x61,
	0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x69, 0x52, 0x03, 0x61, 0x70, 0x69,
	0x12, 0x27, 0x0a, 0x05, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x6b, 0x69,
	0x6c, 0x6c, 0x52, 0x05, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x12, 0x3a, 0x0a, 0x0c, 0x73, 0x6b, 0x69,
	0x6c, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x6b, 0x69,
	0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0b, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x40, 0x0a, 0x0e, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x75, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x6b, 0x69, 0x6c, 0x6c,
	0x73, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x6f, 0x6f, 0x74, 0x73, 0x74, 0x72, 0x61, 0x70, 0x2e, 0x53,
	0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x73, 0x6b, 0x69, 0x6c,
	0x6c, 0x73, 0x12, 0x65, 0x0a, 0x16, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x18, 0x1a, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x42, 0x6f, 0x6f, 0x74, 0x73, 0x74, 0x72, 0x61, 0x70, 0x2e, 0x56, 0x6f, 0x69, 0x63, 0x65, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x70, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x14, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x70, 0x73, 0x1a, 0x4d, 0x0a, 0x0b, 0x53, 0x6b, 0x69,
	0x6c, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x28, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x6d, 0x0a, 0x19, 0x56, 0x6f, 0x69, 0x63,
	0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x70, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x56, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb8, 0x02, 0x0a, 0x06, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x12, 0x2b, 0x0a, 0x04, 0x68, 0x74, 0x74, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x52, 0x04, 0x68, 0x74, 0x74, 0x70, 0x12,
	0x2b, 0x0a, 0x04, 0x67, 0x72, 0x70, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x47, 0x52, 0x50, 0x43, 0x52, 0x04, 0x67, 0x72, 0x70, 0x63, 0x1a, 0x69, 0x0a, 0x04,
	0x48, 0x54, 0x54, 0x50, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12,
	0x0a, 0x04, 0x61, 0x64, 0x64, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64,
	0x64, 0x72, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07,
	0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x1a, 0x69, 0x0a, 0x04, 0x47, 0x52, 0x50, 0x43, 0x12,
	0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x64,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64, 0x64, 0x72, 0x12, 0x33, 0x0a,
	0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x22, 0xba, 0x0d, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x35, 0x0a, 0x08, 0x64,
	0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61,
	0x73, 0x65, 0x12, 0x2c, 0x0a, 0x05, 0x72, 0x65, 0x64, 0x69, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x2e, 0x52, 0x65, 0x64, 0x69, 0x73, 0x52, 0x05, 0x72, 0x65, 0x64, 0x69, 0x73,
	0x12, 0x38, 0x0a, 0x09, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x52,
	0x09, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x37, 0x0a, 0x0b, 0x71, 0x75,
	0x65, 0x72, 0x79, 0x5f, 0x6b, 0x61, 0x66, 0x6b, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x2e, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x52, 0x0a, 0x71, 0x75, 0x65, 0x72, 0x79, 0x4b, 0x61,
	0x66, 0x6b, 0x61, 0x12, 0x40, 0x0a, 0x10, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x6b, 0x61, 0x66,
	0x6b, 0x61, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e,
	0x4b, 0x61, 0x66, 0x6b, 0x61, 0x52, 0x0e, 0x71, 0x75, 0x65, 0x72, 0x79, 0x4b, 0x61, 0x66, 0x6b,
	0x61, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0x3a, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x1a, 0x9d, 0x02, 0x0a, 0x05, 0x52, 0x65, 0x64, 0x69, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x64, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64, 0x64, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x64, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x02, 0x64, 0x62, 0x12, 0x3c, 0x0a, 0x0c, 0x64, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x64, 0x69, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x12, 0x3c, 0x0a, 0x0c, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x72, 0x65, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x12, 0x3e, 0x0a, 0x0d, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x77, 0x72, 0x69, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x1a, 0xc6, 0x05, 0x0a, 0x09, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x12,
	0x2d, 0x0a, 0x02, 0x64, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6b, 0x72,
	0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x45, 0x6d,
	0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x42, 0x52, 0x02, 0x64, 0x62, 0x12, 0x3c,
	0x0a, 0x07, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x2e, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x65, 0x6e, 0x63,
	0x65, 0x6e, 0x74, 0x52, 0x07, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x06,
	0x64, 0x62, 0x5f, 0x66, 0x61, 0x71, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6b,
	0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x45,
	0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x62, 0x46, 0x61, 0x71, 0x52, 0x05,
	0x64, 0x62, 0x46, 0x61, 0x71, 0x12, 0x40, 0x0a, 0x09, 0x64, 0x62, 0x5f, 0x68, 0x6f, 0x74, 0x66,
	0x69, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f,
	0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x45, 0x6d, 0x62, 0x65, 0x64,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x62, 0x48, 0x6f, 0x74, 0x66, 0x69, 0x78, 0x52, 0x08, 0x64,
	0x62, 0x48, 0x6f, 0x74, 0x66, 0x69, 0x78, 0x1a, 0x20, 0x0a, 0x02, 0x44, 0x42, 0x12, 0x1a, 0x0a,
	0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x1a, 0x31, 0x0a, 0x07, 0x54, 0x65, 0x6e,
	0x63, 0x65, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x1a, 0xaf, 0x01, 0x0a,
	0x05, 0x44, 0x62, 0x46, 0x61, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61,
	0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61,
	0x73, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x61, 0x71, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x66, 0x61, 0x71, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x61, 0x71, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f,
	0x6c, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x66, 0x61, 0x71, 0x54, 0x68, 0x72,
	0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x1a, 0xc9,
	0x01, 0x0a, 0x08, 0x44, 0x62, 0x48, 0x6f, 0x74, 0x66, 0x69, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x64,
	0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64,
	0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x61, 0x71, 0x5f, 0x74, 0x68, 0x72,
	0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x66, 0x61,
	0x71, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x1a, 0xf2, 0x02, 0x0a, 0x05, 0x4b,
	0x61, 0x66, 0x6b, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x62, 0x6f, 0x6f, 0x74, 0x73, 0x74, 0x72,
	0x61, 0x70, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x62, 0x6f, 0x6f, 0x74, 0x73, 0x74, 0x72, 0x61, 0x70, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12,
	0x26, 0x0a, 0x0f, 0x73, 0x73, 0x6c, 0x5f, 0x63, 0x61, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x73, 0x6c, 0x43, 0x61, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x61, 0x73, 0x6c, 0x5f,
	0x6d, 0x65, 0x63, 0x68, 0x61, 0x6e, 0x69, 0x73, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x73, 0x61, 0x73, 0x6c, 0x4d, 0x65, 0x63, 0x68, 0x61, 0x6e, 0x69, 0x73, 0x6d, 0x12, 0x23,
	0x0a, 0x0d, 0x73, 0x61, 0x73, 0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x61, 0x73, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x61, 0x73, 0x6c, 0x5f, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x61, 0x73, 0x6c,
	0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x75, 0x74, 0x6f,
	0x5f, 0x61, 0x63, 0x6b, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x61, 0x75, 0x74, 0x6f,
	0x41, 0x63, 0x6b, 0x12, 0x2a, 0x0a, 0x11, 0x6d, 0x61, 0x78, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f,
	0x6d, 0x61, 0x78, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x79, 0x74, 0x65, 0x73, 0x22,
	0xf1, 0x02, 0x0a, 0x0b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12,
	0x3b, 0x0a, 0x06, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x1a, 0x5c, 0x0a, 0x0d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x4b, 0x0a, 0x0e, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x73, 0x1a, 0x4b, 0x0a, 0x0c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x1a, 0x60, 0x0a, 0x0b, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x3b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x90, 0x01, 0x0a, 0x03, 0x4c, 0x6f, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6d, 0x61, 0x78, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x42, 0x61, 0x63, 0x6b, 0x75,
	0x70, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x6d, 0x61, 0x78, 0x41, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f,
	0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x63, 0x6f,
	0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x22, 0xd6, 0x17, 0x0a, 0x05, 0x54, 0x68, 0x69, 0x72, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x55, 0x72, 0x6c, 0x12, 0x1d, 0x0a,
	0x0a, 0x6e, 0x6c, 0x75, 0x5f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6e, 0x6c, 0x75, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x12, 0x25, 0x0a, 0x0e,
	0x6e, 0x6c, 0x75, 0x34, 0x6c, 0x75, 0x69, 0x5f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x6c, 0x75, 0x34, 0x6c, 0x75, 0x69, 0x4f, 0x72, 0x69,
	0x67, 0x69, 0x6e, 0x12, 0x37, 0x0a, 0x0b, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x5f, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f,
	0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4c, 0x65, 0x67, 0x61, 0x6c, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x52, 0x0a, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x3a, 0x0a, 0x0a,
	0x6c, 0x6c, 0x6d, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x68,
	0x69, 0x72, 0x64, 0x2e, 0x4c, 0x6c, 0x6d, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x52, 0x09, 0x6c,
	0x6c, 0x6d, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x78, 0x50, 0x61, 0x64,
	0x31, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x78, 0x50,
	0x61, 0x64, 0x31, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x05, 0x78, 0x50, 0x61, 0x64,
	0x31, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x68, 0x69, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x55, 0x72, 0x6c, 0x52, 0x05, 0x78, 0x50, 0x61, 0x64, 0x31, 0x12, 0x1e, 0x0a, 0x0a, 0x78,
	0x50, 0x61, 0x64, 0x32, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x78, 0x50, 0x61, 0x64, 0x32, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x05, 0x78,
	0x50, 0x61, 0x64, 0x32, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6b, 0x72, 0x61,
	0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x68, 0x69, 0x72, 0x64, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x52, 0x05, 0x78, 0x50, 0x61, 0x64, 0x32, 0x12, 0x2e,
	0x0a, 0x13, 0x6e, 0x6c, 0x75, 0x34, 0x6c, 0x75, 0x69, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6e, 0x6c, 0x75,
	0x34, 0x6c, 0x75, 0x69, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x12, 0x3d,
	0x0a, 0x0b, 0x69, 0x70, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x54, 0x68, 0x69, 0x72, 0x64, 0x2e, 0x49, 0x50, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69,
	0x73, 0x52, 0x0a, 0x69, 0x70, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x12, 0x26, 0x0a,
	0x0f, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x74, 0x74, 0x73,
	0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x57, 0x6f,
	0x72, 0x64, 0x54, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x5f, 0x74, 0x74, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x54, 0x74, 0x73, 0x12, 0x44, 0x0a, 0x10, 0x77, 0x68, 0x69, 0x74,
	0x65, 0x5f, 0x65, 0x78, 0x61, 0x6d, 0x5f, 0x6a, 0x75, 0x64, 0x67, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x57, 0x68, 0x69, 0x74, 0x65, 0x45, 0x78, 0x61, 0x6d, 0x4a, 0x75, 0x64, 0x67, 0x65, 0x52, 0x0e,
	0x77, 0x68, 0x69, 0x74, 0x65, 0x45, 0x78, 0x61, 0x6d, 0x4a, 0x75, 0x64, 0x67, 0x65, 0x12, 0x2c,
	0x0a, 0x12, 0x6c, 0x75, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6c, 0x75, 0x69, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x27, 0x0a, 0x10,
	0x78, 0x73, 0x5f, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x5f, 0x68, 0x75, 0x62, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x78, 0x73, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x48,
	0x75, 0x62, 0x55, 0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x78, 0x50, 0x6f, 0x64, 0x41, 0x70, 0x70,
	0x49, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x78, 0x50, 0x6f, 0x64, 0x41, 0x70,
	0x70, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x78, 0x50, 0x61, 0x64, 0x32, 0x4c, 0x69, 0x74, 0x65,
	0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x78, 0x50, 0x61,
	0x64, 0x32, 0x4c, 0x69, 0x74, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x78,
	0x69, 0x61, 0x6f, 0x73, 0x69, 0x5f, 0x72, 0x61, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x78, 0x69, 0x61, 0x6f, 0x73, 0x69, 0x52, 0x61, 0x67, 0x55, 0x72,
	0x6c, 0x12, 0x45, 0x0a, 0x0c, 0x6d, 0x6f, 0x62, 0x62, 0x79, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d,
	0x65, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x68, 0x69, 0x72, 0x64, 0x2e, 0x4d, 0x6f, 0x62, 0x62, 0x79,
	0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x6d, 0x6f, 0x62,
	0x62, 0x79, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x78, 0x50, 0x61, 0x64,
	0x31, 0x56, 0x32, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x78, 0x50, 0x61, 0x64, 0x31, 0x56, 0x32, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x08,
	0x6e, 0x6c, 0x70, 0x5f, 0x66, 0x75, 0x6e, 0x63, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4e, 0x6c, 0x70, 0x46,
	0x75, 0x6e, 0x63, 0x52, 0x07, 0x6e, 0x6c, 0x70, 0x46, 0x75, 0x6e, 0x63, 0x12, 0x24, 0x0a, 0x0e,
	0x78, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x78, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x55,
	0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x61, 0x69, 0x6b, 0x65, 0x5f, 0x72, 0x61, 0x67, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x69, 0x6b, 0x65,
	0x52, 0x61, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x27, 0x0a, 0x03, 0x6c, 0x6c, 0x6d, 0x18, 0x19, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x54, 0x68, 0x69, 0x72, 0x64, 0x2e, 0x4c, 0x6c, 0x6d, 0x52, 0x03, 0x6c, 0x6c, 0x6d, 0x12,
	0x30, 0x0a, 0x14, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x64,
	0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x72,
	0x6c, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6d,
	0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x12,
	0x1d, 0x0a, 0x0a, 0x6f, 0x63, 0x72, 0x5f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x18, 0x1c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x63, 0x72, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x12, 0x3e,
	0x0a, 0x1c, 0x6d, 0x61, 0x74, 0x68, 0x5f, 0x67, 0x70, 0x74, 0x5f, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x79, 0x5f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x18, 0x1d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x6d, 0x61, 0x74, 0x68, 0x47, 0x70, 0x74, 0x41, 0x73, 0x6b,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x79, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x12, 0x2c,
	0x0a, 0x12, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f,
	0x68, 0x6f, 0x73, 0x74, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x0a,
	0x6d, 0x61, 0x74, 0x68, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4d, 0x61,
	0x74, 0x68, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x68, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x78, 0x50, 0x61, 0x64, 0x32, 0x41, 0x70, 0x70, 0x49, 0x64,
	0x5f, 0x71, 0x69, 0x6a, 0x69, 0x61, 0x6e, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x78,
	0x50, 0x61, 0x64, 0x32, 0x41, 0x70, 0x70, 0x49, 0x64, 0x51, 0x69, 0x6a, 0x69, 0x61, 0x6e, 0x12,
	0x2b, 0x0a, 0x12, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x5f, 0x77, 0x65,
	0x62, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x6c, 0x6c,
	0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x57, 0x65, 0x62, 0x55, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x6f, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x74, 0x74, 0x73, 0x18, 0x22, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x6f, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x74, 0x73, 0x12, 0x4a, 0x0a, 0x22, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x75,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x3d, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x64, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x24, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6b, 0x72,
	0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x68, 0x69, 0x72, 0x64, 0x2e, 0x41,
	0x70, 0x70, 0x49, 0x64, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x4d, 0x61, 0x70, 0x12, 0x40, 0x0a, 0x1e, 0x78, 0x75, 0x65, 0x5f, 0x6c, 0x69,
	0x61, 0x6e, 0x5f, 0x6a, 0x69, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x73,
	0x5f, 0x77, 0x65, 0x62, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x25, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18,
	0x78, 0x75, 0x65, 0x4c, 0x69, 0x61, 0x6e, 0x4a, 0x69, 0x41, 0x6c, 0x6c, 0x53, 0x6b, 0x69, 0x6c,
	0x6c, 0x73, 0x57, 0x65, 0x62, 0x55, 0x72, 0x6c, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x61, 0x64,
	0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x61, 0x64, 0x62, 0x6f, 0x6f, 0x6b, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x37, 0x0a, 0x18, 0x72, 0x65, 0x61, 0x64,
	0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x27, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x72, 0x65, 0x61, 0x64,
	0x62, 0x6f, 0x6f, 0x6b, 0x4e, 0x6f, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x55, 0x72,
	0x6c, 0x12, 0x34, 0x0a, 0x16, 0x72, 0x65, 0x61, 0x64, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x28, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x14, 0x72, 0x65, 0x61, 0x64, 0x62, 0x6f, 0x6f, 0x6b, 0x44, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x69, 0x6c, 0x5f,
	0x74, 0x74, 0x73, 0x18, 0x29, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x74, 0x61, 0x69, 0x6c, 0x54,
	0x74, 0x73, 0x12, 0x31, 0x0a, 0x15, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x2a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x63, 0x68, 0x61, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x46, 0x65,
	0x65, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x26, 0x0a, 0x0f, 0x68, 0x6f, 0x6d, 0x65, 0x5f, 0x77, 0x6f,
	0x72, 0x6b, 0x5f, 0x64, 0x6f, 0x75, 0x64, 0x69, 0x18, 0x2b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d,
	0x68, 0x6f, 0x6d, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x44, 0x6f, 0x75, 0x64, 0x69, 0x12, 0x2d, 0x0a,
	0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x1a, 0x4e, 0x0a, 0x0a,
	0x49, 0x50, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05,
	0x72, 0x65, 0x74, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x65, 0x74,
	0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x1a, 0xbe, 0x05, 0x0a,
	0x09, 0x4c, 0x6c, 0x6d, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0c, 0x62,
	0x61, 0x69, 0x6b, 0x65, 0x5f, 0x74, 0x6d, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x62, 0x61, 0x69, 0x6b, 0x65, 0x54, 0x6d, 0x70, 0x49, 0x64, 0x12, 0x1e, 0x0a,
	0x0b, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x74, 0x6d, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x74, 0x54, 0x6d, 0x70, 0x49, 0x64, 0x12, 0x26, 0x0a,
	0x0f, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x74, 0x74, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x54, 0x74, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11,
	0x62, 0x61, 0x69, 0x6b, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x74, 0x74,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x69, 0x6b, 0x65, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x54, 0x74, 0x73, 0x12, 0x1c, 0x0a, 0x0a, 0x73, 0x75, 0x67, 0x5f,
	0x74, 0x6d, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x75,
	0x67, 0x54, 0x6d, 0x70, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f,
	0x74, 0x6d, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x54, 0x6d, 0x70, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x75, 0x6c, 0x6c,
	0x5f, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0e, 0x66, 0x75, 0x6c, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x32, 0x0a, 0x16, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x5f,
	0x62, 0x61, 0x69, 0x6b, 0x65, 0x5f, 0x74, 0x6d, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x66, 0x75, 0x6c, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x42, 0x61, 0x69, 0x6b,
	0x65, 0x54, 0x6d, 0x70, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x15, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x76,
	0x69, 0x65, 0x77, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x74, 0x6d, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x66, 0x75, 0x6c, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x43,
	0x68, 0x61, 0x74, 0x54, 0x6d, 0x70, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x15, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x43, 0x68, 0x61, 0x74, 0x50, 0x75, 0x6e, 0x63, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x43,
	0x68, 0x61, 0x74, 0x50, 0x75, 0x6e, 0x63, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e,
	0x0a, 0x14, 0x78, 0x73, 0x32, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x70, 0x6c, 0x75, 0x73, 0x5f,
	0x74, 0x6d, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x78, 0x73,
	0x32, 0x43, 0x68, 0x61, 0x74, 0x50, 0x6c, 0x75, 0x73, 0x54, 0x6d, 0x70, 0x49, 0x64, 0x12, 0x30,
	0x0a, 0x15, 0x78, 0x73, 0x32, 0x5f, 0x62, 0x61, 0x69, 0x6b, 0x65, 0x5f, 0x70, 0x6c, 0x75, 0x73,
	0x5f, 0x74, 0x6d, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x78,
	0x73, 0x32, 0x42, 0x61, 0x69, 0x6b, 0x65, 0x50, 0x6c, 0x75, 0x73, 0x54, 0x6d, 0x70, 0x49, 0x64,
	0x12, 0x2b, 0x0a, 0x12, 0x78, 0x73, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x73, 0x65, 0x65, 0x6b, 0x5f,
	0x74, 0x6d, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x78, 0x73,
	0x44, 0x65, 0x65, 0x70, 0x73, 0x65, 0x65, 0x6b, 0x54, 0x6d, 0x70, 0x49, 0x64, 0x12, 0x3e, 0x0a,
	0x1c, 0x78, 0x73, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x73, 0x65, 0x65, 0x6b, 0x5f, 0x65, 0x78, 0x65,
	0x72, 0x63, 0x69, 0x73, 0x65, 0x73, 0x5f, 0x74, 0x6d, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x18, 0x78, 0x73, 0x44, 0x65, 0x65, 0x70, 0x73, 0x65, 0x65, 0x6b, 0x45,
	0x78, 0x65, 0x72, 0x63, 0x69, 0x73, 0x65, 0x73, 0x54, 0x6d, 0x70, 0x49, 0x64, 0x1a, 0x43, 0x0a,
	0x03, 0x4c, 0x6c, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0e,
	0x6c, 0x6c, 0x6d, 0x5f, 0x73, 0x75, 0x67, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6c, 0x6c, 0x6d, 0x53, 0x75, 0x67, 0x53, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x1a, 0x3e, 0x0a, 0x10, 0x4d, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x3b, 0x0a, 0x0d, 0x41, 0x70, 0x70, 0x49, 0x64, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xed, 0x03, 0x0a, 0x07, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x39, 0x0a, 0x19, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x77, 0x68,
	0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x16,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x57, 0x68, 0x69,
	0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x17, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x73, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x52, 0x6f, 0x6c, 0x65, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x39, 0x0a,
	0x19, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x5f, 0x75, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x74,
	0x74, 0x73, 0x5f, 0x78, 0x69, 0x61, 0x6f, 0x7a, 0x68, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x16, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x55, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x74,
	0x73, 0x58, 0x69, 0x61, 0x6f, 0x7a, 0x68, 0x61, 0x12, 0x37, 0x0a, 0x18, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x5f, 0x75, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x74, 0x73, 0x5f, 0x78, 0x69,
	0x61, 0x6f, 0x79, 0x69, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x15, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x55, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x74, 0x73, 0x58, 0x69, 0x61, 0x6f, 0x79,
	0x69, 0x12, 0x67, 0x0a, 0x18, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x5f, 0x6d, 0x61, 0x70, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x70, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x69, 0x64, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x15, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x4e, 0x61, 0x6d, 0x65,
	0x4d, 0x61, 0x70, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x69, 0x64, 0x12, 0x49, 0x0a, 0x22, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x77, 0x68, 0x69,
	0x74, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x74, 0x6f, 0x5f, 0x73, 0x6b, 0x69, 0x6c, 0x6c,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x1d, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x49,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f,
	0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x1a, 0x48, 0x0a, 0x1a, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x70, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x69, 0x64, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xbd, 0x02, 0x0a, 0x09, 0x4d, 0x61, 0x74, 0x68, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x22, 0x0a,
	0x0d, 0x70, 0x61, 0x69, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x69, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72,
	0x6c, 0x12, 0x25, 0x0a, 0x0f, 0x6d, 0x61, 0x74, 0x68, 0x5f, 0x67, 0x70, 0x74, 0x5f, 0x74, 0x6d,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x68,
	0x47, 0x70, 0x74, 0x54, 0x6d, 0x70, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x11, 0x6d, 0x61, 0x74, 0x68,
	0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x6d, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x6d, 0x61, 0x74, 0x68, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x6d,
	0x70, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x75, 0x69, 0x73, 0x68, 0x69, 0x77, 0x65, 0x6e,
	0x5f, 0x68, 0x35, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73,
	0x75, 0x69, 0x73, 0x68, 0x69, 0x77, 0x65, 0x6e, 0x48, 0x35, 0x55, 0x72, 0x6c, 0x12, 0x28, 0x0a,
	0x10, 0x64, 0x6f, 0x77, 0x6e, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x6f, 0x77, 0x6e, 0x77, 0x61, 0x72,
	0x64, 0x4d, 0x73, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x6d, 0x61, 0x74, 0x68, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x61, 0x75, 0x74, 0x6f, 0x4d, 0x61, 0x74, 0x68, 0x55, 0x72, 0x6c, 0x12, 0x20, 0x0a, 0x0c, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x61, 0x74, 0x68, 0x5f, 0x61, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x6f, 0x4d, 0x61, 0x74, 0x68, 0x41, 0x6b, 0x12, 0x20, 0x0a,
	0x0c, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x61, 0x74, 0x68, 0x5f, 0x73, 0x6b, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x6f, 0x4d, 0x61, 0x74, 0x68, 0x53, 0x6b, 0x22,
	0x85, 0x01, 0x0a, 0x0a, 0x4c, 0x65, 0x67, 0x61, 0x6c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x21,
	0x0a, 0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x68, 0x6f, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x69, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0xb1, 0x03, 0x0a, 0x0f, 0x54, 0x68, 0x69, 0x72,
	0x64, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x28, 0x0a, 0x10, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49,
	0x64, 0x73, 0x55, 0x72, 0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x55, 0x72,
	0x6c, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x68,
	0x6f, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x55, 0x72,
	0x6c, 0x12, 0x26, 0x0a, 0x0f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x73, 0x65,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x50, 0x61, 0x72, 0x73, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x28, 0x0a, 0x10, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x75, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x55, 0x72, 0x6c,
	0x12, 0x22, 0x0a, 0x0d, 0x73, 0x74, 0x75, 0x64, 0x79, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x75, 0x64, 0x79, 0x4c, 0x6f,
	0x67, 0x55, 0x72, 0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x55, 0x72, 0x6c,
	0x12, 0x24, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x16, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x5f,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x55, 0x73, 0x65,
	0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x22, 0x8c, 0x01, 0x0a, 0x04,
	0x53, 0x69, 0x67, 0x6e, 0x12, 0x44, 0x0a, 0x0c, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6b, 0x72, 0x61,
	0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x2e, 0x53, 0x69, 0x67,
	0x6e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x73,
	0x69, 0x67, 0x6e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x1a, 0x3e, 0x0a, 0x10, 0x53, 0x69,
	0x67, 0x6e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x73, 0x0a, 0x0e, 0x57, 0x68,
	0x69, 0x74, 0x65, 0x4f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x6e, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x1c, 0x0a, 0x0a, 0x7a, 0x78, 0x5f, 0x73, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x7a, 0x78, 0x53, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x41, 0x0a, 0x0e, 0x57, 0x68, 0x69, 0x74, 0x65, 0x45, 0x78, 0x61, 0x6d, 0x4a, 0x75, 0x64, 0x67,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x6e, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0xe4, 0x03, 0x0a, 0x07, 0x4e, 0x6c, 0x70, 0x46, 0x75, 0x6e, 0x63, 0x12, 0x1e,
	0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x12, 0x2d,
	0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x23, 0x0a,
	0x0d, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61,
	0x6c, 0x6c, 0x12, 0x32, 0x0a, 0x15, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63,
	0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x13, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x6c, 0x6c, 0x54,
	0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x3f, 0x0a, 0x0a, 0x64, 0x6f, 0x75, 0x5f, 0x64, 0x69,
	0x5f, 0x6d, 0x61, 0x70, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6b, 0x72, 0x61,
	0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4e, 0x6c, 0x70, 0x46, 0x75, 0x6e, 0x63, 0x2e,
	0x44, 0x6f, 0x75, 0x44, 0x69, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x64,
	0x6f, 0x75, 0x44, 0x69, 0x4d, 0x61, 0x70, 0x12, 0x28, 0x0a, 0x10, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x5f, 0x6e, 0x6c, 0x70, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0e, 0x71, 0x75, 0x65, 0x72, 0x79, 0x4e, 0x6c, 0x70, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x12, 0x20, 0x0a, 0x0c, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x6e, 0x6c, 0x70, 0x5f, 0x76,
	0x32, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x71, 0x75, 0x65, 0x72, 0x79, 0x4e, 0x6c,
	0x70, 0x56, 0x32, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x5f, 0x61, 0x70,
	0x69, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x41,
	0x70, 0x69, 0x12, 0x2c, 0x0a, 0x12, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x5f, 0x61, 0x70, 0x69,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10,
	0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x41, 0x70, 0x69, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74,
	0x1a, 0x57, 0x0a, 0x0d, 0x44, 0x6f, 0x75, 0x44, 0x69, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x30, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x44, 0x6f, 0x75, 0x44, 0x69, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc5, 0x01, 0x0a, 0x0e, 0x44, 0x6f,
	0x75, 0x44, 0x69, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x12, 0x59, 0x0a, 0x11,
	0x64, 0x6f, 0x75, 0x5f, 0x64, 0x69, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x61,
	0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x6f, 0x75, 0x44, 0x69, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x4d, 0x61, 0x70, 0x2e, 0x44, 0x6f, 0x75, 0x44, 0x69, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x64, 0x6f, 0x75, 0x44, 0x69, 0x49, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x1a, 0x58, 0x0a, 0x13, 0x44, 0x6f, 0x75, 0x44, 0x69,
	0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x2b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x6f, 0x75,
	0x44, 0x69, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x21, 0x0a, 0x09, 0x44, 0x6f, 0x75, 0x44, 0x69, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x22, 0xb8, 0x02, 0x0a, 0x05, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x12, 0x2e,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6b,
	0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x2e,
	0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x54, 0x61, 0x67, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0xfe,
	0x01, 0x0a, 0x08, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x54, 0x61, 0x67, 0x12, 0x15, 0x0a, 0x06, 0x74,
	0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x67,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x61, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a,
	0x0a, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53,
	0x6b, 0x69, 0x6c, 0x6c, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x54, 0x61, 0x67, 0x2e, 0x53, 0x6b,
	0x69, 0x6c, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x61,
	0x74, 0x61, 0x1a, 0x7b, 0x0a, 0x09, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x15, 0x0a, 0x06, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x49, 0x63, 0x6f, 0x6e, 0x22,
	0xfb, 0x04, 0x0a, 0x0b, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x42, 0x0a, 0x09, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x53, 0x6b, 0x69, 0x6c,
	0x6c, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x73, 0x6b, 0x69, 0x6c, 0x6c,
	0x4d, 0x61, 0x70, 0x1a, 0xc1, 0x03, 0x0a, 0x0f, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x58, 0x0a, 0x0b, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6b,
	0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x4c, 0x69, 0x73, 0x74, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73,
	0x74, 0x1a, 0xd3, 0x02, 0x0a, 0x0f, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x66, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x61,
	0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x1a,
	0x85, 0x01, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x20, 0x0a, 0x0c, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x54, 0x61, 0x67, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x54, 0x61, 0x67, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x1a, 0x64, 0x0a, 0x0d, 0x53, 0x6b, 0x69, 0x6c, 0x6c,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3d, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc8, 0x02,
	0x0a, 0x03, 0x41, 0x70, 0x69, 0x12, 0x36, 0x0a, 0x07, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x69, 0x2e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x1a, 0xb1, 0x01,
	0x0a, 0x09, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x41, 0x70, 0x69, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x74, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x72, 0x65, 0x74, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x1a, 0x55, 0x0a, 0x0c, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x41, 0x70, 0x69, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x41, 0x70, 0x69, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xe0, 0x04, 0x0a, 0x0d, 0x53, 0x75, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x5d, 0x0a, 0x12, 0x73, 0x75,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x66, 0x6f, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x46, 0x6f, 0x6e, 0x74, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x46, 0x6f, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x5a, 0x0a, 0x11, 0x73, 0x75, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6a, 0x7a, 0x78, 0x5f, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4a, 0x7a, 0x78, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4a, 0x7a, 0x78,
	0x43, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x0e, 0x6a, 0x7a, 0x78, 0x5f, 0x76, 0x31, 0x35,
	0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6a,
	0x7a, 0x78, 0x56, 0x31, 0x35, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x17, 0x6a,
	0x7a, 0x78, 0x5f, 0x76, 0x31, 0x35, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x5f, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x6a, 0x7a,
	0x78, 0x56, 0x31, 0x35, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x76,
	0x65, 0x72, 0x12, 0x66, 0x0a, 0x15, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6f, 0x76,
	0x65, 0x72, 0x6c, 0x61, 0x79, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53,
	0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x75, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4f, 0x76,
	0x65, 0x72, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x1a, 0x43, 0x0a, 0x15, 0x53, 0x75,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x46, 0x6f, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x42, 0x0a, 0x14, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4a, 0x7a, 0x78, 0x43, 0x6f, 0x76,
	0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x46, 0x0a, 0x18, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4f, 0x76,
	0x65, 0x72, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x6d, 0x0a, 0x06, 0x53,
	0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x12, 0x27, 0x0a, 0x05, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x05, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x12, 0x3a,
	0x0a, 0x0c, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0b, 0x73,
	0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0xea, 0x01, 0x0a, 0x18, 0x56,
	0x6f, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x72, 0x65, 0x5f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x72,
	0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x4d, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f,
	0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x56, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73, 0x2e, 0x56,
	0x6f, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x52, 0x08, 0x63, 0x6f, 0x6d,
	0x6d, 0x61, 0x6e, 0x64, 0x73, 0x1a, 0x60, 0x0a, 0x0c, 0x56, 0x6f, 0x69, 0x63, 0x65, 0x43, 0x6f,
	0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65,
	0x72, 0x79, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x42, 0x1c, 0x5a, 0x1a, 0x6c, 0x75, 0x69, 0x2d, 0x61,
	0x70, 0x69, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6e, 0x66,
	0x3b, 0x63, 0x6f, 0x6e, 0x66, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_internal_conf_conf_proto_rawDescOnce sync.Once
	file_internal_conf_conf_proto_rawDescData = file_internal_conf_conf_proto_rawDesc
)

func file_internal_conf_conf_proto_rawDescGZIP() []byte {
	file_internal_conf_conf_proto_rawDescOnce.Do(func() {
		file_internal_conf_conf_proto_rawDescData = protoimpl.X.CompressGZIP(file_internal_conf_conf_proto_rawDescData)
	})
	return file_internal_conf_conf_proto_rawDescData
}

var file_internal_conf_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 58)
var file_internal_conf_conf_proto_goTypes = []any{
	(*Bootstrap)(nil),                   // 0: kratos.api.Bootstrap
	(*Server)(nil),                      // 1: kratos.api.Server
	(*Data)(nil),                        // 2: kratos.api.Data
	(*ErrorHandle)(nil),                 // 3: kratos.api.ErrorHandle
	(*Log)(nil),                         // 4: kratos.api.Log
	(*Third)(nil),                       // 5: kratos.api.Third
	(*Filters)(nil),                     // 6: kratos.api.Filters
	(*MathAgent)(nil),                   // 7: kratos.api.MathAgent
	(*LegalCheck)(nil),                  // 8: kratos.api.LegalCheck
	(*ThirdContentUrl)(nil),             // 9: kratos.api.ThirdContentUrl
	(*Sign)(nil),                        // 10: kratos.api.Sign
	(*WhiteOsVersion)(nil),              // 11: kratos.api.WhiteOsVersion
	(*WhiteExamJudge)(nil),              // 12: kratos.api.WhiteExamJudge
	(*NlpFunc)(nil),                     // 13: kratos.api.NlpFunc
	(*DouDiIntentMap)(nil),              // 14: kratos.api.DouDiIntentMap
	(*DouDiList)(nil),                   // 15: kratos.api.DouDiList
	(*Skill)(nil),                       // 16: kratos.api.Skill
	(*SkillDetail)(nil),                 // 17: kratos.api.SkillDetail
	(*Api)(nil),                         // 18: kratos.api.Api
	(*SubjectConfig)(nil),               // 19: kratos.api.SubjectConfig
	(*Skills)(nil),                      // 20: kratos.api.Skills
	(*VoiceInteractionCommands)(nil),    // 21: kratos.api.VoiceInteractionCommands
	nil,                                 // 22: kratos.api.Bootstrap.SkillsEntry
	nil,                                 // 23: kratos.api.Bootstrap.VoiceInteractionTipsEntry
	(*Server_HTTP)(nil),                 // 24: kratos.api.Server.HTTP
	(*Server_GRPC)(nil),                 // 25: kratos.api.Server.GRPC
	(*Data_Database)(nil),               // 26: kratos.api.Data.Database
	(*Data_Redis)(nil),                  // 27: kratos.api.Data.Redis
	(*Data_Embedding)(nil),              // 28: kratos.api.Data.Embedding
	(*Data_Kafka)(nil),                  // 29: kratos.api.Data.Kafka
	(*Data_Embedding_DB)(nil),           // 30: kratos.api.Data.Embedding.DB
	(*Data_Embedding_Tencent)(nil),      // 31: kratos.api.Data.Embedding.Tencent
	(*Data_Embedding_DbFaq)(nil),        // 32: kratos.api.Data.Embedding.DbFaq
	(*Data_Embedding_DbHotfix)(nil),     // 33: kratos.api.Data.Embedding.DbHotfix
	(*ErrorHandle_ErrorMessages)(nil),   // 34: kratos.api.ErrorHandle.ErrorMessages
	(*ErrorHandle_ErrorMessage)(nil),    // 35: kratos.api.ErrorHandle.ErrorMessage
	nil,                                 // 36: kratos.api.ErrorHandle.HandleEntry
	(*Third_IPAnalysis)(nil),            // 37: kratos.api.Third.IPAnalysis
	(*Third_LlmPrompt)(nil),             // 38: kratos.api.Third.LlmPrompt
	(*Third_Llm)(nil),                   // 39: kratos.api.Third.Llm
	nil,                                 // 40: kratos.api.Third.MobbySchemeEntry
	nil,                                 // 41: kratos.api.Third.AppIdMapEntry
	nil,                                 // 42: kratos.api.Filters.FiltersNameMapVideoidEntry
	nil,                                 // 43: kratos.api.Sign.SignSecretsEntry
	nil,                                 // 44: kratos.api.NlpFunc.DouDiMapEntry
	nil,                                 // 45: kratos.api.DouDiIntentMap.DouDiIntentMapEntry
	(*Skill_SkillTag)(nil),              // 46: kratos.api.Skill.SkillTag
	(*Skill_SkillTag_SkillData)(nil),    // 47: kratos.api.Skill.SkillTag.SkillData
	(*SkillDetail_SkillDetailList)(nil), // 48: kratos.api.SkillDetail.SkillDetailList
	nil,                                 // 49: kratos.api.SkillDetail.SkillMapEntry
	(*SkillDetail_SkillDetailList_SkillDetailInfo)(nil),             // 50: kratos.api.SkillDetail.SkillDetailList.SkillDetailInfo
	(*SkillDetail_SkillDetailList_SkillDetailInfo_CommandData)(nil), // 51: kratos.api.SkillDetail.SkillDetailList.SkillDetailInfo.CommandData
	(*Api_TargetApi)(nil), // 52: kratos.api.Api.TargetApi
	nil,                   // 53: kratos.api.Api.MappingEntry
	nil,                   // 54: kratos.api.SubjectConfig.SubjectFontColorEntry
	nil,                   // 55: kratos.api.SubjectConfig.SubjectJzxCoverEntry
	nil,                   // 56: kratos.api.SubjectConfig.SubjectOverlayColorEntry
	(*VoiceInteractionCommands_VoiceCommand)(nil), // 57: kratos.api.VoiceInteractionCommands.VoiceCommand
	(*durationpb.Duration)(nil),                   // 58: google.protobuf.Duration
}
var file_internal_conf_conf_proto_depIdxs = []int32{
	1,  // 0: kratos.api.Bootstrap.server:type_name -> kratos.api.Server
	2,  // 1: kratos.api.Bootstrap.data:type_name -> kratos.api.Data
	3,  // 2: kratos.api.Bootstrap.error:type_name -> kratos.api.ErrorHandle
	4,  // 3: kratos.api.Bootstrap.log:type_name -> kratos.api.Log
	5,  // 4: kratos.api.Bootstrap.third:type_name -> kratos.api.Third
	10, // 5: kratos.api.Bootstrap.sign:type_name -> kratos.api.Sign
	11, // 6: kratos.api.Bootstrap.white_os_version:type_name -> kratos.api.WhiteOsVersion
	18, // 7: kratos.api.Bootstrap.api:type_name -> kratos.api.Api
	16, // 8: kratos.api.Bootstrap.skill:type_name -> kratos.api.Skill
	17, // 9: kratos.api.Bootstrap.skill_detail:type_name -> kratos.api.SkillDetail
	19, // 10: kratos.api.Bootstrap.subject_config:type_name -> kratos.api.SubjectConfig
	22, // 11: kratos.api.Bootstrap.skills:type_name -> kratos.api.Bootstrap.SkillsEntry
	23, // 12: kratos.api.Bootstrap.voice_interaction_tips:type_name -> kratos.api.Bootstrap.VoiceInteractionTipsEntry
	24, // 13: kratos.api.Server.http:type_name -> kratos.api.Server.HTTP
	25, // 14: kratos.api.Server.grpc:type_name -> kratos.api.Server.GRPC
	26, // 15: kratos.api.Data.database:type_name -> kratos.api.Data.Database
	27, // 16: kratos.api.Data.redis:type_name -> kratos.api.Data.Redis
	28, // 17: kratos.api.Data.embedding:type_name -> kratos.api.Data.Embedding
	29, // 18: kratos.api.Data.query_kafka:type_name -> kratos.api.Data.Kafka
	29, // 19: kratos.api.Data.query_kafka_list:type_name -> kratos.api.Data.Kafka
	36, // 20: kratos.api.ErrorHandle.handle:type_name -> kratos.api.ErrorHandle.HandleEntry
	8,  // 21: kratos.api.Third.legal_check:type_name -> kratos.api.LegalCheck
	38, // 22: kratos.api.Third.llm_prompt:type_name -> kratos.api.Third.LlmPrompt
	9,  // 23: kratos.api.Third.xPad1:type_name -> kratos.api.ThirdContentUrl
	9,  // 24: kratos.api.Third.xPad2:type_name -> kratos.api.ThirdContentUrl
	37, // 25: kratos.api.Third.ip_analysis:type_name -> kratos.api.Third.IPAnalysis
	12, // 26: kratos.api.Third.white_exam_judge:type_name -> kratos.api.WhiteExamJudge
	40, // 27: kratos.api.Third.mobby_scheme:type_name -> kratos.api.Third.MobbySchemeEntry
	13, // 28: kratos.api.Third.nlp_func:type_name -> kratos.api.NlpFunc
	39, // 29: kratos.api.Third.llm:type_name -> kratos.api.Third.Llm
	7,  // 30: kratos.api.Third.math_agent:type_name -> kratos.api.MathAgent
	41, // 31: kratos.api.Third.app_id_map:type_name -> kratos.api.Third.AppIdMapEntry
	6,  // 32: kratos.api.Third.filters:type_name -> kratos.api.Filters
	42, // 33: kratos.api.Filters.filters_name_map_videoid:type_name -> kratos.api.Filters.FiltersNameMapVideoidEntry
	43, // 34: kratos.api.Sign.sign_secrets:type_name -> kratos.api.Sign.SignSecretsEntry
	44, // 35: kratos.api.NlpFunc.dou_di_map:type_name -> kratos.api.NlpFunc.DouDiMapEntry
	45, // 36: kratos.api.DouDiIntentMap.dou_di_intent_map:type_name -> kratos.api.DouDiIntentMap.DouDiIntentMapEntry
	46, // 37: kratos.api.Skill.list:type_name -> kratos.api.Skill.SkillTag
	49, // 38: kratos.api.SkillDetail.skill_map:type_name -> kratos.api.SkillDetail.SkillMapEntry
	53, // 39: kratos.api.Api.mapping:type_name -> kratos.api.Api.MappingEntry
	54, // 40: kratos.api.SubjectConfig.subject_font_color:type_name -> kratos.api.SubjectConfig.SubjectFontColorEntry
	55, // 41: kratos.api.SubjectConfig.subject_jzx_cover:type_name -> kratos.api.SubjectConfig.SubjectJzxCoverEntry
	56, // 42: kratos.api.SubjectConfig.subject_overlay_color:type_name -> kratos.api.SubjectConfig.SubjectOverlayColorEntry
	16, // 43: kratos.api.Skills.skill:type_name -> kratos.api.Skill
	17, // 44: kratos.api.Skills.skill_detail:type_name -> kratos.api.SkillDetail
	57, // 45: kratos.api.VoiceInteractionCommands.commands:type_name -> kratos.api.VoiceInteractionCommands.VoiceCommand
	20, // 46: kratos.api.Bootstrap.SkillsEntry.value:type_name -> kratos.api.Skills
	21, // 47: kratos.api.Bootstrap.VoiceInteractionTipsEntry.value:type_name -> kratos.api.VoiceInteractionCommands
	58, // 48: kratos.api.Server.HTTP.timeout:type_name -> google.protobuf.Duration
	58, // 49: kratos.api.Server.GRPC.timeout:type_name -> google.protobuf.Duration
	58, // 50: kratos.api.Data.Redis.dial_timeout:type_name -> google.protobuf.Duration
	58, // 51: kratos.api.Data.Redis.read_timeout:type_name -> google.protobuf.Duration
	58, // 52: kratos.api.Data.Redis.write_timeout:type_name -> google.protobuf.Duration
	30, // 53: kratos.api.Data.Embedding.db:type_name -> kratos.api.Data.Embedding.DB
	31, // 54: kratos.api.Data.Embedding.tencent:type_name -> kratos.api.Data.Embedding.Tencent
	32, // 55: kratos.api.Data.Embedding.db_faq:type_name -> kratos.api.Data.Embedding.DbFaq
	33, // 56: kratos.api.Data.Embedding.db_hotfix:type_name -> kratos.api.Data.Embedding.DbHotfix
	35, // 57: kratos.api.ErrorHandle.ErrorMessages.error_messages:type_name -> kratos.api.ErrorHandle.ErrorMessage
	34, // 58: kratos.api.ErrorHandle.HandleEntry.value:type_name -> kratos.api.ErrorHandle.ErrorMessages
	14, // 59: kratos.api.NlpFunc.DouDiMapEntry.value:type_name -> kratos.api.DouDiIntentMap
	15, // 60: kratos.api.DouDiIntentMap.DouDiIntentMapEntry.value:type_name -> kratos.api.DouDiList
	47, // 61: kratos.api.Skill.SkillTag.skill_data:type_name -> kratos.api.Skill.SkillTag.SkillData
	50, // 62: kratos.api.SkillDetail.SkillDetailList.detail_list:type_name -> kratos.api.SkillDetail.SkillDetailList.SkillDetailInfo
	48, // 63: kratos.api.SkillDetail.SkillMapEntry.value:type_name -> kratos.api.SkillDetail.SkillDetailList
	51, // 64: kratos.api.SkillDetail.SkillDetailList.SkillDetailInfo.command_data:type_name -> kratos.api.SkillDetail.SkillDetailList.SkillDetailInfo.CommandData
	52, // 65: kratos.api.Api.MappingEntry.value:type_name -> kratos.api.Api.TargetApi
	66, // [66:66] is the sub-list for method output_type
	66, // [66:66] is the sub-list for method input_type
	66, // [66:66] is the sub-list for extension type_name
	66, // [66:66] is the sub-list for extension extendee
	0,  // [0:66] is the sub-list for field type_name
}

func init() { file_internal_conf_conf_proto_init() }
func file_internal_conf_conf_proto_init() {
	if File_internal_conf_conf_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_internal_conf_conf_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   58,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_internal_conf_conf_proto_goTypes,
		DependencyIndexes: file_internal_conf_conf_proto_depIdxs,
		MessageInfos:      file_internal_conf_conf_proto_msgTypes,
	}.Build()
	File_internal_conf_conf_proto = out.File
	file_internal_conf_conf_proto_rawDesc = nil
	file_internal_conf_conf_proto_goTypes = nil
	file_internal_conf_conf_proto_depIdxs = nil
}
