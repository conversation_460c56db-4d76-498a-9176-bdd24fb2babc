package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-errors/errors"
	"github.com/go-resty/resty/v2"
	"go.opentelemetry.io/otel/trace"

	v1 "lui-api/api/lui/v1"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	"lui-api/internal/pkg/utils"
	"lui-api/pkg/util"
)

const (
	ImageDetectInfoPath    = "/v2/question/inner/image-detect-info"
	AtmsQuestionSearchPath = "/v1/atms/multimodal/question/search"
)

// 查题技能map
var exercisesToolSkillMap = map[string]void{
	"exercises_tool":         {},
	"exercises_tool_answer":  {},
	"exercises_tool_step":    {},
	"question_understanding": {},
	"exam_question":          {},
	"search_knowledge":       {},
}

// ProcessExercisesSkill 处理查题相关的skill
func (u NluUsecase) ProcessExercisesSkill(ctx context.Context, req *v1.LuiNluRequest, skillList []dto.NLUSkillItem, visionInput *dto.VisionInput, functionCall dto.FunctionCall) []dto.NLUSkillItem {
	explainQuestionAgentListGrayHit := u.ExplainQuestionAgentListGrayHit(custom_context.GetXAppId(ctx), custom_context.GetXOsPkgDate(ctx), custom_context.GetXDeviceId(ctx))
	if !explainQuestionAgentListGrayHit {
		return skillList
	}

	// TODO

	if len(skillList) != 1 {
		return skillList
	}

	// 如果技能不是查题工具，不做处理直接返回
	if _, ok := exercisesToolSkillMap[skillList[0].Skill]; !ok {
		return skillList
	}

	if functionCall.MultiModalInfo == nil {
		return skillList
	}

	var (
		sessionID string
		queryType int
		mainBox   []int
		imageUrl  = functionCall.MultiModalInfo.ImageURL
		traceBox  []int
	)

	if visionInput.TraceImageInfo != nil {
		sessionID = visionInput.TraceImageInfo.SessionToken
		queryType = 2
	} else if visionInput.DesktopImageInfo != nil {
		imageUrl = visionInput.DesktopImageInfo.ImageURL
		traceBox = util.CoordinateTranslation(visionInput.DesktopImageInfo.FingerPos2ma)
		sessionID = visionInput.DesktopImageInfo.SessionToken
		mainBox = visionInput.DesktopImageInfo.MainBox
		queryType = 2
	}

	if imageUrl == "" || sessionID == "" {
		return skillList
	}

	if skillList[0].SlotDict.QuestionIndex != 0 {
		queryType = 1
		traceBox = []int{}
	}

	// 调用GetImageDetectInfo获取匹配结果
	detectReq := &dto.GetImageDetectInfoReq{
		SessionID:      sessionID,
		TalId:          req.TalId,
		QueryType:      queryType,
		ImageUrl:       imageUrl,
		PointPosition:  util.ConvertInt2Float(traceBox),
		MainBox:        util.ConvertInt2Float(mainBox),
		QuestionNumber: skillList[0].SlotDict.QuestionIndex,
	}

	matchResult, err := u.GetImageDetectInfo(ctx, detectReq)
	if err != nil {
		return skillList
	}

	if matchResult.MatchFlag {
		// 如果MixedModalData已存在，则扩展，否则创建新的
		if skillList[0].MixedModalData == nil {
			mixedModalData := map[string]interface{}{
				"image_url": utils.ReplaceURLDomain(functionCall.MultiModalInfo.ImageURL),
				"trace_box": functionCall.MultiModalInfo.TraceBox,
			}
			skillList[0].MixedModalData = mixedModalData
		}

		mixedModalData, ok := skillList[0].MixedModalData.(map[string]interface{})
		if ok {
			mixedModalData["qus_index"] = matchResult.QusIndex
			mixedModalData["qus_box"] = matchResult.QusBox
			skillList[0].MixedModalData = mixedModalData
		}
	}

	return skillList
}

// GetImageDetectInfo 调用接口获取图像检测信息
func (u NluUsecase) GetImageDetectInfo(ctx context.Context, req *dto.GetImageDetectInfoReq) (*dto.PageMatchDto, error) {
	u.log.WithContext(ctx).Infof("GetImageDetectInfo req: %+v", util.Marshal(req))

	resp, err := resty.New().OnBeforeRequest(func(client *resty.Client, r *resty.Request) error {
		client.SetRetryCount(0).
			SetTimeout(time.Second * time.Duration(u.third.NlpFunc.DialogApiTimeout)).
			SetHeaders(map[string]string{
				"Content-Type":      "application/json",
				"X-Genie-TraceId":   custom_context.GetTraceId(ctx),
				"X-Genie-DeviceId":  custom_context.GetXDeviceId(ctx),
				"X-Genie-AppId":     custom_context.GetXAppId(ctx),
				"X-Genie-Version":   custom_context.GetXVersion(ctx),
				"X-Genie-Osversion": custom_context.GetXOSVersion(ctx),
				"X-Genie-Platform":  custom_context.GetXPlatform(ctx),
				"X-Request-Id":      custom_context.GetTraceId(ctx),
				"Traceparent":       fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
			})
		return nil
	}).
		R().
		SetBody(req).
		Post(u.third.NlpFunc.DialogApi + ImageDetectInfoPath)

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, errors.Errorf("response status: %s", resp.Status())
	}

	u.log.WithContext(ctx).Infof("GetImageDetectInfo response: %s, latency: %d", string(resp.Body()), resp.Time().Milliseconds())

	var response dto.ImageDetectInfoResp
	if err = json.Unmarshal(resp.Body(), &response); err != nil {
		return nil, err
	}

	if response.Code != 0 {
		return nil, errors.Errorf("code: %d, error_msg: %s", response.Code, response.ErrorMsg)
	}

	return &response.Data, nil
}

// AtmsQuestionSearch 调用ATMS多模态问题检索接口
func (u NluUsecase) AtmsQuestionSearch(ctx context.Context, req *dto.AtmsQuestionSearchReq) (*dto.AtmsQuestionSearchResp, error) {
	u.log.WithContext(ctx).Infof("AtmsQuestionSearch req: %+v", util.Marshal(req))

	resp, err := resty.New().OnBeforeRequest(func(client *resty.Client, r *resty.Request) error {
		client.SetRetryCount(0).
			SetTimeout(time.Second * time.Duration(u.third.NlpFunc.DialogApiTimeout)).
			SetHeaders(map[string]string{
				"Content-Type":      "application/json",
				"X-Genie-TraceId":   custom_context.GetTraceId(ctx),
				"X-Genie-DeviceId":  custom_context.GetXDeviceId(ctx),
				"X-Genie-AppId":     custom_context.GetXAppId(ctx),
				"X-Genie-Version":   custom_context.GetXVersion(ctx),
				"X-Genie-Osversion": custom_context.GetXOSVersion(ctx),
				"X-Genie-Platform":  custom_context.GetXPlatform(ctx),
				"X-Request-Id":      custom_context.GetTraceId(ctx),
				"Traceparent":       fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
			})
		return nil
	}).
		R().
		SetBody(req).
		Post(u.third.NlpFunc.DialogApi + AtmsQuestionSearchPath)

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, errors.Errorf("response status: %s", resp.Status())
	}

	u.log.WithContext(ctx).Infof("AtmsQuestionSearch response: %s, latency: %d", string(resp.Body()), resp.Time().Milliseconds())

	var response dto.AtmsQuestionSearchResponse
	if err = json.Unmarshal(resp.Body(), &response); err != nil {
		return nil, err
	}

	if response.Code != 0 {
		return nil, errors.Errorf("code: %d, error_msg: %s", response.Code, response.ErrorMsg)
	}

	return &response.Data, nil
}
