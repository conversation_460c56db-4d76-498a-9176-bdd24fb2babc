package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/parkingwang/go-sign"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"go.opentelemetry.io/otel/trace"
	"lui-api/internal/common"
	"lui-api/internal/conf"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	"lui-api/internal/pkg/utils"
	"lui-api/pkg/util"
	"time"
)

type StudyLogBiz struct {
	log   *log.Helper
	third *conf.Third
	uBiz  *UCenterBiz
}

func NewStudyLogBiz(logger log.Logger, third *conf.Third, uBiz *UCenterBiz) *StudyLogBiz {
	return &StudyLogBiz{log: log.NewHelper(logger), third: third, uBiz: uBiz}
}

func (u *StudyLogBiz) GetResourceList(ctx context.Context, req *dto.StudyLogReq) (*dto.StudyLog, error) {
	if custom_context.GetXAppId(ctx) == u.third.XPad1AppId {
		return u.GetResourceListPad1(ctx, req)
	} else {
		return u.GetResourceListPad2(ctx, req)
	}
}

func (u *StudyLogBiz) GetResourceListPad1(ctx context.Context, req *dto.StudyLogReq) (*dto.StudyLog, error) {

	now := time.Now().Unix()
	signer := sign.NewGoSignerMd5()
	signer.SetAppId(u.third.XPad1.ContentAppId)
	signer.SetTimeStamp(now)
	params := make(map[string]interface{})
	params["tal_id"] = req.TalID
	params["subject"] = req.Subject
	params["cnt_type"] = req.CntType
	params["course_system"] = req.CourseSystem
	params["time_stamp"] = now

	signer.AddBody("tal_id", req.TalID)
	signer.AddBody("subject", cast.ToString(req.Subject))
	signer.AddBody("cnt_type", cast.ToString(req.CntType))
	signer.AddBody("course_system", cast.ToString(req.CourseSystem))
	signer.AddBody("time_stamp", cast.ToString(now))

	//参与签名的字段参数
	targetSignString := signer.GetSignBodyString()
	//计算得出的签名
	targetSign := signer.GetSignature()
	params["sign"] = targetSign

	header := &dto.ExactlyHeader{
		XRequestId:  custom_context.GetTraceId(ctx),
		XTalSn:      custom_context.GetXDeviceId(ctx),
		XTalVersion: custom_context.GetXVersion(ctx),
		Traceparent: fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
	}
	headers := util.StructToMap(header)
	headers["Content-Type"] = "application/json"
	u.log.WithContext(ctx).Infof("GetResourceList target_sign:%s,target_sign_string:%s; header: %+v", targetSign, targetSignString, header)
	ch := utils.CurlInit(ctx, time.Second*4, 1, u.log)
	resp, err := ch.ChRequest(ch.Req.SetHeaders(headers).SetBody(params)).Post(u.third.XPad1.StudyLogUrl)
	if err != nil {
		u.log.Infof("GetResourceList params: %+v", params)
		return nil, err
	}
	u.log.WithContext(ctx).Infof("GetResourceList resp: %s; params: %+v", string(resp.Body()), params)
	if resp.IsError() {
		return nil, errors.Errorf("resp status: %s", resp.Status())
	}

	var studyLogRes dto.StudyLogRes
	err = json.Unmarshal(resp.Body(), &studyLogRes)
	if err != nil {
		return nil, errors.Errorf("json param err: %v", err)
	}

	if studyLogRes.Errcode == 0 {
		return studyLogRes.Data, nil
	}

	return nil, errors.New(studyLogRes.Errmsg)
}

func (u *StudyLogBiz) GetResourceListPad2(ctx context.Context, req *dto.StudyLogReq) (*dto.StudyLog, error) {

	header := &dto.ExactlyHeader{
		XRequestId:  custom_context.GetTraceId(ctx),
		XTalSn:      custom_context.GetXDeviceId(ctx),
		XTalVersion: custom_context.GetXVersion(ctx),
		Traceparent: fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
	}
	headers := util.StructToMap(header)
	headers["Content-Type"] = "application/json"
	if custom_context.GetXAppId(ctx) != "" && custom_context.GetXAppId(ctx) == u.third.AppIdMap[common.XinRuiAppIdKey] {
		headers[common.XTalDeviceCode] = common.XinRuiDeviceCodeVal
	}

	ch := utils.CurlInit(ctx, time.Second*4, 1, u.log)
	resp, err := ch.ChRequest(ch.Req.SetHeaders(headers).SetBody(req)).Post(u.third.XPad2.StudyLogUrl)
	if err != nil {
		u.log.Infof("GetResourceList params: %+v", req)
		return nil, err
	}
	u.log.WithContext(ctx).Infof("GetResourceList resp: %s; params: %+v, header:%+v", string(resp.Body()), req, headers)
	if resp.IsError() {
		return nil, errors.Errorf("resp status: %s", resp.Status())
	}

	var studyLogRes dto.StudyLogRes
	err = json.Unmarshal(resp.Body(), &studyLogRes)
	if err != nil {
		return nil, errors.Errorf("json param err: %v", err)
	}

	if studyLogRes.Errcode == 0 {
		return studyLogRes.Data, nil
	}

	return nil, errors.New(studyLogRes.Errmsg)
}

// GetUserChoice 用户体系下的版本 （上次选择的版本）
func (u *StudyLogBiz) GetUserChoice(ctx context.Context, req *dto.UserCourseChoiceReq) ([]*dto.UserCourseChoiceResp, error) {

	var clientId string
	stringCmd := u.uBiz.Repo.RdbGetKey(ctx, req.TalID)
	if stringCmd != nil {
		clientId = stringCmd.String()
	}
	if "552403" == clientId {
		return u.GetUserChoicePad2(ctx, req)
	} else {
		return u.GetUserChoicePad1(ctx, req)
	}
}

// GetUserChoicePad1 用户体系下的版本 （上次选择的版本）
func (u *StudyLogBiz) GetUserChoicePad1(ctx context.Context, req *dto.UserCourseChoiceReq) ([]*dto.UserCourseChoiceResp, error) {
	now := time.Now().Unix()
	signer := sign.NewGoSignerMd5()
	signer.SetAppId(u.third.XPad1.ContentAppId)
	signer.SetTimeStamp(now)

	signer.AddBody("tal_id", req.TalID)
	signer.AddBody("subject", cast.ToString(req.Subject))
	signer.AddBody("grade", cast.ToString(req.Grade))
	signer.AddBody("course_system", cast.ToString(req.CourseSystem))
	signer.AddBody("time_stamp", cast.ToString(now))

	//计算得出的签名
	targetSign := signer.GetSignature()

	req.BaodianSignParam.TimeStamp = now
	req.BaodianSignParam.Sign = targetSign
	header := &dto.ExactlyHeader{
		XRequestId:  custom_context.GetTraceId(ctx),
		XTalSn:      req.DeviceId,
		XTalVersion: "",
		Traceparent: fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
	}

	ch := utils.CurlInit(ctx, time.Second*4, 1, u.log)
	resp, err := ch.ChRequest(ch.Req.SetHeaders(util.StructToMap(header)).SetBody(req)).Post(u.third.XPad1.ResourceChoiceUrl)
	if err != nil {
		u.log.Infof("GetUserChoice params: %+v", req)
		return nil, err
	}

	u.log.WithContext(ctx).Infof("GetUserChoice resp: %s; params: %+v,header:%+v", string(resp.Body()), req, header)
	if resp.IsError() {
		return nil, errors.Errorf("resp status: %s", resp.Status())
	}

	var br dto.BaseResponse
	err = json.Unmarshal(resp.Body(), &br)
	if err != nil {
		return nil, err
	}

	var data []*dto.UserCourseChoiceResp
	err = util.ConvertInterface(br.Data, &data)
	if err != nil {
		u.log.WithContext(ctx).Errorf("GetUserChoice convert err: %+v; data: %+v", err, br.Data)
	}
	return data, nil
}

// GetUserChoicePad2 用户体系下的版本 （上次选择的版本）
func (u *StudyLogBiz) GetUserChoicePad2(ctx context.Context, req *dto.UserCourseChoiceReq) ([]*dto.UserCourseChoiceResp, error) {

	header := &dto.ExactlyHeader{
		XRequestId:  custom_context.GetTraceId(ctx),
		XTalSn:      req.DeviceId,
		XTalVersion: "",
		Traceparent: fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
	}

	if custom_context.GetXAppId(ctx) != "" && custom_context.GetXAppId(ctx) == u.third.AppIdMap[common.XinRuiAppIdKey] {
		header.XTalDeviceCode = common.XinRuiDeviceCodeVal
	}

	ch := utils.CurlInit(ctx, time.Second*4, 1, u.log)
	resp, err := ch.ChRequest(ch.Req.SetHeaders(util.StructToMap(header)).SetBody(req)).Post(u.third.XPad2.ResourceChoiceUrl)
	if err != nil {
		u.log.Infof("GetUserChoice params: %+v", req)
		return nil, err
	}

	u.log.WithContext(ctx).Infof("GetUserChoice resp: %s; params: %+v", string(resp.Body()), req)
	if resp.IsError() {
		return nil, errors.Errorf("resp status: %s", resp.Status())
	}

	var br dto.BaseResponse
	err = json.Unmarshal(resp.Body(), &br)
	if err != nil {
		return nil, err
	}

	if br.Errcode != 0 {
		return nil, errors.Errorf("GetUserChoice code: %d; msg: %s", br.Errcode, br.Errmsg)
	}

	var data []*dto.UserCourseChoiceResp
	err = util.ConvertInterface(br.Data, &data)
	if err != nil {
		u.log.WithContext(ctx).Errorf("GetUserChoice convert err: %+v", err)
	}
	return data, nil
}
