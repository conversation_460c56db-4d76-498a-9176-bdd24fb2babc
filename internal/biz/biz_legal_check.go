package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"go.opentelemetry.io/otel/trace"
	"lui-api/internal/common"
	"math/rand"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	"lui-api/internal/conf"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
)

type LegalBizUsecase struct {
	embeddingDBRepo EmbeddingDBRepo
	log             *log.Helper
	conf            *conf.Third
}

const LegalCheckBizType = 1
const AbnormalError = "abnormal_error"

func NewLegalBizUsecase(logger log.Logger, third *conf.Third) *LegalBizUsecase {
	return &LegalBizUsecase{log: log.NewHelper(logger), conf: third}
}

type LegalCheckResp struct {
	ErrorCode int            `json:"error_code"`
	ErrorMsg  string         `json:"error_msg"`
	Data      LegalCheckData `json:"data"`
	TraceId   string         `json:"is_legal"`
}

type LegalCheckData struct {
	RiskLabel     string `json:"risk_label"`
	RiskLabelText string `json:"risk_label_text"`
	RiskLabel1    string `json:"riskLabel1"`
}

func (nc *LegalBizUsecase) LegalCheck(ctx context.Context, talID, text, mode string) (illegalType string, err error) {
	//bytes, _ := json.Marshal(source)
	//text := string(bytes)
	//text, _ = strconv.Unquote(strings.Replace(strconv.Quote(text), `\\u`, `\u`, -1))
	nc.log.WithContext(ctx).Infof("LegalCheckInput %s; mode: %s", text, mode)
	if strings.TrimSpace(text) == "" {
		return "", nil
	}

	reqBody := map[string]interface{}{
		"text":   text,
		"mode":   mode,
		"biz":    LegalCheckBizType,
		"sn":     custom_context.GetXDeviceId(ctx),
		"tal_id": talID,
	}
	header := map[string]string{
		"X-Genie-TraceId":   custom_context.GetTraceId(ctx),
		"X-Genie-DeviceId":  custom_context.GetXDeviceId(ctx),
		"X-Genie-ServiceId": nc.conf.LegalCheck.ServiceId,
		"Content-Type":      "application/json",
		"Traceparent":       fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
	}

	resp, err := resty.New().
		R().SetHeaders(header).
		SetBody(reqBody).
		Post(nc.conf.LegalCheck.Host)

	if err != nil {
		nc.log.WithContext(ctx).Errorf("调用LegalCheck失败, err: %+v", err)
		// 接口失败，判定为不通过
		return AbnormalError, nil
	}
	if resp.IsError() {
		nc.log.WithContext(ctx).Errorf("调用LegalCheck失败, err: %+v", resp.Error())
		// 接口失败，判定为不通过
		return AbnormalError, nil
	}

	var legalCheckResp LegalCheckResp
	err = json.Unmarshal(resp.Body(), &legalCheckResp)
	if err != nil {
		nc.log.WithContext(ctx).Errorf("调用LegalCheck失败, resp: %v，err: %v", resp, err)
		// 接口调用失败，判定为不通过
		return AbnormalError, nil
	}
	// 频控：，服务挂掉：120013，校验不通过：120012
	if legalCheckResp.ErrorCode != 0 {
		nc.log.WithContext(ctx).Infof("legalCheckRespIllegal resp: %+v", legalCheckResp)
		if legalCheckResp.ErrorCode == 120012 {
			return legalCheckResp.Data.RiskLabelText, nil
		}

		return AbnormalError, nil
	}

	return "", nil
}

func (nc *LegalBizUsecase) LegalCheckInfo(ctx context.Context, talID, text, mode string) (illegalType string, legalCheckResp LegalCheckResp, err error) {
	//bytes, _ := json.Marshal(source)
	//text := string(bytes)
	//text, _ = strconv.Unquote(strings.Replace(strconv.Quote(text), `\\u`, `\u`, -1))
	nc.log.WithContext(ctx).Infof("LegalCheckInput %s; mode: %s", text, mode)
	if strings.TrimSpace(text) == "" {
		return "", legalCheckResp, nil
	}

	reqBody := map[string]interface{}{
		"text":   text,
		"mode":   mode,
		"biz":    LegalCheckBizType,
		"sn":     custom_context.GetXDeviceId(ctx),
		"tal_id": talID,
	}
	header := map[string]string{
		"X-Genie-TraceId":   custom_context.GetTraceId(ctx),
		"X-Genie-DeviceId":  custom_context.GetXDeviceId(ctx),
		"X-Genie-ServiceId": nc.conf.LegalCheck.ServiceId,
		"Content-Type":      "application/json",
		"Traceparent":       fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
	}

	resp, err := resty.New().
		R().SetHeaders(header).
		SetBody(reqBody).
		Post(nc.conf.LegalCheck.Host)

	if err != nil {
		nc.log.WithContext(ctx).Errorf("调用LegalCheck失败, err: %+v", err)
		// 接口失败，判定为不通过
		return AbnormalError, legalCheckResp, nil
	}
	if resp.IsError() {
		nc.log.WithContext(ctx).Errorf("调用LegalCheck失败, err: %+v", resp.Error())
		// 接口失败，判定为不通过
		return AbnormalError, legalCheckResp, nil
	}

	//var legalCheckResp LegalCheckResp
	err = json.Unmarshal(resp.Body(), &legalCheckResp)
	if err != nil {
		nc.log.WithContext(ctx).Errorf("调用LegalCheck失败, resp: %v，err: %v", resp, err)
		// 接口调用失败，判定为不通过
		return AbnormalError, legalCheckResp, nil
	}
	// 频控：，服务挂掉：120013，校验不通过：120012
	if legalCheckResp.ErrorCode != 0 {
		nc.log.WithContext(ctx).Infof("legalCheckRespIllegal resp: %+v", legalCheckResp)
		if legalCheckResp.ErrorCode == 120012 {
			return legalCheckResp.Data.RiskLabelText, legalCheckResp, nil
		}

		return AbnormalError, legalCheckResp, nil
	}

	return "", legalCheckResp, nil
}

var checkSkillMap = map[string]struct{}{
	"chat":  {},
	"baike": {},
}

func (nc *LegalBizUsecase) CheckPadIfNeed(data *dto.NluData) (ret string) {
	if data == nil {
		return
	}
	if _, ok := checkSkillMap[data.Skill]; !ok {
		return
	}
	ret = data.TtsNorm + "\n" + data.TtsShow
	return
}

func (nc *LegalBizUsecase) CheckPad2IfNeed(data *dto.QueryPad2Resp) (ret string) {
	ssb := strings.Builder{}
	for _, item := range data.Data {
		if _, ok := checkSkillMap[item.Skill]; !ok {
			continue
		}
		ssb.WriteString(item.Data.TtsNorm)
		ssb.WriteString("\n")
		ssb.WriteString(item.Data.TtsShow)
		ssb.WriteString("\n")
	}
	ret = ssb.String()
	return
}

var illegalText = map[string]string{
	"涉政": "同学抱歉，这题超纲啦，我还没学会。可以换个问题哦～",
	"暴恐": "小朋友要注意了，我们应该反对任何形式的暴力行为！",
	"色情": "同学抱歉，这题超纲啦，我还没学会。可以换个问题哦～",
	"违禁": "小朋友要注意了，我们不可以做任何违法乱纪的行为！",
	"辱骂": "小朋友要注意了，我们应该文明友善，不可以辱骂他人哦！",
	"隐私": "小朋友要注意了，我们要保护好自己的隐私信息，不可以泄漏哦！",
}

func (nc *LegalBizUsecase) BuildPadDefaultIllegalResp(illegalType string, originRes *dto.NluData) *dto.NluData {
	ret := &dto.NluData{
		UserID:    "",
		RequestID: "",
		Skill:     "chat",
		Task:      "chat.small_talk",
		TtsShow:   illegalText[illegalType],
		TtsNorm:   illegalText[illegalType],

		OriginNluResp:  *originRes,
		LegalCheckType: illegalType,
	}

	return ret
}

func (nc *LegalBizUsecase) BuildPad2DefaultIllegalResp(illegalType string, originRes dto.QueryPad2Resp) dto.QueryPad2Resp {
	ret := dto.QueryPad2Resp{
		TtsShow: illegalText[illegalType],
		TtsNorm: illegalText[illegalType],
		Data: []*dto.Pad2SkillData{
			{
				Skill: "chat",
				Data: &dto.NLUSkillItem{
					Task:          "chat.small_talk",
					TtsShow:       illegalText[illegalType],
					TtsNorm:       illegalText[illegalType],
					Count:         1,
					IsResultValid: true,
				},
			},
		},
		ModelOutputIntent: "闲聊",

		OriginNluResp:     originRes,
		LegalCheckType:    illegalType,
		ControllerVersion: originRes.ControllerVersion,
	}
	return ret
}

func (nc *LegalBizUsecase) BuildPad2QueryIllegalResp(illegalType, bizType string, originRes dto.QueryPad2Resp) dto.QueryPad2Resp {
	queryTts := nc.GetLegalCheckTts(bizType)

	rand.Seed(time.Now().UnixNano())
	randomIndex := rand.Intn(len(queryTts))
	randTts := queryTts[randomIndex]

	ret := dto.QueryPad2Resp{
		TtsShow: randTts,
		TtsNorm: randTts,
		Data: []*dto.Pad2SkillData{
			{
				Skill: "chat",
				Data: &dto.NLUSkillItem{
					Task:           "chat.small_talk",
					TtsShow:        randTts,
					TtsNorm:        randTts,
					Count:          1,
					IsResultValid:  true,
					IsAccessingLLM: false,
				},
			},
		},
		ModelOutputIntent: "闲聊",

		OriginNluResp:     originRes,
		LegalCheckType:    illegalType,
		ControllerVersion: originRes.ControllerVersion,
	}
	return ret
}

func (nc *LegalBizUsecase) GetLegalCheckTts(bizType string) []string {
	queryTts := nc.conf.LlmPrompt.LegalCheckTts
	if strings.Contains(bizType, common.BizTypeAiTutor) {
		douDiMap := nc.conf.NlpFunc.DouDiMap["bella"]
		if douDiMap != nil && douDiMap.DouDiIntentMap["闲聊"] != nil {
			queryTts = douDiMap.DouDiIntentMap["闲聊"].Items
		}
	}
	return queryTts
}
