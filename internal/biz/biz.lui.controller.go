package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"go.opentelemetry.io/otel/trace"
	v1 "lui-api/api/lui/v1"
	"lui-api/internal/common"
	"lui-api/internal/conf"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	"lui-api/internal/pkg/utils"
	"lui-api/pkg/util"
	"time"
)

type LuiControllerBiz struct {
	log         *log.Helper
	third       *conf.Third
	contactRepo ContactRepo
}

func NewLuiControllerBiz(logger log.Logger, third *conf.Third, contactRepo ContactRepo) *LuiControllerBiz {
	return &LuiControllerBiz{log: log.NewHelper(logger), third: third, contactRepo: contactRepo}
}

type ContactRepo interface {
	GetContact(ctx context.Context, talID string) []*dto.ContactInfo
}

func (u *LuiControllerBiz) GetLuiFunctions(ctx context.Context, req *v1.LuiNluRequest, memoryContexts []*dto.XPodMemoryContextResp) ([]*dto.LuiFunction, error) {
	Contacts := u.contactRepo.GetContact(ctx, req.TalId)

	contactReq := make([]dto.Contact, 0)

	for _, info := range Contacts {
		contactReq = append(contactReq, dto.Contact{
			ContactID:   info.ContactID,
			ContactName: info.ContactName,
		})
	}

	if len(memoryContexts) == 0 {
		memoryContexts = make([]*dto.XPodMemoryContextResp, 0)
	}
	nluReq := dto.LuiControllerReq{
		RequestId: req.RequestId,
		SessionId: req.RequestId,
		Source:    "pod",
		AsrInput: dto.AsrInput{
			AsrInfo:   req.AsrInfo,
			AsrLen:    len(req.AsrInfo),
			AsrPinyin: req.AsrPinyin,
		},
		UserSystem: dto.UserSystemPad24LUI{
			Location: req.Location,
			Grade:    cast.ToInt(req.GradeId),
			Semester: dto.GetSemester(),
			TalId:    req.TalId,
			DeviceId: custom_context.GetXDeviceId(ctx),
		},
		Contacts:    contactReq,
		HistoryInfo: memoryContexts,
		PagePrompt:  req.PagePrompt,
	}

	u.log.WithContext(ctx).Infof("GetLuiFunctions req: %+v", util.Marshal(nluReq))

	if req.BizType == common.BizTypeCompanionLearn {
		nluReq.SceneCode = common.SceneCompanionLearn
	}
	header := &dto.ExactlyHeader{
		XRequestId:  custom_context.GetTraceId(ctx),
		XTalSn:      custom_context.GetXDeviceId(ctx),
		XTalVersion: "",
		Traceparent: fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
	}
	headers := util.StructToMap(header)
	headers["Content-Type"] = "application/json"
	ch := utils.CurlInit(ctx, time.Second*5, 0, u.log)
	resp, err := ch.ChRequest(ch.Req.SetHeaders(headers).SetBody(nluReq)).Post(u.third.LuiControllerUrl)
	if err != nil {
		u.log.Infof("GetLuiFunctions params: %+v", req)
		return nil, err
	}
	u.log.WithContext(ctx).Infof("GetLuiFunctions resp: %s; params: %+v", string(resp.Body()), req)
	if resp.IsError() {
		return nil, errors.Errorf("resp status: %s", resp.Status())
	}

	var controllerRes dto.LuiControllerRes
	u.log.WithContext(ctx).Infof("GetLuiFunctions resp:%s", string(resp.Body()))
	err = json.Unmarshal(resp.Body(), &controllerRes)
	if err != nil {
		return nil, errors.Errorf("json param err: %v", err)
	}

	return controllerRes.Data.FuncList, nil
}
