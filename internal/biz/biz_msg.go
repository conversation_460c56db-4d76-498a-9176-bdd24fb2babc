package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"go.opentelemetry.io/otel/trace"
	"lui-api/internal/conf"
	"lui-api/internal/pkg/custom_context"
	"lui-api/internal/pkg/utils"
	"time"
)

type MsgUsecase struct {
	log   *log.Helper
	third *conf.Third
}

func NewMsgUsecase(logger log.Logger, third *conf.Third) *MsgUsecase {
	return &MsgUsecase{
		log:   log.NewHelper(logger),
		third: third,
	}
}

type HmiMsgReq struct {
	TraceId string `json:"trace_id"`
	Topic   string `json:"topic"`
	Body    struct {
		TraceId string      `json:"trace_id"`
		Topic   string      `json:"topic"`
		Payload interface{} `json:"payload"`
	} `json:"body"`
}

func (u MsgUsecase) DownwardHmiMsg(ctx context.Context, deviceId string, himMsgReq HmiMsgReq) error {

	headers := map[string]string{
		"Content-Type":     "application/json",
		"X-Genie-DeviceId": deviceId,
		"X-Request-Id":     custom_context.GetTraceId(ctx),
		"Traceparent":      fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
	}
	ch := utils.CurlInit(ctx, time.Second*3, 1, u.log)
	body, _ := json.Marshal(himMsgReq)
	u.log.WithContext(ctx).Infof("downwardMsg params: %s", string(body))
	resp, err := ch.ChRequest(ch.Req.SetHeaders(headers).SetBody(himMsgReq)).Post(u.third.MathAgent.DownwardMsgUrl)
	if err != nil {
		u.log.WithContext(ctx).Errorf("downwardMsg params: %+v, err: %v", himMsgReq, err)
		return err
	}

	if resp.IsError() {
		return errors.Errorf("resp status: %s", resp.Status())
	}

	return nil
}
