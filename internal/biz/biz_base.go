package biz

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cast"
	"lui-api/internal/common"
	"lui-api/internal/conf"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	util "lui-api/internal/pkg/utils"
	"time"
)

// BaseUsecase 基础接口 用户数据等
type BaseUsecase struct {
	log   *log.Helper
	third *conf.Third
	repo  UCenterRepo
}

func NewBaseUsecase(logger log.Logger, third *conf.Third, repo UCenterRepo) *BaseUsecase {
	return &BaseUsecase{log: log.NewHelper(logger), third: third, repo: repo}
}

func (u *BaseUsecase) GetResourceConf(ctx context.Context) (*dto.ContentConf, error) {
	if custom_context.GetXAppId(ctx) == u.third.XPad1AppId {
		return u.GetResourceConfPad1(ctx)
	} else {
		return u.GetResourceConfPad2(ctx)
	}
}

func (u *BaseUsecase) GetResourceConfPad1(ctx context.Context) (*dto.ContentConf, error) {

	var resourceConf *dto.ContentConf

	stringCmd := u.repo.RdbGetKey(ctx, common.ContentResourceConfKey)

	if stringCmd != nil && stringCmd.Val() != "" {
		err := json.Unmarshal([]byte(stringCmd.Val()), &resourceConf)
		if err != nil {
			u.log.WithContext(ctx).Errorf("Unmarshal err: %+v", err)
		} else {
			return resourceConf, nil
		}
	}

	ch := util.CurlInit(ctx, time.Second*4, 1, u.log)

	headers := make(map[string]string)
	postResp, err := ch.ChRequest(ch.Req.SetHeaders(headers)).Post(u.third.XPad1.ResourceConfUrl)

	if err != nil {
		return nil, err
	}
	u.log.WithContext(ctx).Infof("GetResourceConf resp:%s", string(postResp.Body()))
	var resourceConfRes dto.ContentConfRes
	err = json.Unmarshal(postResp.Body(), &resourceConfRes)
	if err != nil {
		return nil, err
	}

	resourceConf = &resourceConfRes.Data
	if resourceConf == nil {
		return nil, errors.New("data nil")
	}

	go func() {
		resourceConfByte, _ := json.Marshal(resourceConf)
		set := u.repo.RdbSetKey(context.TODO(), common.ContentResourceConfKey, resourceConfByte, 300)
		_, err = set.Result()
		if err != nil {
			u.log.WithContext(ctx).Errorf("rdb set err: %+v", err.Error())
		}
	}()

	return resourceConf, nil
}

func (u *BaseUsecase) GetResourceConfPad2(ctx context.Context) (*dto.ContentConf, error) {

	var resourceConf *dto.ContentConf

	stringCmd := u.repo.RdbGetKey(ctx, common.ContentResourceConfPad2Key)

	if stringCmd != nil && stringCmd.Val() != "" {
		err := json.Unmarshal([]byte(stringCmd.Val()), &resourceConf)
		if err != nil {
			u.log.WithContext(ctx).Errorf("Unmarshal err: %+v", err)
		} else {
			return resourceConf, nil
		}
	}

	ch := util.CurlInit(ctx, time.Second*4, 1, u.log)

	headers := make(map[string]string)
	if custom_context.GetXAppId(ctx) != "" && u.third.AppIdMap[common.XinRuiAppIdKey] == custom_context.GetXAppId(ctx) {
		headers[common.XTalDeviceCode] = common.XinRuiDeviceCodeVal
	}
	postResp, err := ch.ChRequest(ch.Req.SetHeaders(headers)).Post(u.third.XPad2.ResourceConfUrl)

	if err != nil {
		return nil, err
	}
	u.log.WithContext(ctx).Infof("GetResourceConf resp:%s,headers:%+v", string(postResp.Body()), headers)
	var resourceConfRes dto.ContentConfRes
	err = json.Unmarshal(postResp.Body(), &resourceConfRes)
	if err != nil {
		return nil, err
	}

	resourceConf = &resourceConfRes.Data
	if resourceConf == nil {
		return nil, errors.New("data nil")
	}
	for _, grade := range resourceConf.PreschoolGrades {
		resourceConf.GradeInfo = append(resourceConf.GradeInfo, grade)
	}

	for _, subject := range resourceConf.PreschoolCourseSubjects {
		resourceConf.CourseSubjects = append(resourceConf.CourseSubjects, subject)
	}
	go func() {
		resourceConfByte, _ := json.Marshal(resourceConf)
		set := u.repo.RdbSetKey(context.TODO(), common.ContentResourceConfPad2Key, resourceConfByte, 300)
		_, err = set.Result()
		if err != nil {
			u.log.WithContext(ctx).Errorf("rdb set err: %+v", err.Error())
		}
	}()

	return resourceConf, nil
}

func (u *BaseUsecase) SlotDictTransfer(ctx context.Context, dict dto.SlotDict) (d dto.SlotDict, err error) {

	resourceConf, err := u.GetResourceConf(ctx)
	if err != nil {
		return dict, err
	}

	resourceConf.BCourseSystemMap = map[string]int{
		"同步":    5,
		"提升A":   6,
		"提升S":   7,
		"奥数":    8,
		"专题":    9,
		"智慧学":   10,
		"作文课":   11,
		"实验课":   21,
		"精准学":   2,
		"同步精准学": 2,
	}
	resourceConf.BCourseSubjectMap = map[string]int{
		"语文": 1,
		"数学": 2,
		"英语": 3,
		"物理": 4,
		"化学": 5,
		"生物": 6,
		"政治": 7,
		"地理": 8,
		"历史": 9,
		"奥数": 20,
		"科学": 21,
		"编程": 24,
	}

	if dict.SubjectName != "" {
		for _, subject := range resourceConf.CourseSubjects {
			if subject.Value == dict.SubjectName {
				dict.SubjectId = subject.ID
			}
		}
		for _, info := range resourceConf.PreschoolLearnBar {
			if info.ColumnName == dict.SubjectName {
				dict.ColumnId = info.ColumnID
				break
			}
			for _, child := range info.ChildData {
				if dict.SubjectName == child.ChildColumnName {
					dict.ColumnId = child.ColumnID
					dict.ChildColumnId = child.ChildColumnID
					break
				}
			}
		}
	}

	if dict.GradeName != "" {
		for _, info := range resourceConf.GradeInfo {
			if info.Value == dict.GradeName {
				dict.GradeId = info.ID
				break
			}
		}
		if dict.GradeId == 0 {
			switch dict.GradeName {
			case "启蒙", "小班":
				dict.GradeId = -3
			case "中班":
				dict.GradeId = -2
			case "大班":
				dict.GradeId = -1
			case "小学":
				dict.GradeId = 1
			case "初中":
				dict.GradeId = 7
			case "高中":
				dict.GradeId = 10
			}
		}
	}

	if dict.SemesterName != "" {
		for _, info := range resourceConf.SemesterInfo {
			if info.Value == dict.SemesterName {
				dict.SemesterId = info.ID
				break
			}
		}
	}

	if dict.VersionName != "" {
		for i, s := range resourceConf.VersionMap {
			if s == dict.VersionName {
				dict.VersionId = i
				break
			}
		}
	}

	if dict.CourseSystemName != "" {
		if id, ok := resourceConf.BCourseSystemMap[dict.CourseSystemName]; ok {
			dict.CourseSystemId = id
		}
	}

	if dict.SubjectName != "" {
		if id, ok := resourceConf.BCourseSubjectMap[dict.SubjectName]; ok {
			dict.SubjectId = id
		}
	}

	if dict.CatalogName != "" {
		for _, info := range resourceConf.Attainment {
			if info.Value == dict.CatalogName {
				dict.CatalogId = info.ID
				break
			}
		}
		for _, info := range resourceConf.PreschoolLookBar {
			if info.ColumnName == dict.CatalogName {
				dict.ColumnId = info.ColumnID
				break
			}
			for _, child := range info.ChildData {
				if dict.CatalogName == child.ChildColumnName {
					dict.ColumnId = child.ColumnID
					dict.ChildColumnId = child.ChildColumnID
					break
				}
			}
		}
	}
	if dict.PaperTypeName != "" {
		for key, info := range resourceConf.PaperType {
			if info == dict.PaperTypeName {
				dict.PaperType = key
				break
			}
		}
	}

	if dict.IsFamousSchool != "" {
		for key, info := range resourceConf.PaperScopeMap {
			if info == dict.IsFamousSchool {
				dict.PaperScope = key
				break
			}
		}
	}

	if dict.DifficultyName != "" {
		for key, info := range resourceConf.PaperDifficult {
			if info == dict.DifficultyName {
				dict.Difficulty = key
				break
			}
		}
	}

	if dict.CupTypeName != "" {
		for key, info := range resourceConf.PaperCupTypeMap {
			if info == dict.CupTypeName {
				dict.CupType = key
				break
			}
		}
	}

	if dict.ProvinceName != "" {
		for _, province := range resourceConf.PaperProvinces {
			if province.TagName == dict.ProvinceName {
				dict.ProvinceId = province.TagID
				break
			}
		}
	}

	return dict, nil
}

// VisibleSubject 该学科 该年级是否可见
func (u *BaseUsecase) VisibleSubject(ctx context.Context, gradeId, subjectId int) (bool, error) {

	resourceConf, err := u.GetResourceConf(ctx)
	if err != nil {
		return true, err
	}

	// 道德与法治和思想政治的id都是7 他们在初中和高中叫法不同，需要取并集
	courseSubjectsMap := make(map[int][]int)
	for _, subject := range resourceConf.CourseSubjects {
		if _, ok := courseSubjectsMap[subject.ID]; ok {
			courseSubjectsMap[subject.ID] = append(courseSubjectsMap[subject.ID], subject.VisibleGrades...)
		} else {
			courseSubjectsMap[subject.ID] = subject.VisibleGrades
		}
	}

	if grades, ok := courseSubjectsMap[subjectId]; ok {
		for _, grade := range grades {
			if gradeId == grade {
				return true, nil
			}
		}
	}

	return false, nil
}

func (u *BaseUsecase) VisibleSubjects(ctx context.Context, gradeId int) ([]dto.Option, error) {

	resourceConf, err := u.GetResourceConf(ctx)
	if err != nil {
		return nil, err
	}

	options := make([]dto.Option, 0)
	for _, subject := range resourceConf.CourseSubjects {
		for _, grade := range subject.VisibleGrades {
			if grade == gradeId {
				options = append(options, dto.Option{
					Id:   cast.ToString(subject.ID),
					Name: subject.Value,
				})
				break
			}
		}
	}

	return options, nil
}
