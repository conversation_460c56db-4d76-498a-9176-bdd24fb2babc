package biz

import (
	"context"
	"encoding/json"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/parkingwang/go-sign"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"lui-api/internal/common"
	"lui-api/internal/conf"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	"lui-api/internal/pkg/utils"
	"lui-api/pkg/util"

	"strings"
	"time"
)

type BookReadingUsecase struct {
	log   *log.Helper
	third *conf.Third
}

func NewBookReadingUsecase(logger log.Logger, third *conf.Third) *BookReadingUsecase {
	return &BookReadingUsecase{log: log.NewHelper(logger), third: third}
}

func (u BookReadingUsecase) QueryBookReadingClass(ctx context.Context, req *dto.BookReadingIdQueryReq) (*dto.SubjectClassesIdQueryResp, error) {

	if custom_context.GetXAppId(ctx) == u.third.XPad1AppId {
		return u.QueryBookReadingClassPad1(ctx, req)
	} else {
		return u.QueryBookReadingClassPad2(ctx, req)
	}
}

func (u BookReadingUsecase) QueryBookReadingClassPad1(ctx context.Context, req *dto.BookReadingIdQueryReq) (*dto.SubjectClassesIdQueryResp, error) {

	now := time.Now().Unix()
	signer := sign.NewGoSignerMd5()
	signer.SetAppId(u.third.XPad1.ContentAppId)
	signer.SetTimeStamp(now)
	params := make(map[string]interface{})
	params["category"] = req.Category
	params["book_reading_ids"] = req.BookReadingIds
	params["time_stamp"] = now
	bookReadingIds, _ := json.Marshal(req.BookReadingIds)
	signer.AddBody("category", cast.ToString(req.Category.Int()))
	signer.AddBody("book_reading_ids", string(bookReadingIds))
	signer.AddBody("time_stamp", cast.ToString(now))

	//计算得出的签名
	targetSign := signer.GetSignature()
	params["sign"] = targetSign

	ch := utils.CurlInit(ctx, time.Second*4, 1, u.log)
	resp, err := ch.ChRequestBody(map[string]string{}, params).Post(u.third.XPad1.ResourceIdsUrl)

	if err != nil {
		u.log.Infof("QueryBookReadingClass params: %+v", params)
		return nil, err
	}

	u.log.WithContext(ctx).Infof("QueryBookReadingClass resp: %s; params: %+v", string(resp.Body()), params)
	if resp.IsError() {
		return nil, errors.Errorf("status: %s", resp.Status())
	}

	var br dto.BaseResponse
	err = json.Unmarshal(resp.Body(), &br)
	if err != nil {
		return nil, err
	}

	var data dto.SubjectClassesIdQueryResp
	err = util.ConvertInterface(br.Data, &data)
	if err != nil {
		u.log.WithContext(ctx).Errorf("QueryBookReadingClass convert err: %+v; data: %+v", err, br.Data)
	}

	return &data, nil
}

func (u BookReadingUsecase) QueryBookReadingClassPad2(ctx context.Context, req *dto.BookReadingIdQueryReq) (*dto.SubjectClassesIdQueryResp, error) {
	headers := map[string]string{}
	if custom_context.GetXAppId(ctx) != "" && custom_context.GetXAppId(ctx) == u.third.AppIdMap[common.XinRuiAppIdKey] {
		headers[common.XTalDeviceCode] = common.XinRuiDeviceCodeVal
	}

	ch := utils.CurlInit(ctx, time.Second*4, 1, u.log)
	resp, err := ch.ChRequestBody(headers, req).Post(u.third.XPad2.ResourceIdsUrl)

	if err != nil {
		u.log.Infof("QueryBookReadingClass params: %+v", req)
		return nil, err
	}

	u.log.WithContext(ctx).Infof("QueryBookReadingClass resp: %s; params: %+v,headers:%+v", string(resp.Body()), req, headers)
	if resp.IsError() {
		return nil, errors.Errorf("status: %s", resp.Status())
	}

	var br dto.BaseResponse
	err = json.Unmarshal(resp.Body(), &br)
	if err != nil {
		return nil, err
	}

	var data dto.SubjectClassesIdQueryResp
	err = util.ConvertInterface(br.Data, &data)
	if err != nil {
		u.log.WithContext(ctx).Errorf("QueryBookReadingClass convert err: %+v; data: %+v", err, br.Data)
	}

	return &data, nil
}

func (u BookReadingUsecase) GetLuiBookReadingID(ctx context.Context, recourseID string) (bookId, catalogueId string) {
	splits := strings.Split(recourseID, ":")

	if len(splits) != 2 {
		u.log.WithContext(ctx).Errorf("GetLuiBookReadingID Split err, recourseID:%s", recourseID)
		return
	}

	return splits[0], splits[1]
}
