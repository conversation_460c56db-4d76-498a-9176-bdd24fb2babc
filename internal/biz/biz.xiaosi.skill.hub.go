package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"go.opentelemetry.io/otel/trace"
	"lui-api/internal/conf"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	"lui-api/internal/pkg/utils"
	"lui-api/pkg/util"
	"time"
)

// XSSkillHubBiz https://git.100tal.com/znxx_xpp/xiaosi-skill-hub
type XSSkillHubBiz struct {
	log         *log.Helper
	third       *conf.Third
	contactRepo ContactRepo
}

func NewXSSkillHubBiz(logger log.Logger, third *conf.Third, contactRepo ContactRepo) *XSSkillHubBiz {
	return &XSSkillHubBiz{log: log.NewHelper(logger), third: third, contactRepo: contactRepo}
}

func (u *XSSkillHubBiz) FetchNLU4LUI(ctx context.Context, functions []*dto.LuiFunction, talID, sn string) (*dto.NLUQueryPad2Resp, error) {
	luiFunctionReq := make([]*dto.LuiFunctionReq, 0)
	for _, function := range functions {
		params, _ := json.Marshal(function.FuncParamaters)
		luiFunctionReq = append(luiFunctionReq, &dto.LuiFunctionReq{
			FuncName:       function.FuncName,
			Intent:         function.Intent,
			FuncParamaters: string(params),
		})
	}
	hubReq := dto.SkillCallReq{
		UserInfo:  dto.SkillHubUserInfo{},
		Functions: luiFunctionReq,
	}
	header := &dto.ExactlyHeader{
		XRequestId:  custom_context.GetTraceId(ctx),
		XTalSn:      sn,
		XTalVersion: "",
		Traceparent: fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
	}
	headers := util.StructToMap(header)
	headers["Content-Type"] = "application/json"
	ch := utils.CurlInit(ctx, time.Second*3, 0, u.log)
	ab, _ := json.Marshal(hubReq)
	ba := string(ab)
	u.log.WithContext(ctx).Infof("FetchNLU4LUI params: %+v", ba)
	resp, err := ch.ChRequest(ch.Req.SetHeaders(headers).SetBody(hubReq)).Post(u.third.XsSkillHubUrl)
	if err != nil {
		u.log.Infof("FetchNLU4LUI params: %+v", hubReq)
		return nil, err
	}
	u.log.WithContext(ctx).Infof("FetchNLU4LUI resp: %s; params: %+v", string(resp.Body()), hubReq)
	if resp.IsError() {
		return nil, errors.Errorf("resp status: %s", resp.Status())
	}

	var nluRes dto.PadV2Nlu4LuiRes
	if err = json.Unmarshal(resp.Body(), &nluRes); err != nil {
		return nil, err
	}

	nluRes.Data.RequestID = nluRes.TraceID
	return &nluRes.Data, nil
}
