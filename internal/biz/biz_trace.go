package biz

import (
	"context"
	"encoding/json"
	"git.100tal.com/jituan_genie_server/mqs"
	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/structpb"
	v1 "lui-api/api/lui/v1"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	"net/http"
	"time"
)

const (
	// HMIQueryType 人机交互
	HMIQueryType = 2
	// TraceFeedbackType
	TraceFeedbackType = 7
	// TraceLLMRes
	TraceLLMRes = 8
	// sug数据
	SugTraceMsgType = 12
	FunctionTypeSug = 8
)

type TracePayload struct {
	Result           string `json:"result"`
	ReasoningContent string `json:"reasoningContent"`
	IllegalType      string `json:"illegalType"`
	LLMModel         string `json:"llmModel"`
	Emotion          string `json:"emotion"`
	TmpId            string `json:"tmpId"`
}

type TraceFeedbackPayload struct {
	FeedbackType  int32   `json:"feedback_type"`
	Feedback      string  `json:"feedback"`
	FeedbackTypes []int32 `json:"feedback_types"`
}

type SugTracePayload struct {
	TalId   string `json:"tal_id"`
	AsrInfo string `json:"asr_info"`
	NluData string `json:"nluData"`
	BizType string `json:"biz_type"`
}

type KafkaProducerRepo interface {
	SendMessage(ctx context.Context, message *mqs.QueryMsg) error
}

type TraceUsecase struct {
	log          *log.Helper
	producerRepo KafkaProducerRepo
}

func NewTraceUsecase(
	logger log.Logger,
	producerRepo KafkaProducerRepo,
) *TraceUsecase {
	return &TraceUsecase{
		log:          log.NewHelper(logger),
		producerRepo: producerRepo,
	}
}

func (u *TraceUsecase) SugQueryTrace(ctx context.Context, req *v1.LuiNluRequest, resp *structpb.Struct) error {
	respByte, err := json.Marshal(resp)
	if err != nil {
		return err
	}
	input := mqs.QueryMsg{
		FunctionType: FunctionTypeSug,
		MsgType:      SugTraceMsgType,
		TraceId:      req.RequestId,
		SessionId:    req.RequestId,
		QueryTime:    time.Now().UnixNano(),
		AppId:        custom_context.GetXAppId(ctx),
		DeviceId:     custom_context.GetXDeviceId(ctx),
		OsVersion:    custom_context.GetXOSVersion(ctx),
		Payload: SugTracePayload{
			TalId:   req.TalId,
			AsrInfo: req.AsrInfo,
			NluData: string(respByte),
			BizType: req.BizType,
		},
		Continuous: req.Continuous,
		Key:        custom_context.GetXDeviceId(ctx),
	}

	return u.producerRepo.SendMessage(ctx, &input)

}

func (u *TraceUsecase) QueryTraceFeedback(ctx context.Context, req *v1.QueryTraceFeedbackRequest) error {
	asrInput := mqs.QueryMsg{
		TraceId:      req.SessionId,
		QueryTime:    time.Now().UnixNano(),
		AppId:        custom_context.GetXAppId(ctx),
		DeviceId:     custom_context.GetXDeviceId(ctx),
		FunctionType: HMIQueryType,
		MsgType:      TraceFeedbackType,
		OsVersion:    custom_context.GetXOSVersion(ctx),
		Payload: TraceFeedbackPayload{
			FeedbackType:  req.FeedbackType,
			Feedback:      req.Feedback,
			FeedbackTypes: req.FeedbackTypes,
		},
		Key: custom_context.GetXDeviceId(ctx),
	}

	err := u.producerRepo.SendMessage(ctx, &asrInput)

	return err
}

func (u *TraceUsecase) QueryTraceLLMRes(r *http.Request, req *dto.LlmTracePayload) error {

	asrInput := mqs.QueryMsg{
		TraceId:      req.SessionId,
		QueryTime:    time.Now().UnixNano(),
		AppId:        r.Header.Get("X-Genie-AppId"),
		DeviceId:     r.Header.Get("X-Genie-DeviceId"),
		FunctionType: HMIQueryType,
		MsgType:      TraceLLMRes,
		OsVersion:    r.Header.Get("X-Genie-Osversion"),
		Payload: TracePayload{
			Result:           req.Result,
			ReasoningContent: req.ReasoningContent,
			IllegalType:      req.IllegalType,
			LLMModel:         req.LLMModel,
			Emotion:          req.Emotion,
			TmpId:            req.TmpId,
		},
		Key: r.Header.Get("X-Genie-DeviceId"),
	}

	if req.SessionId == "" {
		u.log.WithContext(r.Context()).Infof("sessionId is empty, QueryMsg: %+v", asrInput)
		return nil
	}

	err := u.producerRepo.SendMessage(context.Background(), &asrInput)

	return err
}
