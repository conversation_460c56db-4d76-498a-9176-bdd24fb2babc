package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"lui-api/internal/common"
	"lui-api/internal/conf"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	"lui-api/pkg/util"
	"strings"

	"github.com/go-resty/resty/v2"
	"github.com/spf13/cast"

	"github.com/go-kratos/kratos/v2/log"
	"go.opentelemetry.io/otel/trace"
)

type RnUsecase struct {
	log        *log.Helper
	third      *conf.Third
	nluUsecase *NluUsecase
}

func NewRnUsecase(logger log.Logger, third *conf.Third, nluUsecase *NluUsecase) *RnUsecase {
	return &RnUsecase{
		log:        log.NewHelper(logger),
		third:      third,
		nluUsecase: nluUsecase,
	}
}

type void struct{}

var rnModuleNameMap = map[string]void{
	"zh_dic":  {}, // 查字词
	"en_dic":  {}, // 查英文字词
	"poem":    {}, // 查诗歌
	"weather": {}, // 天气
	"time":    {}, // 日期
	//"alarm_clock": {}, // 闹钟   端上暂不支持
	//"timer":       {}, // 定时器
	"blue_tooth": {}, // 蓝牙
}

var xiaosi3RnModuleNameMap = map[string]void{
	"zh2en":   {}, // 中译英
	"zh_dic":  {}, // 查字词
	"en_dic":  {}, // 查英文字词
	"poem":    {}, // 查诗歌
	"weather": {}, // 天气
	"time":    {}, // 日期
	//"alarm_clock": {}, // 闹钟   端上暂不支持
	//"timer":       {}, // 定时器
	"blue_tooth":       {}, // 蓝牙
	"material":         {}, // 句子
	"chat":             {}, // 闲聊
	"baike":            {}, // 百科
	"search_knowledge": {}, // 查学科知识
}

var rnSupportLightModuleNameMap = map[string]void{
	"weather":    {}, // 天气
	"time":       {}, // 日期
	"blue_tooth": {}, // 蓝牙
}

// GetRn 学练机react-native
func (u *RnUsecase) GetRn(ctx context.Context, sceneMode int32, rnVersion, skill string, skillItem *dto.NLUSkillItem, sceneCode common.SceneCode) (dispatch *dto.Dispatch, showType int) {
	if sceneCode != common.SceneXiaoSi {
		return nil, 0
	}

	// 学练机 所有版本支持rn； 24旗舰和24经典从3.6.0起支持rn
	if !judgeSupportRN(custom_context.GetXAppId(ctx), rnVersion, u.third.AppIdMap) {
		return nil, 0
	}

	if skill == "zh_to_en" || skill == "en_dic" {
		var widgetM WidgetM
		if err := json.Unmarshal([]byte(skillItem.Widget), &widgetM); err != nil {
			u.log.WithContext(ctx).Warnf("GetXLJRn Unmarshal err: %v; widget: %s", err, skillItem.Widget)
			return nil, 0
		}

		if widgetM.DataSource == "translation" {

			// 只有学练机支持 翻译的
			if !strings.Contains(u.third.AppIdMap["xueLianJiAppIds"], custom_context.GetXAppId(ctx)) {
				return nil, 0
			}

			bundle := u.GetSkillRn(ctx, rnVersion, skill, sceneCode)
			if len(bundle) > 0 {
				dispatch = &dto.Dispatch{
					OpenType: 2,
					Router:   dto.Router{Bundle: bundle},
				}
				return dispatch, 2
			}
		}
	}

	var rnCall bool
	var widgetM WidgetM
	if err := json.Unmarshal([]byte(skillItem.Widget), &widgetM); err == nil {

		if sceneMode != dto.SceneModeFullView {
			if _, ok := rnSupportLightModuleNameMap[widgetM.ModuleName]; ok {
				// 多个结果不走RN
				if widgetM.Count < 2 {
					rnCall = true
					showType = 0
				}
			}
		} else if _, ok := rnModuleNameMap[widgetM.ModuleName]; ok {
			// 多个结果不走RN
			if widgetM.Count < 2 {
				rnCall = true
				showType = 1
			}
		}
	}

	if rnCall {
		bundle := u.GetSkillRn(ctx, rnVersion, "lui_card", sceneCode)
		if len(bundle) > 0 {
			dispatch = &dto.Dispatch{
				OpenType: 2,
				Router:   dto.Router{Bundle: bundle},
			}
			return dispatch, showType
		}
	}

	return nil, 0
}

func judgeSupportRN(appId string, rnVersion string, appIdMap map[string]string) bool {
	if strings.Contains(appIdMap["xueLianJiAppIds"], appId) {
		return true
	}

	if len(rnVersion) == 0 || !strings.Contains(rnVersion, ".") {
		return false
	}

	rnVersionLow := rnVersion[strings.Index(rnVersion, ".")+1:]
	if (appId == appIdMap["xPad2AppId_qijian"] || appId == appIdMap["xPad2LiteAppId"] || appId == appIdMap["xPad2AppId_pingjia25"]) && compareVersion(rnVersionLow, "3.6.0") >= 0 {
		return true
	}

	return false
}

func compareVersion(v1, v2 string) int {
	arr1 := strings.Split(v1, ".")
	arr2 := strings.Split(v2, ".")

	arrLen := len(arr1)
	if len(arr2) < arrLen {
		arrLen = len(arr2)
	}

	res := 0
	for i := 0; i < arrLen; i++ {
		a1 := cast.ToInt(arr1[i])
		a2 := cast.ToInt(arr2[i])
		if a1 > a2 {
			res = 1
			break
		} else if a1 < a2 {
			res = -1
			break
		}
	}

	return res
}

type WidgetM struct {
	ModuleName string `json:"module_name"`
	DataSource string `json:"data_source"`
	Count      int    `json:"count"`
}

type CloudControlResponse struct {
	ErrorReason string      `json:"error_reason"`
	ErrorMsg    string      `json:"error_msg"`
	MetaData    interface{} `json:"meta_data"`
	TraceId     string      `json:"trace_id"`
	ServerTime  int         `json:"server_time"`
	Data        struct {
		RnList []struct {
			BundleList []dto.Bundle `json:"bundle_list"`
			SkillName  string       `json:"skill_name"`
		} `json:"rn_list"`
	} `json:"data"`
}

func (u *RnUsecase) GetSkillRn(ctx context.Context, rnVersion, skill string, sceneCode common.SceneCode) (bundles []dto.Bundle) {
	if sceneCode != common.SceneXiaoSi {
		return
	}
	if len(rnVersion) == 0 || skill == "" {
		u.log.WithContext(ctx).Warnf("getSkillRn failed, rnVersion: %s, skill: %s", rnVersion, skill)
		return
	}
	result := new(CloudControlResponse)
	// 1. 调用cloud-control接口获取列表
	headers := map[string]string{
		"Traceparent": fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
	}
	res, err := resty.New().
		SetBaseURL(u.third.CloudControlHost).
		R().
		SetHeaders(headers).
		SetResult(&result).
		SetQueryParams(map[string]string{
			"app_version": rnVersion,
		}).
		Get("/v1/skill/rns")
	if err != nil || !res.IsSuccess() {
		u.log.WithContext(ctx).Errorf("getSkillRn failed, err: %v, res: %v", err, res)
		return
	}
	// 2. 从列表中获取skill对应的rn
	bundles = make([]dto.Bundle, 0)
	for _, rn := range result.Data.RnList {
		if rn.SkillName != skill {
			continue
		}
		for _, bundle := range rn.BundleList {
			bundles = append(bundles, bundle)
		}
	}
	return
}
func (u *RnUsecase) GetSetDispatch(ctx context.Context, sceneCode common.SceneCode, rnVersion, bizType string, skillData []*dto.Pad2SkillData) (dispatch *dto.Dispatch, showType int) {
	if (sceneCode != common.SceneXiaoSi) || rnVersion == "" || len(skillData) == 0 {
		return
	}

	result := new(CloudControlResponse)
	// 1. 调用cloud-control接口获取列表
	headers := map[string]string{
		"Traceparent": fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
	}
	res, err := resty.New().
		SetBaseURL(u.third.CloudControlHost).
		R().
		SetHeaders(headers).
		SetResult(&result).
		SetQueryParams(map[string]string{
			"app_version": rnVersion,
		}).
		Get("/v1/skill/rns")

	if err != nil || !res.IsSuccess() {
		u.log.WithContext(ctx).Errorf("getSkillRn failed, err: %v, res: %v", err, res)
		return
	}

	u.log.WithContext(ctx).Infof("GetSetDispatch rnVersion: %s, result: %v", rnVersion, util.Marshal(result))

	rnSkillBundleMap := make(map[string][]dto.Bundle)
	for _, rn := range result.Data.RnList {
		rnSkillBundleMap[rn.SkillName] = rn.BundleList
	}

	luiCardBundles := make([]dto.Bundle, 0)
	luiListBundles := make([]dto.Bundle, 0)

	for _, skill := range skillData {

		skillLuiCardBundle := make([]dto.Bundle, 0)
		skillItem := skill.Data

		explainQuestionAgentListGrayHit := u.nluUsecase.ExplainQuestionAgentListGrayHit(custom_context.GetXAppId(ctx), custom_context.GetXOsPkgDate(ctx), custom_context.GetXDeviceId(ctx))
		if skillItem.Skill == common.SkillExercisesTool.ToString() && !explainQuestionAgentListGrayHit {
			if bundleList, ok := rnSkillBundleMap["conversation_card"]; ok {
				luiListBundles = append(luiListBundles, bundleList...)
			}

			continue
		}

		if bizType == common.BizTypeTopicDialogue {
			if skillItem.Skill == common.Baike.ToString() || skillItem.Skill == common.SkillChat.ToString() {
				if bundleList, ok := rnSkillBundleMap["conversation_card"]; ok {
					luiListBundles = append(luiListBundles, bundleList...)
				}
				continue
			}

			// 如果是查题 则下发 RN
			/*commandM := map[string]interface{}{}
			command, _ := json.Marshal(skillItem.Command)
			_ = json.Unmarshal(command, &commandM)
			if commandM != nil {
				appid, ok := commandM["param"].(map[string]interface{})["appid"].(string)
				if ok && appid == "1025" {
					dispatch = &dto.Dispatch{
						OpenType: 2,
						Router:   dto.Router{Bundle: luiListBundles},
					}
					showType = 1
				}
			}*/
		}

		var widgetM WidgetM
		if err = json.Unmarshal([]byte(skillItem.Widget), &widgetM); err != nil {
			continue
		}

		if _, ok := xiaosi3RnModuleNameMap[widgetM.ModuleName]; !ok {
			continue
		}

		if skillItem.Skill == common.Poem.ToString() {
			if bundleList, ok := rnSkillBundleMap[skillItem.Skill]; ok {
				skillLuiCardBundle = bundleList
				luiCardBundles = append(luiCardBundles, bundleList...)
			}

			if bundleList, ok := rnSkillBundleMap["conversation_card"]; ok {
				luiListBundles = append(luiListBundles, bundleList...)
			}
		} else {

			if bundleList, ok := rnSkillBundleMap["conversation_card"]; ok {
				skillLuiCardBundle = bundleList
				luiCardBundles = append(luiCardBundles, bundleList...)
			}
			if widgetM.Count > 1 {
				if bundleList, ok := rnSkillBundleMap["conversation_card"]; ok {
					luiListBundles = append(luiListBundles, bundleList...)
				}
			}
		}

		if len(skillLuiCardBundle) == 0 {
			continue
		}

		skillItem.Dispatch = &dto.Dispatch{
			OpenType: 2,
			Router:   dto.Router{Bundle: skillLuiCardBundle},
		}

		skillItem.ShowType = 1
	}

	if judgeMixCardRn(skillData) {
		if mixedCardBundles, ok := rnSkillBundleMap["mixed_card"]; ok {
			dispatch = &dto.Dispatch{
				OpenType: 2,
				Router:   dto.Router{Bundle: mixedCardBundles},
			}
			showType = 1
		}
		return
	}

	if len(luiListBundles) > 0 {
		dispatch = &dto.Dispatch{
			OpenType: 2,
			Router:   dto.Router{Bundle: luiListBundles},
		}
		showType = 1
	} else if len(luiCardBundles) > 0 && len(luiListBundles) == 0 {
		// 如果是多个字词或多个句子的情况，下发该技能的rn
		dispatch = &dto.Dispatch{
			OpenType: 2,
			Router:   dto.Router{Bundle: luiCardBundles},
		}
		showType = 1
	}

	return
}

func judgeMixCardRn(skillData []*dto.Pad2SkillData) bool {
	if len(skillData) > 1 {
		return true
	}

	if len(skillData) == 1 && skillData[0].Data != nil && skillData[0].Data.Count > 1 {

		if skillData[0].Skill == common.PaperSubjectConfirm.ToString() {
			return false
		}

		// 如果是 混排 或者资源列表页
		resourceSkillMap := map[string]*struct{}{
			"search_course":          nil, // 课程学习
			"continue_learn_course":  nil, // 课程学习
			"search_paper":           nil, // 真题试卷
			"book":                   nil, // 图书
			"newspaper":              nil, // 报刊
			"documentary":            nil, // 纪录片
			"search_quality":         nil, // 素养视频
			"continue_learn_quality": nil, // 素养视频
			"listen_write":           nil, // 学习助手
			"recite":                 nil, // 学习助手
			"click_read":             nil, // 学习助手
			"search_exercise":        nil, // 找练习

		}
		if _, ok := resourceSkillMap[skillData[0].Data.Skill]; ok {
			return true
		}
	}

	return false
}
