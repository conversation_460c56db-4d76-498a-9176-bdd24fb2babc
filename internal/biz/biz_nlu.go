package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	"github.com/redis/go-redis/v9"
	"lui-api/internal/pkg/utils"
	"math/rand"
	"regexp"
	"strconv"
	"strings"
	"time"

	feature "git.100tal.com/znxx_xpp/feature-ab-client-go"

	"lui-api/internal/common"

	"github.com/go-errors/errors"
	"github.com/go-kratos/kratos/v2/log"
	v1 "lui-api/api/lui/v1"
	"lui-api/internal/conf"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	"lui-api/pkg/util"

	"github.com/google/uuid"
	"github.com/spf13/cast"
	"go.opentelemetry.io/otel/trace"
)

const (
	DouDiCalculate        = "计算"
	DouDiComposition      = "找作文"
	DouDiEbook            = "找电子书"
	DouDiAudio            = "找学习音频"
	DouDiMusic            = "找非学习音频"
	DouDiCartoon          = "找动画片"
	DouDiStory            = "找故事"
	DouDiUnknown          = "无意义"
	DouDiSearchCourseBook = "找课本"
	DouDiSearchPicture    = "找图片"
)

type NluUsecase struct {
	log            *log.Helper
	third          *conf.Third
	sign           *conf.Sign
	ipAnalysisRepo IPAnalysisRepo
	visionRepo     IVisionRepo
	memoryBiz      *MemoryContextBiz
}

type IPAnalysisRepo interface {
	GetIPAnalysisByDeviceID(ctx context.Context, deviceID string) *redis.StringCmd
	SetIPAnalysisByDeviceID(ctx context.Context, deviceID string, value interface{}, expiration int) *redis.StatusCmd
}
type IVisionRepo interface {
	GetVisionImage(ctx context.Context, key string) (string, error)
	DelVisionImage(ctx context.Context, key string) error
}

func NewNluUsecase(logger log.Logger, third *conf.Third, sign *conf.Sign, ipAnalysisRepo IPAnalysisRepo, visionRepo IVisionRepo, memoryBiz *MemoryContextBiz) *NluUsecase {
	return &NluUsecase{log: log.NewHelper(logger), third: third, sign: sign, ipAnalysisRepo: ipAnalysisRepo, visionRepo: visionRepo, memoryBiz: memoryBiz}
}

func (u NluUsecase) FetchNLU(ctx context.Context, req *v1.LuiNluRequest) (*dto.NluData, error) {

	var source string
	if strings.Contains(u.third.XPad2AppId, custom_context.GetXAppId(ctx)) {
		source = "pad2"
	} else {
		source = "pad"
	}

	nluReq := dto.NluReq{
		RequestId: req.RequestId,
		UserId:    req.TalId,
		TimeStamp: time.Now().Unix(),
		Source:    source,
		AsrInput: dto.AsrInput{
			AsrInfo:   req.AsrInfo,
			AsrLen:    len(req.AsrInfo),
			AsrPinyin: req.AsrPinyin,
		},
		UserSystem: dto.UserSystem{
			Location: req.Location,
		},
	}

	u.log.WithContext(ctx).Infof("FetchNLU4LUI nluReq: %+v", nluReq)
	resp, err := resty.New().OnBeforeRequest(func(client *resty.Client, r *resty.Request) error {

		// 计算签名 放在OnBeforeRequest 因为重试需要重新生成签名
		timestamp := cast.ToString(time.Now().Unix())
		secretKey := u.sign.SignSecrets[custom_context.GetXAppId(ctx)]
		nonce := uuid.New().String()
		sign := util.MD5(secretKey + "&X-Genie-Timestamp=" + timestamp + "&X-Genie-Nonce=" + nonce + "&X-Genie-DeviceId=" + custom_context.GetXDeviceId(ctx))

		client.SetRetryCount(1).
			SetTimeout(time.Second * time.Duration(10)).
			SetHeaders(map[string]string{
				"Content-Type":      "application/json",
				"X-Genie-TraceId":   req.RequestId,
				"X-Genie-DeviceId":  custom_context.GetXDeviceId(ctx),
				"X-Genie-Timestamp": timestamp,
				"X-Genie-Nonce":     nonce,
				"X-Genie-AppId":     custom_context.GetXAppId(ctx),
				"X-Genie-Sign":      sign,
				"X-Genie-Version":   custom_context.GetXVersion(ctx),
				"X-Genie-Osversion": custom_context.GetXOSVersion(ctx),
				"X-Genie-Platform":  custom_context.GetXPlatform(ctx),
				"baggage":           "origin=" + u.third.NluOrigin,
				"X-Request-Id":      custom_context.GetTraceId(ctx),
				"Traceparent":       fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
			})
		return nil
	}).
		R().
		SetBody(nluReq).
		Post(u.third.ProxyUrl)

	if err != nil {
		return nil, err
	}

	u.log.WithContext(ctx).Infof("FetchNLU resp: %+v", resp.String())
	if resp.IsError() {
		return nil, errors.Errorf("resp: %v", resp.Error())
	}

	var nluRes dto.NluRes
	if err = json.Unmarshal(resp.Body(), &nluRes); err != nil {
		return u.NLUDefault(), err
	}

	if nluRes.Code != 0 {
		return u.NLUDefault(), errors.Errorf("code %d; msg: %s", nluRes.Code, nluRes.Msg)
	}

	return &nluRes.NluData, nil
}

func (u NluUsecase) FetchNLU4LUI(ctx context.Context, req *v1.LuiNluRequest) (*dto.Nlu4LuiData, error) {

	fillSlots := make([]dto.Slot, 0)
	for _, s := range req.SlotFillList {
		fillSlots = append(fillSlots, dto.Slot{
			Key:  s.Key,
			Id:   s.Id,
			Name: s.Name,
		})
	}

	nluReq := dto.Nlu4LUIReq{
		RequestId: req.RequestId,
		AsrInput: dto.AsrInput{
			AsrInfo:   req.AsrInfo,
			AsrLen:    len(req.AsrInfo),
			AsrPinyin: req.AsrPinyin,
		},
		UserSystem: dto.UserSystem4LUI{
			Location: req.Location,
			Grade:    req.Grade,
			Semester: dto.GetSemester(),
			TalId:    req.TalId,
			DeviceId: custom_context.GetXDeviceId(ctx),
		},
		SlotFillList: fillSlots,
	}

	u.log.WithContext(ctx).Infof("FetchNLU4LUI req: %s", util.Marshal(nluReq))

	resp, err := resty.New().OnBeforeRequest(func(client *resty.Client, r *resty.Request) error {
		// 计算签名
		timestamp := strconv.Itoa(int(time.Now().Unix()))
		secretKey := u.sign.SignSecrets[custom_context.GetXAppId(ctx)]
		nonce := uuid.New().String()
		sign := util.MD5(secretKey + "&X-Genie-Timestamp=" + timestamp + "&X-Genie-Nonce=" + nonce + "&X-Genie-DeviceId=" + custom_context.GetXDeviceId(ctx))

		client.SetRetryCount(1).
			SetTimeout(time.Second * time.Duration(10)).
			SetHeaders(map[string]string{
				"Content-Type":      "application/json",
				"X-Genie-TraceId":   req.RequestId,
				"X-Genie-DeviceId":  custom_context.GetXDeviceId(ctx),
				"X-Genie-Timestamp": timestamp,
				"X-Genie-Nonce":     nonce,
				"X-Genie-AppId":     custom_context.GetXAppId(ctx),
				"X-Genie-Sign":      sign,
				"X-Genie-Version":   custom_context.GetXVersion(ctx),
				"X-Genie-Osversion": custom_context.GetXOSVersion(ctx),
				"X-Genie-Platform":  custom_context.GetXPlatform(ctx),
				"baggage":           "origin=" + u.third.Nlu4LuiOrigin,
				"X-Request-Id":      custom_context.GetTraceId(ctx),
				"Traceparent":       fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
			})
		return nil
	}).
		R().
		SetBody(nluReq).
		Post(u.third.ProxyUrl)

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, errors.Errorf("resp status: %s", resp.Status())
	}

	var nluRes dto.Nlu4LuiRes
	if err = json.Unmarshal(resp.Body(), &nluRes); err != nil {
		return nil, err
	}

	if nluRes.Code != 0 {
		return nil, errors.Errorf("nluRes code %d; msg: %s", nluRes.Code, nluRes.Msg)
	}

	nluRes.Data.RequestID = nluRes.TraceID
	// 基础槽位和算法识别槽位取交并集
	nluRes.Data.AllSlots = dto.MergeStructs(nluRes.Data.DefaultSlots, nluRes.Data.SlotDict)
	return &nluRes.Data, nil
}

func (u NluUsecase) IPAnalysis(ctx context.Context) *dto.IPAnalysis {
	// 查 IPAnalysis 缓存
	ipCmd := u.ipAnalysisRepo.GetIPAnalysisByDeviceID(ctx, custom_context.GetXDeviceId(ctx))
	if ipCmd != nil && ipCmd.Val() != "" {
		ipAnalysis := dto.IPAnalysis{}
		err := json.Unmarshal([]byte(ipCmd.Val()), &ipAnalysis)
		if err != nil {
			u.log.WithContext(ctx).Errorf("IPAnalysis Unmarshal err:%s", err.Error())
		} else {
			return &ipAnalysis
		}
	}

	// 计算签名
	timestamp := strconv.Itoa(int(time.Now().Unix()))
	nonce := uuid.New().String()
	secretKey := u.sign.SignSecrets[custom_context.GetXAppId(ctx)]
	ipReq := &dto.IPRequest{
		IP: custom_context.GetRequestIp(ctx),
	}
	sign := util.MD5(secretKey + "&X-Genie-Timestamp=" + timestamp + "&X-Genie-Nonce=" + nonce + "&X-Genie-DeviceId=" + custom_context.GetXDeviceId(ctx))
	resp, err := resty.New().
		SetRetryCount(int(u.third.IpAnalysis.Retry)).
		SetTimeout(time.Second * time.Duration(u.third.IpAnalysis.Timeout)).
		R().
		SetHeaders(map[string]string{
			"Content-Type":      "application/json",
			"X-Genie-TraceId":   custom_context.GetTraceId(ctx),
			"X-Genie-DeviceId":  custom_context.GetXDeviceId(ctx),
			"X-Genie-Timestamp": timestamp,
			"X-Genie-Nonce":     nonce,
			"X-Genie-AppId":     custom_context.GetXAppId(ctx),
			"X-Genie-Sign":      sign,
			"X-Genie-Version":   custom_context.GetXVersion(ctx),
			"X-Genie-Osversion": custom_context.GetXOSVersion(ctx),
			"X-Genie-Platform":  custom_context.GetXPlatform(ctx),
			"baggage":           "origin=IPAnalysis",
			"Traceparent":       fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
		}).
		SetBody(ipReq).
		Post(u.third.IpAnalysis.Url)
	if err != nil {
		u.log.WithContext(ctx).Errorf("IPAnalysis error, request: %+v, error: %s", ipReq, err)
		return nil
	}
	if resp.IsError() {
		u.log.WithContext(ctx).Errorf("IPAnalysis resp IsError, resp: %+v", resp)
		return nil
	}

	u.log.WithContext(ctx).Infof("IPAnalysis resp: %s", string(resp.Body()))
	var response dto.IPAnalysisRes
	if err = json.Unmarshal(resp.Body(), &response); err != nil {
		u.log.WithContext(ctx).Errorf("IPAnalysis Unmarshal err: %+v", err)
		return nil
	}

	// 缓存 IPAnalysis 信息
	ipDataByte, _ := json.Marshal(response.Data)
	_, err = u.ipAnalysisRepo.SetIPAnalysisByDeviceID(ctx, custom_context.GetXDeviceId(ctx), ipDataByte, 120).Result()
	if err != nil {
		u.log.WithContext(ctx).Errorf("set token cache err:%s", err.Error())
	}

	return &response.Data
}
func (u NluUsecase) NLUDefault() (res *dto.NluData) {

	res = &dto.NluData{
		UserID:        "0000",
		RequestID:     "",
		Skill:         "",
		Task:          "",
		TtsShow:       "",
		TtsNorm:       "抱歉，我还不会这个问题",
		Count:         0,
		ShouldConfirm: false,
	}

	return res
}

func (u NluUsecase) FetchPadV2NLU4LUI(ctx context.Context, req *v1.LuiNluRequest) (*dto.NLUQueryPad2Resp, error) {

	fillSlots := make([]dto.Slot, 0)
	for _, s := range req.SlotFillList {
		fillSlots = append(fillSlots, dto.Slot{
			Key:  s.Key,
			Id:   s.Id,
			Name: s.Name,
		})
	}

	nluReq := dto.NluPad24LUIReq{
		RequestId: req.RequestId,
		AsrInput: dto.AsrInput{
			AsrInfo:   req.AsrInfo,
			AsrLen:    len(req.AsrInfo),
			AsrPinyin: req.AsrPinyin,
		},
		UserSystem: dto.UserSystemPad24LUI{
			Location: req.Location,
			Grade:    cast.ToInt(req.GradeId),
			Semester: dto.GetSemester(),
			TalId:    req.TalId,
			DeviceId: custom_context.GetXDeviceId(ctx),
		},
		SlotFillList: fillSlots,
		Source:       "pad2",
		Version:      custom_context.GetXVersion(ctx),
		AppId:        custom_context.GetXAppId(ctx),
	}
	if req.BizType == common.BizTypeCompanionLearn {
		nluReq.SceneCode = common.SceneCompanionLearn
	}
	u.log.WithContext(ctx).Infof("FetchPadV2NLU4LUI req: %s", util.Marshal(nluReq))

	resp, err := resty.New().OnBeforeRequest(func(client *resty.Client, r *resty.Request) error {
		// 计算签名
		timestamp := strconv.Itoa(int(time.Now().Unix()))
		secretKey := u.sign.SignSecrets[custom_context.GetXAppId(ctx)]
		nonce := uuid.New().String()
		sign := util.MD5(secretKey + "&X-Genie-Timestamp=" + timestamp + "&X-Genie-Nonce=" + nonce + "&X-Genie-DeviceId=" + custom_context.GetXDeviceId(ctx))

		headers := map[string]string{
			"Content-Type":      "application/json",
			"X-Genie-TraceId":   req.RequestId,
			"X-Genie-DeviceId":  custom_context.GetXDeviceId(ctx),
			"X-Genie-Timestamp": timestamp,
			"X-Genie-Nonce":     nonce,
			"X-Genie-AppId":     custom_context.GetXAppId(ctx),
			"X-Genie-Sign":      sign,
			"X-Genie-Version":   custom_context.GetXVersion(ctx),
			"X-Genie-Osversion": custom_context.GetXOSVersion(ctx),
			"X-Genie-Platform":  custom_context.GetXPlatform(ctx),
			"baggage":           "origin=" + u.third.Nlu4LuiListOrigin,
			"X-Request-Id":      custom_context.GetTraceId(ctx),
			"Traceparent":       fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
		}
		client.SetRetryCount(1).
			SetTimeout(time.Second * time.Duration(10)).
			SetHeaders(headers)
		return nil
	}).
		R().
		SetBody(nluReq).
		Post(u.third.ProxyUrl)

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, errors.Errorf("resp status: %s", resp.Status())
	}
	u.log.WithContext(ctx).Infof("FetchPadV2NLU4LUI nluRes: %s", string(resp.Body()))
	var nluRes dto.PadV2Nlu4LuiRes
	if err = json.Unmarshal(resp.Body(), &nluRes); err != nil {
		return nil, err
	}

	if nluRes.Code != 0 {
		return nil, errors.Errorf("nluRes code %d; msg: %s", nluRes.Code, nluRes.Msg)
	}

	nluRes.Data.RequestID = nluRes.TraceID
	return &nluRes.Data, nil
}

func (u NluUsecase) FetchPadV3NLU4LUI(ctx context.Context, req *v1.LuiNluRequest, memoryContexts []*dto.XPadMemoryContextResp) (*dto.NLUQueryPad2Resp, error) {
	//作业模式
	if req.BizType == common.BizTypeCompanionLearn {
		return u.FetchPadV2NLU4LUI(ctx, req)
	}

	//获取nluQuery
	contextInfo := &dto.ContextInfo{
		HistoryInfo: memoryContexts,
		CurrentInfo: &dto.ContextCurrentInfo{
			WakeUpType: req.WakeUpType,
		},
	}

	fillSlots := make([]dto.NLUSlotDict, 0)
	for _, s := range req.SlotFillList {
		fillSlots = append(fillSlots, dto.NLUSlotDict{
			Key:  s.Key,
			Id:   s.Id,
			Name: s.Name,
		})
	}

	sceneCode := common.SceneXiaoSi
	if strings.HasPrefix(req.BizType, common.BizTypeAiTutor) {
		sceneCode = common.SceneAiTutor
	}

	// 判断是否应该启用多模态NLU
	multiModalNluFlag := u.GetMultiModelFlag(ctx, sceneCode, req.BizType)
	// 设置作业标志
	var isHomeWorkFlag int
	if strings.HasPrefix(req.BizType, common.BizTypeHomeWork) {
		isHomeWorkFlag = 1
	}

	visionInput, _ := u.GetVisionInput(ctx, req)

	var routeType = 0
	if sceneCode == common.SceneXiaoSi {
		unclearHit := u.XsUnclearIntentUpgradeGrayHit(custom_context.GetXAppId(ctx), custom_context.GetXOsPkgDate(ctx), custom_context.GetXDeviceId(ctx))
		if unclearHit {
			routeType = 1
		}
	}

	nluQuery := &dto.NLUQuery{
		AsrInput: dto.AsrInput{
			AsrInfo:   req.AsrInfo,
			AsrLen:    len(req.AsrInfo),
			AsrPinyin: req.AsrPinyin,
		},
		UserSystem: dto.UserSystemV2{
			Location: req.Location,
			Grade:    cast.ToInt(req.GradeId),
			Semester: dto.GetSemester(),
			TalId:    req.TalId,
			DeviceId: custom_context.GetXDeviceId(ctx),
		},
		RequestID:         req.RequestId,
		Continuous:        req.Continuous,
		SentenceID:        req.SentenceId,
		SceneCode:         sceneCode,
		ContextInfo:       contextInfo,
		SlotFillList:      fillSlots,
		Source:            "pad2",
		PagePrompt:        req.PagePrompt,
		PageLayoutData:    req.ImagePrompt,
		MrIntent:          u.MrIntentSnListGrayHit(ctx, custom_context.GetXDeviceId(ctx)),
		MultiModalNluFlag: multiModalNluFlag,
		VisionInput:       visionInput,
		RouteType:         routeType,
		IsHomeWorkFlag:    isHomeWorkFlag,
	}
	if req.LlmAgent != nil {
		nluQuery.LlmAgent = dto.LlmAgent{
			Name:   req.LlmAgent.Name,
			Status: int(req.LlmAgent.Status),
		}
	}

	nluQuery = u.PreHandler(ctx, nluQuery)

	//调用XPadController
	controllerResp, err := u.ControllerCall(ctx, nluQuery)
	if err != nil {
		u.log.WithContext(ctx).Warnf("ControllerCall,err:%s", err)
		return nil, err
	}

	supportResp := u.IsXueLianJiSupportIntent(ctx, req, controllerResp.Data.Intent)
	if supportResp != nil {
		return supportResp, nil
	}

	if req.BizType == common.BizTypeHomeWork && controllerResp.Chain != "new" {
		return u.IsHomeWorkSupportIntent(ctx, req, controllerResp.Data.Intent), nil
	}

	resp := &dto.NLUQueryPad2Resp{
		Rewrite:           controllerResp.Data.Rewrite,
		SceneCode:         int(nluQuery.SceneCode),
		FunctionList:      controllerResp.Data.FunctionCall.Functions,
		ControllerVersion: controllerResp.ControllerVersion,
		RewriteInfo:       controllerResp.Data.RewriteInfo,
		ModelOutputIntent: controllerResp.Data.Intent,
		VisionInput:       visionInput,
		BizType:           req.BizType,
	}

	// 多模结构处理
	mixedModal, ocrType := u.HandleMultiModeOcrData(ctx, req.BizType, resp.ModelOutputIntent, resp.FunctionList)
	if mixedModal {
		resp.MixedModalInfo = &dto.MixedModalInfo{
			MixedModal: mixedModal,
			OcrType:    ocrType,
		}

		return resp, nil
	}

	//可见即可说
	if controllerResp.Data.SceneResult != nil && controllerResp.Data.SceneResult.ClickArea != nil {
		resp.SceneResult = controllerResp.Data.SceneResult
		return resp, nil
	}

	// 深度思考模式处理
	deepSeekFlag, deepSeekIntent := u.GetSetDeepSeekFlag(ctx, req, resp.FunctionList)
	if deepSeekIntent {
		resp.DeepSeekInfo = &dto.DeepSeekInfo{
			DeepSeekFlag:   deepSeekFlag,
			DeepSeekIntent: deepSeekIntent,
		}
		return resp, nil
	}

	// 查题意图
	underScreenExercises := u.GetUnderScreenExercises(ctx, req, visionInput, resp.FunctionList)
	if underScreenExercises {
		resp.ExtraResp = &dto.ExtraResp{
			UnderScreenExercises: underScreenExercises,
			MultiModalInfo:       controllerResp.Data.FunctionCall.MultiModalInfo,
		}
		return resp, nil
	}

	resp.ExtraResp = &dto.ExtraResp{
		UnderScreenExercises: false,
		MultiModalInfo:       controllerResp.Data.FunctionCall.MultiModalInfo,
	}

	// 小思滤镜处理
	filtersFlag, filtersName, change2xiaosi := u.GetSetXiaoSiFiltersFlag(ctx, req, resp.FunctionList)
	if filtersFlag {
		resp.FiltersInfo = &dto.FiltersInfo{
			FiltersFlag: true,
			FiltersName: filtersName,
			ToSkill:     false,
		}
		return resp, nil
	}
	if change2xiaosi {
		resp.SkillList = append(resp.SkillList, dto.NLUSkillItem{
			Skill:   "no_skill",
			Task:    "no_skill",
			TtsShow: "好的",
			TtsNorm: "好的",
		})
		resp.ModelOutputIntent = ""
		return resp, err
	}

	// 是否继续小思滤镜模式
	continueFilters, filtersName, ToSkill := u.checkAndExitFiltersMode(ctx, req.TalId, resp.ModelOutputIntent)
	// 继续小思滤镜模式，走agent
	if continueFilters && !ToSkill {
		resp.FiltersInfo = &dto.FiltersInfo{
			FiltersFlag: true,
			FiltersName: filtersName,
			ToSkill:     false,
		}
		return resp, nil
	}

	//命中兜底意图
	intent := controllerResp.Data.Intent
	skill, task, tts := u.getDouDiData(intent, req.BizType)
	if tts != "" {
		resp.SkillList = append(resp.SkillList, dto.NLUSkillItem{
			Skill:   skill,
			Task:    task,
			TtsShow: tts,
			TtsNorm: tts,
		})
		resp.ModelOutputIntent = intent
		return resp, err
	}

	if controllerResp.Chain != "new" {
		//调用NLP-Center接口
		if resp.Rewrite.RewriteQuery != "" && resp.Rewrite.RewriteQuery != nluQuery.AsrInput.AsrInfo {
			nluQuery.AsrInput.AsrInfo = resp.Rewrite.RewriteQuery
			nluQuery.AsrInput.AsrPinyin = ""
			nluQuery.AsrInput.AsrLen = len(resp.Rewrite.RewriteQuery)
		}

		nluQuery.ControllerResp = controllerResp
		nlpResp, err := u.QueryNlpCenter(ctx, nluQuery)
		if err != nil {
			return nil, err
		}

		resp.SkillList = nlpResp.Data.SkillList
		resp.RequestID = nlpResp.Data.RequestID
		resp.UserID = nlpResp.Data.UserID
		resp.LegalCheckType = nlpResp.Data.LegalCheckType
		resp.ControllerVersion = nlpResp.Data.ControllerVersion

		return resp, nil
	}

	bizExtra := &dto.BizNlUExtra{}
	if req.BizExtra != "" {
		_ = json.Unmarshal([]byte(req.BizExtra), bizExtra)
	}
	////工作坊处理非系统调用不走skill
	//if strings.HasPrefix(req.BizType, common.BizTypeWorkshop) && resp.ModelOutputIntent != "系统控制" {
	//	return resp, nil
	//}

	//调用Skill-Hub /v2/call
	funcReq := &dto.FunctionCallReq{
		RequestId:          nluQuery.RequestID,
		UserSystem:         nluQuery.UserSystem,
		AsrInput:           nluQuery.AsrInput,
		SceneCode:          nluQuery.SceneCode,
		Intent:             controllerResp.Data.Intent,
		FunctionCall:       controllerResp.Data.FunctionCall,
		SlotFillList:       fillSlots,
		ScreenMode:         req.ScreenMode,
		BizType:            req.BizType,
		AiTutorPlanBizCode: bizExtra.AiTutorPlanBizCode,
		AiTutorQAType:      bizExtra.AiTutorQAType,
	}
	funcResp, err := u.FunctionCall(ctx, funcReq, req)
	if err != nil {
		return nil, err
	}

	if len(funcResp.SkillList) > 0 {
		if controllerResp.Data.Baike.Task == "baike.knowledge" && controllerResp.Data.Baike.Text != "" {
			funcResp.SkillList = append(funcResp.SkillList, dto.NLUSkillItem{
				Skill:         "baike",
				Task:          controllerResp.Data.Baike.Task,
				TtsNorm:       controllerResp.Data.Baike.TtsNormText,
				TtsShow:       controllerResp.Data.Baike.Text,
				IsResultValid: controllerResp.Data.Baike.IsValidWidget,
			})
		}
	} else {
		if controllerResp.Data.Baike.Text != "" {
			funcResp.SkillList = append(funcResp.SkillList, dto.NLUSkillItem{
				Skill:         "baike",
				Task:          controllerResp.Data.Baike.Task,
				TtsNorm:       controllerResp.Data.Baike.TtsNormText,
				TtsShow:       controllerResp.Data.Baike.Text,
				IsResultValid: controllerResp.Data.Baike.IsValidWidget,
			})
		}
	}

	skillList := funcResp.SkillList
	skillList = u.ProcessExercisesSkill(ctx, req, skillList, visionInput, controllerResp.Data.FunctionCall)
	resp.SkillList = skillList
	// 继续小思滤镜模式,走skill
	if continueFilters && ToSkill {
		resp.FiltersInfo = &dto.FiltersInfo{
			FiltersFlag: true,
			FiltersName: filtersName,
			ToSkill:     true,
		}
	}

	resp.SkillList = funcResp.SkillList
	resp.ModelOutputIntent = funcResp.ModelOutputIntent
	return resp, nil
}

func (u NluUsecase) GetMultiModelFlag(ctx context.Context, sceneCode common.SceneCode, bizType string) (multiModalNluFlag int) {

	// 判断是否应该启用多模态NLU
	isXPad25App := strings.Contains(u.third.AppIdMap["xPad25AppIds"], custom_context.GetXAppId(ctx))

	isMultiModalAllowed := !common.GetXWithOutMultiModal(ctx)
	isXiaoSiOrHomework := sceneCode == common.SceneXiaoSi || strings.HasPrefix(bizType, common.BizTypeHomeWork)
	if isMultiModalAllowed && isXiaoSiOrHomework {
		if isXPad25App {
			multiModalNluFlag = 1
		}
	}

	if strings.HasPrefix(bizType, common.BizTypeTopicDialogue) {
		multiModalNluFlag = 0
	}

	return multiModalNluFlag
}

// 深度思考的意图判断方式
func (u NluUsecase) GetSetDeepSeekFlag(ctx context.Context, req *v1.LuiNluRequest, functions []dto.Function) (deepSeekFlag bool, deepSeekIntent bool) {

	for _, function := range functions {
		if function.FuncName != "app_operation" {
			deepSeekIntent = false
		}
		paramMap, ok := function.FuncParamaters.(map[string]interface{})
		if !ok {
			continue
		}

		for key, value := range paramMap {
			if key == "app_name" && value == "deep_thinking_qa" {
				deepSeekIntent = true
			}
		}
	}

	deepseekGrayHit := u.DeepseekGrayHit(custom_context.GetXAppId(ctx), custom_context.GetXOsPkgDate(ctx), custom_context.GetXDeviceId(ctx))
	if deepSeekIntent && deepseekGrayHit {
		deepSeekFlag = true
	}

	return
}

func (u NluUsecase) GetUnderScreenExercises(ctx context.Context, req *v1.LuiNluRequest, visionInput *dto.VisionInput, functions []dto.Function) (underScreenExercises bool) {
	return false
	/*explainQuestionAgentListGrayHit := u.ExplainQuestionAgentListGrayHit(custom_context.GetXAppId(ctx), custom_context.GetXOsPkgDate(ctx), custom_context.GetXDeviceId(ctx))
	if explainQuestionAgentListGrayHit {
		return false
	}

	if visionInput == nil {
		return false
	}

	if visionInput.DesktopImageInfo == nil || visionInput.DesktopImageInfo.ImageURL == "" {
		return false
	}

	for _, function := range functions {
		if function.FuncName != common.SkillExercisesTool.ToString() {
			return false
		}
		var req dto.SearchQuestionReqParams
		marshal, err := json.Marshal(function.FuncParamaters)
		if err != nil {
			return false
		}
		err = json.Unmarshal(marshal, &req)
		if err != nil {
			return false
		}
		if req.Info == "exam" {
			return false
		}
	}
	//作业模式需要有集合搜能力
	if strings.HasPrefix(req.BizType, common.BizTypeHomeWork) {
		return false
	}

	return true*/
}

func (u NluUsecase) HandleMultiModeOcrData(ctx context.Context, bizType, modelOutputIntent string, functions []dto.Function) (mixedModal bool, ocrType common.MixedModalOcrType) {
	/*if strings.Contains(u.third.AppIdMap["xPad25AppIds"], custom_context.GetXAppId(ctx)) &&
		bizType != common.BizTypeHomeWork &&
		bizType != common.BizTypeTopicDialogue &&
		(strings.Contains(modelOutputIntent, "闲聊") || strings.Contains(modelOutputIntent, "百科问答") || strings.Contains(modelOutputIntent, "查学科知识")) {
		return true, common.MixedModalOcrTypePageBaike
	}*/

	if bizType == common.BizTypeTopicDialogue || bizType == common.BizTypeHomeWork {
		return false, common.MixedModalOcrTypeDefault
	}

	for _, function := range functions {
		paramMap, ok := function.FuncParamaters.(map[string]interface{})
		if !ok {
			continue
		}

		for key, value := range paramMap {
			if !strings.HasSuffix(key, "_multi") {
				continue
			}
			nestedMap, ok := value.(map[string]interface{})
			if !ok {
				continue
			}

			slotType, slotTypeExists := nestedMap["slot_type"]
			slotSource, slotSourceExists := nestedMap["slot_source"]

			if slotTypeExists && slotSourceExists {
				mixedModal = true
				ocrType = common.MixedModalOcrTypeToInt(slotType.(string))

				// 识别类型为图片时，ocrType为page_baike
				if slotSource.(string) == "photo" {
					ocrType = common.MixedModalOcrTypePageBaike
				}

				u.log.WithContext(ctx).Infof("slot_type: %v, slot_source: %v", slotType, slotSource)
			}
		}
	}

	return
}

// ControllerCall 调用nlu-controller
func (u NluUsecase) ControllerCall(ctx context.Context, query *dto.NLUQuery) (*dto.NLUControllerResp, error) {
	u.log.WithContext(ctx).Infof("ControllerCall input: %+v", util.Marshal(query))

	resp, err := resty.New().OnBeforeRequest(func(client *resty.Client, r *resty.Request) error {
		headers := map[string]string{
			"Traceparent": fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
		}
		client.SetRetryCount(0).
			SetTimeout(time.Second * time.Duration(u.third.NlpFunc.ControllerTimeout)).
			SetHeaders(headers)
		return nil
	}).
		R().
		SetBody(query).
		Post(u.third.NlpFunc.Controller)

	if err != nil {
		u.log.WithContext(ctx).Warnf("获取nlu-controller服务失败， err:%+v", err)
		return nil, err
	}

	var rawResp dto.NLUControllerResp
	err = json.Unmarshal(resp.Body(), &rawResp)
	if err != nil {
		u.log.WithContext(ctx).Warnf("ControllerRawResp resp:%v，latency:%v, err:%s", resp.String(), resp.Time().Milliseconds(), err.Error())
		return nil, err
	}
	if rawResp.Code != 0 {
		u.log.WithContext(ctx).Infof("ControllerRawResp.Code!=0,latency:%v,resp:%s", resp.Time().Milliseconds(), util.Marshal(rawResp))
		return nil, errors.New("ControllerCall第三方code码不正确")
	}

	u.log.WithContext(ctx).Infof("ControllerCall output: %+v, latency: %v", util.Marshal(rawResp), resp.Time().Milliseconds())

	return &rawResp, nil
}

// 获取兜底话术
func (u NluUsecase) getDouDiData(intent string, bizType string) (skill string, task string, tts string) {
	//AT场景func处理
	biz := "xiaosi"
	if strings.Contains(bizType, common.BizTypeAiTutor) {
		biz = "bella"
	}

	//tts
	douDiBizMap := u.third.NlpFunc.DouDiMap[biz]
	if douDiBizMap == nil {
		return
	}
	ttsArr := douDiBizMap.DouDiIntentMap[intent]
	if ttsArr == nil {
		return
	}

	tts = ttsArr.Items[rand.Intn(len(ttsArr.Items))]

	//skill task
	switch intent {
	case DouDiCalculate:
		skill = "calculate"
		task = "calculate.default_tts"
		break
	case DouDiComposition:
		skill = "composition"
		task = "composition.default_tts"
		break
	case DouDiEbook:
		skill = "ebook"
		task = "ebook.default_tts"
		break
	case DouDiAudio:
		skill = "audio"
		task = "audio.default_tts"
		break
	case DouDiMusic:
		skill = "music"
		task = "music.default_tts"
		break
	case DouDiCartoon:
		skill = "cartoon"
		task = "cartoon.default_tts"
		break
	case DouDiStory:
		skill = "story"
		task = "story.default_tts"
		break
	case DouDiUnknown:
		skill = "unknown"
		task = "unknown.default_tts"
		break
	case DouDiSearchPicture:
		skill = "no_skill"
		task = "no_task"
		break
	default:
		break
	}

	return
}

// FunctionCall 调用nlp-func
func (u NluUsecase) FunctionCall(ctx context.Context, funcReq *dto.FunctionCallReq, nluReq *v1.LuiNluRequest) (*dto.FunctionCallRespData, error) {
	//判断版本
	version := utils.GetVersionNum(custom_context.GetXOSVersion(ctx))
	if version > "240712" {
		funcReq.FunctionCall = u.GetRealFunctionActionList(ctx, funcReq.FunctionCall, nluReq)
	}
	u.log.WithContext(ctx).Infof("FunctionCall input: %+v", util.Marshal(funcReq))

	resp, err := resty.New().OnBeforeRequest(func(client *resty.Client, r *resty.Request) error {
		headers := map[string]string{
			"X-Genie-TraceId":   nluReq.RequestId,
			"X-Genie-AppId":     custom_context.GetXAppId(ctx),
			"X-Genie-DeviceId":  custom_context.GetXDeviceId(ctx),
			"X-Genie-Osversion": custom_context.GetXOSVersion(ctx),
			"Traceparent":       fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
		}
		client.SetRetryCount(0).
			SetTimeout(time.Second * time.Duration(u.third.NlpFunc.FunctionCallTimeout)).
			SetHeaders(headers)
		return nil
	}).
		R().
		SetBody(funcReq).
		Post(u.third.NlpFunc.FunctionCall)

	if err != nil {
		u.log.WithContext(ctx).Warnf("获取function-call， err:%+v", err)
		return nil, err
	}

	var rawResp dto.FunctionCallResp
	err = json.Unmarshal(resp.Body(), &rawResp)
	if err != nil {
		u.log.WithContext(ctx).Warnf("FunctionCallResp resp，latency:%v,err:%s", resp.Time().Milliseconds(), err.Error())
		return nil, err
	}

	if rawResp.Code != 0 {
		u.log.WithContext(ctx).Infof("FunctionCallResp.Code!=0,latency:%v,resp:%s", resp.Time().Milliseconds(), util.Marshal(rawResp))
		return nil, errors.New("FunctionCall第三方code码不正确")
	}

	u.log.WithContext(ctx).Infof("FunctionCall output: %+v", util.Marshal(rawResp))

	return rawResp.Data, nil
}

// PreHandler 预处理
func (u NluUsecase) PreHandler(ctx context.Context, nluQuery *dto.NLUQuery) *dto.NLUQuery {

	if nluQuery.SessionID == "" {
		nluQuery.SessionID = custom_context.GetTraceId(ctx)
	}

	txt := nluQuery.AsrInput.AsrInfo
	//过滤标点符号
	symbolTxt := "!\"#$&()\\*\\+/:;<=>?@[\\]\\^\\{|}~"
	//汉译英需要保留标点，因此对，。！？、保留
	zhSymbolTxt := `;《》""''@#￥…&×（）——+【】{};；●&～|:：`
	symbolReg := regexp.MustCompile("[" + symbolTxt + zhSymbolTxt + "]")
	symbolReplace := symbolReg.ReplaceAllLiteralString(txt, "")

	symbolReplace = strings.TrimSpace(symbolReplace)
	if txt != symbolReplace {
		nluQuery.AsrInput.AsrInfo = symbolReplace
		nluQuery.AsrInput.AsrLen = len(symbolReplace)
		u.log.WithContext(ctx).Infof("替换标点符号后的字符串：%s", symbolReplace)
	}

	return nluQuery
}

// QueryNlpCenter 调用nlp-center
func (u NluUsecase) QueryNlpCenter(ctx context.Context, req *dto.NLUQuery) (*dto.NLPQueryPad2Resp, error) {
	u.log.WithContext(ctx).Infof("QueryNlpCenter input:%+v", util.Marshal(req))
	req.AppId = custom_context.GetXAppId(ctx)

	resp, err := resty.New().OnBeforeRequest(func(client *resty.Client, r *resty.Request) error {
		headers := map[string]string{
			"X-Genie-TraceId":   custom_context.GetTraceId(ctx),
			"X-Genie-DeviceId":  custom_context.GetXDeviceId(ctx),
			"X-Genie-Osversion": custom_context.GetXOSVersion(ctx),
			"Traceparent":       fmt.Sprintf("00-%s-%s-01", req.RequestID, trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
		}
		client.SetRetryCount(0).
			SetTimeout(time.Second * time.Duration(6)).
			SetHeaders(headers)
		return nil
	}).
		R().
		SetBody(req).
		Post(u.third.NlpFunc.QueryNlpV2)

	if err != nil {
		u.log.WithContext(ctx).Warnf("获取nlp-center服务失败， err:%+v", err)
		return nil, err
	}
	u.log.WithContext(ctx).Infof("QueryNlpCenter output:%s", string(resp.Body()))

	var rawResp dto.NLPQueryPad2Resp
	err = json.Unmarshal(resp.Body(), &rawResp)
	if err != nil {
		u.log.WithContext(ctx).Warnf("NlpCenterResp resp，latency:%v, err:%s", resp.Time().Milliseconds(), err.Error())
		return nil, err
	}
	if rawResp.Code != 0 {
		u.log.WithContext(ctx).Infof("NlpCenterResp.Code!=0,latency:%v,resp:%s", resp.Time().Milliseconds(), util.Marshal(rawResp))

		return nil, errors.Errorf("NlpCenterResp.Code %d, msg: %s", rawResp.Code, rawResp.Msg)
	}
	return &rawResp, nil
}

// 判断学练机是否支持的意图
func (u NluUsecase) IsXueLianJiSupportIntent(ctx context.Context, req *v1.LuiNluRequest, intent string) *dto.NLUQueryPad2Resp {
	if !strings.Contains(u.third.AppIdMap["xueLianJiAppIds"], custom_context.GetXAppId(ctx)) {
		return nil
	}

	resp := &dto.NLUQueryPad2Resp{
		RequestID:         req.RequestId,
		Version:           custom_context.GetXVersion(ctx),
		Input:             req.AsrInfo,
		SessionId:         req.RequestId,
		SkillList:         make([]dto.NLUSkillItem, 0),
		ModelOutputIntent: intent,
	}

	var (
		screenMode = dto.ScreeMode(req.GetScreenMode())
		ok         bool
		tts        string
	)

	if custom_context.GetXAppId(ctx) == u.third.AppIdMap["xueLianJiAppId"] {
		if ok, tts = screenMode.IsXueLianJiSupport(intent); ok {
			return nil
		}
	}

	if custom_context.GetXAppId(ctx) == u.third.AppIdMap["xueLianJi25AppId"] {
		if ok, tts = screenMode.Is25XueLianJiSupport(intent); ok {
			return nil
		}
	}

	skillItem := dto.NLUSkillItem{
		Skill:   "no_skill",
		TtsNorm: tts,
		TtsShow: tts,
	}
	resp.SkillList = append(resp.SkillList, skillItem)

	return resp
}

// 判断作业模式是否支持的意图
func (u NluUsecase) IsHomeWorkSupportIntent(ctx context.Context, req *v1.LuiNluRequest, intent string) *dto.NLUQueryPad2Resp {
	resp := &dto.NLUQueryPad2Resp{
		RequestID:         req.RequestId,
		Version:           custom_context.GetXVersion(ctx),
		Input:             req.AsrInfo,
		SessionId:         req.RequestId,
		SkillList:         make([]dto.NLUSkillItem, 0),
		ModelOutputIntent: intent,
	}
	randomIndex := rand.Intn(len(u.third.HomeWorkDoudi))
	tts := u.third.HomeWorkDoudi[randomIndex]
	skillItem := dto.NLUSkillItem{
		Skill:   "no_skill",
		TtsNorm: tts,
		TtsShow: tts,
	}
	resp.SkillList = append(resp.SkillList, skillItem)

	return resp
}

func (u NluUsecase) FetchZhConfirmNLU4LUI(ctx context.Context, req *v1.LuiNluConfirmRequest) (*dto.NewNluResData, error) {
	resp := &dto.NewNluResData{}
	//获取nluQuery
	asrInput := dto.AsrInput{
		AsrInfo:   req.AsrInput.AsrInfo,
		AsrLen:    len(req.AsrInput.AsrInfo),
		AsrPinyin: req.AsrInput.AsrPinyin,
	}

	userSystem := dto.UserSystemV2{
		Location: req.UserSystem.Location,
		Grade:    int(req.UserSystem.Grade),
		Semester: req.UserSystem.Semester,
	}

	funcCall := dto.FunctionCall{
		Task: "zh_dic",
		Functions: []dto.Function{
			{
				FuncName: "zh_word_confirm", //自定义的，和skill-hub一致,算法中用的fetch_chinese_character_or_word_attributes
				FuncParamaters: dto.ZhWordSearchParams{
					TargetText: req.AsrInput.AsrInfo,
				},
			},
		},
	}

	//调用Skill-Hub /v2/call
	funcReq := &dto.FunctionCallReq{
		RequestId:    req.RequestId,
		UserSystem:   userSystem,
		AsrInput:     asrInput,
		FunctionCall: funcCall,
	}
	nluReq := &v1.LuiNluRequest{
		RequestId: req.RequestId,
	}
	funcResp, err := u.FunctionCall(ctx, funcReq, nluReq)
	if err != nil {
		return resp, err
	}

	if len(funcResp.SkillList) > 0 {
		skillItem := funcResp.SkillList[0]
		resp = &dto.NewNluResData{
			UserID:    req.UserId,
			RequestID: req.RequestId,
			Skill:     skillItem.Skill,
			Task:      skillItem.Task,
			TtsShow:   skillItem.TtsShow,
			//TtsNorm:       skillItem.TtsNorm, //一定要屏蔽，否则端上二次确认不读出来
			ShowTypeList:  skillItem.ShowTypeList,
			ShowCarType:   skillItem.ShowCarType,
			IsResultValid: skillItem.IsResultValid,
			NearWord:      skillItem.NearWord,
			Widget:        skillItem.Widget,
			Input:         req.AsrInput.AsrInfo,
		}
	}

	return resp, nil
}

func (u NluUsecase) GetRealFunctionActionList(ctx context.Context, action dto.FunctionCall, nluReq *v1.LuiNluRequest) dto.FunctionCall {
	var (
		screenMode = dto.MODE_Default
		deviceType = dto.ActionMapDeviceTypeDefault
	)
	if strings.Contains(u.third.AppIdMap["xueLianJiAppIds"], custom_context.GetXAppId(ctx)) {
		deviceType = dto.ActionMapDeviceTypeXueLianJi
		screenMode = dto.ScreeMode(nluReq.ScreenMode)
	}

	actionList := make([]dto.Function, 0)
	screenMap, screenMapExists := dto.ActionMap[deviceType][screenMode]

	for _, function := range action.Functions {
		if screenMapExists {
			if f, ok := screenMap[function.FuncName]; ok {
				for _, f2 := range f {
					// 主要透传重写后的 asr info
					f2.FuncParamaters = function.FuncParamaters
					actionList = append(actionList, f2)
				}
				continue
			}
		}
		actionList = append(actionList, function)
	}

	action.Functions = actionList
	return action
}

func (u NluUsecase) GetSceneCode(ctx context.Context, bizType string) common.SceneCode {
	var sceneCode common.SceneCode
	switch {
	case strings.HasPrefix(bizType, common.BizTypeAiTutor):
		sceneCode = common.SceneAiTutor
	case strings.HasPrefix(bizType, common.BizTypeCompanionLearn):
		sceneCode = common.SceneCompanionLearn

	//case strings.HasPrefix(bizType, common.BizTypeHomeWork) && custom_context.GetXAppId(ctx) == u.third.AppIdMap["xPad2AppId_qijian25"]:
	//	sceneCode = common.SceneHomeWork
	default:
		sceneCode = common.SceneXiaoSi
	}

	return sceneCode
}

func (u NluUsecase) MrIntentSnListGrayHit(ctx context.Context, sn string) int {
	user := feature.UserWithAttrs(map[string]interface{}{
		"sn": sn,
	})
	hitSn := feature.BoolDetail(common.XsMrIntentSnList, user, false)
	log.Infof("XsMrIntentSnListGrayHit sn:%s, hit:%v", sn, hitSn.Value)

	if hitSn.Value {
		return 1
	}

	return 0
}

func (u NluUsecase) GetVisionInput(ctx context.Context, req *v1.LuiNluRequest) (*dto.VisionInput, string) {
	var (
		sessionToken     string
		visionInput      = &dto.VisionInput{}
		traceImageInfo   = &dto.TraceImageInfoData{}
		desktopImageInfo = &dto.DesktopImageInfoData{}
	)

	sessionId := req.RequestId
	if strings.Index(sessionId, "-") > 0 {
		sessionId = sessionId[:strings.Index(sessionId, "-")]
	}

	visionData, err := u.visionRepo.GetVisionImage(ctx, sessionId)
	u.log.WithContext(ctx).Infof("GetVisionInput RequestId: %s, visionData: %s", sessionId, visionData)
	if err != nil {
		return visionInput, sessionToken
	}

	err = json.Unmarshal([]byte(visionData), desktopImageInfo)
	if err == nil {
		if len(desktopImageInfo.FingerPos2ma) > 0 {
			_ = u.visionRepo.DelVisionImage(ctx, sessionId)
			visionInput.DesktopImageInfo = desktopImageInfo
			sessionToken = desktopImageInfo.SessionToken
			return visionInput, sessionToken
		}
	}

	err = json.Unmarshal([]byte(visionData), traceImageInfo)
	if err == nil {
		visionInput.TraceImageInfo = traceImageInfo
		sessionToken = traceImageInfo.SessionToken
		return visionInput, sessionToken
	}

	return visionInput, sessionToken
}

func (u NluUsecase) XsUnclearIntentUpgradeGrayHit(appId string, osVersion int, sn string) bool {

	user := feature.UserWithAttrs(map[string]interface{}{
		"app_id":  appId,
		"version": osVersion,
		"sn":      sn,
	})
	hitSn := feature.BoolDetail(common.XsUnclearIntentUpgrade, user, false)
	log.Infof("XsUnclearIntentUpgradeGrayHit app_id:%s; version: %d; sn:%s, hit:%v", appId, osVersion, sn, hitSn.Value)
	return hitSn.Value
}

func (u NluUsecase) DeepseekGrayHit(appId string, osVersion int, sn string) bool {
	user := feature.UserWithAttrs(map[string]interface{}{
		"app_id":  appId,
		"version": osVersion,
		"sn":      sn,
	})
	hitSn := feature.BoolDetail(common.XsDeepseek, user, false)
	log.Infof("DeepseekSnListGrayHit sn:%s, hit:%v", sn, hitSn.Value)

	return hitSn.Value
}

func (u NluUsecase) GetSetXiaoSiFiltersFlag(ctx context.Context, req *v1.LuiNluRequest, functions []dto.Function) (filtersFlag bool, filtersName string, change2xiaosi bool) {
	// 检查当前滤镜模式
	CurrentFiltersName := u.memoryBiz.GetFiltersFlag(ctx, req.TalId)

	for _, function := range functions {
		// "小思退出"特殊处理,退出全屏页,当前滤镜做最后回复
		if function.FuncName == "system_operate" && CurrentFiltersName != "" {
			paramMap, ok := function.FuncParamaters.(map[string]interface{})
			if !ok {
				continue
			}

			operate, ok := paramMap["operate"].(string)
			if !ok {
				continue
			}

			if operate == "close_microphone" {
				u.memoryBiz.DelFiltersFlag(ctx, req.TalId)
				u.log.WithContext(ctx).Infof("小思退出全屏页特殊处理_小思滤镜模式 talId: %s", req.TalId)
				return true, CurrentFiltersName, false
			}
		}

		// 获取指令参数
		if function.FuncName != "app_operation" {
			continue
		}
		paramMap, ok := function.FuncParamaters.(map[string]interface{})
		if !ok {
			continue
		}
		operate, ok := paramMap["operate"].(string)
		if !ok {
			continue
		}
		appName, ok := paramMap["app_name"].(string)
		if !ok {
			continue
		}

		// 切到小思，特殊处理，可以理解为退出滤镜返回小思模式
		if operate == "open" && appName == "小思" {
			u.memoryBiz.DelFiltersFlag(ctx, req.TalId)
			u.log.WithContext(ctx).Infof("返回小思模式特殊处理_指令关闭小思滤镜模式 talId: %s", req.TalId)
			return false, "", true
		}

		// 检查 app_name 是否在白名单中
		filtersRoleWhitelist := u.third.Filters.FiltersRoleWhiteList
		if !util.SliceContainStr(filtersRoleWhitelist, appName) {
			continue
		}

		// 根据 operate 的值设置返回结果
		switch operate {
		case "open":
			filtersFlag = true
			filtersName = appName
			u.memoryBiz.SetFiltersFlag(ctx, req.TalId, filtersName)
			u.log.WithContext(ctx).Infof("指令开启小思滤镜模式 talId: %s, filtersName: %s", req.TalId, filtersName)
			return
		case "close":
			// 当前未在小思滤镜模式，关闭无效，直接返回
			if CurrentFiltersName == "" {
				return
			}
			// 当前在小思滤镜模式，但不是当前app_name，关闭无效，直接返回
			if CurrentFiltersName != appName {
				return
			}
			// 命中关闭指令，当前滤镜做最后回复
			filtersFlag = true
			filtersName = CurrentFiltersName
			u.memoryBiz.DelFiltersFlag(ctx, req.TalId)
			u.log.WithContext(ctx).Infof("指令关闭小思滤镜模式 talId: %s", req.TalId)
			return
		default:
			u.log.WithContext(ctx).Warnf("unknown operate type: %s", operate)
			continue
		}
	}
	return
}

func (u NluUsecase) checkAndExitFiltersMode(ctx context.Context, talId string, intent string) (bool, string, bool) {
	// 检查当前是否在小思滤镜模式
	CurrentFiltersName := u.memoryBiz.GetFiltersFlag(ctx, talId)
	if CurrentFiltersName == "" {
		return false, "", false
	}

	filtersIntentWhitelist := u.third.Filters.FiltersIntentWhiteList
	filtersIntentWhitelistToSkill := u.third.Filters.FiltersIntentWhiteListToSkill

	if !util.SliceContainStr(filtersIntentWhitelist, intent) {
		// 命中退出意图，小思做最后回复
		u.memoryBiz.DelFiltersFlag(ctx, talId)
		u.log.WithContext(ctx).Infof("意图退出删除小思滤镜模式 talId: %s", talId)
		return false, "", false
	}

	// 命中保持意图
	if util.SliceContainStr(filtersIntentWhitelistToSkill, intent) {
		// 音色继续使用滤镜，走skill
		println("音色继续使用滤镜，走skill,intent:", intent)
		return true, CurrentFiltersName, true
	}

	// 命中保持意图, 音色继续使用滤镜，走agent
	u.log.WithContext(ctx).Infof("保持小思滤镜模式 talId: %s", talId)
	return true, CurrentFiltersName, false
}

func (u NluUsecase) ExplainQuestionAgentListGrayHit(appId string, osVersion int, sn string) bool {
	user := feature.UserWithAttrs(map[string]interface{}{
		"app_id":  appId,
		"version": osVersion,
		"sn":      sn,
	})
	hitSn := feature.BoolDetail(common.XsExplainQuestionAgentList, user, false)
	log.Infof("ExplainQuestionAgentListGrayHit app_id:%s; version: %d; sn:%s, hit:%v", appId, osVersion, sn, hitSn.Value)
	return hitSn.Value
}
