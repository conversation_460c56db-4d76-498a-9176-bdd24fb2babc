package biz

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"lui-api/internal/conf"
	"lui-api/internal/data/dto"
)

type EmbeddingDBRepo interface {
	Hint(ctx context.Context, database, collection string, filter dto.SlotCombineCheckReq) (hintStatus bool, err error)
	FaqQuery(ctx context.Context, database, collection, query string) (do *dto.FaqVectorDo, err error)
	HotfixQuery(ctx context.Context, query string) (do *dto.HotfixVectorDo, err error)
}

type EmbeddingSyncBiz struct {
	embeddingDBRepo EmbeddingDBRepo
	log             *log.Helper
	conf            *conf.Data
}

func NewEmbeddingSyncBiz(logger log.Logger, embeddingDBRepo EmbeddingDBRepo, data *conf.Data) *EmbeddingSyncBiz {
	return &EmbeddingSyncBiz{
		embeddingDBRepo: embeddingDBRepo,
		log:             log.<PERSON><PERSON><PERSON><PERSON>(logger),
		conf:            data,
	}
}

func (em EmbeddingSyncBiz) HintStatus(ctx context.Context, filter dto.SlotCombineCheckReq) (hintStatus bool, err error) {
	collectionName, err := filter.Category.Collection()
	if err != nil {
		return false, errors.WithMessagef(err, "embeddingDBRepo: HintStatus error, filter: %v", filter)
	}
	return em.embeddingDBRepo.Hint(ctx, em.conf.Embedding.Db.Database, collectionName, filter)
}

// QueryFaqAnswer 查询FAQ答案
func (em EmbeddingSyncBiz) QueryFaqAnswer(ctx context.Context, query string) (do *dto.FaqVectorDo, err error) {
	if !em.conf.Embedding.DbFaq.FaqEnable {
		return nil, errors.New("embeddingDBRepo: QueryFaqAnswer error, faq not enable")
	}
	if query == "" {
		return nil, errors.New("embeddingDBRepo: QueryFaqAnswer error, query is empty")
	}

	return em.embeddingDBRepo.FaqQuery(ctx, em.conf.Embedding.DbFaq.Database, em.conf.Embedding.DbFaq.Collection, query)
}

// QueryHotfix 快修查询
func (em EmbeddingSyncBiz) QueryHotfix(ctx context.Context, query string) (do *dto.HotfixVectorDo, err error) {
	if !em.conf.Embedding.DbHotfix.Enable {
		return nil, errors.New("embeddingDBRepo: QueryHotfix error, not enable")
	}
	if query == "" {
		return nil, errors.New("embeddingDBRepo: QueryHotfix error, query is empty")
	}

	return em.embeddingDBRepo.HotfixQuery(ctx, query)
}
