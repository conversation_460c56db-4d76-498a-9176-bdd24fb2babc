package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"lui-api/internal/conf"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	util "lui-api/internal/pkg/utils"
	"net/http"
	"net/url"
	"time"
)

type ToolsBiz struct {
	log   *log.Helper
	third *conf.Third
}

func NewToolsBiz(logger log.Logger, third *conf.Third) *ToolsBiz {
	return &ToolsBiz{log: log.NewHelper(logger), third: third}
}

func (u *ToolsBiz) GetResourceList(ctx context.Context, params []*dto.ToolsResourceParam) (*dto.GetResourceListRes, error) {

	in := &dto.GetResourceListReq{List: params}
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	var toolsUrl string
	if custom_context.GetXAppId(ctx) == u.third.XPad1AppId {
		toolsUrl = u.third.XPad1.ToolsUrl
	} else {
		toolsUrl = u.third.XPad2.ToolsUrl
	}
	ch := util.CurlInit(ctx, time.Second*4, 1, u.log)
	postResp, err := ch.ChRequest(ch.Req.SetHeaders(headers).SetBody(in)).Post(toolsUrl)

	if postResp == nil {
		return nil, errors.New(500, "rpc", "err")
	}

	if postResp.StatusCode() != http.StatusOK {
		var errResp dto.Response
		err = json.Unmarshal(postResp.Body(), &errResp)
		if err != nil {
			err = errors.New(400, "param", "err")
		} else {
			err = &errors.Error{
				Status: errors.Status{
					Code:     int32(postResp.StatusCode()),
					Reason:   errResp.ErrorReason,
					Message:  errResp.ErrorMsg,
					Metadata: errResp.MetaData,
				},
			}
		}

		return nil, err
	}
	u.log.WithContext(ctx).Infof("GetResourceList resp:%s", string(postResp.Body()))
	var resourceList dto.GetToolsResourceRes
	err = json.Unmarshal(postResp.Body(), &resourceList)
	if err != nil {
		return nil, errors.New(400, "param", "err")
	}

	return &resourceList.Data, nil
}

func (u *ToolsBiz) GetUserVersion(ctx context.Context, params *dto.UserVersionReq) (*dto.UserVersionRes, error) {

	input, err := json.Marshal(params)
	if err != nil {
		return nil, err
	}
	queryParams := make(map[string]interface{})
	err = json.Unmarshal(input, &queryParams)
	if err != nil {
		return nil, err
	}
	values := url.Values{}
	for key, value := range queryParams {
		values.Add(key, fmt.Sprintf("%v", value))
	}
	var toolsUserVersionUrl string
	if custom_context.GetXAppId(ctx) == u.third.XPad1AppId {
		toolsUserVersionUrl = u.third.XPad1.ToolsUserVersionUrl
	} else {
		toolsUserVersionUrl = u.third.XPad2.ToolsUserVersionUrl
	}

	ch := util.CurlInit(ctx, time.Second*4, 1, u.log)
	getResp, err := ch.ChRequest(ch.Req.SetQueryString(values.Encode())).Get(toolsUserVersionUrl)

	if getResp == nil {
		return nil, errors.New(500, "rpc", "err")
	}

	if getResp.StatusCode() != http.StatusOK {
		var errResp dto.Response
		err = json.Unmarshal(getResp.Body(), &errResp)
		if err != nil {
			err = errors.New(400, "param", "GetUserVersion err")
		} else {
			err = &errors.Error{
				Status: errors.Status{
					Code:     int32(getResp.StatusCode()),
					Reason:   errResp.ErrorReason,
					Message:  errResp.ErrorMsg,
					Metadata: errResp.MetaData,
				},
			}
		}

		return nil, err
	}
	u.log.WithContext(ctx).Infof("GetUserVersion resp:%s", string(getResp.Body()))
	var resourceList dto.GetUserVersionApiRes
	err = json.Unmarshal(getResp.Body(), &resourceList)
	if err != nil {
		return nil, errors.New(400, "param", "GetUserVersion err")
	}

	return &resourceList.Data, nil
}
