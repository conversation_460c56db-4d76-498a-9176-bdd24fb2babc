package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-errors/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/parkingwang/go-sign"
	"github.com/spf13/cast"
	"go.opentelemetry.io/otel/trace"
	"lui-api/internal/conf"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	"lui-api/internal/pkg/utils"
	"lui-api/pkg/util"
	"time"
)

// PaperUsecase 试卷资源
type PaperUsecase struct {
	log   *log.Helper
	third *conf.Third
}

func NewPaperUsecase(logger log.Logger, third *conf.Third) *PaperUsecase {
	return &PaperUsecase{log: log.NewHelper(logger), third: third}
}

func (u PaperUsecase) QueryExamPaper(ctx context.Context, req *dto.PaperIdsQueryReq) (*dto.PaperIdQueryResp, error) {
	if custom_context.GetXAppId(ctx) == u.third.XPad1AppId {
		return u.QueryExamPaperPad1(ctx, req)
	} else {
		return u.QueryExamPaperPad2(ctx, req)
	}
}

func (u PaperUsecase) QueryExamPaperPad1(ctx context.Context, req *dto.PaperIdsQueryReq) (*dto.PaperIdQueryResp, error) {
	now := time.Now().Unix()
	signer := sign.NewGoSignerMd5()
	signer.SetAppId(u.third.XPad1.ContentAppId)
	signer.SetTimeStamp(now)
	params := make(map[string]interface{})
	params["category"] = req.Category
	params["paper_ids"] = req.PaperIds
	params["time_stamp"] = now
	paperIds, _ := json.Marshal(req.PaperIds)
	signer.AddBody("category", cast.ToString(req.Category.Int()))
	signer.AddBody("paper_ids", string(paperIds))
	signer.AddBody("time_stamp", cast.ToString(now))

	//计算得出的签名
	targetSign := signer.GetSignature()
	params["sign"] = targetSign

	ch := utils.CurlInit(ctx, time.Second*4, 1, u.log)
	resp, err := ch.ChRequestBody(map[string]string{}, params).Post(u.third.XPad1.ResourceIdsUrl)
	if err != nil {
		u.log.Infof("QueryExamPaper params: %+v", params)
		return nil, err
	}

	u.log.WithContext(ctx).Infof("QueryExamPaper resp: %s; params: %+v", string(resp.Body()), params)
	if resp.IsError() {
		return nil, errors.Errorf("resp status: %s", resp.Status())
	}

	var br dto.BaseResponse
	err = json.Unmarshal(resp.Body(), &br)
	if err != nil {
		return nil, err
	}

	var data dto.PaperIdQueryResp
	err = util.ConvertInterface(br.Data, &data)
	if err != nil {
		u.log.WithContext(ctx).Errorf("QueryExamPaper convert err: %+v; data: %+v", err, br.Data)
	}

	return &data, nil
}

func (u PaperUsecase) QueryExamPaperPad2(ctx context.Context, req *dto.PaperIdsQueryReq) (*dto.PaperIdQueryResp, error) {
	header := &dto.ExactlyHeader{
		XRequestId:     custom_context.GetTraceId(ctx),
		XTalSn:         custom_context.GetXDeviceId(ctx),
		XTalVersion:    custom_context.GetXVersion(ctx),
		XTalDeviceCode: dto.GetXTalDeviceCode(ctx, u.third.AppIdMap).String(),
		Traceparent:    fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
	}

	ch := utils.CurlInit(ctx, time.Second*4, 1, u.log)
	resp, err := ch.ChRequestBody(util.StructToMap(header), req).Post(u.third.XPad2.ResourceIdsUrl)
	if err != nil {
		u.log.Infof("QueryExamPaper params: %+v", req)
		return nil, err
	}

	u.log.WithContext(ctx).Infof("QueryExamPaper resp: %s; params: %+v", string(resp.Body()), req)
	if resp.IsError() {
		return nil, errors.Errorf("resp status: %s", resp.Status())
	}

	var br dto.BaseResponse
	err = json.Unmarshal(resp.Body(), &br)
	if err != nil {
		return nil, err
	}

	var data dto.PaperIdQueryResp
	err = util.ConvertInterface(br.Data, &data)
	if err != nil {
		u.log.WithContext(ctx).Errorf("QueryExamPaper convert err: %+v; data: %+v", err, br.Data)
	}

	return &data, nil
}
