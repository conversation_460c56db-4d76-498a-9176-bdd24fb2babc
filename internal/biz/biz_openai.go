package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"git.100tal.com/jituan_genie_server/mqs"
	"github.com/go-resty/resty/v2"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"go.opentelemetry.io/otel/trace"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	"lui-api/pkg/util"
	"regexp"
	"strings"
	"time"
)

func (u NluUsecase) MathGptAskIsMath(ctx context.Context, header *mqs.Header, traceId string, ask string) (bool, error) {

	mathGptAskClassifyReq := make(map[string]interface{})
	mathGptAskClassifyReq["ask"] = ask

	u.log.WithContext(ctx).Infof("MathGptAskClassify nluReq: %+v", mathGptAskClassifyReq)
	resp, err := resty.New().OnBeforeRequest(func(client *resty.Client, r *resty.Request) error {

		// 计算签名 放在OnBeforeRequest 因为重试需要重新生成签名
		timestamp := cast.ToString(time.Now().Unix())
		secretKey := u.sign.SignSecrets[custom_context.GetXAppId(ctx)]
		nonce := uuid.New().String()
		sign := util.MD5(secretKey + "&X-Genie-Timestamp=" + timestamp + "&X-Genie-Nonce=" + nonce + "&X-Genie-DeviceId=" + header.XGenieDeviceId)

		client.SetRetryCount(1).
			SetTimeout(time.Second * time.Duration(10)).
			SetHeaders(map[string]string{
				"Content-Type":      "application/json",
				"X-Genie-TraceId":   traceId,
				"X-Genie-DeviceId":  header.XGenieDeviceId,
				"X-Genie-Timestamp": timestamp,
				"X-Genie-Nonce":     nonce,
				"X-Genie-AppId":     header.XGenieAppId,
				"X-Genie-Sign":      sign,
				"X-Genie-Version":   header.XGenieVersion,
				"X-Genie-Osversion": header.XGenieOsVersion,
				"X-Genie-Platform":  header.XGeniePlatform,
				"baggage":           "origin=" + u.third.MathGptAskClassifyOrigin,
				"X-Request-Id":      custom_context.GetTraceId(ctx),
				"Traceparent":       fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
			})
		return nil
	}).
		R().
		SetBody(mathGptAskClassifyReq).
		Post(u.third.ProxyUrl)

	u.log.WithContext(ctx).Infof("MathGptAskClassify resp: %+v", resp.String())
	if err != nil {
		return false, err
	}
	if resp.IsError() {
		return false, errors.Errorf("resp: %v", resp.Error())
	}

	var mathGptAskClassifyRes dto.MathGptAskClassifyRes
	if err = json.Unmarshal(resp.Body(), &mathGptAskClassifyRes); err != nil {
		return false, err
	}
	if mathGptAskClassifyRes.Data.Subject == 1 {
		return true, nil
	}
	return false, nil
}

func (u NluUsecase) CheckForNestedImagesWithOCR(ctx context.Context, header *mqs.Header, traceId string, imageUrl, imageBase64 string) (bool, string, error) {

	ocrReq := make(map[string]interface{})
	ocrReq["image_url"] = imageUrl
	ocrReq["image_base64"] = imageBase64

	u.log.WithContext(ctx).Infof("CheckForNestedImagesWithOCR nluReq: %+v", ocrReq)
	resp, err := resty.New().OnBeforeRequest(func(client *resty.Client, r *resty.Request) error {

		// 计算签名 放在OnBeforeRequest 因为重试需要重新生成签名
		timestamp := cast.ToString(time.Now().Unix())
		secretKey := u.sign.SignSecrets[custom_context.GetXAppId(ctx)]
		nonce := uuid.New().String()
		sign := util.MD5(secretKey + "&X-Genie-Timestamp=" + timestamp + "&X-Genie-Nonce=" + nonce + "&X-Genie-DeviceId=" + header.XGenieDeviceId)

		client.SetRetryCount(1).
			SetTimeout(time.Second * time.Duration(10)).
			SetHeaders(map[string]string{
				"Content-Type":      "application/json",
				"X-Genie-TraceId":   traceId,
				"X-Genie-DeviceId":  header.XGenieDeviceId,
				"X-Genie-Timestamp": timestamp,
				"X-Genie-Nonce":     nonce,
				"X-Genie-AppId":     header.XGenieAppId,
				"X-Genie-Sign":      sign,
				"X-Genie-Version":   header.XGenieVersion,
				"X-Genie-Osversion": header.XGenieOsVersion,
				"X-Genie-Platform":  header.XGeniePlatform,
				"baggage":           "origin=" + u.third.OcrOrigin,
				"X-Request-Id":      custom_context.GetTraceId(ctx),
				"Traceparent":       fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
			})
		return nil
	}).
		R().
		SetBody(ocrReq).
		Post(u.third.ProxyUrl)

	u.log.WithContext(ctx).Infof("CheckForNestedImagesWithOCR resp: %+v", resp.String())
	if err != nil {
		return false, "", err
	}
	if resp.IsError() {
		return false, "", errors.Errorf("resp: %v", resp.Error())
	}

	var ocrRes dto.OcrRes
	if err = json.Unmarshal(resp.Body(), &ocrRes); err != nil {
		return false, "", err
	}

	var text string
	// 有印刷体 只展示印刷体
	if len(ocrRes.Data.PrintFormula) > 0 || len(ocrRes.Data.PrintText) > 0 {
		for _, r := range ocrRes.Data.Result {
			text += replaceHashEnclosedStrings(r.Text)
		}
	} else {
		for _, r := range ocrRes.Data.Result {
			text += strings.ReplaceAll(r.Text, "##", "")
		}
	}

	if len(ocrRes.Data.Images) != 0 && len(ocrRes.Data.Sheets) == 0 {
		return true, text, nil
	}
	return false, text, nil
}

func replaceHashEnclosedStrings(input string) string {
	// 定义正则表达式，用于匹配被两个##包围的文本
	re := regexp.MustCompile(`##.*?##`)
	// 使用空格替换所有匹配到的部分
	return re.ReplaceAllString(input, " ")
}
