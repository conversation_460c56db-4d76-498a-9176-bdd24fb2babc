package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"lui-api/internal/conf"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	"lui-api/internal/pkg/utils"
	"lui-api/pkg/util"
	"strconv"
	"time"
)

type XiaosiRagBiz struct {
	log   *log.Helper
	third *conf.Third
}

func NewXiaosiRagBiz(
	logger log.Logger,
	third *conf.Third,
) *XiaosiRagBiz {
	return &XiaosiRagBiz{
		log:   log.NewHelper(logger),
		third: third,
	}
}

func (u *XiaosiRagBiz) FetchXiaosiRag(ctx context.Context, req *dto.RagReq, ragUrl string) (*dto.RagData, error) {
	header := &dto.ExactlyHeader{
		XRequestId: custom_context.GetTraceId(ctx),
	}
	headers := util.StructToMap(header)
	headers["Content-Type"] = "application/json"
	ch := utils.CurlInit(ctx, time.Second*3, 1, u.log)
	resp, err := ch.ChRequest(ch.Req.SetHeaders(headers).SetBody(req)).Post(ragUrl)
	if err != nil {
		u.log.WithContext(ctx).Errorf("FetchXiaosiRag params: %+v, err: %v", req, err)
		return nil, err
	}
	u.log.WithContext(ctx).Infof("FetchXiaosiRag resp: %s; params: %+v", string(resp.Body()), req)
	if resp.IsError() {
		return nil, errors.Errorf("resp status: %s", resp.Status())
	}

	var ragResp dto.RagResp
	if err = json.Unmarshal(resp.Body(), &ragResp); err != nil {
		return nil, err
	}

	if ragResp.Code != 0 {
		u.log.WithContext(ctx).Errorf("FetchXiaosiRag resp: %+v", ragResp)
		return nil, errors.Errorf("resp code: %d", ragResp.Code)
	}

	return &ragResp.Data, nil
}

func (u *XiaosiRagBiz) FetchAutoMathRag(ctx context.Context, req dto.AutoMathCheckRequest) (canSolve bool, ragInput string, videoUrl string, outputTitle string) {

	appKey := u.third.MathAgent.AutoMathAk
	appSecret := u.third.MathAgent.AutoMathSk

	timestamp := strconv.Itoa(int(time.Now().Unix()))
	nonce := uuid.New().String()
	signStr := fmt.Sprintf("%sX-Alv-Timestamp=%s&X-Alv-Nonce=%s&X-Alv-AppKey=%s", appSecret, timestamp, nonce, appKey)
	sign := util.MD5(signStr)

	headers := map[string]string{
		"Trace-ID":        req.RequestID,
		"Content-Type":    "application/json",
		"X-Alv-AppKey":    appKey,
		"X-Alv-Timestamp": timestamp,
		"X-Alv-Nonce":     nonce,
		"X-Alv-Sign":      sign,
	}

	ch := utils.CurlInit(ctx, time.Second*3, 1, u.log)
	resp, err := ch.ChRequest(ch.Req.SetHeaders(headers).SetBody(req)).Post(u.third.MathAgent.AutoMathUrl)
	u.log.WithContext(ctx).Infof("FetchAutoMathRag req: %+v", req)
	if err != nil {
		u.log.WithContext(ctx).Errorf("AutoMathCheck response error: %s", err)
		return false, "", "", ""
	}

	if resp.IsError() {
		u.log.WithContext(ctx).Errorf("AutoMathCheck response error: %s", resp.String())
		return false, "", "", ""
	}

	var br dto.AutoMathCheckResp
	err = json.Unmarshal(resp.Body(), &br)
	if err != nil {
		u.log.WithContext(ctx).Warnf("FetchAutoMathRag resp: %+v; err: %v", string(resp.Body()), err)
		return false, "", "", ""
	}

	if br.Code != 0 {
		u.log.WithContext(ctx).Warnf("FetchAutoMathRag resp: %+v; err: %v", string(resp.Body()), br.Code)
		return false, "", "", ""
	}

	if !br.Data.IsCalc {
		u.log.WithContext(ctx).Warnf("FetchAutoMathRag resp: %+v; err: %v", string(resp.Body()), br.Data.IsCalc)
		return false, "", "", ""
	}

	if br.Data.Result == "" {
		u.log.WithContext(ctx).Warnf("FetchAutoMathRag resp: %+v; err: %v", string(resp.Body()), br.Data.Result)
		return false, "", "", ""
	}

	var output dto.OutPut
	_ = json.Unmarshal([]byte(br.Data.OutPut), &output)
	return true, "【答案】" + br.Data.Result, br.Data.VideoURL, output.Context
}
