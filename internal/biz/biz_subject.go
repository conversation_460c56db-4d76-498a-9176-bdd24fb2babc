package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-errors/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/parkingwang/go-sign"
	"github.com/spf13/cast"
	"go.opentelemetry.io/otel/trace"
	"lui-api/internal/conf"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	"lui-api/internal/pkg/utils"
	"lui-api/pkg/util"
	"strings"
	"time"
)

// SubjectUsecase 学科资源
type SubjectUsecase struct {
	log   *log.Helper
	third *conf.Third
}

func NewSubjectUsecase(logger log.Logger, third *conf.Third) *SubjectUsecase {
	return &SubjectUsecase{log: log.NewHelper(logger), third: third}
}

// QuerySubjectClass 学科课程
func (u SubjectUsecase) QuerySubjectClass(ctx context.Context, req *dto.SubjectClassesIdQueryReq) (*dto.SubjectClassesIdQueryResp, error) {

	if custom_context.GetXAppId(ctx) == u.third.XPad1AppId {
		return u.QuerySubjectClassPad1(ctx, req)
	} else {
		req.ShowXpad2Cover = 1
		return u.QuerySubjectClassPad2(ctx, req)
	}
}

// QuerySubjectClassPad1 学科课程
func (u SubjectUsecase) QuerySubjectClassPad1(ctx context.Context, req *dto.SubjectClassesIdQueryReq) (*dto.SubjectClassesIdQueryResp, error) {

	now := time.Now().Unix()
	signer := sign.NewGoSignerMd5()
	signer.SetAppId(u.third.XPad1.ContentAppId)
	signer.SetTimeStamp(now)
	courseIds, _ := json.Marshal(req.CourseIds)
	subjectClassesIds, _ := json.Marshal(req.SubjectClassesIds)
	signer.AddBody("category", cast.ToString(req.Category.Int()))
	signer.AddBody("course_ids", string(courseIds))
	signer.AddBody("subject_classes_ids", string(subjectClassesIds))
	signer.AddBody("time_stamp", cast.ToString(now))

	//计算得出的签名
	targetSign := signer.GetSignature()

	req.BaodianSignParam.TimeStamp = now
	req.BaodianSignParam.Sign = targetSign

	header := &dto.ExactlyHeader{
		XRequestId:  custom_context.GetTraceId(ctx),
		XTalSn:      custom_context.GetXDeviceId(ctx),
		XTalVersion: custom_context.GetXVersion(ctx),
		Traceparent: fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
	}

	ch := utils.CurlInit(ctx, time.Second*4, 1, u.log)
	resp, err := ch.ChRequestBody(util.StructToMap(header), req).Post(u.third.XPad1.ResourceIdsUrl)
	if err != nil {
		u.log.Infof("QuerySubjectClass params: %+v", req)
		return nil, err
	}

	u.log.WithContext(ctx).Infof("QuerySubjectClass resp: %s; req: %+v", string(resp.Body()), req)
	if resp.IsError() {
		return nil, errors.Errorf("resp status: %s", resp.Status())
	}

	var br dto.BaseResponse
	err = json.Unmarshal(resp.Body(), &br)
	if err != nil {
		return nil, err
	}

	var data dto.SubjectClassesIdQueryResp
	err = util.ConvertInterface(br.Data, &data)
	if err != nil {
		u.log.WithContext(ctx).Errorf("QuerySubjectClass convert err: %+v; data: %+v", err, br.Data)
	}

	return &data, nil
}

// QuerySubjectClassPad2 学科课程
func (u SubjectUsecase) QuerySubjectClassPad2(ctx context.Context, req *dto.SubjectClassesIdQueryReq) (*dto.SubjectClassesIdQueryResp, error) {

	header := &dto.ExactlyHeader{
		XRequestId:  custom_context.GetTraceId(ctx),
		XTalSn:      custom_context.GetXDeviceId(ctx),
		XTalVersion: custom_context.GetXVersion(ctx),
		Traceparent: fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
	}

	ch := utils.CurlInit(ctx, time.Second*4, 1, u.log)
	resp, err := ch.ChRequestBody(util.StructToMap(header), req).Post(u.third.XPad2.ResourceIdsUrl)
	if err != nil {
		u.log.Infof("QuerySubjectClass params: %+v", req)
		return nil, err
	}

	u.log.WithContext(ctx).Infof("QuerySubjectClass resp: %s; req: %+v", string(resp.Body()), req)
	if resp.IsError() {
		return nil, errors.Errorf("resp status: %s", resp.Status())
	}

	var br dto.BaseResponse
	err = json.Unmarshal(resp.Body(), &br)
	if err != nil {
		return nil, err
	}

	if br.Errcode != 0 {
		return nil, errors.Errorf("QuerySubjectClass code: %d; msg: %s", br.Errcode, br.Errmsg)
	}

	var data dto.SubjectClassesIdQueryResp
	err = util.ConvertInterface(br.Data, &data)
	if err != nil {
		u.log.WithContext(ctx).Errorf("QuerySubjectClass convert err: %+v", err)
	}

	return &data, nil
}

// QuerySlotScheme 卡槽类scheme
func (u SubjectUsecase) QuerySlotScheme(ctx context.Context, req *dto.SlotSchemeReq) (*dto.SlotSchemeResp, error) {

	if custom_context.GetXAppId(ctx) == u.third.XPad1AppId {
		return u.QuerySlotSchemePad1(ctx, req)
	} else {
		return u.QuerySlotSchemePad2(ctx, req)
	}
}

// QuerySlotSchemePad1 卡槽类scheme
func (u SubjectUsecase) QuerySlotSchemePad1(ctx context.Context, req *dto.SlotSchemeReq) (*dto.SlotSchemeResp, error) {

	now := time.Now().Unix()
	signer := sign.NewGoSignerMd5()
	signer.SetAppId(u.third.XPad1.ContentAppId)
	signer.SetTimeStamp(now)
	signer.AddBody("scheme_type", cast.ToString(req.SchemeType))
	signer.AddBody("channel_id", cast.ToString(req.ChannelId))
	signer.AddBody("column_id", cast.ToString(req.ColumnId))
	signer.AddBody("course_type", cast.ToString(req.CourseType))
	signer.AddBody("semester_id", cast.ToString(req.SemesterId))
	signer.AddBody("grade", cast.ToString(req.Grade))
	signer.AddBody("time_stamp", cast.ToString(now))

	//计算得出的签名
	targetSign := signer.GetSignature()
	req.BaodianSignParam.TimeStamp = now
	req.BaodianSignParam.Sign = targetSign
	header := &dto.ExactlyHeader{
		XRequestId:  custom_context.GetTraceId(ctx),
		XTalSn:      custom_context.GetXDeviceId(ctx),
		XTalVersion: custom_context.GetXVersion(ctx),
		Traceparent: fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
	}
	ch := utils.CurlInit(ctx, time.Second*4, 1, u.log)
	resp, err := ch.ChRequestBody(util.StructToMap(header), req).Post(u.third.XPad1.ResourceSlotUrl)

	if err != nil {
		u.log.Infof("QuerySlotScheme params: %+v", req)
		return nil, err
	}

	u.log.WithContext(ctx).Infof("QuerySlotScheme resp: %s; params: %+v", string(resp.Body()), req)
	if resp.IsError() {
		return nil, errors.Errorf("resp status: %s", resp.Status())
	}

	var br dto.BaseResponse
	err = json.Unmarshal(resp.Body(), &br)
	if err != nil {
		return nil, err
	}

	var data dto.SlotSchemeResp
	err = util.ConvertInterface(br.Data, &data)
	if err != nil {
		u.log.WithContext(ctx).Errorf("QuerySlotScheme convert err: %+v; data: %+v", err, br.Data)
		return nil, errors.Errorf("data format error")
	}

	return &data, nil
}

// QuerySlotSchemePad2 卡槽类scheme
func (u SubjectUsecase) QuerySlotSchemePad2(ctx context.Context, req *dto.SlotSchemeReq) (*dto.SlotSchemeResp, error) {

	header := &dto.ExactlyHeader{
		XRequestId:     custom_context.GetTraceId(ctx),
		XTalSn:         custom_context.GetXDeviceId(ctx),
		XTalVersion:    custom_context.GetXVersion(ctx),
		XTalDeviceCode: dto.GetXTalDeviceCode(ctx, u.third.AppIdMap).String(),
		Traceparent:    fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
	}

	ch := utils.CurlInit(ctx, time.Second*4, 1, u.log)
	resp, err := ch.ChRequestBody(util.StructToMap(header), req).Post(u.third.XPad2.ResourceSlotUrl)

	if err != nil {
		u.log.Infof("QuerySlotScheme params: %+v", req)
		return nil, err
	}

	u.log.WithContext(ctx).Infof("QuerySlotScheme resp: %s; params: %+v", string(resp.Body()), req)
	if resp.IsError() {
		return nil, errors.Errorf("QuerySlotScheme resp status: %s", resp.Status())
	}

	var br dto.BaseResponse
	err = json.Unmarshal(resp.Body(), &br)
	if err != nil {
		return nil, err
	}

	if br.Errcode != 0 {
		return nil, errors.Errorf("QuerySlotScheme code: %d; msg: %s", br.Errcode, br.Errmsg)
	}

	var data dto.SlotSchemeResp
	err = util.ConvertInterface(br.Data, &data)
	if err != nil {
		return nil, errors.Errorf("QuerySlotScheme data format error: %v", err)
	}

	return &data, nil
}

func (u PaperUsecase) QueryEBook(ctx context.Context, req *dto.EbookResourceReq) (*dto.EbookResourceRes, error) {

	ch := utils.CurlInit(ctx, time.Second*4, 1, u.log)
	url := u.third.ReadbookSchemeUrl
	xuelianjiAPPID := u.third.AppIdMap["xueLianJiAppId"]
	if strings.Contains(xuelianjiAPPID, custom_context.GetXAppId(ctx)) {
		url = u.third.ReadbookNoteSchemeUrl
	}

	xuelianji25APPID := u.third.AppIdMap["xueLianJi25AppId"]
	if strings.Contains(xuelianji25APPID, custom_context.GetXAppId(ctx)) {
		url = u.third.ReadbookNoteSchemeUrl
	}
	resp, err := ch.ChRequestBody(map[string]string{}, req).Post(url)
	if err != nil {
		u.log.Infof("QueryEBook params: %+v", req)
		return nil, err
	}

	u.log.WithContext(ctx).Infof("QueryEBook resp: %s; params: %+v", string(resp.Body()), req)
	if resp.IsError() {
		return nil, errors.Errorf("resp status: %s", resp.Status())
	}

	var br dto.BaseResponse
	err = json.Unmarshal(resp.Body(), &br)
	if err != nil {
		return nil, err
	}

	var data dto.EbookResourceRes
	err = util.ConvertInterface(br.Data, &data)
	if err != nil {
		u.log.WithContext(ctx).Errorf("QueryEBook convert err: %+v; data: %+v", err, br.Data)
	}

	return &data, nil
}
