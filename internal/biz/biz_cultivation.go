package biz

import (
	"context"
	"encoding/json"
	"github.com/go-errors/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/parkingwang/go-sign"
	"github.com/spf13/cast"
	"lui-api/internal/conf"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	"lui-api/internal/pkg/utils"
	"lui-api/pkg/util"
	"time"
)

// CultivationUsecase 素养课程
type CultivationUsecase struct {
	log   *log.Helper
	third *conf.Third
}

func NewCultivationUsecase(logger log.Logger, third *conf.Third) *CultivationUsecase {
	return &CultivationUsecase{log: log.<PERSON><PERSON>per(logger), third: third}
}

func (u CultivationUsecase) QueryAttainmentAlbum(ctx context.Context, req *dto.AttainmentAlbumQueryReq) (*dto.AlbumAttainmentIdQueryResp, error) {
	if custom_context.GetXAppId(ctx) == u.third.XPad1AppId {
		return u.QueryAttainmentAlbumPad1(ctx, req)
	} else {
		return u.QueryAttainmentAlbumPad2(ctx, req)
	}
}

func (u CultivationUsecase) QueryAttainmentAlbumPad1(ctx context.Context, req *dto.AttainmentAlbumQueryReq) (*dto.AlbumAttainmentIdQueryResp, error) {
	now := time.Now().Unix()
	signer := sign.NewGoSignerMd5()
	signer.SetAppId(u.third.XPad1.ContentAppId)
	signer.SetTimeStamp(now)
	params := make(map[string]interface{})
	params["category"] = req.Category
	params["attainment_album_ids"] = req.AttainmentAlbumIds
	params["attainment_ids"] = req.AttainmentIds
	params["time_stamp"] = now
	attainmentAlbumIds, _ := json.Marshal(req.AttainmentAlbumIds)
	attainmentIds, _ := json.Marshal(req.AttainmentIds)
	signer.AddBody("category", cast.ToString(req.Category.Int()))
	signer.AddBody("attainment_album_ids", string(attainmentAlbumIds))
	signer.AddBody("attainment_ids", string(attainmentIds))
	signer.AddBody("time_stamp", cast.ToString(now))

	//计算得出的签名
	targetSign := signer.GetSignature()
	params["sign"] = targetSign
	ch := utils.CurlInit(ctx, time.Second*4, 1, u.log)
	resp, err := ch.ChRequestBody(map[string]string{}, params).Post(u.third.XPad1.ResourceIdsUrl)

	if err != nil {
		u.log.Infof("QueryAttainmentAlbum params: %+v", params)
		return nil, err
	}

	u.log.WithContext(ctx).Infof("QueryAttainmentAlbum resp: %s; params: %+v", string(resp.Body()), params)
	if resp.IsError() {
		return nil, errors.Errorf("resp status: %s", resp.Status())
	}

	var br dto.BaseResponse
	err = json.Unmarshal(resp.Body(), &br)
	if err != nil {
		return nil, err
	}

	var data dto.AlbumAttainmentIdQueryResp
	err = util.ConvertInterface(br.Data, &data)
	if err != nil {
		u.log.WithContext(ctx).Errorf("QueryAttainmentAlbum convert err: %+v; data: %+v", err, br.Data)
	}

	return &data, nil
}

func (u CultivationUsecase) QueryAttainmentAlbumPad2(ctx context.Context, req *dto.AttainmentAlbumQueryReq) (*dto.AlbumAttainmentIdQueryResp, error) {

	ch := utils.CurlInit(ctx, time.Second*4, 1, u.log)
	resp, err := ch.ChRequestBody(map[string]string{}, req).Post(u.third.XPad2.ResourceIdsUrl)

	if err != nil {
		u.log.Infof("QueryAttainmentAlbum params: %+v", req)
		return nil, err
	}

	u.log.WithContext(ctx).Infof("QueryAttainmentAlbum resp: %s; params: %+v", string(resp.Body()), req)
	if resp.IsError() {
		return nil, errors.Errorf("resp status: %s", resp.Status())
	}

	var br dto.BaseResponse
	err = json.Unmarshal(resp.Body(), &br)
	if err != nil {
		return nil, err
	}

	var data dto.AlbumAttainmentIdQueryResp
	err = util.ConvertInterface(br.Data, &data)
	if err != nil {
		u.log.WithContext(ctx).Errorf("QueryAttainmentAlbum convert err: %+v; data: %+v", err, br.Data)
	}

	return &data, nil
}
