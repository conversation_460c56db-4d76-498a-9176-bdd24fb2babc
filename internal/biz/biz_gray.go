package biz

import (
	"context"
	"encoding/json"
	feature "git.100tal.com/znxx_xpp/feature-ab-client-go"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/mitchellh/mapstructure"
	"lui-api/internal/common"
	"lui-api/internal/conf"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	"strings"
)

// GrayBizUsecase 灰度策略
type GrayBizUsecase struct {
	log  *log.Helper
	conf *conf.Third
}

func NewGrayBizUsecase(logger log.Logger, conf *conf.Third) *GrayBizUsecase {
	return &GrayBizUsecase{log: log.NewHelper(logger), conf: conf}
}

// NLU4ExamJudgeGrayHitPad2 试卷诊断 二代灰度
func (u GrayBizUsecase) NLU4ExamJudgeGrayHitPad2(ctx context.Context, nluItem *dto.NLUSkillItem) (bool, *dto.NLUSkillItem) {

	if u.conf.WhiteExamJudge.Status == 1 {
		if nluItem.Skill == "functional" {
			cmd := dto.CommendTmp{}
			_ = mapstructure.Decode(nluItem.Command, &cmd)
			if cmd.Param.Scheme == "geniepad://www.geniepad.com/exam_judge?from=lui" {
				deviceId := custom_context.GetXDeviceId(ctx)
				if len(deviceId) == 0 || !strings.Contains(u.conf.WhiteExamJudge.SnList, deviceId) {
					return false, defaultExamJudgeNLUSkillItem(nluItem)
				}
			}
		}
	}

	return true, nil
}

// 试卷诊断非灰度用户兜底话术
func defaultExamJudgeNLUSkillItem(nluItem *dto.NLUSkillItem) *dto.NLUSkillItem {
	nlu4luiReq := convertNLUSkillItemToNlu4LuiData(nluItem)
	luiWidgetContent := dto.Nlu4LuiWidgetContent{
		Category: 0,
		SlotDict: nluItem.AllSlots,
		Title:    "",
		Data:     nil,
		Word:     nluItem.Word,
		Scheme:   "",
	}

	luiWidget := dto.Nlu4LuiWidget{
		ModuleName:       nlu4luiReq.ModuleName,
		IsValidWidget:    nlu4luiReq.IsValidWidget,
		ResultConfidence: nlu4luiReq.ResultConfidence,
		NeedConfirm:      nlu4luiReq.NeedConfirm,
		Content:          luiWidgetContent,
	}
	luiWidgetBt, _ := json.Marshal(luiWidget)
	nluItem.TtsNorm = "试卷诊断功能还在内测中，敬请期待"
	nluItem.TtsShow = "试卷诊断功能还在内测中，敬请期待"
	nluItem.Widget = string(luiWidgetBt)
	nluItem.Command = nil
	return nluItem
}

func convertNLUSkillItemToNlu4LuiData(b *dto.NLUSkillItem) *dto.Nlu4LuiData {
	b.AllSlots = dto.MergeStructs(b.DefaultSlots, b.SlotDict)
	return &dto.Nlu4LuiData{
		RequestID:        b.SessionId,
		ModuleName:       "",
		IsValidWidget:    b.IsResultValid,
		ResultConfidence: b.Count,
		Skill:            b.Skill,
		SubIntent:        0,
		DefaultSlots:     b.DefaultSlots,
		SlotDict:         b.SlotDict,
		RawSlotDict:      b.RawSlotDict,
		AllSlots:         b.AllSlots,
		Category:         b.Category,
		ResourceList:     b.ResourceList,
		Word:             b.Word,
		Hit:              b.Hit,
		SimScore:         b.SimScore,
	}
}

func (u GrayBizUsecase) MathExplainAppSchemeGray(ctx context.Context, nluItem *dto.NLUSkillItem, modelOutputIntent string) {

	if nluItem.Skill == "functional" {
		cmd := dto.CommendTmp{}
		_ = mapstructure.Decode(nluItem.Command, &cmd)
		if cmd.Param.Scheme == "tal://cameralesson/main" && modelOutputIntent == "查题" {
			if u.MathExplainJudgeGrayHit(ctx, custom_context.GetXDeviceId(ctx), custom_context.GetXAppId(ctx), custom_context.GetXOsPkgDate(ctx)) {
				cmd.Param.Scheme = "geniepad://www.geniepad.com/ai_eyes/finger_crop_image?session_id=" + nluItem.SessionId + "&tts_preview=请将试题放置识别区域，小思即将为你解答~&tts_crop_image=请框选需要讲解的单道题目~"
				nluItem.Command = cmd
			}
		}
	}
}

func (u GrayBizUsecase) XS20AgentJudgeGrayHit(ctx context.Context, sn string) bool {
	user := feature.UserWithAttrs(map[string]interface{}{
		"sn": sn,
	})
	hitSn := feature.BoolDetail(common.XsdhXiaosi20AgentSnList, user, false)
	u.log.WithContext(ctx).Infof("XS20AgentJudgeGrayHit sn:%s, hit:%+v", sn, hitSn)
	return hitSn.Value
}

func (u GrayBizUsecase) MathExplainJudgeGrayHit(ctx context.Context, sn, appId string, osPkgDate int) bool {

	if common.CurrentEnv == common.LocalEnv || common.CurrentEnv == common.DevelopEnv || common.CurrentEnv == common.TestEnv {
		return true
	}

	user := feature.UserWithAttrs(map[string]interface{}{
		"sn":        sn,
		"appId":     appId,
		"osPkgDate": osPkgDate,
	})
	hitSn := feature.BoolDetail(common.XsdhMathExplainSnList, user, false)
	u.log.WithContext(ctx).Infof("MathExplainJudgeGrayHit sn:%s, hit:%+v", sn, hitSn)
	return hitSn.Value
}

func (u GrayBizUsecase) XSZixishiJudgeGrayHit(ctx context.Context, sn string) bool {
	if common.CurrentEnv == common.LocalEnv || common.CurrentEnv == common.DevelopEnv || common.CurrentEnv == common.TestEnv {
		return true
	}
	user := feature.UserWithAttrs(map[string]interface{}{
		"sn": sn,
	})
	hitSn := feature.BoolDetail(common.XszixishiSnList, user, false)
	u.log.WithContext(ctx).Infof("XSZixishiJudgeGrayHit sn:%s, hit:%+v", sn, hitSn)
	return hitSn.Value
}

func (u GrayBizUsecase) XSXieZuoYinDaoJudgeGrayHit(ctx context.Context, appId, sn string) bool {
	if common.CurrentEnv == common.LocalEnv || common.CurrentEnv == common.DevelopEnv {
		return true
	}
	user := feature.UserWithAttrs(map[string]interface{}{
		"sn":     sn,
		"app_id": appId,
	})
	hitSn := feature.BoolDetail(common.XsxiezuoyindaoSnList, user, false)
	u.log.WithContext(ctx).Infof("XSXieZuoYinDaoJudgeGrayHit sn:%s, hit:%+v", sn, hitSn)
	return hitSn.Value
}

func (u GrayBizUsecase) XSYueDuLiJieJudgeGrayHit(ctx context.Context, sn string) bool {
	if common.CurrentEnv == common.LocalEnv || common.CurrentEnv == common.DevelopEnv {
		return true
	}
	user := feature.UserWithAttrs(map[string]interface{}{
		"sn": sn,
	})
	hitSn := feature.BoolDetail(common.XsYueDuLiJieSnList, user, false)
	u.log.WithContext(ctx).Infof("XSYueDuLiJieJudgeGrayHit sn:%s, hit:%+v", sn, hitSn)
	return hitSn.Value
}
func (u GrayBizUsecase) XSAtEnglishWordJudgeGrayHit(ctx context.Context, sn string) bool {
	if common.CurrentEnv == common.LocalEnv || common.CurrentEnv == common.DevelopEnv {
		return true
	}
	user := feature.UserWithAttrs(map[string]interface{}{
		"sn": sn,
	})
	hitSn := feature.BoolDetail(common.XsAtEnglishWordSnList, user, false)
	u.log.WithContext(ctx).Infof("XSAtEnglishWordJudgeGrayHit sn:%s, hit:%+v", sn, hitSn)
	return hitSn.Value
}
