package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"lui-api/internal/common"
	"lui-api/internal/conf"
	util "lui-api/internal/pkg/utils"
	"net/http"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
)

type UCenterBiz struct {
	log   *log.Helper
	third *conf.Third
	Repo  UCenterRepo
}

type UCenterRepo interface {
	RdbGetKey(ctx context.Context, key string) *redis.StringCmd
	RdbSetKey(ctx context.Context, key string, value interface{}, expiration int) *redis.StatusCmd
}

func NewUCenterBiz(repo UCenterRepo, logger log.Logger, third *conf.Third) *UCenterBiz {
	return &UCenterBiz{
		log:   log.NewHelper(logger),
		third: third,
		Repo:  repo,
	}
}

type UserInfoParseRes struct {
	Errcode  int           `json:"errcode"`
	Errmsg   string        `json:"errmsg"`
	Data     UserInfoParse `json:"data"`
	Trace    string        `json:"trace"`
	DataType int           `json:"data_type"`
}

type UserInfoParse struct {
	ClientID string `json:"client_id"`
	TalID    string `json:"tal_id"`
}

type TokenParseReq struct {
	Token string `json:"token"`
}

type Response struct {
	ErrorReason string            `json:"error_reason"`
	ErrorMsg    string            `json:"error_msg"`
	MetaData    map[string]string `json:"meta_data"`
	TraceId     string            `json:"trace_id"`
	ServerTime  int64             `json:"server_time"`
	Data        interface{}       `json:"data"`
}

func (us *UCenterBiz) TokenParse(ctx context.Context, token string) (*UserInfoParse, error) {

	userTokenRedisKey := fmt.Sprintf(common.UserTokenRedisKey, token)
	result, err := us.Repo.RdbGetKey(ctx, userTokenRedisKey).Result()
	if err == nil {
		var userInfoParse UserInfoParse
		err := json.Unmarshal([]byte(result), &userInfoParse)
		if err == nil {
			return &userInfoParse, nil
		}
	}

	ch := util.CurlInit(ctx, time.Second*4, 1, us.log)
	in := &TokenParseReq{Token: token}
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	postResp, err := ch.ChRequest(ch.Req.SetHeaders(headers).
		SetBody(in)).
		Post(us.third.XPad1.TokenParseUrl)
	if err != nil {
		return nil, errors.New(500, "rpc", "error: "+err.Error())
	}

	if postResp.StatusCode() != http.StatusOK {
		var errResp Response
		err = json.Unmarshal(postResp.Body(), &errResp)
		if err != nil {
			err = errors.New(400, "param", "err")
		} else {
			err = &errors.Error{
				Status: errors.Status{
					Code:     int32(postResp.StatusCode()),
					Reason:   errResp.ErrorReason,
					Message:  errResp.ErrorMsg,
					Metadata: errResp.MetaData,
				},
			}
		}

		return nil, err
	}

	us.log.WithContext(ctx).Infof("TokenParse resp:%s", string(postResp.Body()))
	var userInfoParseRes UserInfoParseRes
	err = json.Unmarshal(postResp.Body(), &userInfoParseRes)
	if err != nil {
		return nil, errors.New(400, "param", "err")
	}

	us.Repo.RdbSetKey(ctx, userTokenRedisKey, userInfoParseRes.Data, 60*5)
	return &userInfoParseRes.Data, nil
}

type GetUserProfileRes struct {
	Trace       string          `json:"trace"`
	Errmsg      string          `json:"errmsg"`
	OtelSpanID  string          `json:"otel_span_id"`
	DataType    float64         `json:"data_type"`
	Data        UserBaseProfile `json:"data"`
	Errcode     float64         `json:"errcode"`
	OtelTraceID string          `json:"otel_trace_id"`
}

type UserProfile struct {
	ProvinceName   string `json:"province_name"`
	Nickname       string `json:"nickname"`
	Realname       string `json:"realname"`
	ClientNickname string `json:"client_nickname"`
	FamilyData     []struct {
		FamilyID       string  `json:"family_id"`
		IsManager      float64 `json:"is_manager"`
		FamilyRoleName string  `json:"family_role_name"`
		TalID          string  `json:"tal_id"`
		FamilyRole     float64 `json:"family_role"`
	} `json:"family_data"`
	Sex           string  `json:"sex"`
	CityName      string  `json:"city_name"`
	Grade         int32   `json:"grade"`
	GradeName     string  `json:"grade_name"`
	City          string  `json:"city"`
	HidePhone     string  `json:"hide_phone"`
	IsFactoryUser float64 `json:"is_factory_user"`
	SchoolYear    string  `json:"school_year"`
	Email         string  `json:"email"`
	Birthday      string  `json:"birthday"`
	UdcNickname   string  `json:"udc_nickname"`
	CountyName    string  `json:"county_name"`
	RoleType      float64 `json:"role_type"`
	TalName       string  `json:"tal_name"`
	County        string  `json:"county"`
	AvatorURL     string  `json:"avator_url"`
	Province      string  `json:"province"`
	FamilyID      string  `json:"family_id"`
	UID           float64 `json:"uid"`
	TalID         string  `json:"tal_id"`
}

type UserBaseProfile struct {
	Grade     int32  `json:"grade"`
	GradeName string `json:"grade_name"`
	TalID     string `json:"tal_id"`
	Nickname  string `json:"nickname"`
}

func (us *UCenterBiz) GetUserProfile(ctx context.Context, userInfoParse *UserInfoParse) (*UserBaseProfile, error) {

	if userInfoParse.TalID == "" {
		return nil, errors.New(400, "", "talId为空")
	}

	ch := util.CurlInit(ctx, time.Second*4, 1, us.log)
	req := map[string]string{
		"client_id": userInfoParse.ClientID,
		"tal_id":    userInfoParse.TalID,
	}

	postResp, err := ch.ChRequestBody(map[string]string{}, req).Post(us.third.XPad1.UserProfileUrl)
	if err != nil || postResp == nil {
		return nil, errors.New(500, "rpc", "error: "+err.Error())
	}

	if postResp.StatusCode() != http.StatusOK {
		var errResp Response
		err = json.Unmarshal(postResp.Body(), &errResp)
		if err != nil {
			err = errors.New(400, "param", "err")
		} else {
			err = &errors.Error{
				Status: errors.Status{
					Code:     int32(postResp.StatusCode()),
					Reason:   errResp.ErrorReason,
					Message:  errResp.ErrorMsg,
					Metadata: errResp.MetaData,
				},
			}
		}

		return nil, err
	}
	us.log.WithContext(ctx).Infof("GetUserProfile req:%v, resp:%s", req, string(postResp.Body()))
	var profileRes GetUserProfileRes
	err = json.Unmarshal(postResp.Body(), &profileRes)
	if err != nil {
		return nil, errors.New(400, "param", "err")
	}

	return &profileRes.Data, nil
}
