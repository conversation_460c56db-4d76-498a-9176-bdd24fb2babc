package biz

import (
	"context"
	"encoding/json"
	"lui-api/internal/common"
	"lui-api/internal/conf"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/utils"
	"lui-api/pkg/util"
	"strings"
	"time"

	"github.com/pkg/errors"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/types/known/structpb"
)

type IMemoryContextRepo interface {
	AddMemoryContext(ctx context.Context, userTalID, sessionID string, context dto.MemoryContext) error
	GetMemoryContext(ctx context.Context, userTalID string, limit int) ([]*dto.MemoryContext, error)
	GetMemoryContextBySessionID(ctx context.Context, sessionID string) (*dto.MemoryContext, error)
	UpdateMemoryContextResponse(ctx context.Context, sessionID string, context dto.MemoryContext) error
	SetAddMemoryContextRejectFlag(ctx context.Context, sessionID string) error
	GetAddMemoryContextRejectFlag(ctx context.Context, sessionID string) (int64, error)
	SetNXXiaoSiCommand(ctx context.Context, talId string) (bool, error)

	AddSugMemory(ctx context.Context, userTalID string, sug []string) error
	GetSugMemory(ctx context.Context, userTalID string) ([]string, error)
	DelSugMemory(ctx context.Context, userTalID string) error

	SetMemoryContextMathAgentFlag(ctx context.Context, talId string, uuid string) (string, error)
	GetMemoryContextMathAgentFlag(ctx context.Context, talId string) (string, error)
	DelMemoryContextMathAgentFlag(ctx context.Context, talId string) (int64, error)
	SetMemoryContextMathAgentQuestion(ctx context.Context, talId string, question string, rag string) (int64, error)
	GetMemoryContextMathAgentQuestion(ctx context.Context, talId string) (map[string]string, error)
	DelMemoryContextMathAgentQuestion(ctx context.Context, talId string) (int64, error)
	GetMemoryContextKETQAgentFlag(ctx context.Context, talId string) (string, error)

	SetDeepSeekFlag(ctx context.Context, talId string, uuid string) error
	GetDeepSeekFlag(ctx context.Context, talId string) (string, error)
	DelDeepSeekFlag(ctx context.Context, talId string) error

	SetExerciseOcr(ctx context.Context, talId string, exerciseOcr string) error
	GetExerciseOcr(ctx context.Context, talId string) (string, error)

	SetWorkshopFlag(ctx context.Context, talId string, uuid string) error
	GetWorkshopFlag(ctx context.Context, talId string) (string, error)
	DelWorkshopFlag(ctx context.Context, talId string) error

	GetAtmsExplainType(ctx context.Context, talId string) (int, error)
	DelAtmsExplainType(ctx context.Context, talId string) error

	SetFiltersFlag(ctx context.Context, talId string, filtersName string) error
	GetFiltersFlag(ctx context.Context, talId string) (string, error)
	DelFiltersFlag(ctx context.Context, talId string) error
}

type MemoryContextBiz struct {
	log               *log.Helper
	memoryContextData IMemoryContextRepo
	third             *conf.Third
}

func NewMemoryContextBiz(
	logger log.Logger,
	memoryContextData IMemoryContextRepo,
	third *conf.Third,
) *MemoryContextBiz {
	return &MemoryContextBiz{
		log:               log.NewHelper(logger),
		memoryContextData: memoryContextData,
		third:             third,
	}
}

func (m *MemoryContextBiz) GetMemoryContextBySessionID(ctx context.Context, sessionID string) (*dto.MemoryContext, error) {
	data, err := m.memoryContextData.GetMemoryContextBySessionID(ctx, sessionID)
	if err != nil {
		return nil, err
	}

	return data, nil
}

func (m *MemoryContextBiz) AddMemoryContext(ctx context.Context, userTalID, sessionID string, memoryContext dto.MemoryContext) error {
	if err := m.memoryContextData.AddMemoryContext(ctx, userTalID, sessionID, memoryContext); err != nil {
		return err
	}

	return nil
}

func (m *MemoryContextBiz) AddXPodMemoryContext(ctx context.Context, req dto.MemoryContextReq, funcListResp []*dto.LuiFunction, resp *dto.QueryPad2Resp) error {
	if resp == nil || len(funcListResp) == 0 {
		return nil
	}

	var (
		ttsShow  string
		intent   string
		response interface{}
		funcList []*dto.ContextFuncList
	)

	for _, v := range funcListResp {
		funcList = append(funcList, &dto.ContextFuncList{
			FuncName:       v.FuncName,
			FuncParameters: v.FuncParamaters,
			Intent:         v.Intent,
		})

		intent += v.Intent + ","
	}

	intent = intent[:len(intent)-1]

	if intent != "闲聊" {
		if resp.Data != nil && len(resp.Data) == 1 && resp.Data[0].Data != nil {
			ttsShow = resp.Data[0].Data.TtsShow
			if ttsShow == "" {
				ttsShow = resp.Data[0].Data.TtsNorm
			}
		} else {
			ttsShow = resp.TtsShow
			if ttsShow == "" {
				ttsShow = resp.TtsNorm
			}
		}
	}

	memoryContext := dto.MemoryContext{
		TimeStamp: time.Now().UnixMilli(),
		Intent:    intent,
		AsrInfo:   req.AsrInfo,
		TtsInfo:   ttsShow,
		Response:  response,
		FuncList:  funcList,
	}

	if err := m.memoryContextData.AddMemoryContext(ctx, req.UserTalID, req.SessionId, memoryContext); err != nil {
		return err
	}

	return nil
}

func (m *MemoryContextBiz) GetXPodMemoryContext(ctx context.Context, userTalID string) ([]*dto.XPodMemoryContextResp, error) {
	data, err := m.memoryContextData.GetMemoryContext(ctx, userTalID, 0)

	if err != nil {
		return nil, err
	}

	var resp []*dto.XPodMemoryContextResp
	for _, v := range data {
		xpod := &dto.XPodMemoryContextResp{
			TimeStamp: v.TimeStamp,
			Intent:    strings.Split(v.Intent, ","),
			AsrInfo:   v.AsrInfo,
			TtsInfo:   v.TtsInfo,
			FuncList:  v.FuncList,
		}

		resp = append(resp, xpod)
	}

	if len(resp) > 30 {
		resp = resp[len(resp)-30:]
	}

	return resp, nil
}

func (m *MemoryContextBiz) AddXPadMemoryContext(ctx context.Context, req dto.MemoryContextReq, resp *structpb.Struct, nlu4lui *dto.NLUQueryPad2Resp) error {
	// 判断是否需要存储
	flag, err := m.memoryContextData.GetAddMemoryContextRejectFlag(ctx, req.SessionId)
	if err == nil && flag == 1 {
		return errors.New("reject add memory context")
	}

	if resp == nil {
		return errors.New("resp is nil")
	}

	respByte, err := json.Marshal(resp)
	if err != nil {
		return err
	}

	var padResp dto.QueryPad2Resp
	if err = json.Unmarshal(respByte, &padResp); err != nil {
		return err
	}

	var (
		asrInfo         = req.AsrInfo
		isLlm           bool
		source          int64
		ttsShow         string
		ttsNorm         string
		intent          string
		rewriteQuery    string
		response        interface{}
		funcList        []*dto.ContextFuncList
		scenarioType    string
		showType        = padResp.ShowType
		mixedModalQuery string
		multiModalInfo  *dto.MultiModalInfo
		ttsParam        *dto.TtsParam
	)

	if padResp.Dispatch != nil && len(padResp.Dispatch.Router.Bundle) > 0 {
		// 混合卡片
		for _, v := range padResp.Dispatch.Router.Bundle {
			if v.BundleName == "mixed_card_bus" {
				showType = 2
			}
		}
	}

	if nlu4lui != nil {
		if len(nlu4lui.FunctionList) > 0 {
			for _, v := range nlu4lui.FunctionList {
				funcList = append(funcList, &dto.ContextFuncList{
					FuncName:       v.FuncName,
					FuncParameters: v.FuncParamaters,
				})
			}
		}

		if nlu4lui.RewriteInfo != nil {
			rewriteQuery = nlu4lui.RewriteInfo.RewriteQuery
			mixedModalQuery = nlu4lui.RewriteInfo.MultiModalRewriteQuery
		}

		if nlu4lui.ExtraResp != nil {
			multiModalInfo = nlu4lui.ExtraResp.MultiModalInfo
		}
	}

	intent = padResp.ModelOutputIntent
	ttsParam = padResp.TtsParam
	response = formatXPadResponse(padResp)

	if intent != common.IntentChat && padResp.Data != nil {
		if padResp.Data != nil && len(padResp.Data) == 1 && padResp.Data[0].Data != nil {

			if padResp.Data[0].Data.SceneMode == dto.SceneModeFullView {
				ttsShow = ""
				ttsNorm = ""
			} else {
				ttsShow = padResp.Data[0].Data.TtsShow
				if ttsShow == "" {
					ttsShow = padResp.Data[0].Data.TtsNorm
				} else if ttsShow != padResp.Data[0].Data.TtsNorm {
					// 兼容ttsNorm 有ssml的情况，需要端上用此字段播tts
					ttsNorm = padResp.Data[0].Data.TtsNorm
				}
			}
			isLlm = padResp.Data[0].Data.IsAccessingLLM
		} else {
			ttsShow = padResp.TtsShow
			if ttsShow == "" {
				ttsShow = padResp.TtsNorm
			} else if ttsShow != padResp.TtsNorm {
				ttsNorm = padResp.TtsNorm
			}
		}
	}

	// 风控||快修
	if intent == common.IntentChat && (req.IllegalType != "" || req.Hotfix != nil) {
		ttsShow = padResp.TtsShow
		if ttsShow == "" {
			ttsShow = padResp.TtsNorm
		}
	}

	if (intent == common.IntentKnowledge || intent == common.IntentBaiKeWenDa) && padResp.Data != nil {
		widget := dto.ContextWidgetInfo{}
		if padResp.Data != nil && len(padResp.Data) == 1 && padResp.Data[0].Data != nil && padResp.Data[0].Data.Widget != "" {
			_ = jsoniter.Unmarshal([]byte(padResp.Data[0].Data.Widget), &widget)
			if len(widget.Content) > 0 {
				response = []string{widget.Content[0].Subtitle}
				source = int64(dto.ContextSourceKnowledge)
			}
		}
	}

	//澄清追问标识
	if ttsShow == common.TTSFollowUpSubject || ttsShow == common.TTSFollowUpVersion {
		scenarioType = common.TTSFollowUp
	}

	var llmSkill string
	if showType == 1 {
		llmSkill = dto.MemoryContextRnCardType
		response = formatRnResponse(padResp)
	}

	if req.Hotfix != nil && req.Hotfix.HotfixType == dto.HotfixTypeSimilarQuery {
		asrInfo = req.Hotfix.AsrInfo
	}

	memoryContext := dto.MemoryContext{
		TimeStamp:       time.Now().UnixMilli(),
		Intent:          intent,
		AsrInfo:         asrInfo,
		TtsInfo:         ttsShow,
		TtsNorm:         ttsNorm,
		TtsParam:        ttsParam,
		Response:        response,
		FuncList:        funcList,
		RewriteQuery:    rewriteQuery,
		Source:          source,
		IsLlm:           isLlm,
		LlmSkill:        llmSkill,
		ScenarioType:    scenarioType,
		SceneCode:       req.SceneCode,
		MixedModalQuery: mixedModalQuery,
		MultiModalInfo:  multiModalInfo,
	}

	if err = m.memoryContextData.AddMemoryContext(ctx, req.UserTalID, req.SessionId, memoryContext); err != nil {
		return err
	}

	return nil
}

func (m *MemoryContextBiz) GetXPadMemoryContext(ctx context.Context, userTalID string, limit int) ([]*dto.XPadMemoryContextResp, error) {
	data, err := m.memoryContextData.GetMemoryContext(ctx, userTalID, limit)

	if err != nil {
		return nil, err
	}

	var resp []*dto.XPadMemoryContextResp
	for _, v := range data {
		if v.LlmSkill == dto.MemoryContextXiaoSiCommandLLmSkill {
			continue
		}
		funcList := make([]*dto.XPadContextFuncList, 0)
		for _, v := range v.FuncList {
			funcList = append(funcList, &dto.XPadContextFuncList{
				FuncName:       v.FuncName,
				FuncParameters: v.FuncParameters,
			})
		}

		response := ""
		if v.Response != nil && v.LlmSkill != dto.MemoryContextRnCardType {
			responseByte, _ := json.Marshal(v.Response)
			response = string(responseByte)
		}

		xpad := &dto.XPadMemoryContextResp{
			TimeStamp:       v.TimeStamp,
			Intent:          v.Intent,
			AsrInfo:         v.AsrInfo,
			TtsInfo:         v.TtsInfo,
			TtsNorm:         v.TtsNorm,
			FuncList:        funcList,
			Response:        response,
			RewriteQuery:    v.RewriteQuery,
			ScenarioType:    v.ScenarioType,
			MixedModalQuery: v.MixedModalQuery,
			MultiModalInfo:  v.MultiModalInfo,
		}

		resp = append(resp, xpad)
	}

	if len(resp) > 30 {
		resp = resp[len(resp)-30:]
	}

	return resp, nil
}

// GetLlmMemoryContext 小思对话上下文
func (m *MemoryContextBiz) GetLlmMemoryContext(ctx context.Context, sessionId, userTalID string) ([]*dto.LlmMemoryContextResp, error) {
	data, err := m.memoryContextData.GetMemoryContext(ctx, userTalID, dto.LuiPad2MaxLen)
	if err != nil {
		return nil, err
	}

	var resp []*dto.LlmMemoryContextResp
	for _, v := range data {
		if v.Intent == "异常query" {
			continue
		}
		// 过滤当前会话
		if v.SessionId == sessionId {
			continue
		}
		user := &dto.LlmMemoryContextResp{
			Role:    "user",
			Content: v.AsrInfo,
		}

		content := v.TtsInfo
		if v.IsLlm {
			response := ""
			responseSlice, ok := v.Response.([]interface{})
			if ok && len(responseSlice) > 0 {
				for _, v := range responseSlice {
					response += v.(string) + ","
				}
				response = response[:len(response)-1]
			}

			content = v.TtsInfo + " " + response
		}
		assistant := &dto.LlmMemoryContextResp{
			Role:    "assistant",
			Content: strings.TrimSpace(content),
		}

		if strings.TrimSpace(content) == "" {
			continue
		}

		resp = append(resp, user, assistant)
	}

	return resp, nil
}

// GetFullViewLlmMemoryContext 全屏页大模型上下文
func (m *MemoryContextBiz) GetFullViewLlmMemoryContext(ctx context.Context, currentSessionId, userTalID string, needLLmSkill []string) ([]*dto.LlmMemoryContextResp, error) {
	data, err := m.memoryContextData.GetMemoryContext(ctx, userTalID, 0)
	if err != nil {
		return nil, err
	}

	var resp []*dto.LlmMemoryContextResp
	if len(needLLmSkill) == 0 {
		return resp, nil
	}

	for _, v := range data {
		// 过滤当前会话
		if v.SessionId == currentSessionId {
			continue
		}

		if !util.InSliceString(v.LlmSkill, needLLmSkill) {
			continue
		}

		asrInfo := v.AsrInfo
		if v.MixedModalQuery != "" {
			asrInfo = v.MixedModalQuery
		}
		user := &dto.LlmMemoryContextResp{
			Role:       "user",
			Content:    asrInfo,
			DialogueId: v.DialogueId,
		}

		content := strings.TrimSpace(v.LlmResponse)
		if content == "" {
			continue
		}

		assistant := &dto.LlmMemoryContextResp{
			Role:       "assistant",
			Content:    strings.TrimSpace(content),
			DialogueId: v.DialogueId,
		}

		resp = append(resp, user, assistant)
	}

	if len(resp) > dto.LuiPad2MaxLen*2 {
		resp = resp[len(resp)-dto.LuiPad2MaxLen*2:]
	}

	return resp, nil
}

func (m *MemoryContextBiz) GetAgentMemoryContext(ctx context.Context, currentSessionId, userTalID string) ([]*dto.LlmMemoryContextResp, error) {
	data, err := m.memoryContextData.GetMemoryContext(ctx, userTalID, 0)
	if err != nil {
		m.log.WithContext(ctx).Warnf("获取记忆上下文失败: %v", err)
		return nil, err
	}

	allowedIntents := []string{"百科问答", "闲聊", "模糊意图", "查学科知识", "查句子", "查诗歌", "查字", "查词语", "查单词", "翻译句子", "搜文字"}

	var filteredData []*dto.MemoryContext
	for i := len(data) - 1; i >= 0; i-- {
		v := data[i]
		// 过滤当前会话
		if v.SessionId == currentSessionId {
			continue
		}

		if !util.InSliceString(v.Intent, allowedIntents) {
			continue
		}

		filteredData = append(filteredData, v)
		if len(filteredData) >= dto.AgentMemoryContextListMaxLength {
			break
		}
	}

	for i, j := 0, len(filteredData)-1; i < j; i, j = i+1, j-1 {
		filteredData[i], filteredData[j] = filteredData[j], filteredData[i]
	}

	m.log.WithContext(ctx).Infof("用户 %s 过滤后对话数量: %d", userTalID, len(filteredData))

	var resp []*dto.LlmMemoryContextResp
	for _, v := range filteredData {
		asrInfo := v.AsrInfo
		if v.MixedModalQuery != "" {
			asrInfo = v.MixedModalQuery
		}
		user := &dto.LlmMemoryContextResp{
			Role:    "user",
			Content: asrInfo,
		}

		content := v.TtsInfo
		if v.IsLlm {
			responseSlice, ok := v.Response.([]interface{})
			if ok && len(responseSlice) > 0 {
				response := ""
				for _, item := range responseSlice {
					str, strOk := item.(string)
					if strOk {
						response += str + ","
					}
				}
				if len(response) > 0 {
					response = response[:len(response)-1]
					content = v.TtsInfo + " " + response
				}
			}
		}

		if strings.TrimSpace(content) == "" {
			continue
		}

		assistant := &dto.LlmMemoryContextResp{
			Role:    "assistant",
			Content: strings.TrimSpace(content),
		}

		resp = append(resp, user, assistant)
	}

	return resp, nil
}

// GetMemoryContext 上下文api
func (m *MemoryContextBiz) GetMemoryContext(ctx context.Context, userTalID string, limit int) ([]*dto.MemoryContextApi, error) {
	data, err := m.memoryContextData.GetMemoryContext(ctx, userTalID, limit)

	if err != nil {
		return nil, err
	}

	var resp []*dto.MemoryContextApi
	for _, v := range data {
		res := &dto.MemoryContextApi{
			SessionId:         v.SessionId,
			TimeStamp:         v.TimeStamp,
			Intent:            v.Intent,
			AsrInfo:           v.AsrInfo,
			TtsInfo:           v.TtsInfo,
			TtsNorm:           v.TtsNorm,
			Response:          v.Response,
			RewriteQuery:      v.RewriteQuery,
			IsLlm:             v.IsLlm,
			Source:            dto.ContextSource(v.Source),
			ImageUrl:          v.ImageUrl,
			VideoUrl:          v.VideoUrl,
			LlmSkill:          v.LlmSkill,
			Media:             v.Media,
			XiaosiCommandList: v.XiaosiCommandList,
			ReasoningContent:  v.ReasoningContent,
		}

		resp = append(resp, res)
	}

	return resp, nil
}

func (m *MemoryContextBiz) UpdateMemoryContextLlmResponse(sessionID string, data dto.UpdateMemoryContextLlmResponse) error {
	source := dto.GetLLMSource(data.LlmModel).ToInt64()

	var isLlm = true
	var response interface{}
	response = []string{data.Response}
	if data.LlmSkill == dto.MemoryContextRnCardType {
		isLlm = false
		response = data.MixedModalResponse
	}

	memoryContext := dto.MemoryContext{
		Response:         response,
		IsLlm:            isLlm,
		Source:           source,
		ImageUrl:         data.ImageUrl,
		VideoUrl:         data.VideoUrl,
		LlmSkill:         data.LlmSkill,
		LlmResponse:      data.LlmResponse,
		DialogueId:       data.DialogueId,
		Media:            data.Media,
		TtsInfo:          data.TtsInfo,
		MixedModalQuery:  data.MixedModalQuery,
		ReasoningContent: data.ReasoningContent,
	}

	if err := m.memoryContextData.UpdateMemoryContextResponse(context.Background(), sessionID, memoryContext); err != nil {
		return err
	}

	return nil
}

// format response
func formatXPadResponse(resp dto.QueryPad2Resp) []string {
	var (
		response []string
		skill    []string
	)
	skill = []string{
		"search_paper",
		"search_course",
		"search_quality",
		"search_exercise",
		"paper_subject_confirm",
		"continue_learn_course",
		"continue_learn_quality",
	}

	if resp.Data == nil {
		return response
	}

	for _, v := range resp.Data {
		if !util.InSliceString(v.Skill, skill) {
			continue
		}

		widget := dto.NluDataWidget{}
		_ = jsoniter.Unmarshal([]byte(v.Data.Widget), &widget)

		if len(widget.Content.Data) == 0 {
			continue
		}

		for _, dataItem := range widget.Content.Data {
			str := ""
			if dataItem.Item.CourseName != "" {
				str += dataItem.Item.CourseName + "_"
			}

			if dataItem.Item.CourseSystemName != "" {
				str += dataItem.Item.CourseSystemName + "_"
			}

			if dataItem.Item.LessonName != "" {
				str += dataItem.Item.LessonName + "_"
			}

			if dataItem.Item.CategoryName != "" {
				str += dataItem.Item.CategoryName + "_"
			}

			if dataItem.Item.ResourceName != "" {
				str += dataItem.Item.ResourceName + "_"
			}

			if str != "" {
				str = str[:len(str)-1]
				response = append(response, str)
			}
		}
	}

	return response
}

func formatRnResponse(resp dto.QueryPad2Resp) dto.QueryPad2RespSimple {
	simpleData := make([]*dto.Pad2SkillDataSimple, 0)
	for _, v := range resp.Data {
		if v.Data == nil {
			continue
		}
		simpleData = append(simpleData, &dto.Pad2SkillDataSimple{
			Skill:          v.Skill,
			Sort:           v.Sort,
			VerticalDomain: v.VerticalDomain,
			ModuleId:       v.ModuleId,
			ParserId:       v.ParserId,
			Data: &dto.NLUSkillItemSimple{
				Skill:          v.Data.Skill,
				Task:           v.Data.Task,
				Widget:         v.Data.Widget,
				ShowTypeList:   v.Data.ShowTypeList,
				ShowCarType:    v.Data.ShowCarType,
				IsResultValid:  v.Data.IsResultValid,
				Dispatch:       v.Data.Dispatch,
				Command:        v.Data.Command,
				MixedModalData: v.Data.MixedModalData,
			},
		})
	}

	return dto.QueryPad2RespSimple{
		Data:     simpleData,
		Dispatch: resp.Dispatch,
		ShowType: resp.ShowType,
	}
}

func (m *MemoryContextBiz) AddSugMemory(ctx context.Context, userTalID string, sug []string) error {
	return m.memoryContextData.AddSugMemory(ctx, userTalID, sug)
}

func (m *MemoryContextBiz) GetSugMemory(ctx context.Context, userTalID string) ([]string, error) {
	return m.memoryContextData.GetSugMemory(ctx, userTalID)
}

func (m *MemoryContextBiz) DelSugMemory(ctx context.Context, userTalID string) error {
	return m.memoryContextData.DelSugMemory(ctx, userTalID)
}

func (m *MemoryContextBiz) SetAddMemoryContextRejectFlag(sessionID string) error {
	if err := m.memoryContextData.SetAddMemoryContextRejectFlag(context.Background(), sessionID); err != nil {
		return err
	}

	return nil
}

func (m *MemoryContextBiz) GetAddMemoryContextRejectFlag(sessionID string) bool {
	flag, err := m.memoryContextData.GetAddMemoryContextRejectFlag(context.Background(), sessionID)
	if err != nil {
		return false
	}

	return flag == 1
}

// SetMathAgentFlag 讲题标识
func (m *MemoryContextBiz) SetMathAgentFlag(ctx context.Context, talId string) error {
	uid := uuid.New().String()
	val, err := m.memoryContextData.SetMemoryContextMathAgentFlag(context.TODO(), talId, uid)
	if err != nil {
		m.log.WithContext(ctx).Errorf("SetMathAgentFlag talId: %s; err: %v", talId, err)
		return err
	}
	m.log.WithContext(ctx).Infof("SetMathAgentFlag talId: %s; val: %s", talId, val)
	return nil
}

// GetMathAgentFlag 讲题标识
func (m *MemoryContextBiz) GetMathAgentFlag(ctx context.Context, talId string) string {
	val, err := m.memoryContextData.GetMemoryContextMathAgentFlag(context.TODO(), talId)
	m.log.WithContext(ctx).Infof("GetMathAgentFlag talId: %s; val: %v; err: %v", talId, val, err)
	return val
}

// DelMathAgentFlag 讲题标识
func (m *MemoryContextBiz) DelMathAgentFlag(ctx context.Context, talId string) error {
	val, err := m.memoryContextData.DelMemoryContextMathAgentFlag(context.TODO(), talId)
	if err != nil {
		m.log.WithContext(ctx).Errorf("DelMathAgentFlag talId: %s; err: %v", talId, err)
		return err
	}
	m.log.WithContext(ctx).Infof("DelMathAgentFlag talId: %s; val: %d", talId, val)
	return nil
}

// SetMathAgentQuestion 讲题question
func (m *MemoryContextBiz) SetMathAgentQuestion(talId string, agentQuestion string, agentRag string) error {
	if _, err := m.memoryContextData.SetMemoryContextMathAgentQuestion(context.Background(), talId, agentQuestion, agentRag); err != nil {
		return err
	}
	return nil
}

// GetMathAgentQuestion 讲题question
func (m *MemoryContextBiz) GetMathAgentQuestion(talId string) (agentQuestion string, agentRag string, err error) {
	questionMap, err := m.memoryContextData.GetMemoryContextMathAgentQuestion(context.Background(), talId)
	if err != nil {
		return "", "", err
	}
	return questionMap["question"], questionMap["rag"], nil
}

// DelMathAgentQuestion 讲题question
func (m *MemoryContextBiz) DelMathAgentQuestion(talId string) error {
	if _, err := m.memoryContextData.DelMemoryContextMathAgentQuestion(context.Background(), talId); err != nil {
		return err
	}
	return nil
}

func (m *MemoryContextBiz) SetNXXiaosiCommandKey(ctx context.Context, talId string) (bool, error) {
	return m.memoryContextData.SetNXXiaoSiCommand(ctx, talId)
}

// GetKETQAgentFlag KET答疑
func (m *MemoryContextBiz) GetKETQAgentFlag(ctx context.Context, talId string) string {
	val, err := m.memoryContextData.GetMemoryContextKETQAgentFlag(context.TODO(), talId)
	m.log.WithContext(ctx).Infof("GetKETQAgentFlag talId: %s; val: %v; err: %v", talId, val, err)
	return val
}

func (m *MemoryContextBiz) SetDeepSeekFlag(ctx context.Context, talId string) error {
	if talId == "" {
		return nil
	}
	uid := uuid.New().String()
	if err := m.memoryContextData.SetDeepSeekFlag(ctx, talId, uid); err != nil {
		return err
	}
	return nil
}

func (m *MemoryContextBiz) GetDeepSeekFlag(ctx context.Context, talId string) string {
	val, err := m.memoryContextData.GetDeepSeekFlag(ctx, talId)
	if err != nil {
		return ""
	}
	return val
}

func (m *MemoryContextBiz) DelDeepSeekFlag(ctx context.Context, talId string) error {
	if err := m.memoryContextData.DelDeepSeekFlag(ctx, talId); err != nil {
		return err
	}
	return nil
}

func (m *MemoryContextBiz) SetExerciseOcr(ctx context.Context, talId string, exerciseOcr string) error {
	if talId == "" || exerciseOcr == "" {
		return nil
	}
	if err := m.memoryContextData.SetExerciseOcr(ctx, talId, exerciseOcr); err != nil {
		return err
	}
	return nil
}

func (m *MemoryContextBiz) GetExerciseOcr(ctx context.Context, talId string) string {
	val, err := m.memoryContextData.GetExerciseOcr(ctx, talId)
	if err != nil {
		return ""
	}
	return val
}

func (m *MemoryContextBiz) SetWorkshopFlag(ctx context.Context, talId string) error {
	if talId == "" {
		return nil
	}
	uuid := uuid.New().String()
	if err := m.memoryContextData.SetWorkshopFlag(ctx, talId, uuid); err != nil {
		return err
	}
	return nil
}

func (m *MemoryContextBiz) GetWorkshopFlag(ctx context.Context, talId string) string {
	val, err := m.memoryContextData.GetWorkshopFlag(ctx, talId)
	if err != nil {
		return ""
	}
	return val
}

func (m *MemoryContextBiz) DelWorkshopFlag(ctx context.Context, talId string) error {
	if err := m.memoryContextData.DelWorkshopFlag(ctx, talId); err != nil {
		return err
	}
	return nil
}

func (m *MemoryContextBiz) FeedChatQueryContext(ctx context.Context, talId, sessionId, query string) error {

	chatFeedUrl := m.third.ChatContextFeedUrl
	if len(chatFeedUrl) == 0 {
		return nil
	}

	type ChatQueryContextFeedRequest struct {
		TalId      string `json:"tal_id,omitempty"`
		Query      string `json:"query,omitempty"`
		SessionId  string `json:"session_id,omitempty"`
		CreateTime int64  `json:"create_time,omitempty"`
	}

	req := ChatQueryContextFeedRequest{
		TalId:      talId,
		Query:      query,
		SessionId:  sessionId,
		CreateTime: time.Now().Unix(),
	}

	ch := utils.CurlInit(ctx, time.Second*5, 1, m.log)
	resp, err := ch.ChRequest(ch.Req.SetHeaders(map[string]string{}).SetBody(req)).Post(chatFeedUrl)
	if err != nil {
		m.log.WithContext(ctx).Errorf("ChatContextFeed params: %+v, err: %v", req, err)
		return err
	}
	m.log.WithContext(ctx).Infof("ChatContextFeed resp: %s; params: %+v", string(resp.Body()), req)
	return nil
}

func (m *MemoryContextBiz) GetAtmsExplainType(ctx context.Context, talId string) int {
	val, err := m.memoryContextData.GetAtmsExplainType(ctx, talId)
	if err != nil {
		return 0
	}
	return val
}

func (m *MemoryContextBiz) DelAtmsExplainType(ctx context.Context, talId string) error {
	return m.memoryContextData.DelAtmsExplainType(ctx, talId)
}

func (m *MemoryContextBiz) SetFiltersFlag(ctx context.Context, talId string, filtersName string) error {
	if talId == "" {
		return nil
	}
	if err := m.memoryContextData.SetFiltersFlag(ctx, talId, filtersName); err != nil {
		return err
	}
	return nil
}

func (m *MemoryContextBiz) GetFiltersFlag(ctx context.Context, talId string) string {
	val, err := m.memoryContextData.GetFiltersFlag(ctx, talId)
	if err != nil {
		return ""
	}
	return val
}

func (m *MemoryContextBiz) DelFiltersFlag(ctx context.Context, talId string) error {
	if err := m.memoryContextData.DelFiltersFlag(ctx, talId); err != nil {
		return err
	}
	return nil
}
