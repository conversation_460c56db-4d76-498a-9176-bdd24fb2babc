package service

import (
	"context"
	"encoding/json"
	"fmt"
	"lui-api/internal/pkg/utils"
	"math/rand"
	"net/url"
	"sort"
	"strings"
	"sync"
	"time"

	v1 "lui-api/api/lui/v1"
	"lui-api/internal/biz"
	"lui-api/internal/common"
	"lui-api/internal/conf"
	"lui-api/internal/data"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	"lui-api/pkg/util"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/mitchellh/mapstructure"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/types/known/structpb"
)

type LuiService struct {
	v1.UnimplementedLuiContentApiServer
	contentService       *ContentService
	bizBase              *biz.BaseUsecase
	bizNlu               *biz.NluUsecase
	log                  *log.Helper
	logger               log.Logger
	studyLogBiz          *biz.StudyLogBiz
	toolsBiz             *biz.ToolsBiz
	thirdConf            *conf.Third
	uCenterBiz           *biz.UCenterBiz
	legalBiz             *biz.LegalBizUsecase
	confWhiteOsVersion   *conf.WhiteOsVersion
	bizEmbeddingSync     *biz.EmbeddingSyncBiz
	traceBiz             *biz.TraceUsecase
	Api                  *conf.Api
	bizGrayBiz           *biz.GrayBizUsecase
	luiControllerBiz     *biz.LuiControllerBiz
	skillHub             *biz.XSSkillHubBiz
	dataConf             *conf.Data
	ragBiz               *biz.XiaosiRagBiz
	memoryBiz            *biz.MemoryContextBiz
	sugRepo              *data.SugRepo
	msgBiz               *biz.MsgUsecase
	rnBiz                *biz.RnUsecase
	voiceInteractionTips map[string]*conf.VoiceInteractionCommands
}

func NewLuiService(logger log.Logger, bizBase *biz.BaseUsecase, bizNlu *biz.NluUsecase, contentService *ContentService,
	studyLogBiz *biz.StudyLogBiz, toolsBiz *biz.ToolsBiz, thirdConf *conf.Third, uCenterBiz *biz.UCenterBiz,
	legalBiz *biz.LegalBizUsecase, confWhiteOsVersion *conf.WhiteOsVersion, bizEmbeddingSync *biz.EmbeddingSyncBiz,
	traceBiz *biz.TraceUsecase, api *conf.Api, bizGrayBiz *biz.GrayBizUsecase, luiControllerBiz *biz.LuiControllerBiz,
	skillHub *biz.XSSkillHubBiz, dataConf *conf.Data, ragBiz *biz.XiaosiRagBiz, memoryBiz *biz.MemoryContextBiz,
	sugRepo *data.SugRepo, msgBiz *biz.MsgUsecase, rnBiz *biz.RnUsecase, voiceInteractionTips map[string]*conf.VoiceInteractionCommands) *LuiService {
	return &LuiService{
		log:                  log.NewHelper(logger),
		logger:               logger,
		bizBase:              bizBase,
		bizNlu:               bizNlu,
		contentService:       contentService,
		studyLogBiz:          studyLogBiz,
		toolsBiz:             toolsBiz,
		thirdConf:            thirdConf,
		uCenterBiz:           uCenterBiz,
		legalBiz:             legalBiz,
		confWhiteOsVersion:   confWhiteOsVersion,
		bizEmbeddingSync:     bizEmbeddingSync,
		traceBiz:             traceBiz,
		Api:                  api,
		bizGrayBiz:           bizGrayBiz,
		luiControllerBiz:     luiControllerBiz,
		skillHub:             skillHub,
		dataConf:             dataConf,
		ragBiz:               ragBiz,
		memoryBiz:            memoryBiz,
		sugRepo:              sugRepo,
		msgBiz:               msgBiz,
		rnBiz:                rnBiz,
		voiceInteractionTips: voiceInteractionTips,
	}
}

func (s *LuiService) LuiNluResult(ctx context.Context, req *v1.LuiNluRequest) (*structpb.Struct, error) {

	// 兼容拿不到grade的客户端问题
	if req.Grade == "" {
		req.Grade = "四年级"
		req.GradeId = "4"
	}

	// 全屏态
	if req.SceneMode == dto.SceneModeFullView {
		// 深度思考模式
		deepSeekResp, _ := s.HandleDeepseek(ctx, req, nil)
		if deepSeekResp != nil {
			return deepSeekResp, nil
		}
	}

	if req.SceneMode == dto.SceneModeNotUnderstand {
		ketFlag := s.memoryBiz.GetKETQAgentFlag(ctx, req.TalId)
		if ketFlag != "" {
			isMath, nluResp, nluData := s.QueryKETQSceneNlu(ctx, req)
			if isMath && nluData != nil {
				resp, err := util.ReplyAny(nluResp)
				if err != nil {
					s.log.WithContext(ctx).Errorf("FetchNLU ReplyAny err: %+v; nlu: %+v", err, resp)
					return nil, v1.ErrorInternalError("nlu")
				}
				err = s.memoryBiz.AddXPadMemoryContext(context.TODO(), dto.MemoryContextReq{
					UserTalID: req.TalId,
					SessionId: req.RequestId,
					AsrInfo:   req.AsrInfo,
				}, resp, nluData)
				s.log.WithContext(ctx).Infof("QueryKETQSceneNlu nluResp: %+v; AddXPadMemoryContext err: %v", resp, err)
				return resp, nil
			}
		}
	}

	if req.BizType != common.AtmsExplainBizType {
		_ = s.memoryBiz.DelAtmsExplainType(ctx, req.TalId)
	} else {
		return s.AtmsExplainNluResp(ctx, req)
	}

	//单字校验
	vectorAsrInfo, flag, err := s.SingleAsrVectorLib(ctx, req)
	if flag && err == nil {
		return vectorAsrInfo, nil
	}

	// ces 测生肖需求
	if req.BizType == "com.tal.pad.animal" {
		return s.GetZodiac(ctx, req)
	}

	//判断版本 走新的逻辑
	if (strings.Contains(s.thirdConf.XPad2AppId, custom_context.GetXAppId(ctx)) || strings.Contains(s.thirdConf.XPad1V2AppId, custom_context.GetXAppId(ctx))) && s.confWhiteOsVersion.Status == 1 {
		return s.LuiNluPad2Result(ctx, req)
	}

	if custom_context.GetXAppId(ctx) == s.thirdConf.XPodAppId {
		return s.LuiNluControllerResult(ctx, req)
	}

	// 一代快修
	padHotfix, flag, err := s.QueryPadHotfix2Vdb(ctx, req)
	if flag && err == nil {
		return padHotfix, nil
	}

	var (
		nlu     *dto.NluData
		err1    error
		nlu4lui *dto.NluData
		err2    error
	)

	wg := sync.WaitGroup{}
	wg.Add(2)

	go func() {
		defer wg.Done()

		nlu, err1 = s.bizNlu.FetchNLU(ctx, req)
		if err1 != nil {
			s.log.WithContext(ctx).Errorf("FetchNLU err: %+v", err1)
		}
		if nlu != nil {
			tmp := dto.CommendTmp{}
			_ = mapstructure.Decode(nlu.Command, &tmp)

			interceptFlag, interceptTts := CheckInterceptAppXPad1(req.Grade, tmp.Param.Appid)
			if interceptFlag {
				nlu.TtsNorm = interceptTts
				nlu.Command = nil
			}
		}
	}()

	go func() {
		defer wg.Done()

		nlu4lui, err2 = s.LuiNlu4LuiData(ctx, req)
		if err2 != nil {
			s.log.WithContext(ctx).Errorf("FetchNLU4LUI err: %+v", err2)
		}
	}()

	wg.Wait()
	osVersion := custom_context.GetXOSVersion(ctx)
	pkgTime := 0
	if len(osVersion) > 2 {
		pkgTime = cast.ToInt(osVersion[1:])
	}
	// 没命中lui意图，走老的
	if nlu4lui == nil || nlu4lui.Skill == "" {
		text := s.legalBiz.CheckPadIfNeed(nlu)
		if s.thirdConf.LegalCheck.CheckEnable && text != "" {
			illegalType, _ := s.legalBiz.LegalCheck(ctx, req.TalId, text, "lui_output")
			if illegalType != "" {
				nlu = s.legalBiz.BuildPadDefaultIllegalResp(illegalType, nlu)
			}
		}

		if nlu != nil && pkgTime != 0 && pkgTime < common.PaiVersionFlag {
			nlu.TtsNorm = strings.ReplaceAll(nlu.TtsNorm, "小思", "小派")
			nlu.TtsShow = strings.ReplaceAll(nlu.TtsShow, "小思", "小派")
		}
		resp, err := util.ReplyAny(nlu)
		if err != nil {
			s.log.WithContext(ctx).Errorf("FetchNLU ReplyAny err: %+v; nlu: %+v", err, nlu)
			return nil, v1.ErrorInternalError("nlu")
		}
		return resp, nil
	}
	text := s.legalBiz.CheckPadIfNeed(nlu4lui)
	if s.thirdConf.LegalCheck.CheckEnable && text != "" {
		illegalType, _ := s.legalBiz.LegalCheck(ctx, req.TalId, text, "lui_output")
		if illegalType != "" {
			nlu4lui = s.legalBiz.BuildPadDefaultIllegalResp(illegalType, nlu4lui)
		}
	}

	if pkgTime != 0 && pkgTime < common.PaiVersionFlag {
		nlu4lui.TtsNorm = strings.ReplaceAll(nlu4lui.TtsNorm, "小思", "小派")
		nlu4lui.TtsShow = strings.ReplaceAll(nlu4lui.TtsShow, "小思", "小派")
	}

	// 走新的lui意图
	resp, err := util.ReplyAny(nlu4lui)
	if err != nil {
		s.log.WithContext(ctx).Errorf("FetchNLU4LUI ReplyAny err: %+v; nlu4lui: %+v", err, nlu4lui)
		return nil, v1.ErrorInternalError("nlu4lui")
	}
	return resp, nil
}

func (s *LuiService) LuiNlu4Lui(ctx context.Context, req *v1.LuiNluRequest) (*structpb.Struct, error) {

	//判断版本 走新的逻辑
	if (strings.Contains(s.thirdConf.XPad2AppId, custom_context.GetXAppId(ctx)) || strings.Contains(s.thirdConf.XPad1V2AppId, custom_context.GetXAppId(ctx))) && s.confWhiteOsVersion.Status == 1 {
		return s.LuiNluPad2Result(ctx, req)
	}

	nlu4lui, err := s.LuiNlu4LuiData(ctx, req)
	if err != nil {
		s.log.WithContext(ctx).Errorf("LuiNlu4Lui err: %+v", err)
		return nil, v1.ErrorInternalError("lui")
	}
	resp, err := util.ReplyAny(nlu4lui)
	if err != nil {
		s.log.WithContext(ctx).Errorf("LuiNlu4Lui ReplyAny err: %+v; nlu4lui: %+v", err, nlu4lui)
		return nil, v1.ErrorInternalError("lui format")
	}
	s.log.WithContext(ctx).Infof("LuiNlu4Lui resp: %+v", resp)
	return resp, nil
}

// LuiSug2Lui Sug功能 接入 FetchNLU & LuiNlu4LuiData
func (s *LuiService) LuiSug2Lui(ctx context.Context, req *v1.LuiSugRequest) (*structpb.Struct, error) {

	var (
		// 年级设置默认值， 规避没拿到年级(客户端未登录调用bug)传0报错的问题
		gradeId    = "3"
		gradeName  = "三年级"
		talId      = req.UserId
		city       string
		screenMode int32 = -1
	)
	authorization := custom_context.GetAuthorization(ctx)
	if len(authorization) > 7 {
		token := custom_context.GetAuthorization(ctx)[7:]
		authInfo, err := s.uCenterBiz.TokenParse(ctx, token)
		if err != nil {
			s.log.WithContext(ctx).Errorf("LuiSug2Lui TokenParse err: %v; token: %s", err, token)
		} else {
			if authInfo.TalID == "" {
				s.log.WithContext(ctx).Errorf("LuiSug2Lui TokenParse TalID is nil; token: %s", token)
			} else {
				talId = authInfo.TalID
				userProfile, err := s.uCenterBiz.GetUserProfile(ctx, authInfo)
				if err != nil {
					s.log.WithContext(ctx).Errorf("LuiSug2Lui GetUserProfile err: %v; authInfo: %+v", err, authInfo)
				} else {
					gradeName = userProfile.GradeName
					gradeId = cast.ToString(userProfile.Grade)
				}
			}
		}
	} else {
		s.log.WithContext(ctx).Warnf("LuiSug2Lui authorization token wrong: %s", authorization)
	}

	ipLocation := s.bizNlu.IPAnalysis(ctx)
	if ipLocation != nil {
		city = ipLocation.City
	}
	if req.ScreenState != "" {
		screenMode = cast.ToInt32(req.ScreenState)
	}
	nluReq := &v1.LuiNluRequest{
		RequestId:  req.RequestId,
		AsrInfo:    req.AsrInput.AsrInfo,
		AsrPinyin:  req.AsrInput.AsrPinyin,
		Location:   city,
		Grade:      gradeName,
		TalId:      talId,
		GradeId:    gradeId,
		BizType:    req.BizType,
		ScreenMode: screenMode,
		RnVersion:  req.RnVersion,
		BizExtra:   req.BizExtra,
		From:       req.From,
	}

	// 全屏态
	if req.SceneMode == dto.SceneModeFullView {
		// 判断上一条是否为讲题
		mathFlag := s.memoryBiz.GetMathAgentFlag(ctx, req.UserId)
		if mathFlag != "" {

			isMath, nluResp, nluData := s.QueryMathSceneNlu(ctx, nluReq)
			nluResp.BizType = req.BizType
			if isMath && nluData != nil {
				resp, err := util.ReplyAny(nluResp)
				if err != nil {
					s.log.WithContext(ctx).Errorf("FetchNLU ReplyAny err: %+v; nlu: %+v", err, resp)
					return nil, v1.ErrorInternalError("nlu")
				}
				err = s.memoryBiz.AddXPadMemoryContext(context.TODO(), dto.MemoryContextReq{
					UserTalID: nluReq.TalId,
					SessionId: nluReq.RequestId,
					AsrInfo:   nluReq.AsrInfo,
				}, resp, nluData)
				s.log.WithContext(ctx).Infof("nluResp: %v; AddXPadMemoryContext err: %v", resp, err)

				_ = s.traceBiz.SugQueryTrace(ctx, nluReq, resp)
				return resp, nil
			}
		}

		// 深度思考模式
		deepSeekResp, _ := s.HandleDeepseek(ctx, nluReq, nil)
		if deepSeekResp != nil {
			_ = s.traceBiz.SugQueryTrace(ctx, nluReq, deepSeekResp)
			return deepSeekResp, nil
		}
	}

	if req.From == common.SuggestExercisesTool {
		_ = s.memoryBiz.SetDeepSeekFlag(ctx, nluReq.TalId)
		_ = s.memoryBiz.SetExerciseOcr(ctx, nluReq.TalId, req.ExerciseOcr)
		deepSeekResp, _ := s.HandleDeepseek(ctx, nluReq, nil)
		if deepSeekResp != nil {
			_ = s.traceBiz.SugQueryTrace(ctx, nluReq, deepSeekResp)
			return deepSeekResp, nil
		}
	}

	// 根据appid&status判断 走新的逻辑
	if (strings.Contains(s.thirdConf.XPad2AppId, custom_context.GetXAppId(ctx)) || strings.Contains(s.thirdConf.XPad1V2AppId, custom_context.GetXAppId(ctx))) && s.confWhiteOsVersion.Status == 1 {
		ctx = context.WithValue(ctx, common.XWithOutMultiModal, 1)
		pad2Resp, err := s.LuiNluPad2Result(ctx, nluReq)

		_ = s.traceBiz.SugQueryTrace(ctx, nluReq, pad2Resp)
		return pad2Resp, err
	}

	var (
		nlu     *dto.NluData
		err1    error
		nlu4lui *dto.NluData
		err2    error
	)

	wg := sync.WaitGroup{}
	wg.Add(2)

	go func() {
		defer wg.Done()

		nlu, err1 = s.bizNlu.FetchNLU(ctx, nluReq)
		if err1 != nil {
			s.log.WithContext(ctx).Errorf("FetchNLU err: %+v", err1)
			return
		}
	}()

	go func() {
		defer wg.Done()

		nlu4lui, err2 = s.LuiNlu4LuiData(ctx, nluReq)
		if err2 != nil {
			s.log.WithContext(ctx).Errorf("FetchNLU4LUI err: %+v", err2)
			return
		}
	}()

	wg.Wait()
	osVersion := custom_context.GetXOSVersion(ctx)
	pkgTime := 0
	if len(osVersion) > 2 {
		pkgTime = cast.ToInt(osVersion[1:])
	}
	// 没命中lui意图，走老的
	if nlu4lui == nil || nlu4lui.Skill == "" {
		text := s.legalBiz.CheckPadIfNeed(nlu)
		if s.thirdConf.LegalCheck.CheckEnable && text != "" {
			illegalType, _ := s.legalBiz.LegalCheck(ctx, nluReq.TalId, text, "lui_output")
			if illegalType != "" {
				nlu = s.legalBiz.BuildPadDefaultIllegalResp(illegalType, nlu)
			}
		}

		if pkgTime != 0 && pkgTime < common.PaiVersionFlag {
			nlu.TtsNorm = strings.Replace(nlu.TtsNorm, "小思", "小派", -1)
			nlu.TtsShow = strings.Replace(nlu.TtsShow, "小思", "小派", -1)
		}
		resp, err := util.ReplyAny(nlu)
		if err != nil {
			s.log.WithContext(ctx).Errorf("FetchNLU ReplyAny err: %+v; nlu: %+v", err, nlu)
			return nil, v1.ErrorInternalError("nlu")
		}
		return resp, nil
	}
	text := s.legalBiz.CheckPadIfNeed(nlu4lui)
	if s.thirdConf.LegalCheck.CheckEnable && text != "" {
		illegalType, _ := s.legalBiz.LegalCheck(ctx, nluReq.TalId, text, "lui_output")
		if illegalType != "" {
			nlu4lui = s.legalBiz.BuildPadDefaultIllegalResp(illegalType, nlu4lui)
		}
	}

	if pkgTime != 0 && pkgTime < common.PaiVersionFlag {
		nlu4lui.TtsNorm = strings.Replace(nlu4lui.TtsNorm, "小思", "小派", -1)
		nlu4lui.TtsShow = strings.Replace(nlu4lui.TtsShow, "小思", "小派", -1)
	}

	// 走新的lui意图
	resp, err := util.ReplyAny(nlu4lui)
	if err != nil {
		s.log.WithContext(ctx).Errorf("FetchNLU4LUI ReplyAny err: %+v; nlu4lui: %+v", err, nlu4lui)
		return nil, v1.ErrorInternalError("nlu4lui")
	}
	return resp, nil
}

// LuiNlu4LuiData -> paas-proxy: NLU4LUI -> nlu
func (s *LuiService) LuiNlu4LuiData(ctx context.Context, req *v1.LuiNluRequest) (*dto.NluData, error) {

	var nlu4lui *dto.Nlu4LuiData
	nlu4lui, err2 := s.bizNlu.FetchNLU4LUI(ctx, req)
	if err2 != nil {
		return nil, errors.WithMessage(err2, "bizNlu FetchNLU4LUI err")
	}

	s.log.WithContext(ctx).Infof("LuiNlu4LuiData nlu4lui: %+v", util.Marshal(nlu4lui))

	// 跨学段拦截
	interceptFlag, interceptTts := s.CheckIntercept(ctx, nlu4lui.AllSlots.GradeName, nlu4lui.Skill)
	if interceptFlag {
		luiWidgetContent := dto.Nlu4LuiWidgetContent{
			Category: 0,
			SlotDict: nlu4lui.AllSlots,
			Title:    "",
			Data:     nil,
			Word:     nlu4lui.Word,
			Scheme:   "",
		}

		luiWidget := dto.Nlu4LuiWidget{
			ModuleName:       nlu4lui.ModuleName,
			IsValidWidget:    nlu4lui.IsValidWidget,
			ResultConfidence: nlu4lui.ResultConfidence,
			NeedConfirm:      nlu4lui.NeedConfirm,
			Content:          luiWidgetContent,
		}
		luiWidgetBt, _ := json.Marshal(luiWidget)
		nluByLui := &dto.NluData{
			UserID:    req.TalId,
			RequestID: nlu4lui.RequestID,
			Skill:     nlu4lui.Skill,
			TtsNorm:   interceptTts,
			TtsShow:   interceptTts,
			Widget:    string(luiWidgetBt),
		}
		return nluByLui, nil
	}

	var nluByLui *dto.NluData
	var err error
	switch nlu4lui.Skill {
	case common.SearchCourse.ToString():
		nluByLui, err = s.buildSearchCourseNLU4LuiResp(ctx, nlu4lui, 1, req.TalId)
	case common.SearchQuality.ToString():
		nluByLui, err = s.buildSearchCultivationResp(ctx, nlu4lui, 1)
	case common.ContinueLearnSubject.ToString():
		nluByLui, err = s.buildCourseLearnTrackResp(ctx, nlu4lui, req.TalId)
	case common.ContinueLearnQuality.ToString():
		nluByLui, err = s.buildCultivationLearnTrackResp(ctx, nlu4lui, req.TalId)
	case common.PaperSubjectConfirm.ToString():
		nluByLui, err = s.buildPaperConfirmResp(ctx, nlu4lui, req.AsrInfo, req.AsrPinyin)
	case common.SearchPaper.ToString():
		nluByLui, err = s.buildSearchPaperResp(ctx, nlu4lui, req.TalId)
	case common.ListenWrite.ToString():
		nluByLui, err = s.buildDictationResp(ctx, nlu4lui, req.TalId)
	case common.ClickRead.ToString():
		nluByLui, err = s.buildClickReadResp(ctx, nlu4lui)
	case common.Recite.ToString():
		nluByLui, err = s.buildRecitationResp(ctx, nlu4lui, req.TalId)
	case common.SearchCourseBook.ToString():
		nluByLui, err = s.buildClickReadResp(ctx, nlu4lui)
	default:
		return nil, nil
	}

	if err != nil {
		s.log.WithContext(ctx).Warnf("LuiNlu4LuiData buildResp Err: %+v", err)
		return nil, err
	}

	return nluByLui, nil
}

func (s *LuiService) judgeSupportCourse(ctx context.Context, slotDict dto.SlotDict) bool {
	// 学练机课程资源 仅仅支持同步课
	if strings.Contains(s.thirdConf.AppIdMap["xueLianJiAppIds"], custom_context.GetXAppId(ctx)) {
		if slotDict.CourseSystemName == "同步" {
			return false
		}
	} else if strings.Contains(s.thirdConf.AppIdMap["xPad2AppId_pingjia25"], custom_context.GetXAppId(ctx)) {
		unsupportedCourseSystemNames := []string{"提升a", "提升s", "奥数", "培优a", "培优s", "能力提升"}
		for _, unsupportedCourseSystemName := range unsupportedCourseSystemNames {
			if strings.ToLower(slotDict.CourseSystemName) == unsupportedCourseSystemName {
				return false
			}
		}
	}

	return true
}

func (s *LuiService) NotSupportCourseDefault(ctx context.Context, nlu4lui *dto.Nlu4LuiData) (*dto.NluData, error) {
	// 生成一个随机索引
	randomIndex := util.RandomInt(len(tssSlice))
	// 使用随机索引从切片中选取一个字符串
	ttsNorm := tssSlice[randomIndex]
	slots := dto.SlotDict{
		SubjectId:    nlu4lui.AllSlots.SubjectId,
		SubjectName:  nlu4lui.AllSlots.SubjectName,
		SemesterId:   nlu4lui.AllSlots.SemesterId,
		SemesterName: nlu4lui.AllSlots.SemesterName,
		VersionId:    nlu4lui.AllSlots.VersionId,
		VersionName:  nlu4lui.AllSlots.VersionName,
		GradeId:      nlu4lui.AllSlots.GradeId,
		GradeName:    nlu4lui.AllSlots.GradeName,
	}
	_, scheme, err := s.contentService.buildSubjectSlotLuiNluData(ctx, nlu4lui.Hit, slots)
	if err != nil {
		s.log.WithContext(ctx).Warnf("buildSubjectSlotLuiNluData err: %+v", err)
		return nil, err
	}

	luiWidgetContent := dto.Nlu4LuiWidgetContent{
		Category:      common.Subject.Int(),
		Scheme:        scheme,
		SlotDict:      nlu4lui.AllSlots,
		Toast:         "",
		ToastDuration: 0,
		Title:         ttsNorm,
		Data:          nil,
		Word:          nlu4lui.Word,
		Filters:       nil,
		Confirm:       nil,
	}
	luiWidget := dto.Nlu4LuiWidget{
		ModuleName:       nlu4lui.ModuleName,
		IsValidWidget:    nlu4lui.IsValidWidget,
		ResultConfidence: nlu4lui.ResultConfidence,
		NeedConfirm:      nlu4lui.NeedConfirm,
		Content:          luiWidgetContent,
	}
	luiWidgetBt, _ := json.Marshal(luiWidget)

	return &dto.NluData{
		UserID:    "",
		RequestID: nlu4lui.RequestID,
		Skill:     nlu4lui.Skill,
		TtsNorm:   ttsNorm,
		TtsShow:   ttsNorm,
		Widget:    string(luiWidgetBt),
		Count:     0,
	}, nil
}

// 找学科课程
func (s *LuiService) buildSearchCourseNLU4LuiResp(ctx context.Context, nlu4lui *dto.Nlu4LuiData, skillNum int, talId string) (*dto.NluData, error) {

	s.log.WithContext(ctx).Infof("buildSubjectSlotLuiNluData nlu4lui: %s", util.Marshal(nlu4lui))

	// 找一类学科课程 有学科 无结果
	if !s.judgeSupportCourse(ctx, nlu4lui.AllSlots) {
		return s.NotSupportCourseDefault(ctx, nlu4lui)
	}

	var (
		ttsNorm          string
		luiWidgetContent interface{}
		count            int
	)

	if len(nlu4lui.ResourceList) == 0 {

		hitStatus, scheme, err := s.contentService.buildSubjectSlotLuiNluData(ctx, nlu4lui.Hit, nlu4lui.AllSlots)
		if err != nil {
			s.log.WithContext(ctx).Warnf("buildSubjectSlotLuiNluData err: %+v", err)
			return nil, err
		}

		// 检索有该槽位组合
		if hitStatus {
			if len(nlu4lui.SlotDict.CourseSystemName) == 0 {
				// 找全部学科课程
				if nlu4lui.RawSlotDict.SubjectName != "" {
					ttsNorm = fmt.Sprintf(common.TtsTpl113, nlu4lui.RawSlotDict.SubjectName, "课程资源")
				}
			} else {
				// 找一类学科课程 有学科
				if nlu4lui.RawSlotDict.SubjectName != "" || nlu4lui.RawSlotDict.CourseSystemName != "" {
					ttsNorm = fmt.Sprintf(common.TtsTpl113, nlu4lui.RawSlotDict.SubjectName+nlu4lui.RawSlotDict.CourseSystemName, "课程资源")
				}
			}
		}

		if ttsNorm == "" && nlu4lui.AllSlots.SearchFlag {
			ttsNorm = fmt.Sprintf(common.TtsTpl113, nlu4lui.RawSlotDict.SubjectName, "课程资源")
		}

		if ttsNorm == "" {
			// 找一类学科课程 有学科 无结果
			// 生成一个随机索引
			randomIndex := util.RandomInt(len(tssSlice))
			// 使用随机索引从切片中选取一个字符串
			ttsNorm = tssSlice[randomIndex]

		} else {
			//旗舰款 多轮对话 预测下一步需求并引导,在同一轮对话中，用户通过小思首次打开了全部课程页
			appId := custom_context.GetXAppId(ctx)
			version := utils.GetVersionNum(custom_context.GetXOSVersion(ctx))
			if strings.Contains(s.thirdConf.AppIdMap["xiaosi2AppId"], appId) && version >= Version0606 {
				memoryContexts, _ := s.memoryBiz.GetXPadMemoryContext(ctx, talId, 3)
				mc, _ := json.Marshal(memoryContexts)
				s.log.WithContext(ctx).Infof("memoryContexts:%s", string(mc))
				if memoryContexts != nil && len(memoryContexts) > 0 {
					if !strings.Contains(memoryContexts[len(memoryContexts)-1].TtsInfo, common.TTSNextStepGuide) &&
						(len(memoryContexts) >= 2 && !strings.Contains(memoryContexts[len(memoryContexts)-2].TtsInfo, common.TTSNextStepGuide)) &&
						(len(memoryContexts) >= 3 && !strings.Contains(memoryContexts[len(memoryContexts)-3].TtsInfo, common.TTSNextStepGuide)) {
						ttsNorm = fmt.Sprintf("%s%s", ttsNorm, common.TTSNextStepGuide)
					} else if strings.Contains(memoryContexts[len(memoryContexts)-1].TtsInfo, common.TTSNextStepGuide) {
						ttsNorm = fmt.Sprintf("%s%s", ttsNorm, s.GetPolishTTS())
					}
				}
			}
		}

		luiWidgetContent = dto.Nlu4LuiWidgetContent{
			Category:      common.Subject.Int(),
			Scheme:        scheme,
			SlotDict:      nlu4lui.AllSlots,
			Toast:         "",
			ToastDuration: 0,
			Title:         ttsNorm,
			Data:          nil,
			Word:          nlu4lui.Word,
			Filters:       nil,
			Confirm:       nil,
		}

	} else {
		realResource, scheme := s.GetRealResourceList(nlu4lui.ResourceList)
		if scheme != "" {
			ttsNorm = fmt.Sprintf(common.TtsTpl112, nlu4lui.ResourceList[0].ResourceName)
			luiWidgetContent = dto.Nlu4LuiWidgetContent{
				Category:      common.Subject.Int(),
				Scheme:        scheme,
				SlotDict:      nlu4lui.AllSlots,
				Toast:         ttsNorm,
				ToastDuration: 0,
				Title:         fmt.Sprintf(common.TtsTpl112, nlu4lui.ResourceList[0].ResourceName),
				Data:          nil,
				Word:          nlu4lui.Word,
				Filters:       nil,
				Confirm:       nil,
			}
		} else {
			nluDataList, err := s.contentService.buildSubjectClassResourceLuiNluData(ctx, nlu4lui.AllSlots, realResource)
			if err != nil {
				s.log.WithContext(ctx).Errorf("buildSubjectClassResourceLuiNluData err: %+v", err)
				return nil, err
			}

			// 根据场景设置TTS文本
			if len(nlu4lui.SlotDict.CourseName) > 0 { // 有课程名称
				if len(nluDataList) == 0 {
					ttsNorm = fmt.Sprintf(common.TtsTpl132, "", "课程资源")
				} else if len(nluDataList) == 1 {
					ttsNorm = fmt.Sprintf(common.TtsTpl112, nlu4lui.ResourceList[0].ResourceName)
				} else {
					ttsNorm = common.TtsTpl421
				}
			} else if len(nlu4lui.SlotDict.CourseSystemName) > 0 { // 有学科 有体系类型
				if len(nluDataList) == 0 {
					ttsNorm = fmt.Sprintf(common.TtsTpl132, nlu4lui.RawSlotDict.CourseSystemName, "课程资源")
				} else if len(nluDataList) == 1 {
					ttsNorm = fmt.Sprintf(common.TtsTpl112, nlu4lui.ResourceList[0].ResourceName)
				} else {
					ttsNorm = fmt.Sprintf(common.TtsTpl121, nlu4lui.RawSlotDict.CourseSystemName)
				}
			} else {
				if len(nluDataList) == 0 {
					ttsNorm = common.TtsTpl131
				} else if len(nluDataList) == 1 {
					if nluDataList[0].IconType == 1 { // 视频
						ttsNorm = fmt.Sprintf(common.TtsTpl111, nlu4lui.ResourceList[0].ResourceName)
					} else { // 非视频
						ttsNorm = fmt.Sprintf(common.TtsTpl112, nlu4lui.ResourceList[0].ResourceName)
					}
				} else {
					ttsNorm = common.TtsTpl421
				}
			}

			var toast string
			if len(nluDataList) == 1 && skillNum == 1 {
				// 小思3.0 直接播。2.0暂停
				if !strings.Contains(s.thirdConf.AppIdMap["xPad25AppIds"], custom_context.GetXAppId(ctx)) {
					nluDataByte, _ := json.Marshal(nluDataList[0])
					nluDataStr := strings.Replace(string(nluDataByte), "playAfterVoice=0", "playAfterVoice=1", 1)
					var nluDataNew dto.ResourceSortRespItem
					_ = json.Unmarshal([]byte(nluDataStr), &nluDataNew)
					nluDataList = []*dto.ResourceSortRespItem{&nluDataNew}
					toast = ttsNorm
				}
			}

			count = len(nluDataList)
			luiWidgetContent = dto.Nlu4LuiWidgetContent{
				Category:      common.Subject.Int(),
				SlotDict:      nlu4lui.AllSlots,
				Toast:         toast,
				ToastDuration: 0,
				Title:         ttsNorm,
				Data:          nluDataList,
				Word:          nlu4lui.Word,
				Filters:       dto.TransSubjectResource2Filter(nlu4lui.ResourceList),
				Confirm:       nil,
			}
		}
	}

	luiWidget := dto.Nlu4LuiWidget{
		ModuleName:       nlu4lui.ModuleName,
		IsValidWidget:    nlu4lui.IsValidWidget,
		ResultConfidence: nlu4lui.ResultConfidence,
		NeedConfirm:      nlu4lui.NeedConfirm,
		Content:          luiWidgetContent,
	}
	luiWidgetBt, err := json.Marshal(luiWidget)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Marshal err: %+v", err)
		return nil, err
	}

	return &dto.NluData{
		UserID:    "",
		RequestID: nlu4lui.RequestID,
		Skill:     nlu4lui.Skill,
		TtsNorm:   ttsNorm,
		Widget:    string(luiWidgetBt),
		Count:     count,
	}, nil
}

// 找素养课程
func (s *LuiService) buildSearchCultivationResp(ctx context.Context, nlu4lui *dto.Nlu4LuiData, skillNum int) (*dto.NluData, error) {

	// 找一类学科课程 有学科 无结果
	if !s.judgeSupportCourse(ctx, nlu4lui.AllSlots) {
		return s.NotSupportCourseDefault(ctx, nlu4lui)
	}

	var luiWidgetContent dto.Nlu4LuiWidgetContent
	var count int
	if len(nlu4lui.ResourceList) == 0 {
		hitStatus, scheme, err := s.contentService.buildCultivationSlotLuiNluData(ctx, nlu4lui.Hit, nlu4lui.AllSlots)
		if err != nil {
			s.log.WithContext(ctx).Errorf("buildCultivationSlotLuiNluData err: %+v", err)
			return nil, err
		}
		var ttsNorm string
		if hitStatus {
			if len(nlu4lui.SlotDict.CatalogName) > 0 {
				// 专辑类型 一个结果
				ttsNorm = fmt.Sprintf(common.TtsTpl121, nlu4lui.RawSlotDict.CatalogName)
			}
		}
		if ttsNorm == "" {
			// 生成一个随机索引
			randomIndex := util.RandomInt(len(tssSlice))
			// 使用随机索引从切片中选取一个字符串
			ttsNorm = tssSlice[randomIndex]
		}
		luiWidgetContent = dto.Nlu4LuiWidgetContent{
			Category:      common.Cultivation.Int(),
			Scheme:        scheme,
			SlotDict:      nlu4lui.AllSlots,
			Toast:         "",
			ToastDuration: 0,
			Title:         ttsNorm,
			Data:          nil,
			Word:          nlu4lui.Word,
			Filters:       nil,
			Confirm:       nil,
		}
	} else {
		var ttsNorm string
		realResource, scheme := s.GetRealResourceList(nlu4lui.ResourceList)
		if scheme != "" {
			ttsNorm = fmt.Sprintf(common.TtsTpl112, nlu4lui.ResourceList[0].ResourceName)
			luiWidgetContent = dto.Nlu4LuiWidgetContent{
				Category:      common.Cultivation.Int(),
				Scheme:        scheme,
				SlotDict:      nlu4lui.AllSlots,
				Toast:         ttsNorm,
				ToastDuration: 0,
				Title:         fmt.Sprintf(common.TtsTpl112, nlu4lui.ResourceList[0].ResourceName),
				Data:          nil,
				Word:          nlu4lui.Word,
				Filters:       nil,
				Confirm:       nil,
			}
		} else {
			nluData, err := s.contentService.buildAttainmentResourceLuiNluData(ctx, nlu4lui.AllSlots, realResource)
			if err != nil {
				s.log.WithContext(ctx).Errorf("buildAttainmentResourceLuiNluData err: %+v", err)
				return nil, err
			}

			if len(nluData) == 1 {
				if nlu4lui.SubIntent == 4 { // 视频
					ttsNorm = fmt.Sprintf(common.TtsTpl111, nlu4lui.ResourceList[0].ResourceName)
				} else { // 专辑
					ttsNorm = fmt.Sprintf(common.TtsTpl112, nlu4lui.ResourceList[0].ResourceName)
				}
			} else if len(nluData) == 0 {
				ttsNorm = common.TtsTpl131
			} else {
				if len(nlu4lui.SlotDict.CatalogName) > 0 {
					ttsNorm = fmt.Sprintf(common.TtsTpl121, nlu4lui.SlotDict.CatalogName)
				} else {
					ttsNorm = common.TtsTpl421
				}
			}
			var toast string
			if len(nluData) == 1 && skillNum == 1 {
				// 小思3.0 直接播。2.0暂停
				if !strings.Contains(s.thirdConf.AppIdMap["xPad25AppIds"], custom_context.GetXAppId(ctx)) {
					nluDataByte, _ := json.Marshal(nluData[0])
					nluDataStr := strings.Replace(string(nluDataByte), "playAfterVoice=0", "playAfterVoice=1", 1)
					var nluDataNew dto.ResourceSortRespItem
					_ = json.Unmarshal([]byte(nluDataStr), &nluDataNew)
					nluData = []*dto.ResourceSortRespItem{&nluDataNew}
					toast = ttsNorm
				}
			}

			count = len(nluData)
			luiWidgetContent = dto.Nlu4LuiWidgetContent{
				Category:      common.Cultivation.Int(),
				SlotDict:      nlu4lui.AllSlots,
				Toast:         toast,
				ToastDuration: 0,
				Title:         ttsNorm,
				Data:          nluData,
				Word:          nlu4lui.Word,
				Filters:       []dto.Filter{},
				Confirm:       nil,
			}
		}
	}

	luiWidget := dto.Nlu4LuiWidget{
		ModuleName:       nlu4lui.ModuleName,
		IsValidWidget:    nlu4lui.IsValidWidget,
		ResultConfidence: nlu4lui.ResultConfidence,
		NeedConfirm:      nlu4lui.NeedConfirm,
		Content:          luiWidgetContent,
	}
	luiWidgetBt, _ := json.Marshal(luiWidget)
	nluByLui := &dto.NluData{
		UserID:    "",
		RequestID: nlu4lui.RequestID,
		Skill:     nlu4lui.Skill,
		TtsNorm:   luiWidgetContent.Title,
		Widget:    string(luiWidgetBt),
		Count:     count,
	}

	return nluByLui, nil
}

// 查试卷
func (s *LuiService) buildSearchPaperResp(ctx context.Context, nlu4lui *dto.Nlu4LuiData, talId string) (*dto.NluData, error) {

	// 找一类学科课程 有学科 无结果
	if !s.judgeSupportCourse(ctx, nlu4lui.AllSlots) {
		return s.NotSupportCourseDefault(ctx, nlu4lui)
	}

	var luiWidgetContent dto.Nlu4LuiWidgetContent
	var count int

	if len(nlu4lui.ResourceList) == 0 {
		luiWidgetContent = s.getSearchPaperNoResult(ctx, nlu4lui, talId)
	} else {

		nluData, err := s.contentService.buildPaperResourceLuiNluData(ctx, nlu4lui.AllSlots, nlu4lui.ResourceList)
		if err != nil {
			s.log.WithContext(ctx).Errorf("buildPaperResourceLuiNluData err: %+v", err)
			return nil, err
		}

		if len(nluData) == 0 {
			luiWidgetContent = s.getSearchPaperNoResult(ctx, nlu4lui, talId)
		} else {
			var ttsNorm string
			if len(nluData) == 1 {
				ttsNorm = fmt.Sprintf(common.TtsTpl112, nlu4lui.ResourceList[0].ResourceName)
			} else {
				ttsNorm = common.TtsTpl421
			}

			//旗舰款 润色话术 上一轮包含下一步引导
			appId := custom_context.GetXAppId(ctx)
			version := utils.GetVersionNum(custom_context.GetXOSVersion(ctx))
			if strings.Contains(s.thirdConf.AppIdMap["xiaosi2AppId"], appId) && version >= Version0606 {
				memoryContexts, _ := s.memoryBiz.GetXPadMemoryContext(ctx, talId, 1)
				s.log.WithContext(ctx).Infof("memoryContexts:%s", util.Marshal(memoryContexts))
				if memoryContexts != nil && len(memoryContexts) > 0 {
					if strings.Contains(memoryContexts[len(memoryContexts)-1].TtsInfo, common.TTSNextStepGuide) {
						ttsNorm = fmt.Sprintf("%s%s", ttsNorm, s.GetPolishTTS())
					}
				}
			}

			count = len(nluData)
			luiWidgetContent = dto.Nlu4LuiWidgetContent{
				Category:      common.ExamPaper.Int(),
				SlotDict:      nlu4lui.AllSlots,
				Toast:         "",
				ToastDuration: 0,
				Title:         ttsNorm,
				Data:          nluData,
				Word:          nlu4lui.Word,
				Filters:       dto.TransPaperResource2Filter(nlu4lui.ResourceList),
			}
		}
	}

	luiWidget := dto.Nlu4LuiWidget{
		ModuleName:       nlu4lui.ModuleName,
		IsValidWidget:    nlu4lui.IsValidWidget,
		ResultConfidence: nlu4lui.ResultConfidence,
		NeedConfirm:      nlu4lui.NeedConfirm,
		Content:          luiWidgetContent,
	}
	luiWidgetBt, _ := json.Marshal(luiWidget)
	nluByLui := &dto.NluData{
		UserID:    "",
		RequestID: nlu4lui.RequestID,
		Skill:     nlu4lui.Skill,
		TtsNorm:   luiWidgetContent.Title,
		Widget:    string(luiWidgetBt),
		Count:     count,
	}

	return nluByLui, nil
}

func (s *LuiService) getSearchPaperNoResult(ctx context.Context, nlu4lui *dto.Nlu4LuiData, talId string) (luiWidgetContent dto.Nlu4LuiWidgetContent) {
	histStatus, scheme, err := s.contentService.buildPaperSlotLuiNluData(ctx, nlu4lui.Hit, nlu4lui.AllSlots)
	if err != nil {
		s.log.WithContext(ctx).Errorf("buildPaperSlotLuiNluData err: %+v", err)
		return
	}
	var ttsNorm string
	if histStatus {
		if nlu4lui.SlotDict.SubjectName != "" {
			ttsNorm = fmt.Sprintf(common.TtsTpl113, nlu4lui.SlotDict.SubjectName, "真题套卷")

			//旗舰款 预测下一步需求并引导,润色话术
			appId := custom_context.GetXAppId(ctx)
			version := utils.GetVersionNum(custom_context.GetXOSVersion(ctx))
			if strings.Contains(s.thirdConf.AppIdMap["xiaosi2AppId"], appId) && version >= Version0606 {
				memoryContexts, _ := s.memoryBiz.GetXPadMemoryContext(ctx, talId, 3)
				s.log.WithContext(ctx).Infof("memoryContexts:%s", util.Marshal(memoryContexts))
				if memoryContexts != nil && len(memoryContexts) > 0 {
					if !strings.Contains(memoryContexts[len(memoryContexts)-1].TtsInfo, common.TTSNextStepGuide) &&
						(len(memoryContexts) > 1 && !strings.Contains(memoryContexts[len(memoryContexts)-2].TtsInfo, common.TTSNextStepGuide)) &&
						(len(memoryContexts) > 2 && !strings.Contains(memoryContexts[len(memoryContexts)-3].TtsInfo, common.TTSNextStepGuide)) {
						ttsNorm = fmt.Sprintf("%s%s", ttsNorm, common.TTSNextStepGuide)
					} else if strings.Contains(memoryContexts[len(memoryContexts)-1].TtsInfo, common.TTSNextStepGuide) {
						ttsNorm = fmt.Sprintf("%s%s", ttsNorm, s.GetPolishTTS())
					}
				}
			}

		} else {
			ttsNorm = "哎呀，这个试卷我没找到哇，不过看看这些，也许有更好的等你选择～  "
		}
	} else {
		ttsNorm = "哎呀，这个试卷我没找到哇，不过看看这些，也许有更好的等你选择～  "
	}

	luiWidgetContent = dto.Nlu4LuiWidgetContent{
		Category:      common.ExamPaper.Int(),
		Scheme:        scheme,
		SlotDict:      nlu4lui.AllSlots,
		Toast:         "",
		ToastDuration: 0,
		Title:         ttsNorm,
		Data:          nil,
		Word:          nlu4lui.Word,
	}

	return
}

// 试卷二次确认
func (s *LuiService) buildPaperConfirmResp(ctx context.Context, nlu4lui *dto.Nlu4LuiData, asrInfo, asrPinyin string) (*dto.NluData, error) {
	s.log.WithContext(ctx).Infof("buildPaperConfirmResp: %+v", nlu4lui)
	// 找一类学科课程 有学科 无结果
	if !s.judgeSupportCourse(ctx, nlu4lui.AllSlots) {
		return s.NotSupportCourseDefault(ctx, nlu4lui)
	}

	ttsNorm := common.TTSFollowUpSubject

	dictMap, _ := s.contentService.bizBase.SlotDictTransfer(ctx, nlu4lui.AllSlots)
	options, err := s.bizBase.VisibleSubjects(ctx, dictMap.GradeId)
	if err != nil {
		s.log.WithContext(ctx).Errorf("VisibleSubjects err: %+v", err)
		options = dto.GetSubjects(dictMap.GradeId)
	}

	luiWidgetContent := dto.Nlu4LuiWidgetContent{
		Category:      common.ExamPaper.Int(),
		SlotDict:      dictMap,
		Toast:         "",
		ToastDuration: 0,
		Title:         ttsNorm,
		Data:          nil,
		Word:          nlu4lui.Word,
		Filters:       nil,
		Confirm: &dto.Confirm{
			AsrInfo:   asrInfo,
			AsrPinyin: asrPinyin,
			Key:       "subject",
			Name:      "学科",
			Options:   options,
		},
	}

	luiWidget := dto.Nlu4LuiWidget{
		ModuleName:       nlu4lui.ModuleName,
		IsValidWidget:    nlu4lui.IsValidWidget,
		ResultConfidence: nlu4lui.ResultConfidence,
		NeedConfirm:      nlu4lui.NeedConfirm,
		Content:          luiWidgetContent,
	}
	luiWidgetBt, _ := json.Marshal(luiWidget)
	nluByLui := &dto.NluData{
		UserID:    "",
		RequestID: nlu4lui.RequestID,
		Skill:     common.PaperSubjectConfirm.ToString(),
		TtsNorm:   ttsNorm,
		Widget:    string(luiWidgetBt),
	}

	return nluByLui, nil
}

func (s *LuiService) buildCourseLearnTrackResp(ctx context.Context, nlu4lui *dto.Nlu4LuiData, talId string) (*dto.NluData, error) {

	// 找一类学科课程 有学科 无结果
	if !s.judgeSupportCourse(ctx, nlu4lui.AllSlots) {
		return s.NotSupportCourseDefault(ctx, nlu4lui)
	}

	cntType := 1
	var count int
	switch nlu4lui.AllSlots.GradeName {
	case "小班", "中班", "大班", "启蒙":
		if nlu4lui.AllSlots.SubjectName == "数学" {
			nlu4lui.AllSlots.SubjectName = "思维训练"
		}
		if nlu4lui.AllSlots.SubjectName == "语文" {
			nlu4lui.AllSlots.SubjectName = "口语启蒙"
		}
		if nlu4lui.AllSlots.SubjectName == "英语" {
			nlu4lui.AllSlots.SubjectName = "表达启蒙"
		}
	}
	nlu4lui.AllSlots, _ = s.contentService.bizBase.SlotDictTransfer(ctx, nlu4lui.AllSlots)
	columnId := nlu4lui.AllSlots.SubjectId
	if nlu4lui.AllSlots.GradeId <= 0 && nlu4lui.AllSlots.ColumnId > 0 {
		columnId = nlu4lui.AllSlots.ColumnId
	}
	StudyLogReq := &dto.StudyLogReq{
		TalID:        talId,
		Subject:      columnId,
		CourseSystem: nlu4lui.AllSlots.CourseSystemId,
		CntType:      cntType,
		Grade:        nlu4lui.AllSlots.GradeId,
	}
	studyLogRes, logErr := s.studyLogBiz.GetResourceList(ctx, StudyLogReq)
	if logErr != nil {
		s.log.WithContext(ctx).Errorf("GetResourceList err: %+v", logErr)
		return nil, logErr
	}

	if studyLogRes == nil {
		return nil, nil
	}

	var (
		traceItems = make([]*dto.UniLearnTrackItem, 0)
		scheme     string
		toast      string
		ttsNorm    string
	)

	if studyLogRes.JumpType == 1 {
		traceItems = append(traceItems, &dto.UniLearnTrackItem{
			Type: common.StudyLogCourse,
			Item: &dto.UniLearnTrack{
				Scheme: studyLogRes.SchemeURL,
			},
		})
		scheme = studyLogRes.SchemeURL
		ttsNorm = common.TTSNullJiXuXue
	} else if len(studyLogRes.FindData) == 0 {
		scheme = studyLogRes.SchemeURL
		ttsNorm = "未找到相关资源，已打开“学习广场”。"
	} else {
		for _, learnTrack := range studyLogRes.FindData {
			if ttsNorm == "" {
				subjectName := ""
				if nlu4lui.SlotDict.SubjectName != "" {
					subjectName = nlu4lui.RawSlotDict.SubjectName
					if nlu4lui.SlotDict.CourseSystemName != "" {
						subjectName = subjectName + " "
					}
				}
				ttsNorm = fmt.Sprintf(common.TtsTpl221, subjectName+nlu4lui.RawSlotDict.CourseSystemName)
				ttsNorm = strings.Replace(ttsNorm, "“”", "", -1)
			}
			if learnTrack.CourseSystem == common.SystemJZXV15 {
				learnTrack.IconType = 0
				learnTrack.VideoTime = 0
			}
			traceItems = append(traceItems, &dto.UniLearnTrackItem{
				Type: common.StudyLogCourse,
				Item: &dto.UniLearnTrack{
					Scheme:           learnTrack.SchemeURL,
					SubjectName:      learnTrack.SubjectStr,
					SemesterName:     learnTrack.SemesterStr,
					VersionName:      learnTrack.VersionName,
					GradeName:        learnTrack.GradeStr,
					CourseSystemName: learnTrack.CourseSystemStr,
					ResourceName:     learnTrack.ModuleName,
					VideoTime:        learnTrack.VideoTime,
					Navigation:       learnTrack.Navigation,
					Cover:            learnTrack.Cover,
					IconType:         learnTrack.IconType,
					ModuleName:       learnTrack.ModuleName,
				},
				Attr: GetStudyLogFilter(learnTrack),
			})
		}
		if len(studyLogRes.FindData) == 1 {
			scheme = studyLogRes.FindData[0].SchemeURL
			toast = fmt.Sprintf(common.TtsTpl211, studyLogRes.FindData[0].ModuleName)
			ttsNorm = strings.Replace(ttsNorm, "《》", "", -1)
		}
		count = len(studyLogRes.FindData)
	}

	if strings.Contains(s.thirdConf.AppIdMap["xPad25AppIds"], custom_context.GetXAppId(ctx)) {
		scheme = strings.Replace(scheme, "playAfterVoice=1", "playAfterVoice=0", 1)
	}
	luiWidgetContent := dto.Nlu4LuiWidgetContent{
		Category:      common.Subject.Int(),
		SlotDict:      nlu4lui.AllSlots,
		Title:         ttsNorm,
		Data:          traceItems,
		Word:          nlu4lui.Word,
		Scheme:        scheme,
		Filters:       dto.TransSubjectStudyLog2Filter(studyLogRes.FindData),
		Toast:         toast,
		ToastDuration: common.ToastDuration,
	}

	luiWidget := dto.Nlu4LuiWidget{
		ModuleName:       nlu4lui.ModuleName,
		IsValidWidget:    nlu4lui.IsValidWidget,
		ResultConfidence: nlu4lui.ResultConfidence,
		NeedConfirm:      nlu4lui.NeedConfirm,
		Content:          luiWidgetContent,
	}
	luiWidgetBt, _ := json.Marshal(luiWidget)

	nluByLui := &dto.NluData{
		UserID:    talId,
		RequestID: nlu4lui.RequestID,
		Skill:     nlu4lui.Skill,
		TtsNorm:   ttsNorm,
		Widget:    string(luiWidgetBt),
		Count:     count,
	}

	return nluByLui, nil
}

func (s *LuiService) buildCultivationLearnTrackResp(ctx context.Context, nlu4lui *dto.Nlu4LuiData, talId string) (*dto.NluData, error) {

	// 找一类学科课程 有学科 无结果
	if !s.judgeSupportCourse(ctx, nlu4lui.AllSlots) {
		return s.NotSupportCourseDefault(ctx, nlu4lui)
	}

	cntType := 2
	var count int
	nlu4lui.AllSlots, _ = s.contentService.bizBase.SlotDictTransfer(ctx, nlu4lui.AllSlots)
	columnId := nlu4lui.AllSlots.CatalogId
	if nlu4lui.AllSlots.GradeId <= 0 && nlu4lui.AllSlots.ColumnId > 0 {
		columnId = nlu4lui.AllSlots.ColumnId
	}
	StudyLogReq := &dto.StudyLogReq{
		TalID:        talId,
		Subject:      nlu4lui.AllSlots.SubjectId,
		CourseSystem: columnId,
		CntType:      cntType,
		Grade:        nlu4lui.AllSlots.GradeId,
	}
	studyLogRes, logErr := s.studyLogBiz.GetResourceList(ctx, StudyLogReq)
	if logErr != nil {
		s.log.WithContext(ctx).Errorf("GetResourceList err: %+v", logErr)
		return nil, logErr
	}

	if studyLogRes == nil {
		return nil, nil
	}

	var (
		trackItems = make([]*dto.UniCultivationTrackItem, 0)
		scheme     string
		toast      string
		ttsNorm    string
	)

	if len(studyLogRes.AlbumFindData) == 0 {
		scheme = studyLogRes.SchemeURL
		ttsNorm = "未找到相关资源，已打开“素养天地”。"
	} else if studyLogRes.JumpType == 1 {

		trackItems = append(trackItems, &dto.UniCultivationTrackItem{
			Type: common.StudyLogCultivation,
			Item: &dto.UniCultivationTrack{
				Scheme: studyLogRes.SchemeURL,
			},
		})
		scheme = studyLogRes.SchemeURL
		ttsNorm = common.TTSNullJiXuXue
	} else {
		sort.Slice(studyLogRes.AlbumFindData, func(i, j int) bool {
			return studyLogRes.AlbumFindData[i].LatestStudyTime > studyLogRes.AlbumFindData[j].LatestStudyTime
		})

		scheme = studyLogRes.AlbumFindData[0].SchemeURL
		name := studyLogRes.AlbumFindData[0].VideoTitle
		if name == "" {
			name = studyLogRes.AlbumFindData[0].AlbumTitle
		}

		trackItems = append(trackItems, &dto.UniCultivationTrackItem{
			Type: common.StudyLogCultivation,
			Item: &dto.UniCultivationTrack{
				Scheme:       scheme,
				Cover:        studyLogRes.AlbumFindData[0].AlbumCover,
				CategoryName: studyLogRes.AlbumFindData[0].CategoryName,
				Title:        name,
			},
		})

		toast = fmt.Sprintf(common.TtsTpl211, name)
		count = 1
	}

	if strings.Contains(s.thirdConf.AppIdMap["xPad25AppIds"], custom_context.GetXAppId(ctx)) {
		scheme = strings.Replace(scheme, "playAfterVoice=1", "playAfterVoice=0", 1)
	}
	luiWidgetContent := dto.Nlu4LuiWidgetContent{
		Category:      common.Cultivation.Int(),
		SlotDict:      nlu4lui.SlotDict,
		Title:         ttsNorm,
		Data:          trackItems,
		Word:          nlu4lui.Word,
		Scheme:        scheme,
		Toast:         toast,
		ToastDuration: common.ToastDuration,
	}

	luiWidget := dto.Nlu4LuiWidget{
		ModuleName:       nlu4lui.ModuleName,
		IsValidWidget:    nlu4lui.IsValidWidget,
		ResultConfidence: nlu4lui.ResultConfidence,
		NeedConfirm:      nlu4lui.NeedConfirm,
		Content:          luiWidgetContent,
	}
	luiWidgetBt, _ := json.Marshal(luiWidget)

	nluByLui := &dto.NluData{
		UserID:    talId,
		RequestID: nlu4lui.RequestID,
		Skill:     nlu4lui.Skill,
		TtsNorm:   ttsNorm,
		Widget:    string(luiWidgetBt),
		Count:     count,
	}

	return nluByLui, nil
}

// 听写
func (s *LuiService) buildDictationResp(ctx context.Context, nlu4lui *dto.Nlu4LuiData, talId string) (*dto.NluData, error) {
	toolsReq := make([]*dto.ToolsResourceParam, 0)

	for _, resource := range nlu4lui.ResourceList {
		toolsReq = append(toolsReq, &dto.ToolsResourceParam{
			TalID: talId,
			Type:  resource.ResourceType,
			Id:    cast.ToInt(resource.ResourceId),
		})
	}

	var (
		items   = make([]*dto.UniDictationItem, 0)
		scheme  string
		ttsNorm string
		count   int
	)

	if len(toolsReq) == 0 {
		ttsNorm = common.TtsTpl431
	} else {
		toolsRes, err := s.toolsBiz.GetResourceList(ctx, toolsReq)
		if err != nil {
			s.log.WithContext(ctx).Errorf("GetResourceList err: %+v", err)
			return nil, err
		}

		if len(toolsRes.Dictation) == 1 {
			info := toolsRes.Dictation[0]
			scheme = info.Scheme
			appName := ""
			if info.Subject == "语文" {
				appName = common.CnDictationAppName
			} else {
				appName = common.EnDictationAppName
			}
			items = append(items, &dto.UniDictationItem{
				Type: common.ToolsDictation,
				Item: &dto.ToolsInfo{
					Cover:        info.CoverURL,
					ResourceName: info.Name,
					VersionName:  info.BookVersion,
					GradeName:    info.Grade,
					SemesterName: info.Term,
					UnitName:     info.UnitName,
					Scheme:       info.Scheme,
					SubjectName:  info.Subject,
					IsOutside:    info.IsOutside,
				},
			})
			ttsNorm = fmt.Sprintf(common.TtsTpl411, info.Name, appName)
		} else {
			for _, info := range toolsRes.Dictation {
				items = append(items, &dto.UniDictationItem{
					Type: common.ToolsDictation,
					Item: &dto.ToolsInfo{
						Cover:        info.CoverURL,
						ResourceName: info.Name,
						VersionName:  info.BookVersion,
						GradeName:    info.Grade,
						SemesterName: info.Term,
						UnitName:     info.UnitName,
						Scheme:       info.Scheme,
						SubjectName:  info.Subject,
						IsOutside:    info.IsOutside,
					},
				})
			}
			ttsNorm = common.TtsTpl421
		}

		count = len(toolsRes.Dictation)
	}

	luiWidgetContent := dto.Nlu4LuiWidgetContent{
		Category: common.Dictation.Int(),
		SlotDict: nlu4lui.AllSlots,
		Title:    ttsNorm,
		Data:     items,
		Word:     nlu4lui.Word,
		Scheme:   scheme,
	}

	luiWidget := dto.Nlu4LuiWidget{
		ModuleName:       nlu4lui.ModuleName,
		IsValidWidget:    nlu4lui.IsValidWidget,
		ResultConfidence: nlu4lui.ResultConfidence,
		NeedConfirm:      nlu4lui.NeedConfirm,
		Content:          luiWidgetContent,
	}
	luiWidgetBt, _ := json.Marshal(luiWidget)

	nluByLui := &dto.NluData{
		UserID:    talId,
		RequestID: nlu4lui.RequestID,
		Skill:     nlu4lui.Skill,
		TtsNorm:   ttsNorm,
		Widget:    string(luiWidgetBt),
		Count:     count,
	}

	return nluByLui, nil
}

func (s *LuiService) buildRecitationResp(ctx context.Context, nlu4lui *dto.Nlu4LuiData, talId string) (*dto.NluData, error) {
	toolsReq := make([]*dto.ToolsResourceParam, 0)

	for _, resource := range nlu4lui.ResourceList {
		toolsReq = append(toolsReq, &dto.ToolsResourceParam{
			TalID: talId,
			Type:  resource.ResourceType,
			Id:    cast.ToInt(resource.ResourceId),
		})
	}

	var (
		items   = make([]*dto.UniDictationItem, 0)
		scheme  string
		ttsNorm string
		count   int
	)

	if len(toolsReq) == 0 {
		ttsNorm = common.TtsTpl431
	} else {
		toolsRes, err := s.toolsBiz.GetResourceList(ctx, toolsReq)
		if err != nil {
			s.log.WithContext(ctx).Errorf("GetResourceList err: %+v", err)
			return nil, err
		}

		if len(toolsRes.Recitation) == 1 {
			ttsNorm = fmt.Sprintf(common.TtsTpl411, nlu4lui.ResourceList[0].ResourceName, common.RecitationAppName)
		} else if len(toolsRes.Recitation) > 1 {
			ttsNorm = common.TtsTpl421
		} else {
			ttsNorm = common.TtsTpl431
		}

		for _, info := range toolsRes.Recitation {

			items = append(items, &dto.UniDictationItem{
				Type: common.ToolsRecitation,
				Item: &dto.ToolsInfo{
					Cover:        info.CoverURL,
					ResourceName: info.Name,
					VersionName:  info.BookVersion,
					GradeName:    info.Grade,
					SemesterName: info.Term,
					UnitName:     info.UnitName,
					Scheme:       info.Scheme,
					SubjectName:  info.Subject,
					IsOutside:    info.IsOutside,
				},
			})
		}
		count = len(toolsRes.Recitation)
	}

	luiWidgetContent := dto.Nlu4LuiWidgetContent{
		Category:      common.Recitation.Int(),
		SlotDict:      nlu4lui.AllSlots,
		Toast:         "",
		ToastDuration: 0,
		Title:         ttsNorm,
		Data:          items,
		Word:          nlu4lui.Word,
		Scheme:        scheme,
	}

	luiWidget := dto.Nlu4LuiWidget{
		ModuleName:       nlu4lui.ModuleName,
		IsValidWidget:    nlu4lui.IsValidWidget,
		ResultConfidence: nlu4lui.ResultConfidence,
		NeedConfirm:      nlu4lui.NeedConfirm,
		Content:          luiWidgetContent,
	}
	luiWidgetBt, _ := json.Marshal(luiWidget)

	nluByLui := &dto.NluData{
		UserID:    talId,
		RequestID: nlu4lui.RequestID,
		Skill:     nlu4lui.Skill,
		TtsNorm:   ttsNorm,
		Widget:    string(luiWidgetBt),
		Count:     count,
	}

	return nluByLui, nil
}

func (s *LuiService) LuiUserChoice(ctx context.Context, req *v1.LuiUserChoiceRequest) (*v1.LuiUserChoiceReply, error) {

	choiceReq := &dto.UserCourseChoiceReq{
		TalID:        req.TalId,
		CourseSystem: req.CourseSystem,
		Subject:      req.Subject,
		Grade:        req.Grade,
		DeviceId:     req.DeviceId,
	}
	choices, err := s.studyLogBiz.GetUserChoice(ctx, choiceReq)
	if err != nil {
		s.log.WithContext(ctx).Errorf("GetUserChoice err: %+v", err)
		return nil, err
	}
	if choices == nil {
		return nil, nil
	}

	items := make([]*v1.LuiUserChoiceReplyItem, 0)
	for _, choice := range choices {
		items = append(items, &v1.LuiUserChoiceReplyItem{
			CourseSystem:      choice.CourseSystem,
			CourseSystemAlias: choice.CourseSystemAlias,
			Version:           choice.Version,
			VersionName:       choice.VersionName,
			Subject:           choice.Subject,
			SubjectName:       choice.SubjectAlias,
		})
	}
	return &v1.LuiUserChoiceReply{
		List: items,
	}, nil
}

func (s *LuiService) buildClickReadResp(ctx context.Context, nlu4lui *dto.Nlu4LuiData) (*dto.NluData, error) {
	toolsReq := &dto.BookReadingIdQueryReq{Category: common.PointReading}

	for _, resource := range nlu4lui.ResourceList {
		bookId, catalogueId := s.contentService.bizBookReading.GetLuiBookReadingID(ctx, resource.ResourceId)
		toolsReq.BookReadingIds = append(toolsReq.BookReadingIds, dto.LuiBookReadingID{
			BookID:      bookId,
			CatalogueID: cast.ToInt(catalogueId),
		})
	}

	var (
		readingItems = make([]*dto.UniBookReadingItem, 0)
		scheme       string
		ttsNorm      string
		count        int
	)

	if len(toolsReq.BookReadingIds) == 0 {
		ttsNorm = common.TtsTpl431
	} else {
		toolsRes, err := s.contentService.bizBookReading.QueryBookReadingClass(ctx, toolsReq)
		if err != nil {
			s.log.WithContext(ctx).Errorf("QueryBookReadingClass err: %+v", err)
			return nil, err
		}
		if len(toolsRes.BookReadingList) == 0 {
			ttsNorm = common.TtsTpl431
		}

		for _, info := range toolsRes.BookReadingList {
			baseUrl, urlErr := url.Parse(info.Scheme)
			if urlErr != nil {
				s.log.WithContext(ctx).Errorf("buildClickReadResp Parse err: %+v", urlErr)
				return nil, urlErr
			}

			params := baseUrl.Query()
			params.Add("install_tip", "正在下载课本，请稍等")
			params.Add("detail_tip", fmt.Sprintf("已打开《%s》课本点读", info.CatalogueName))
			baseUrl.RawQuery = params.Encode()
			if len(toolsRes.BookReadingList) == 1 {
				scheme = baseUrl.String()
				ttsNorm = fmt.Sprintf(common.TtsTpl411, info.CatalogueName, "课本点读")
			}
			if ttsNorm == "" {
				ttsNorm = common.TtsTpl421
			}
			readingItems = append(readingItems, &dto.UniBookReadingItem{
				Type: common.ClickRead.ToString(),
				Item: &dto.BookReadingInfo{
					Cover:        info.BookCover,
					ResourceName: info.CatalogueName,
					VersionName:  info.BookName,
					GradeName:    info.GradeName,
					SemesterName: info.SemesterName,
					UnitName:     info.UnitName,
					Scheme:       baseUrl.String(),
					SubjectName:  info.SubjectName,
				},
			})
		}
		count = len(toolsRes.BookReadingList)
	}
	luiWidgetContent := dto.Nlu4LuiWidgetContent{
		Category: common.PointReading.Int(),
		SlotDict: nlu4lui.AllSlots,
		Title:    ttsNorm,
		Data:     readingItems,
		Word:     nlu4lui.Word,
		Scheme:   scheme,
	}

	luiWidget := dto.Nlu4LuiWidget{
		ModuleName:       nlu4lui.ModuleName,
		IsValidWidget:    nlu4lui.IsValidWidget,
		ResultConfidence: nlu4lui.ResultConfidence,
		NeedConfirm:      nlu4lui.NeedConfirm,
		Content:          luiWidgetContent,
	}

	nluByLui := &dto.NluData{
		UserID:    "",
		RequestID: nlu4lui.RequestID,
		Skill:     nlu4lui.Skill,
		TtsNorm:   ttsNorm,
		Widget:    util.Marshal(luiWidget),
		Count:     count,
	}

	return nluByLui, nil
}

func (s *LuiService) LuiToolsUserChoice(ctx context.Context, req *v1.LuiToolsUserChoiceRequest) (*v1.LuiToolsUserChoiceReply, error) {
	choiceReq := &dto.UserVersionReq{
		UserId:  req.TalId,
		Subject: req.Subject,
	}
	choices, err := s.toolsBiz.GetUserVersion(ctx, choiceReq)
	if err != nil {
		s.log.WithContext(ctx).Errorf("GetUserVersion err: %+v", err)
		return nil, err
	}
	if choices == nil {
		return nil, nil
	}

	return &v1.LuiToolsUserChoiceReply{
		VersionName: choices.VersionName,
	}, nil
}

func (s *LuiService) LuiUserLlmSug(ctx context.Context, req *v1.LuiUserLlmSugRequest) (*v1.LuiUserLlmSugReply, error) {

	if req.UserId == "" {
		s.log.WithContext(ctx).Warnf("userId is empty")
		return nil, nil
	}

	reply := &v1.LuiUserLlmSugReply{
		UserId: req.UserId,
	}

	sugArr, err := s.memoryBiz.GetSugMemory(ctx, req.UserId)
	if err != nil {
		return nil, err
	}

	if len(sugArr) == 0 {
		return reply, nil
	}

	sugCount := len(sugArr)
	if sugCount > 3 {
		rand.Seed(time.Now().UnixNano())
		randIndices := rand.Perm(sugCount)[:3]
		var selectedSug []string
		for _, index := range randIndices {
			selectedSug = append(selectedSug, sugArr[index])
		}
		reply.Sug = strings.Join(selectedSug, "\n")
	} else {
		reply.Sug = strings.Join(sugArr, "\n")
	}

	_ = s.memoryBiz.DelSugMemory(ctx, req.UserId)

	return reply, nil
}

func (s *LuiService) LuiUserLlmTails(ctx context.Context, req *v1.LuiUserLlmTailsRequest) (*structpb.Struct, error) {
	tts := "你没有说话哦，试着问\"小思小思，你有什么技能?\""
	if strings.Contains(req.BizType, common.BizTypeAiTutor) {
		tts = "你没有说话哦，轻拍肩膀唤醒我跟我对话吧~"
	}
	res := dto.QueryPad2Resp{
		TtsShow: tts,
		TtsNorm: tts,
		Data: []*dto.Pad2SkillData{
			{
				Skill: "no_skill",
				Data: &dto.NLUSkillItem{
					Task:          "",
					TtsShow:       tts,
					TtsNorm:       tts,
					Count:         0,
					IsResultValid: true,
				},
			},
		},
		ModelOutputIntent: "异常query",
		RejectRecType:     4,
	}
	reply, _ := util.ReplyAny(res)

	appId := custom_context.GetXAppId(ctx)
	version := utils.GetVersionNum(custom_context.GetXOSVersion(ctx))

	if strings.Contains(req.BizType, common.BizTypeAiTutor) {
		return reply, nil
	}

	if req.UserId != "" && strings.Contains(s.thirdConf.AppIdMap["xiaosi2AppId"], appId) && version >= Version0701 && !strings.Contains(s.thirdConf.AppIdMap["xPad25AppIds"], appId) {
		setNX, err := s.memoryBiz.SetNXXiaosiCommandKey(ctx, req.UserId)
		s.log.WithContext(ctx).Infof("LuiUserLlmTails userId:%+v,appid_%v,version_%v, req.UserId, appId, version, setNX:%+v,err:%+v", setNX, err)
		if err == nil && setNX {
			sessionID := custom_context.GetTraceId(ctx)
			if sessionID == "" {
				sessionID = util.NewUUID()
			}
			if req.ScreenMode == 0 && !strings.Contains(s.thirdConf.AppIdMap["xueLianJiAppIds"], appId) {
				req.ScreenMode = -1
			}
			commands := s.GetCommandsCard(custom_context.GetXAppId(ctx), req.ScreenMode)
			if commands == nil {
				commands = s.GetCommandsCard("default", -1)
			}
			s.log.WithContext(ctx).Infof("commandValue userId:%+v,appid_%v,version_%v,commands:%+v", req.UserId, appId, version, commands)
			tts = "你没有说话哦，我有很多技能，你可以问我下面这些问题哦！"
			res.TtsNorm = tts
			res.TtsShow = tts
			res.Data = []*dto.Pad2SkillData{
				{
					Skill: "no_skill",
					Data: &dto.NLUSkillItem{
						Task:              "",
						TtsShow:           tts,
						TtsNorm:           tts,
						Count:             0,
						IsResultValid:     true,
						Skill:             "no_skill",
						XiaosiCommandList: commands,
						SceneMode:         2,
						SessionId:         sessionID,
					},
				},
			}

			memoryContext := dto.MemoryContext{
				SessionId:         sessionID,
				TimeStamp:         time.Now().UnixMilli(),
				TtsInfo:           tts,
				LlmSkill:          dto.MemoryContextXiaoSiCommandLLmSkill,
				XiaosiCommandList: commands,
			}
			_ = s.memoryBiz.AddMemoryContext(ctx, req.UserId, sessionID, memoryContext)
			reply, _ = util.ReplyAny(res)
			return reply, nil
		}
	}

	s.log.WithContext(ctx).Infof("Tail 空,走之后逻辑")

	if req.UserId == "" {
		s.log.WithContext(ctx).Warnf("RdbGetTailsKey userId is empty,userId%+v", req.UserId)
		return reply, nil
	}

	tailTts := s.thirdConf.GetTailTts()
	tts = fmt.Sprintf("你还没有说话哦，%s", tailTts[rand.Intn(len(tailTts))])
	if strings.TrimSpace(tts) == "" {
		s.log.Infof("RdbGetTailsKey tts is empty,tts:%+v,userId:%+v", tts, req.UserId)
		return reply, nil
	}

	res = dto.QueryPad2Resp{
		TtsShow: tts,
		TtsNorm: tts,
		Data: []*dto.Pad2SkillData{
			{
				Skill: "no_skill",
				Data: &dto.NLUSkillItem{
					Task:          "",
					TtsShow:       tts,
					TtsNorm:       tts,
					Count:         0,
					IsResultValid: true,
				},
			},
		},
		ModelOutputIntent: "异常query",
		RejectRecType:     4,
	}

	reply, _ = util.ReplyAny(res)

	s.log.Infof("RdbGetTailsKey userId:%+v,reply:%+v", req.UserId, reply)
	return reply, nil
}

func (s *LuiService) ContainsPolishTTS(tts string) bool {
	//润色话术
	polishSlice := s.thirdConf.GetPolishTts()
	for _, v := range polishSlice {
		if strings.Contains(tts, v) {
			return true
		}
	}
	return false
}

func (s *LuiService) GetPolishTTS() string {
	//润色话术
	polishSlice := s.thirdConf.GetPolishTts()
	// 生成一个随机索引
	randomIndex := util.RandomInt(len(polishSlice))
	// 使用随机索引从切片中选取一个字符串
	return polishSlice[randomIndex]
}

func (s *LuiService) GetCommandsCard(appId string, screenMode int32) []dto.XiaosiCommands {
	res := make([]dto.XiaosiCommands, 0)
	rand.Seed(time.Now().UnixNano())

	var (
		voiceTips *conf.VoiceInteractionCommands
		ok        bool
	)
	if voiceTips, ok = s.voiceInteractionTips[fmt.Sprintf("%s:%d", appId, screenMode)]; !ok {
		if defaultVoiceTips, defaultOk := s.voiceInteractionTips[fmt.Sprintf("%s:%d", "default", -1)]; !defaultOk {
			return res
		} else {
			voiceTips = defaultVoiceTips
		}
	}

	// 创建一个副本以避免修改原始切片
	voiceTipCommands := make([]*conf.VoiceInteractionCommands_VoiceCommand, len(voiceTips.Commands))
	copy(voiceTipCommands, voiceTips.Commands)
	voiceTips = &conf.VoiceInteractionCommands{
		Commands:  voiceTipCommands,
		MoreImage: voiceTips.MoreImage,
	}

	randomSlice := make([]*conf.VoiceInteractionCommands_VoiceCommand, 0)
	// 构建剩余的元素切片
	remainingSlice := make([]*conf.VoiceInteractionCommands_VoiceCommand, 0)
	if len(voiceTipCommands) > 5 {
		// 随机选择5个元素
		selectedIndices := rand.Perm(len(voiceTipCommands))[:5]
		for _, index := range selectedIndices {
			randomSlice = append(randomSlice, voiceTipCommands[index])
		}

		elementMap := make(map[string]bool)
		for _, v := range randomSlice {
			elementMap[v.Name] = true
		}
		for _, v := range voiceTipCommands {
			if !elementMap[v.Name] {
				remainingSlice = append(remainingSlice, v)
			}
		}

	} else {
		randomSlice = voiceTipCommands
	}

	for _, command := range randomSlice {
		query := command.Query[0]
		res = append(res, dto.XiaosiCommands{
			Title: query,
			Query: query,
			Apps: []dto.CommandApp{{
				Name: command.Name,
				Icon: command.Icon,
			}},
		})
	}
	apps := make([]dto.CommandApp, 0)
	for i, command := range remainingSlice {
		if i > 3 {
			if voiceTips.MoreImage != "" {
				apps = append(apps, dto.CommandApp{
					Name: "",
					Icon: voiceTips.MoreImage,
				})
			}
			break
		}
		apps = append(apps, dto.CommandApp{
			Name: "",
			Icon: command.Icon,
		})
	}

	if len(apps) == 0 {
		apps = append(apps, dto.CommandApp{
			Name: "",
			Icon: voiceTips.MoreImage,
		})
	}

	res = append(res, dto.XiaosiCommands{
		Title: "查看更多技能",
		Query: "你有什么技能",
		Apps:  apps,
	})

	return res
}

// 找学科课程
func (s *LuiService) buildEBookNLU4LuiResp(ctx context.Context, nlu4lui *dto.Nlu4LuiData) (*dto.NluData, error) {
	s.log.WithContext(ctx).Infof("buildSearchBookNLU4LuiResp nlu4lui: %+v", nlu4lui)

	var (
		ttsNorm          string
		luiWidgetContent interface{}
		count            int
	)

	if len(nlu4lui.ResourceList) == 0 {
		if nlu4lui.SearchFlag {
			var tssSlice = []string{
				"抱歉，我没有找到相关资源，已帮你打开全部%s",
				"抱歉，目前未能找到相关内容；已帮你打开全部%s",
				"未找到匹配的内容，不过别担心，已帮你打开全部%s",
			}
			// 生成一个随机索引
			randomIndex := util.RandomInt(len(tssSlice))
			// 使用随机索引从切片中选取一个字符串
			ttsNorm = tssSlice[randomIndex]

		} else {
			ttsNorm = "好的，已为你打开%s"
		}
		var reqItems []dto.EbookResourceReqItem
		resourceType := "reading_library_recommend"
		switch nlu4lui.Skill {
		case common.SkillDocumentary.ToString():
			ttsNorm = fmt.Sprintf(ttsNorm, "纪录片")
			resourceType = "reading_library_video"
		case common.SkillNewspaper.ToString():
			ttsNorm = fmt.Sprintf(ttsNorm, "报刊")
			resourceType = "reading_library_paper"

		default:
			ttsNorm = fmt.Sprintf(ttsNorm, "图书")
			resourceType = "reading_library_book"
		}
		reqItems = append(reqItems, dto.EbookResourceReqItem{
			ResourceType: resourceType,
		})
		req := &dto.EbookResourceReq{
			List: reqItems,
		}

		eBook, err := s.contentService.bizPaper.QueryEBook(ctx, req)
		if err != nil {
			s.log.WithContext(ctx).Errorf("buildSearchBookNLU4LuiResp err: %+v", err)
			return nil, err
		}
		scheme := ""
		if len(eBook.List) > 0 {
			info := eBook.List[0]
			scheme = info.LandingPage
		}
		luiWidgetContent = dto.Nlu4LuiWidgetContent{
			Scheme:        scheme,
			SlotDict:      nlu4lui.AllSlots,
			Toast:         "",
			ToastDuration: 0,
			Title:         ttsNorm,
			Data:          nil,
			Word:          nlu4lui.Word,
			Filters:       nil,
			Confirm:       nil,
		}

	} else {
		nluData, err := s.contentService.buildEBookResourceLuiNluData(ctx, nlu4lui.AllSlots, nlu4lui.ResourceList, nlu4lui.Skill, s.thirdConf.ReadbookDefaultCover)
		if err != nil {
			s.log.WithContext(ctx).Errorf("buildSearchBookNLU4LuiResp err: %+v", err)
			return nil, err
		}

		if len(nluData) == 0 {
			ttsNorm = common.TtsTpl131
		} else if len(nluData) == 1 {
			ttsNorm = fmt.Sprintf("找到以下%s资源", nlu4lui.AllSlots.CollectionName+nlu4lui.AllSlots.BookName+nlu4lui.AllSlots.Author+nlu4lui.AllSlots.ThemeLabel)
		} else {
			ttsNorm = common.TtsTpl421
		}

		count = len(nluData)
		luiWidgetContent = dto.Nlu4LuiWidgetContent{
			SlotDict:      nlu4lui.AllSlots,
			Toast:         "",
			ToastDuration: 0,
			Title:         ttsNorm,
			Data:          nluData,
			Word:          nlu4lui.Word,
			Confirm:       nil,
		}
	}

	luiWidget := dto.Nlu4LuiWidget{
		ModuleName:       nlu4lui.ModuleName,
		IsValidWidget:    nlu4lui.IsValidWidget,
		ResultConfidence: nlu4lui.ResultConfidence,
		NeedConfirm:      nlu4lui.NeedConfirm,
		Content:          luiWidgetContent,
	}
	luiWidgetBt, _ := json.Marshal(luiWidget)

	return &dto.NluData{
		UserID:    "",
		RequestID: nlu4lui.RequestID,
		Skill:     nlu4lui.Skill,
		TtsNorm:   ttsNorm,
		Widget:    string(luiWidgetBt),
		Count:     count,
	}, nil
}
