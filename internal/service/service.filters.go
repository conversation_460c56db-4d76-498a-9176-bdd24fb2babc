package service

import (
	"context"
	v1 "lui-api/api/lui/v1"
	"lui-api/internal/common"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	pkgSync "lui-api/internal/pkg/sync"
	"lui-api/pkg/util"
	"strings"

	"google.golang.org/protobuf/types/known/structpb"
)

func (s *LuiService) HandleFilters(ctx context.Context, req *v1.LuiNluRequest, nlu4lui *dto.NLUQueryPad2Resp) (*structpb.Struct, error) {
	// 对于非特定版本，使用兜底处理
	if !strings.Contains(s.thirdConf.AppIdMap["filtersAppIds"], custom_context.GetXAppId(ctx)) {
		filtersResp, _ := s.FiltersUnderNluResp(ctx, req, nlu4lui)

		pkgSync.Go(ctx, s.log, func() {
			_ = s.memoryBiz.AddXPadMemoryContext(context.TODO(), dto.MemoryContextReq{
				UserTalID: req.TalId,
				SessionId: req.RequestId,
				AsrInfo:   req.AsrInfo,
			}, filtersResp, nil)
		})
		s.log.WithContext(ctx).Infof("FiltersUnderNluResp: %v", util.Marshal(filtersResp))
		return filtersResp, nil
	}

	// 构建小思滤镜的NLU响应
	filtersResp, _ := s.FiltersNluResp(ctx, req, nlu4lui)
	if filtersResp == nil {
		return nil, nil
	}

	pkgSync.Go(ctx, s.log, func() {
		_ = s.memoryBiz.AddXPadMemoryContext(context.TODO(), dto.MemoryContextReq{
			UserTalID: req.TalId,
			SessionId: req.RequestId,
			AsrInfo:   req.AsrInfo,
		}, filtersResp, nlu4lui)
	})
	s.log.WithContext(ctx).Infof("FiltersNluResp: %v", util.Marshal(filtersResp))
	return filtersResp, nil
}

func (s *LuiService) FiltersNluResp(ctx context.Context, req *v1.LuiNluRequest, nlu4lui *dto.NLUQueryPad2Resp) (*structpb.Struct, error) {
	aiAgent := common.AiAgentFilters
	voiceId, _ := s.thirdConf.Filters.FiltersNameMapVideoid[nlu4lui.FiltersInfo.FiltersName]

	res := &dto.QueryPad2Resp{
		TtsShow: "",
		TtsNorm: "",
		TtsParam: &dto.TtsParam{
			VoiceId: voiceId,
		},
		Data: []*dto.Pad2SkillData{
			{
				Skill: common.SkillFilters.ToString(),
				Data: &dto.NLUSkillItem{
					Skill:          common.SkillFilters.ToString(),
					Task:           "",
					TtsShow:        "",
					TtsNorm:        "",
					Count:          0,
					AsrInfo:        req.AsrInfo,
					SessionId:      req.RequestId,
					SentenceId:     req.SentenceId,
					SceneMode:      dto.SceneModeFullView,
					AiAgent:        aiAgent,
					IsAccessingLLM: true,
					IsResultValid:  true,
				},
			},
		},
		ModelOutputIntent: nlu4lui.ModelOutputIntent,
	}
	resp, _ := util.ReplyAny(res)

	return resp, nil
}

func (s *LuiService) FiltersUnderNluResp(ctx context.Context, req *v1.LuiNluRequest, nlu4lui *dto.NLUQueryPad2Resp) (*structpb.Struct, error) {
	// 从配置文件获取兜底tts
	var tts string
	ttsXiaozha := s.thirdConf.Filters.FiltersUnderTtsXiaozha
	ttsXiaoyi := s.thirdConf.Filters.FiltersUnderTtsXiaoyi

	switch nlu4lui.FiltersInfo.FiltersName {
	case "哪吒":
		tts = ttsXiaozha[util.RandomInt(len(ttsXiaozha))]
	case "太乙真人":
		tts = ttsXiaoyi[util.RandomInt(len(ttsXiaoyi))]
	default:
		s.log.WithContext(ctx).Warnf("unknown filters name: %s", nlu4lui.FiltersInfo.FiltersName)
	}

	res := &dto.QueryPad2Resp{
		TtsShow: tts,
		TtsNorm: tts,
		Data: []*dto.Pad2SkillData{
			{
				Skill: "no_skill",
				Data: &dto.NLUSkillItem{
					Task:          "no_task",
					TtsShow:       tts,
					TtsNorm:       tts,
					Count:         0,
					AsrInfo:       req.AsrInfo,
					SessionId:     req.RequestId,
					SentenceId:    req.SentenceId,
					IsResultValid: true,
				},
			},
		},
		ModelOutputIntent: "闲聊",
	}
	res.BizType = req.BizType
	resp, _ := util.ReplyAny(res)

	return resp, nil
}
