package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"lui-api/internal/pkg/custom_context"
	"math/rand"
	"net/http"
	"strings"
	"time"

	"github.com/spf13/cast"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"

	v1 "lui-api/api/lui/v1"
	"lui-api/internal/biz"
	"lui-api/internal/common"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/sse"
	"lui-api/internal/pkg/sync"
	"lui-api/internal/server/middleware"
)

// 旧版闲聊/百科SSE输出
func (s *LuiService) SseLuiFaq(w http.ResponseWriter, r *http.Request) {

	// 设置响应头部
	w.Header().Set("Content-Type", "text/event-stream; charset=utf-8")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	w.<PERSON><PERSON>().Set("Access-Control-Allow-Origin", "*")
	// 向客户端推送事件流数据
	flusher, ok := w.(http.Flusher)
	if !ok {
		// 如果响应写入器不支持刷新，则直接返回
		http.Error(w, "writer flusher is error", http.StatusInternalServerError)
		return
	}

	ctx, cancel := context.WithCancel(r.Context())
	defer cancel()
	go func() {
		<-r.Context().Done()
		cancel()
		s.log.WithContext(ctx).Infof("SseLuiFaq_context_done")
	}()

	// 获取请求体信息
	body, err := io.ReadAll(r.Body)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Body-err %+v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	var reqParam dto.SseLuiReq
	err = json.Unmarshal(body, &reqParam)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Unmarshal-err %+v, body %+v", err, string(body))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	formatSessionId := FormatSessionId(reqParam.SessionId)
	tracer := otel.Tracer(formatSessionId)
	_, span := tracer.Start(ctx, "SseLuiFaq", trace.WithSpanKind(trace.SpanKindServer))
	span.SetAttributes(attribute.String("trace_id", formatSessionId))
	defer span.End()

	// 获取所有的请求头部信息
	headerMap := s.FetchHeaderMap(r, reqParam.SessionId)
	headerMap["Traceparent"] = fmt.Sprintf("00-%s-%s-01", formatSessionId, span.SpanContext().SpanID().String())
	s.log.WithContext(ctx).Infof("SseLuiFaq_headerMapInfo %+v", headerMap)

	apiConf := s.Api.Mapping["dialogue_llm"]
	if apiConf == nil {
		s.log.WithContext(ctx).Errorf("Mapping-Empty %+v", headerMap)
		http.Error(w, "config is not found", http.StatusBadRequest)
		return
	}

	ragArg := s.FetchXiaosiRag(ctx, &reqParam, reqParam.Intent, false)

	tmpId := s.thirdConf.LlmPrompt.BaikeTmpId
	var enableSearch = true
	isChatSkill := 0
	if reqParam.Intent == common.LlmChatIntent {
		isChatSkill = 1
		enableSearch = false
		tmpId = s.thirdConf.LlmPrompt.ChatTmpId
	}

	uid := s.FetchUserTalId(ctx, reqParam.Uid, headerMap)
	llmHistoryMessage := s.FetchHistory(ctx, reqParam.SessionId, uid)
	check, info, err := s.legalBiz.LegalCheckInfo(ctx, reqParam.Uid, reqParam.Message, "lui_output")
	if err != nil {
		s.log.WithContext(ctx).Errorf("LegalCheckInfo-err %+v, check %+v,info:%+v", err, check, info)
		return
	}
	if check == "" && info.Data.RiskLabel1 == common.LegalCheckRiskPolitics {
		s.log.WithContext(ctx).Infof("LegalCheckInfo politics history nil,info%+v", info)
		llmHistoryMessage = nil
	}
	gptReqParam := dto.LlmServiceCommonChatReq{
		Biz:          1,
		TmpId:        tmpId,
		Args:         []string{ragArg, "", reqParam.Message},
		EnableSearch: enableSearch,
		Message:      reqParam.Message,
		SessionId:    reqParam.SessionId,
		History:      llmHistoryMessage,
		MsgNoCheck:   true, // 入参在nlu接口已经走过风控，调模型不需重复
	}
	body, err = json.Marshal(gptReqParam)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Marshal-err %+v, body %+v", err, string(body))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	c := sse.NewClient(apiConf.Url, time.Second*time.Duration(apiConf.Timeout))
	eventChan, err := c.Send(ctx, sse.PostMethod, headerMap, body)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Send-err %+v, body %+v", err, string(body))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	if isChatSkill == 1 {
		eventChan, err = s.FetchSseByFilterPunctuation(ctx, eventChan)
		if err != nil {
			s.log.WithContext(ctx).Errorf("FetchSseByFilterPunctuation-err %+v", err)
			http.Error(w, err.Error(), http.StatusBadRequest)
			return
		}
	}

	var (
		allRes        string
		illegalType   string
		llmModel      string
		sseRes        dto.SseRes
		firstResponse = true
	)
	for data := range eventChan {
		if !strings.HasPrefix(data.Data, "data:") {
			continue
		}
		if reqParam.Intent == common.LlmChatIntent {
			substring := strings.TrimPrefix(data.Data, "data:")
			err = json.Unmarshal([]byte(substring), &sseRes)
			if err == nil {
				sseRes.IsStart = false
				if firstResponse {
					firstResponse = false
					sseRes.IsStart = true
				}
				sseRes.TraceId = reqParam.SessionId
				sseResByte, _ := json.Marshal(sseRes)
				data.Data = "data:" + string(sseResByte)
			}
		}

		substring := strings.TrimPrefix(data.Data, "data:")
		err = json.Unmarshal([]byte(substring), &sseRes)
		if err == nil {
			llmModel = sseRes.Model
			if sseRes.ErrorCode == 0 && sseRes.Result != "" {
				allRes += sseRes.Result
			}

			// 风控拦截记录
			if sseRes.ErrorCode == 120014 {
				illegalType = sseRes.ErrorMsg
			}

			//闲聊返回兜底tts
			if reqParam.Intent == common.LlmChatIntent && sseRes.ErrorCode != 0 {
				queryTts := s.GetLegalCheckTts(reqParam.BizType)

				randTts := queryTts[rand.Intn(len(queryTts))]

				allRes = randTts

				sseRes.Result = randTts
				sseResByte, _ := json.Marshal(sseRes)
				data.Data = "data:" + string(sseResByte)
			}

			//baike兜底
			if reqParam.Intent != common.LlmChatIntent && sseRes.ErrorCode != 0 {
				defaultTts := s.GetBaiKeDefaultTts(reqParam.BizType)

				randomIndex := rand.Intn(len(defaultTts))
				randTts := defaultTts[randomIndex]

				allRes += randTts

				sseRes.Result = randTts
				sseResByte, _ := json.Marshal(sseRes)
				data.Data = "data:" + string(sseResByte)
			}
		}

		// 向客户端发送事件流数据
		s.log.WithContext(ctx).Infof("SseLuiFaq_data %+v", data.Data)
		_, _ = fmt.Fprint(w, data.Data+"\n\n")
		flusher.Flush()
	}

	_ = s.traceBiz.QueryTraceLLMRes(r, &dto.LlmTracePayload{
		SessionId:        reqParam.SessionId,
		Result:           allRes,
		ReasoningContent: "",
		IllegalType:      illegalType,
		LLMModel:         llmModel,
		Emotion:          "",
		TmpId:            tmpId,
	})

	// 上下文存储大模型返回结果
	if len(allRes) > 0 {
		uData := dto.UpdateMemoryContextLlmResponse{
			LlmModel: llmModel,
			Response: allRes,
		}
		_ = s.memoryBiz.UpdateMemoryContextLlmResponse(reqParam.SessionId, uData)
	}

	return
}

// 全屏页小思对话SSE输出
func (s *LuiService) DialogueAgentSse(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()

	// 设置响应头部
	w.Header().Set("Content-Type", "text/event-stream; charset=utf-8")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	// 向客户端推送事件流数据
	flusher, ok := w.(http.Flusher)
	if !ok {
		// 如果响应写入器不支持刷新，则直接返回
		http.Error(w, "writer flusher is error", http.StatusInternalServerError)
		return
	}

	ctx, cancel := context.WithCancel(r.Context())
	defer cancel()
	go func() {
		<-r.Context().Done()
		cancel()
		s.log.WithContext(ctx).Infof("DialogueAgentSse_context_done")
	}()

	// 获取请求体信息
	body, err := io.ReadAll(r.Body)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Body-err %+v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	var reqParam dto.SseLuiReq
	err = json.Unmarshal(body, &reqParam)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Unmarshal-err %+v, body %+v", err, string(body))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	formatSessionId := FormatSessionId(reqParam.SessionId)
	tracer := otel.Tracer(formatSessionId)
	_, span := tracer.Start(ctx, "DialogueAgentSse", trace.WithSpanKind(trace.SpanKindServer))
	span.SetAttributes(attribute.String("trace_id", formatSessionId))
	defer span.End()

	// 获取所有的请求头部信息
	traceparent := fmt.Sprintf("00-%s-%s-01", formatSessionId, span.SpanContext().SpanID().String())
	headerMap := s.FetchHeaderMap(r, reqParam.SessionId)
	headerMap["Traceparent"] = traceparent
	s.log.WithContext(ctx).Infof("DialogueAgentSse_headerMapInfo %+v, reqParam %+v", headerMap, reqParam)

	apiConf := s.Api.Mapping["dialogue_llm"]
	if apiConf == nil {
		s.log.WithContext(ctx).Errorf("Mapping-Empty %+v", headerMap)
		http.Error(w, "config is not found", http.StatusBadRequest)
		return
	}

	var (
		enableSearch      = true
		randTts           string
		defaultTts        []string
		args              []string
		rag               string
		memory            string
		userProfile       string
		profile           dto.DialogueProfile
		llmHistoryMessage []dto.LlmHistoryMessage
	)

	sync.GoGroupWait(ctx, s.logger, func() string {
		userProfile = s.FetchUserProfile(ctx, reqParam.Uid)
		return "FetchUserProfile"
	}, func() string {
		rag = s.FetchXiaosiRag(ctx, &reqParam, reqParam.Intent, true)
		return "FetchXiaosiRag"
	}, func() string {
		memory = s.FetchMemory(ctx, &reqParam)
		return "FetchMemory"
	}, func() string {
		profile = s.FetchProfile(ctx, reqParam.Uid)
		return "FetchProfile"
	}, func() string {
		llmHistoryMessage = s.FetchFullViewLLmHistory(ctx, reqParam.SessionId, reqParam.Uid, []string{"baike", "chat"})
		return "FetchHistory"
	})

	s.log.WithContext(ctx).Infof("DialogueAgentSse_sync_time %+v", time.Since(startTime).Milliseconds())

	llmSkill := "baike"
	tmpId := s.thirdConf.LlmPrompt.Xs2BaikePlusTmpId
	defaultTts = s.GetBaiKeDefaultTts(reqParam.BizType)
	args = []string{rag, userProfile, profile.Interests}
	if reqParam.Intent == common.LlmChatIntent {
		llmSkill = "chat"
		enableSearch = false
		tmpId = s.thirdConf.LlmPrompt.Xs2ChatPlusTmpId

		defaultTts = s.GetLegalCheckTts(reqParam.BizType)
		args = []string{rag, profile.Recent, memory, profile.Task, userProfile, profile.Interests}

		// 闲聊对话感知
		sync.Go(ctx, s.log, func() {
			_ = s.memoryBiz.FeedChatQueryContext(ctx, reqParam.Uid, reqParam.SessionId, reqParam.Message)
		})
	}
	check, info, err := s.legalBiz.LegalCheckInfo(ctx, reqParam.Uid, reqParam.Message, "lui_output")
	if err != nil {
		s.log.WithContext(ctx).Errorf("LegalCheckInfo-err %+v, check %+v,info:%+v", err, check, info)
		return
	}
	if check == "" && info.Data.RiskLabel1 == common.LegalCheckRiskPolitics {
		s.log.WithContext(ctx).Infof("LegalCheckInfo politics history nil,info%+v", info)
		llmHistoryMessage = nil
	}
	gptReqParam := dto.LlmServiceCommonChatReq{
		Biz:           1,
		TmpId:         tmpId,
		Args:          args,
		EnableSearch:  enableSearch,
		Message:       reqParam.Message,
		SessionId:     reqParam.SessionId,
		History:       llmHistoryMessage,
		MsgNoCheck:    true, // 入参在nlu接口已经走过风控，调模型不需重复
		OutPutNoCheck: true, // 输出不走风控
		Sn:            custom_context.GetXDeviceId(ctx),
		TalId:         reqParam.Uid,
	}
	body, err = json.Marshal(gptReqParam)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Marshal-err %+v, body %+v", err, string(body))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	targetUrl := apiConf.Url
	c := sse.NewClient(targetUrl, time.Second*time.Duration(apiConf.Timeout))
	eventChan, err := c.Send(ctx, sse.PostMethod, headerMap, body)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Send-err %+v, body %+v", err, string(body))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	eventChan, err = s.FetchSseByFilterPunctuation(ctx, eventChan)
	if err != nil {
		s.log.WithContext(ctx).Errorf("FetchSseByFilterPunctuation-err %+v", err)
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	var (
		sugRes      string
		sugArr      []string
		sugStart    bool
		emotion     string
		dbEmotion   string
		allRes      string
		allLLMRes   string
		illegalType string
		llmModel    string
		isReply     bool
		hasReply    bool
		sseRes      dto.DialogueSseRes
	)

	for data := range eventChan {
		if !strings.HasPrefix(data.Data, "data:") {
			continue
		}

		substring := strings.TrimPrefix(data.Data, "data:")
		err = json.Unmarshal([]byte(substring), &sseRes)
		if err != nil {
			continue
		}

		allLLMRes += sseRes.Result
		llmModel = sseRes.Model
		sseRes.Sug = []string{}
		sseRes.IsReplyStart = false
		sseRes.IsReplyEnd = false
		sseRes.Source = FormatLlmModel(llmModel)
		if sseRes.ErrorCode == 120014 { // 风控拦截记录
			illegalType = sseRes.ErrorMsg
		}

		// 回复分隔符列表
		separators := []string{"回复内容:", "回复内容：", "回复内容", "回复呢:", "回复呢：", "回复呢", "回复: ", "回复:", "回复", "回复."}
		var emotionData []string
		for _, sep := range separators {
			emotionData = strings.Split(sseRes.Result, sep)
			if len(emotionData) > 1 {
				break
			}
		}

		if len(emotionData) > 1 {
			emotion = strings.TrimSpace(emotionData[0])

			sseRes.Result = strings.TrimSpace(emotionData[1])

			isReply = true
			hasReply = true
			sseRes.IsReplyStart = true

			s.log.WithContext(ctx).Infof("DialogueAgentSse_ReplyStart_Latency %+v", time.Since(startTime).Milliseconds())
		}

		// 表情处理
		prefixes := []string{"表达", "express", ":", "："}
		trimmedEmotion := emotion
		for _, prefix := range prefixes {
			trimmedEmotion = strings.TrimPrefix(trimmedEmotion, prefix)
		}
		sseRes.Emotion = strings.TrimSpace(trimmedEmotion)
		dbEmotion = sseRes.Emotion

		// 拼接sug
		if sugStart {
			sugRes += sseRes.Result
		}

		// 输出回复，不是回复的话清空
		if !isReply {
			sseRes.Result = ""
		}

		// sug分隔符列表
		sugSeparators := []string{"sug:", "sug："}
		var sugData []string
		for _, sugSeparator := range sugSeparators {
			sugData = strings.Split(sseRes.Result, sugSeparator)
			if len(sugData) > 1 {
				break
			}
		}
		if len(sugData) > 1 {
			isReply = false
			sseRes.Result = strings.TrimSpace(sugData[0])
			sseRes.IsReplyEnd = true
			allRes += sseRes.Result

			// sug
			sugStart = true
			sugRes = sugData[1]
		}

		// 拼接allRes
		if sseRes.ErrorCode == 0 && sseRes.Result != "" && isReply {
			allRes += sseRes.Result
		}

		// sugStart为true时，不输出
		if sugStart && !sseRes.IsEnd && !sseRes.IsReplyEnd {
			continue
		}

		if sseRes.IsEnd {
			sseRes.Sug = s.DealDialogueSugStr(reqParam.SessionId, sugRes)
			sugArr = sseRes.Sug
			if info.Data.RiskLabel1 == common.LegalCheckRiskPolitics && check == "" {
				s.log.WithContext(ctx).Infof("LegalCheckInfo politics sug nil,info%+v", info)
				sseRes.Sug = nil
				sugArr = sseRes.Sug
			}
		}

		// 会话结束&&没有sug
		if sseRes.IsEnd && !sugStart {
			sseRes.IsReplyEnd = true
		}

		// 风控
		if sseRes.Result != "" {
			illegalType, _ = s.legalBiz.LegalCheck(ctx, reqParam.Uid, allRes, "lui_output")
			if illegalType != "" {
				sseRes.ErrorCode = 120014
			}
		}

		if len(sseRes.Sug) > 0 {
			sugStr := strings.Join(sseRes.Sug, " ")
			sugIllegalType, _ := s.legalBiz.LegalCheck(ctx, reqParam.Uid, sugStr, "lui_output")
			if sugIllegalType != "" {
				// 如果不通过风控检查，清空sug
				sseRes.Sug = []string{}
			}
		}

		// 兜底回复
		if sseRes.ErrorCode != 0 {
			randTts = defaultTts[rand.Intn(len(defaultTts))]

			allRes = randTts
			sseRes.Result = randTts
			if !hasReply {
				sseRes.IsReplyStart = true
			}
			sseRes.IsReplyEnd = true
			allLLMRes = "" // 清空结果
		}

		// 兜底回复
		if sseRes.ErrorCode == 0 && allRes == "" && sseRes.IsEnd && len(sseRes.Sug) == 0 && allLLMRes != "" {
			sugSplits := []string{"sug:", "sug："}
			for _, sugSplit := range sugSplits {
				sugData = strings.Split(allLLMRes, sugSplit)
				if len(sugData) > 1 {
					break
				}
			}
			if len(sugData) > 1 {
				allLLMRes = strings.TrimSpace(sugData[0])

				sseRes.Sug = s.DealDialogueSugStr(reqParam.SessionId, sugData[1])
				sugArr = sseRes.Sug
				if info.Data.RiskLabel1 == common.LegalCheckRiskPolitics && check == "" {
					s.log.WithContext(ctx).Infof("LegalCheckInfo politics sug nil,info%+v", info)
					sseRes.Sug = nil
					sugArr = sseRes.Sug
				}
			}

			emotionPrefixes := []string{"表达", "express", ":", "："}
			for _, emotionPrefix := range emotionPrefixes {
				allLLMRes = strings.TrimPrefix(allLLMRes, emotionPrefix)
			}
			allLLMRes = strings.TrimSpace(allLLMRes)
			wordsToReplace := []string{"happy", "calm", "cheerful", "disgruntled", "sad", "chat", "angry", "excited", "playful", "understanding", "sympathetic", "urgent", "serious", "concerned", "supportive", "confident", "encouraging", "reassuring"}
			for _, word := range wordsToReplace {
				allLLMRes = strings.TrimPrefix(allLLMRes, word)
			}

			allLLMRes = strings.TrimSpace(allLLMRes)

			allRes = allLLMRes
			sseRes.Result = allLLMRes
			sseRes.IsReplyStart = true
			sseRes.IsReplyEnd = true
			allLLMRes = ""
		}

		sseRes.Result = strings.TrimLeft(sseRes.Result, " ")
		sseResByte, _ := json.Marshal(sseRes)
		data.Data = "data:" + string(sseResByte)

		// 向客户端发送事件流数据
		_, _ = fmt.Fprint(w, data.Data+"\n\n")
		flusher.Flush()
	}

	if len(allLLMRes) == 0 {
		s.log.WithContext(ctx).Infof("DialogueAgentSse_allLLMRes_is_empty, allRes: %s sessionId: %s tmp_id: %s", allRes, reqParam.SessionId, tmpId)
	}

	s.log.WithContext(ctx).Infof("DialogueAgentSse_allRes: %s llmAllRes: %s sessionId: %s tmp_id: %s", allRes, allLLMRes, reqParam.SessionId, tmpId)

	_ = s.traceBiz.QueryTraceLLMRes(r, &dto.LlmTracePayload{
		SessionId:        reqParam.SessionId,
		Result:           allRes,
		ReasoningContent: "",
		IllegalType:      illegalType,
		LLMModel:         llmModel,
		Emotion:          dbEmotion,
		TmpId:            tmpId,
	})

	if len(sugArr) > 0 {
		_ = s.memoryBiz.AddSugMemory(ctx, reqParam.Uid, sugArr)
	}

	// 上下文存储大模型返回结果
	if len(allRes) > 0 || len(allLLMRes) > 0 {
		uData := dto.UpdateMemoryContextLlmResponse{
			LlmSkill:    llmSkill,
			LlmModel:    llmModel,
			Response:    allRes,
			LlmResponse: allLLMRes,
		}
		_ = s.memoryBiz.UpdateMemoryContextLlmResponse(reqParam.SessionId, uData)
	}

	return
}

// DeepSeekSse 小思深度思考SSE输出
func (s *LuiService) DeepSeekSse(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()

	// 设置响应头部
	w.Header().Set("Content-Type", "text/event-stream; charset=utf-8")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	// 向客户端推送事件流数据
	flusher, ok := w.(http.Flusher)
	if !ok {
		// 如果响应写入器不支持刷新，则直接返回
		http.Error(w, "writer flusher is error", http.StatusInternalServerError)
		return
	}

	ctx, cancel := context.WithCancel(r.Context())
	defer cancel()
	go func() {
		<-r.Context().Done()
		cancel()
		s.log.WithContext(ctx).Infof("DeepSeekSse_context_done")
	}()

	// 获取请求体信息
	body, err := io.ReadAll(r.Body)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Body-err %+v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var reqParam dto.SseLuiReq
	err = json.Unmarshal(body, &reqParam)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Unmarshal-err %+v, body %+v", err, string(body))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	formatSessionId := FormatSessionId(reqParam.SessionId)
	tracer := otel.Tracer(formatSessionId)
	_, span := tracer.Start(ctx, "DeepSeekAgentSse", trace.WithSpanKind(trace.SpanKindServer))
	span.SetAttributes(attribute.String("trace_id", formatSessionId))
	defer span.End()

	// 获取所有的请求头部信息
	traceparent := fmt.Sprintf("00-%s-%s-01", formatSessionId, span.SpanContext().SpanID().String())
	headerMap := s.FetchHeaderMap(r, reqParam.SessionId)
	headerMap["Traceparent"] = traceparent
	s.log.WithContext(ctx).Infof("DeepSeekSse_headerMapInfo %+v, reqParam %+v", headerMap, reqParam)

	apiConf := s.Api.Mapping["dialogue_llm"]
	if apiConf == nil {
		s.log.WithContext(ctx).Errorf("Mapping-Empty %+v", headerMap)
		http.Error(w, "config is not found", http.StatusBadRequest)
		return
	}

	var (
		randTts           string
		defaultTts        []string
		args              []string
		rag               string
		userProfile       string
		profile           dto.DialogueProfile
		llmHistoryMessage []dto.LlmHistoryMessage
	)

	sync.GoGroupWait(ctx, s.logger, func() string {
		userProfile = s.FetchUserProfile(ctx, reqParam.Uid)
		return "FetchUserProfile"
	}, func() string {
		rag = s.FetchXiaosiRag(ctx, &reqParam, common.SkillDeepseek.ToString(), false)
		return "FetchXiaosiRag"
	}, func() string {
		profile = s.FetchProfile(ctx, reqParam.Uid)
		return "FetchProfile"
	}, func() string {
		llmHistoryMessage = s.FetchAgentHistory(ctx, reqParam.SessionId, reqParam.Uid)
		return "FetchHistory"
	})

	s.log.WithContext(ctx).Infof("DeepSeekSse_sync_time %+v", time.Since(startTime).Milliseconds())

	tmpId := s.thirdConf.LlmPrompt.XsDeepseekTmpId
	defaultTts = s.GetBaiKeDefaultTts(reqParam.BizType)
	args = []string{rag, userProfile, profile.Interests}

	gptReqParam := dto.LlmServiceCommonChatReq{
		Biz:           1,
		TmpId:         tmpId,
		Args:          args,
		EnableSearch:  true,
		Message:       reqParam.Message,
		SessionId:     reqParam.SessionId,
		History:       llmHistoryMessage,
		OutPutNoCheck: true,
		Sn:            custom_context.GetXDeviceId(ctx),
		TalId:         reqParam.Uid,
	}
	body, err = json.Marshal(gptReqParam)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Marshal-err %+v, body %+v", err, string(body))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	targetUrl := apiConf.Url
	c := sse.NewClient(targetUrl, time.Second*time.Duration(apiConf.Timeout))
	eventChan, err := c.Send(ctx, sse.PostMethod, headerMap, body)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Send-err %+v, body %+v", err, string(body))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	eventChan, err = s.FetchSseByFilterPunctuation(ctx, eventChan)
	if err != nil {
		s.log.WithContext(ctx).Errorf("FetchSseByFilterPunctuation-err %+v", err)
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	var (
		sugStart         bool
		firstResponse    = true
		illegalType      string
		llmModel         string
		sseRes           dto.DialogueSseRes
		orgBuilder       strings.Builder
		procBuilder      strings.Builder
		sugBuilder       strings.Builder
		reasoningBuilder strings.Builder
	)

	for data := range eventChan {
		if !strings.HasPrefix(data.Data, "data:") {
			continue
		}

		substring := strings.TrimPrefix(data.Data, "data:")
		err = json.Unmarshal([]byte(substring), &sseRes)
		if err != nil {
			continue
		}

		llmModel = sseRes.Model
		sseRes.Sug = []string{}
		sseRes.IsReplyStart = false
		sseRes.IsReplyEnd = false
		if firstResponse {
			firstResponse = false
			sseRes.IsReplyStart = true
		}

		if sseRes.IsDeepSeek {
			reasoningBuilder.WriteString(sseRes.Result)
		}

		orgBuilder.WriteString(sseRes.Result)
		if sseRes.ErrorCode == 120014 {
			illegalType = sseRes.ErrorMsg
		}

		// 回复分隔符列表
		separators := []string{"回复内容:", "回复内容：", "内容:", "内容："}
		for _, sep := range separators {
			if strings.Contains(sseRes.Result, sep) && !sseRes.IsDeepSeek {
				res := strings.Split(sseRes.Result, sep)[1]
				sseRes.Result = res
				break
			}
		}

		// 拼接sug
		if sugStart {
			sugBuilder.WriteString(sseRes.Result)
			sseRes.Result = ""
		}

		// Sug内容
		sugSeparators := []string{"sug:", "sug："}
		var sugSlice []string
		for _, sugSep := range sugSeparators {
			if !sseRes.IsDeepSeek {
				sugSlice = strings.Split(sseRes.Result, sugSep)
				if len(sugSlice) > 1 {
					break
				}
			}
		}
		if len(sugSlice) > 1 {
			sseRes.IsReplyEnd = true
			sseRes.Result = strings.TrimSpace(sugSlice[0])
			procBuilder.WriteString(sseRes.Result)

			// sug
			sugStart = true
			sugBuilder.WriteString(sugSlice[1])
		}

		if sseRes.ErrorCode == 0 && sseRes.Result != "" && !sugStart && !sseRes.IsDeepSeek {
			procBuilder.WriteString(sseRes.Result)
		}

		// sugStart为true时，不输出
		if sugStart && !sseRes.IsEnd && !sseRes.IsReplyEnd {
			continue
		}

		if sseRes.IsEnd {
			sseRes.Sug = s.DealDialogueSugStr(reqParam.SessionId, sugBuilder.String())
		}

		// 会话结束&&没有sug
		if sseRes.IsEnd && !sugStart {
			sseRes.IsReplyEnd = true
		}

		// 风控
		if sseRes.Result != "" {
			if reasoningBuilder.String() != "" {
				illegalType, _ = s.legalBiz.LegalCheck(ctx, reqParam.Uid, reasoningBuilder.String(), "lui_output")
				if illegalType != "" {
					sseRes.ErrorCode = 120014
				}
			}

			if procBuilder.String() != "" {
				illegalType, _ = s.legalBiz.LegalCheck(ctx, reqParam.Uid, procBuilder.String(), "lui_output")
				if illegalType != "" {
					sseRes.ErrorCode = 120014
				}
			}
		}

		if len(sseRes.Sug) > 0 {
			sugStr := strings.Join(sseRes.Sug, " ")
			sugIllegalType, _ := s.legalBiz.LegalCheck(ctx, reqParam.Uid, sugStr, "lui_output")
			if sugIllegalType != "" {
				// 如果不通过风控检查，清空sug
				sseRes.Sug = []string{}
			}
		}

		// 兜底回复
		if sseRes.ErrorCode != 0 {
			randTts = defaultTts[rand.Intn(len(defaultTts))]

			procBuilder.Reset()
			procBuilder.WriteString(randTts)

			reasoningBuilder.Reset()
			//reasoningBuilder.WriteString(randTts)

			sseRes.Result = randTts
			sseRes.IsReplyStart = true
			sseRes.IsReplyEnd = true
			// 清空结果
			orgBuilder.Reset()
		}

		sseRes.Result = strings.TrimLeft(sseRes.Result, " ")
		sseResByte, _ := json.Marshal(sseRes)
		data.Data = "data:" + string(sseResByte)

		// 向客户端发送事件流数据
		_, err := fmt.Fprint(w, data.Data+"\n\n")
		if err != nil {
			s.log.WithContext(ctx).Warnf("DeepSeekSse_Fprint_err: %v", err)
			break
		}

		flusher.Flush()
	}

	s.log.WithContext(ctx).Infof("DeepSeekSse_procBuilder: %s reasoningBuilder: %s sessionId: %s tmp_id: %s", procBuilder.String(), reasoningBuilder.String(), reqParam.SessionId, tmpId)

	_ = s.traceBiz.QueryTraceLLMRes(r, &dto.LlmTracePayload{
		SessionId:        reqParam.SessionId,
		Result:           procBuilder.String(),
		ReasoningContent: reasoningBuilder.String(),
		IllegalType:      illegalType,
		LLMModel:         llmModel,
		Emotion:          "",
		TmpId:            tmpId,
	})

	// 上下文存储大模型返回结果
	if procBuilder.String() != "" || reasoningBuilder.String() != "" {
		uData := dto.UpdateMemoryContextLlmResponse{
			LlmSkill:         common.SkillDeepseek.ToString(),
			LlmModel:         llmModel,
			Response:         procBuilder.String(),
			ReasoningContent: reasoningBuilder.String(),
		}
		_ = s.memoryBiz.UpdateMemoryContextLlmResponse(reqParam.SessionId, uData)
	}

	return
}

func (s *LuiService) DeepSeekExercisesSse(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()

	// 设置响应头部
	w.Header().Set("Content-Type", "text/event-stream; charset=utf-8")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	// 向客户端推送事件流数据
	flusher, ok := w.(http.Flusher)
	if !ok {
		// 如果响应写入器不支持刷新，则直接返回
		http.Error(w, "writer flusher is error", http.StatusInternalServerError)
		return
	}

	ctx, cancel := context.WithCancel(r.Context())
	defer cancel()
	go func() {
		<-r.Context().Done()
		cancel()
		s.log.WithContext(ctx).Infof("DeepSeekExercisesSse_context_done")
	}()

	// 获取请求体信息
	body, err := io.ReadAll(r.Body)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Body-err %+v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var reqParam dto.SseLuiReq
	err = json.Unmarshal(body, &reqParam)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Unmarshal-err %+v, body %+v", err, string(body))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	formatSessionId := FormatSessionId(reqParam.SessionId)
	tracer := otel.Tracer(formatSessionId)
	_, span := tracer.Start(ctx, "DeepSeekAgentSse", trace.WithSpanKind(trace.SpanKindServer))
	span.SetAttributes(attribute.String("trace_id", formatSessionId))
	defer span.End()

	// 获取所有的请求头部信息
	traceparent := fmt.Sprintf("00-%s-%s-01", formatSessionId, span.SpanContext().SpanID().String())
	headerMap := s.FetchHeaderMap(r, reqParam.SessionId)
	headerMap["Traceparent"] = traceparent
	s.log.WithContext(ctx).Infof("DeepSeekSse_headerMapInfo %+v, reqParam %+v", headerMap, reqParam)

	apiConf := s.Api.Mapping["dialogue_llm"]
	if apiConf == nil {
		s.log.WithContext(ctx).Errorf("Mapping-Empty %+v", headerMap)
		http.Error(w, "config is not found", http.StatusBadRequest)
		return
	}

	var (
		randTts           string
		defaultTts        []string
		args              []string
		rag               string
		userProfile       string
		llmHistoryMessage []dto.LlmHistoryMessage
	)

	sync.GoGroupWait(ctx, s.logger, func() string {
		userProfile = s.FetchUserProfile(ctx, reqParam.Uid)
		return "FetchUserProfile"
	}, func() string {
		rag = s.memoryBiz.GetExerciseOcr(ctx, reqParam.Uid)
		return "FetchXiaosiRag"
	}, func() string {
		llmHistoryMessage = s.FetchAgentHistory(ctx, reqParam.SessionId, reqParam.Uid)
		return "FetchHistory"
	})

	s.log.WithContext(ctx).Infof("DeepSeekSse_sync_time %+v", time.Since(startTime).Milliseconds())

	tmpId := s.thirdConf.LlmPrompt.XsDeepseekExercisesTmpId
	defaultTts = s.GetBaiKeDefaultTts(reqParam.BizType)
	args = []string{rag, userProfile}

	gptReqParam := dto.LlmServiceCommonChatReq{
		Biz:          1,
		TmpId:        tmpId,
		Args:         args,
		EnableSearch: true,
		Message:      reqParam.Message,
		SessionId:    reqParam.SessionId,
		History:      llmHistoryMessage,
	}
	body, err = json.Marshal(gptReqParam)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Marshal-err %+v, body %+v", err, string(body))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	targetUrl := apiConf.Url
	c := sse.NewClient(targetUrl, time.Second*time.Duration(apiConf.Timeout))
	eventChan, err := c.Send(ctx, sse.PostMethod, headerMap, body)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Send-err %+v, body %+v", err, string(body))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	eventChan, err = s.FetchSseByFilterPunctuation(ctx, eventChan)
	if err != nil {
		s.log.WithContext(ctx).Errorf("FetchSseByFilterPunctuation-err %+v", err)
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	var (
		sugStart         bool
		firstResponse    = true
		illegalType      string
		llmModel         string
		sseRes           dto.DialogueSseRes
		orgBuilder       strings.Builder
		procBuilder      strings.Builder
		sugBuilder       strings.Builder
		reasoningBuilder strings.Builder
	)

	for data := range eventChan {
		if !strings.HasPrefix(data.Data, "data:") {
			continue
		}

		substring := strings.TrimPrefix(data.Data, "data:")
		err = json.Unmarshal([]byte(substring), &sseRes)
		if err != nil {
			continue
		}

		llmModel = sseRes.Model
		sseRes.Sug = []string{}
		sseRes.IsReplyStart = false
		sseRes.IsReplyEnd = false
		if firstResponse {
			firstResponse = false
			sseRes.IsReplyStart = true
		}

		if sseRes.IsDeepSeek {
			reasoningBuilder.WriteString(sseRes.Result)
		}

		orgBuilder.WriteString(sseRes.Result)
		if sseRes.ErrorCode == 120014 {
			illegalType = sseRes.ErrorMsg
		}

		// 回复分隔符列表
		separators := []string{"回复内容:", "回复内容：", "内容:", "内容："}
		for _, sep := range separators {
			if strings.Contains(sseRes.Result, sep) && !sseRes.IsDeepSeek {
				res := strings.Split(sseRes.Result, sep)[1]
				sseRes.Result = res
				break
			}
		}

		// 拼接sug
		if sugStart {
			sugBuilder.WriteString(sseRes.Result)
			sseRes.Result = ""
		}

		// Sug内容
		sugSeparators := []string{"sug:", "sug："}
		var sugSlice []string
		for _, sugSep := range sugSeparators {
			if !sseRes.IsDeepSeek {
				sugSlice = strings.Split(sseRes.Result, sugSep)
				if len(sugSlice) > 1 {
					break
				}
			}
		}
		if len(sugSlice) > 1 {
			sseRes.IsReplyEnd = true
			sseRes.Result = strings.TrimSpace(sugSlice[0])
			procBuilder.WriteString(sseRes.Result)

			// sug
			sugStart = true
			sugBuilder.WriteString(sugSlice[1])
		}

		if sseRes.ErrorCode == 0 && sseRes.Result != "" && !sugStart && !sseRes.IsDeepSeek {
			procBuilder.WriteString(sseRes.Result)
		}

		// sugStart为true时，不输出
		if sugStart && !sseRes.IsEnd && !sseRes.IsReplyEnd {
			continue
		}

		if sseRes.IsEnd {
			sseRes.Sug = s.DealDialogueSugStr(reqParam.SessionId, sugBuilder.String())
		}

		// 会话结束&&没有sug
		if sseRes.IsEnd && !sugStart {
			sseRes.IsReplyEnd = true
		}

		// 兜底回复
		if sseRes.ErrorCode != 0 {
			randTts = defaultTts[rand.Intn(len(defaultTts))]

			procBuilder.Reset()
			procBuilder.WriteString(randTts)

			reasoningBuilder.Reset()
			//reasoningBuilder.WriteString(randTts)

			sseRes.Result = randTts
			sseRes.IsReplyStart = true
			sseRes.IsReplyEnd = true
			// 清空结果
			orgBuilder.Reset()
		}

		sseRes.Result = strings.TrimLeft(sseRes.Result, " ")
		sseResByte, _ := json.Marshal(sseRes)
		data.Data = "data:" + string(sseResByte)

		// 向客户端发送事件流数据
		_, err := fmt.Fprint(w, data.Data+"\n\n")
		if err != nil {
			s.log.WithContext(ctx).Warnf("DeepSeekSse_Fprint_err: %v", err)
			break
		}

		flusher.Flush()
	}

	s.log.WithContext(ctx).Infof("DeepSeekSse_procBuilder: %s reasoningBuilder: %s sessionId: %s tmp_id: %s", procBuilder.String(), reasoningBuilder.String(), reqParam.SessionId, tmpId)

	_ = s.traceBiz.QueryTraceLLMRes(r, &dto.LlmTracePayload{
		SessionId:        reqParam.SessionId,
		Result:           procBuilder.String(),
		ReasoningContent: reasoningBuilder.String(),
		IllegalType:      illegalType,
		LLMModel:         llmModel,
		Emotion:          "",
		TmpId:            tmpId,
	})

	// 上下文存储大模型返回结果
	if procBuilder.String() != "" || reasoningBuilder.String() != "" {
		uData := dto.UpdateMemoryContextLlmResponse{
			LlmSkill:         common.SkillDeepseek.ToString(),
			LlmModel:         llmModel,
			Response:         procBuilder.String(),
			ReasoningContent: reasoningBuilder.String(),
		}
		_ = s.memoryBiz.UpdateMemoryContextLlmResponse(reqParam.SessionId, uData)
	}

	return
}

// MathExplainAgent 讲题
func (s *LuiService) MathExplainAgent(w http.ResponseWriter, r *http.Request) {

	// 设置响应头部
	w.Header().Set("Content-Type", "text/event-stream; charset=utf-8")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	// 向客户端推送事件流数据
	flusher, ok := w.(http.Flusher)
	if !ok {
		// 如果响应写入器不支持刷新，则直接返回
		http.Error(w, "writer flusher is error", http.StatusInternalServerError)
		return
	}

	// 获取请求体信息
	body, err := io.ReadAll(r.Body)
	if err != nil {
		s.log.WithContext(r.Context()).Errorf("Body-err %+v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var reqParam MathExplainAgentRequest
	err = json.Unmarshal(body, &reqParam)
	if err != nil {
		s.log.WithContext(r.Context()).Errorf("Unmarshal-err %+v, body %+v", err, string(body))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	s.log.WithContext(r.Context()).Infof("MathExplainAgent reqParam %+v", reqParam)

	tracer := otel.Tracer(reqParam.SessionId)
	_, span := tracer.Start(r.Context(), "MathExplainAgent", trace.WithSpanKind(trace.SpanKindServer))
	span.SetAttributes(attribute.String("trace_id", reqParam.SessionId))
	defer span.End()

	var (
		question       string
		questionLatex  string
		paiVideoUrl    string
		paiOutputTitle string

		agentMessage  string
		agentQuestion string
		agentRag      string
	)

	if reqParam.Entrance == 1 || reqParam.Entrance == 3 {
		question = reqParam.Text
		questionLatex = question
		// 拍题和一笔灵犀进来 都是重新开始
		err = s.memoryBiz.SetMathAgentFlag(r.Context(), reqParam.Uid)
		if err != nil {
			s.log.WithContext(r.Context()).Errorf("SetMathAgentFlag-err %+v, tal_id %+v", err, reqParam.Uid)
		}
	} else if reqParam.Entrance == 2 {
		question = reqParam.Query
		questionLatex = question
		agentQuestion, agentRag, err = s.memoryBiz.GetMathAgentQuestion(reqParam.Uid)
		if err != nil {
			s.log.WithContext(r.Context()).Errorf("Unmarshal-err %+v, body %+v", err, string(body))
		}
	}

	if len(question) == 0 {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	if len(agentQuestion) == 0 {

		if reqParam.Entrance == 2 {

			var memoryCtx *dto.MemoryContext
			mathFlag := s.memoryBiz.GetMathAgentFlag(r.Context(), reqParam.Uid)
			if mathFlag == "" {
				_ = s.memoryBiz.SetMathAgentFlag(r.Context(), reqParam.Uid)
			}

			memoryCtx, err = s.memoryBiz.GetMemoryContextBySessionID(r.Context(), reqParam.SessionId)
			if err != nil {
				s.log.WithContext(r.Context()).Warnf("GetMemoryContextBySessionID-err %+v, tal_id %+v", err, reqParam.SessionId)
			}
			s.log.WithContext(r.Context()).Infof("mathFlag: %s; uid: %s; memoryCtx: %+v", mathFlag, reqParam.Uid, memoryCtx)
			if memoryCtx != nil && memoryCtx.FuncList != nil {

				for _, fun := range memoryCtx.FuncList {
					btFunParameters, _ := json.Marshal(fun.FuncParameters)
					s.log.WithContext(r.Context()).Infof("memoryCtx.FuncList: %+v; sessionId: %s", string(btFunParameters), reqParam.SessionId)
					var fp struct {
						Context string `json:"context"`
					}
					_ = json.Unmarshal(btFunParameters, &fp)
					if len(fp.Context) > 0 {
						questionLatex = fp.Context
					}
					break
				}
			}
		}

		// 小派讲题
		canSolve, ragInput, videoUrl, outputTitle := s.ragBiz.FetchAutoMathRag(r.Context(), dto.AutoMathCheckRequest{
			Sum:        questionLatex,
			ComputeDot: false,
			RequestID:  reqParam.SessionId,
		})
		s.log.WithContext(r.Context()).Infof("FetchAutoMathRag resp: %v; %s; %s; %s; sessionId: %s", canSolve, ragInput, videoUrl, outputTitle, reqParam.SessionId)

		// 小派讲题无结果，则调用math-gpt
		if !canSolve {
			//gptCh := make(chan string)
			//
			//sync.Go(r.Context(), s.log, func() {
			//	mathGPTMsgChan := s.requestMathGPT(r.Context(), reqParam.SessionId, question, gptCh)
			//	if mathGPTMsgChan == nil {
			//		http.Error(w, err.Error(), http.StatusInternalServerError)
			//		return
			//	}
			//})
			//
			//ragInput = <-gptCh
			ragInput = ""
		} else {
			paiVideoUrl = videoUrl
			paiOutputTitle = outputTitle
		}

		//// math-gpt判断不是题
		//if len(ragInput) == 0 {
		//	s.log.WithContext(r.Context()).Infof("rag input empty")
		//	sseExitRes := dto.CommonSseOutputRes{
		//		ErrorCode:    -1,
		//		ErrorMsg:     "MathGpt不是题目",
		//		TraceId:      "",
		//		Created:      time.Now().Second(),
		//		SentenceId:   0,
		//		IsEnd:        true,
		//		Result:       "",
		//		IsReplyStart: true,
		//		IsReplyEnd:   true,
		//		Source:       "",
		//		Sug:          nil,
		//	}
		//	sseResByte, _ := json.Marshal(sseExitRes)
		//	_, _ = fmt.Fprint(w, "data:"+string(sseResByte)+"\n\n")
		//	flusher.Flush()
		//
		//	header := ConvertHTTPHeaderToKratosHeader(r.Context(), r.Header)
		//	ctx := middleware.HeaderContext(r.Context(), header)
		//	// 退出了解题意图
		//	sync.Go(r.Context(), s.log, func() {
		//
		//		deviceId := custom_context.GetXDeviceId(ctx)
		//		s.log.WithContext(ctx).Infof("send downward msg header deviceId: %v", deviceId)
		//		if len(deviceId) < 1 {
		//			return
		//		}
		//
		//		// 展示随时问的结果
		//		result := s.DefaultSearchQuestionSkill(ctx)
		//
		//		type HMINluResponse struct {
		//			Type       int         `json:"type"`
		//			NLUContent interface{} `json:"nlu_content"`
		//		}
		//
		//		topic := "asr"
		//		if reqParam.Continuous {
		//			topic = "asr/continuous"
		//		}
		//		hmiMsgReq := biz.HmiMsgReq{
		//			TraceId: custom_context.GetTraceId(ctx),
		//			Topic:   topic,
		//		}
		//		hmiMsgReq.Body.TraceId = hmiMsgReq.TraceId
		//		hmiMsgReq.Body.Topic = hmiMsgReq.Topic
		//		wsResp := WsResponse{
		//			Data: HMINluResponse{
		//				Type:       4,
		//				NLUContent: result,
		//			},
		//		}
		//		wsResp.MsgType = 2
		//		wsResp.Errno = 0
		//		wsResp.Cmd = 1
		//		wsResp.SessionId = reqParam.SessionId
		//		hmiMsgReq.Body.Payload = wsResp
		//
		//		// 非讲题，下发nlu数据
		//		err = s.msgBiz.DownwardHmiMsg(ctx, deviceId, hmiMsgReq)
		//		if err != nil {
		//			s.log.WithContext(ctx).Warnf("DownwardHmiMsg err: %v", err)
		//			return
		//		}
		//	})
		//
		//	sync.Go(ctx, s.log, func() {
		//		s.log.WithContext(ctx).Infof("DelMathAgentFlag and DelMathAgentQuestion uid: %s", reqParam.Uid)
		//		err = s.memoryBiz.DelMathAgentFlag(ctx, reqParam.Uid)
		//		if err != nil {
		//			s.log.WithContext(ctx).Errorf("DelMathAgentFlag err: %v", err)
		//		}
		//		err = s.memoryBiz.DelMathAgentQuestion(reqParam.Uid)
		//		if err != nil {
		//			s.log.WithContext(ctx).Errorf("DelMathAgentQuestion err: %v", err)
		//		}
		//	})
		//
		//	return
		//}

		agentQuestion = question
		agentRag = ragInput
		agentMessage = "这道题怎么做"
		sync.Go(r.Context(), s.log, func() {
			err = s.memoryBiz.SetMathAgentQuestion(reqParam.Uid, agentQuestion, agentRag)
			if err != nil {
				s.log.WithContext(r.Context()).Errorf("SetMathAgentQuestion err: %v", err)
			}
		})
	} else {
		agentMessage = question
	}

	s.log.WithContext(r.Context()).Infof("GetFullViewLlmMemoryContext sessionId: %s; uid: %s", reqParam.SessionId, reqParam.Uid)
	historyList, err := s.memoryBiz.GetFullViewLlmMemoryContext(context.Background(), reqParam.SessionId, reqParam.Uid, []string{common.SkillSearchQuestion.ToString()})
	if err != nil {
		s.log.WithContext(r.Context()).Errorf("GetLlmMemoryContext err: %+v; sessionId: %s; uid: %s", err, reqParam.SessionId, reqParam.Uid)
	}

	historyListFiltered := make([]*dto.LlmMemoryContextResp, 0)
	mathFlag := s.memoryBiz.GetMathAgentFlag(r.Context(), reqParam.Uid)
	s.log.WithContext(r.Context()).Infof("GetMathAgentFlag: %s; %s", reqParam.Uid, mathFlag)

	if mathFlag != "" {
		for _, history := range historyList {
			if history.DialogueId == mathFlag {
				historyListFiltered = append(historyListFiltered, history)
			}
		}
	}

	agentMethodRequest := MathAgentMethodRequest{
		sessionId:   reqParam.SessionId,
		talId:       reqParam.Uid,
		question:    agentQuestion,
		message:     agentMessage,
		historyList: historyListFiltered,
		rag:         strings.ReplaceAll(agentRag, "\n", ""),
		videoUrl:    paiVideoUrl,
		outputTitle: paiOutputTitle,
	}
	validMathChan, mathAgentMsgChan := s.requestMathExplainAgentByQwenMax(r.Context(), agentMethodRequest)
	if mathAgentMsgChan == nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 大模型判断是一道数学题
	var (
		validMath           bool
		allMathAgentMsgData string
		rawMathAgentMsgData string
		mediaMsg            *dto.Media
	)

	for mathAgentMsgChan != nil || validMathChan != nil {
		select {
		case vm, ok := <-validMathChan:
			if !ok {
				validMathChan = nil
				continue
			}
			validMath = vm
		case mas, ok := <-mathAgentMsgChan:
			if !ok {
				mathAgentMsgChan = nil
				continue
			}

			sseResByte, _ := json.Marshal(mas)
			mathAgentMsgData := "data:" + string(sseResByte)
			s.log.WithContext(r.Context()).Infof("mathAgentMsgData: %s", mathAgentMsgData)

			allMathAgentMsgData += mas.Result
			rawMathAgentMsgData += mas.RawResult
			if mas.Media.Type > 0 {
				mediaMsg = &mas.Media
			}
			_, _ = fmt.Fprint(w, mathAgentMsgData+"\n\n")
			flusher.Flush()
		}
	}

	// 上报历史记录
	s.UploadMathAgentHistory(r.Context(), reqParam, paiVideoUrl, allMathAgentMsgData, rawMathAgentMsgData, mathFlag, mediaMsg)

	if !validMath {

		header := ConvertHTTPHeaderToKratosHeader(r.Context(), r.Header)
		ctx := middleware.HeaderContext(r.Context(), header)
		// 退出了解题意图
		sync.Go(r.Context(), s.log, func() {

			deviceId := custom_context.GetXDeviceId(ctx)
			s.log.WithContext(ctx).Infof("send downward msg header deviceId: %v", deviceId)
			if len(deviceId) < 1 {
				return
			}

			nluReq := &v1.LuiNluRequest{
				AsrInfo:    question,
				RequestId:  reqParam.SessionId,
				SentenceId: reqParam.SentenceId,
				TalId:      reqParam.Uid,
				GradeId:    "3",
				LlmAgent: &v1.LlmAgent{
					Name:   common.SkillSearchQuestion.ToString(),
					Status: 1,
				},
			}
			result, err := s.LuiNluPad2Result(ctx, nluReq)
			if err != nil {
				s.log.WithContext(r.Context()).Errorf("LuiNluPad2Result err: %v", err)
				return
			}

			type HMINluResponse struct {
				Type       int         `json:"type"`
				NLUContent interface{} `json:"nlu_content"`
			}

			topic := "asr"
			if reqParam.Continuous {
				topic = "asr/continuous"
			}
			hmiMsgReq := biz.HmiMsgReq{
				TraceId: custom_context.GetTraceId(ctx),
				Topic:   topic,
			}
			hmiMsgReq.Body.TraceId = hmiMsgReq.TraceId
			hmiMsgReq.Body.Topic = hmiMsgReq.Topic
			wsResp := WsResponse{
				Data: HMINluResponse{
					Type:       4,
					NLUContent: result,
				},
			}
			wsResp.MsgType = 2
			wsResp.Errno = 0
			wsResp.Cmd = 1
			wsResp.SessionId = reqParam.SessionId
			hmiMsgReq.Body.Payload = wsResp

			// 非讲题，下发nlu数据
			err = s.msgBiz.DownwardHmiMsg(ctx, deviceId, hmiMsgReq)
			if err != nil {
				s.log.WithContext(ctx).Warnf("DownwardHmiMsg err: %v", err)
				return
			}
		})

		sync.Go(ctx, s.log, func() {
			s.log.WithContext(ctx).Infof("DelMathAgentFlag and DelMathAgentQuestion uid: %s", reqParam.Uid)
			err = s.memoryBiz.DelMathAgentFlag(ctx, reqParam.Uid)
			if err != nil {
				s.log.WithContext(ctx).Errorf("DelMathAgentFlag err: %v", err)
			}
			err = s.memoryBiz.DelMathAgentQuestion(reqParam.Uid)
			if err != nil {
				s.log.WithContext(ctx).Errorf("DelMathAgentQuestion err: %v", err)
			}
		})
	}
}

type WsRespCommon struct {
	MsgType   int    `json:"msgtype"`
	MsgId     string `json:"msgid"`
	Cmd       int    `json:"cmd"`
	SessionId string `json:"session_id"`
	// 透传
	AsrType int `json:"asr_type,omitempty"`
	// 从1开始递增来标识。 单轮的话 sentence_id传0。
	SentenceId int `json:"sentence_id,omitempty"`
	// 唤醒类型
	WakeUpType string `json:"wake_up_type,omitempty"`
	// 闭麦类型
	MuteMicType string `json:"mute_mic_type,omitempty"`

	Extra string `json:"extra,omitempty"`
}

// WsResponse format
type WsResponse struct {
	WsRespCommon
	Errno  int         `json:"errno"`
	ErrMsg string      `json:"errmsg"`
	Ts     int64       `json:"ts"`
	Data   interface{} `json:"data"`
}

func (s *LuiService) QueryKETQSceneNlu(ctx context.Context, req *v1.LuiNluRequest) (isMath bool, resp *dto.QueryPad2Resp, nluData *dto.NLUQueryPad2Resp) {

	nluQuery := &dto.NLUQuery{
		RequestID: req.RequestId,
		AsrInput: dto.AsrInput{
			AsrInfo:   req.AsrInfo,
			AsrLen:    len(req.AsrInfo),
			AsrPinyin: req.AsrPinyin,
		},
		UserSystem: dto.UserSystemV2{
			Location: req.Location,
			Grade:    cast.ToInt(req.GradeId),
			Semester: dto.GetSemester(),
			TalId:    req.TalId,
			DeviceId: custom_context.GetXDeviceId(ctx),
		},
		SlotFillList: nil,
		Source:       "pad2",
		Version:      custom_context.GetXVersion(ctx),
		LlmAgent: dto.LlmAgent{
			Name:   common.SkillTutorQA.ToString(),
			Status: 0,
		},
	}
	controllerResp, err := s.bizNlu.ControllerCall(ctx, nluQuery)
	if err != nil {
		return
	}

	if controllerResp.Data.Intent == "tutor答疑" {

		return true, &dto.QueryPad2Resp{

				TtsShow: "",
				TtsNorm: "",
				Data: []*dto.Pad2SkillData{
					{
						Skill: common.SkillTutorQA.ToString(),
						Data: &dto.NLUSkillItem{
							Task:           "",
							TtsShow:        "",
							TtsNorm:        "",
							Count:          0,
							AsrInfo:        req.AsrInfo,
							SessionId:      req.RequestId,
							SentenceId:     req.SentenceId,
							SceneMode:      dto.SceneModeFullView,
							IsAccessingLLM: true,
							IsResultValid:  true,
							AiAgent:        common.AiAgentKetTutorQA,
						},
					},
				},
				ModelOutputIntent: controllerResp.Data.Intent,
				RejectRecType:     4,
			}, &dto.NLUQueryPad2Resp{
				FunctionList: controllerResp.Data.FunctionCall.Functions,
			}
	}

	return
}

func (s *LuiService) GetLegalCheckTts(bizType string) []string {
	queryTts := s.thirdConf.LlmPrompt.LegalCheckTts
	if strings.Contains(bizType, common.BizTypeAiTutor) {
		douDiMap := s.thirdConf.NlpFunc.DouDiMap["bella"]
		if douDiMap != nil && douDiMap.DouDiIntentMap["闲聊"] != nil {
			queryTts = douDiMap.DouDiIntentMap["闲聊"].Items
		}
	}
	return queryTts
}

func (s *LuiService) GetBaiKeDefaultTts(bizType string) []string {
	queryTts := s.thirdConf.LlmPrompt.BaikeDefaultTts
	if strings.Contains(bizType, common.BizTypeAiTutor) {
		douDiMap := s.thirdConf.NlpFunc.DouDiMap["bella"]
		if douDiMap != nil && douDiMap.DouDiIntentMap["百科"] != nil {
			queryTts = douDiMap.DouDiIntentMap["百科"].Items
		}
	}
	return queryTts
}
