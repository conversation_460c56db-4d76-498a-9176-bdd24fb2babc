package service

import (
	"context"
	"encoding/json"
	"fmt"
	"lui-api/internal/pkg/custom_context"
	"net/http"
	"runtime/debug"
	"strings"
	"time"

	"go.opentelemetry.io/otel/trace"

	"lui-api/internal/biz"
	"lui-api/internal/common"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/sse"
	"lui-api/internal/pkg/utils"
	"lui-api/pkg/util"

	"github.com/go-resty/resty/v2"
	"github.com/spf13/cast"
)

// FetchUserTalId
func (s *LuiService) FetchUserTalId(ctx context.Context, uid string, headerMap map[string]string) string {
	if uid != "" {
		return uid
	}

	authorization, ok := headerMap["Authorization"]
	if !ok || len(authorization) <= 7 {
		return ""
	}

	token := authorization[7:]
	authInfo, err := s.uCenterBiz.TokenParse(ctx, token)
	if err != nil {
		return ""
	}

	return authInfo.TalID
}

// FetchXiaosiRag
func (s *LuiService) FetchXiaosiRag(ctx context.Context, sseReq *dto.SseLuiReq, intent string, isFullView bool) string {
	ragReq := &dto.RagReq{
		Query:     sseReq.Message,
		RequestId: sseReq.SessionId,
	}

	isChat := intent == common.LlmChatIntent
	ragUrl := s.thirdConf.BaikeRagUrl
	if isChat {
		ragUrl = s.thirdConf.XiaosiRagUrl
	}
	ragResp, err := s.ragBiz.FetchXiaosiRag(ctx, ragReq, ragUrl)
	if err != nil || ragReq == nil {
		return ""
	}

	if isFullView {
		return formatFullViewXiaosiRag(ctx, ragResp.Recall)
	}

	if intent == common.SkillDeepseek.ToString() {
		return formatDeepSeekXiaosiRag(ctx, sseReq, ragResp)
	}

	return formatXiaosiRag(ctx, ragResp.Recall)
}

func formatDeepSeekXiaosiRag(ctx context.Context, sseReq *dto.SseLuiReq, ragData *dto.RagData) string {
	if ragData == nil {
		return ""
	}

	if ragData.Task == "baike.knowledge" && ragData.Text != "" {
		rag := "相关回答：\n"
		rag += "{用户输入:\"" + sseReq.Message + "\",结果:\"" + ragData.Text + "\"}"

		return rag
	}

	return formatXiaosiRagV2(ctx, ragData.Recall)
}

// formatFullViewXiaosiRag
func formatFullViewXiaosiRag(_ context.Context, ragResp []*dto.RagRecallItem) string {
	rag := ""
	if len(ragResp) == 0 {
		return rag
	}

	rag += "* 相关回答：\n"
	if len(ragResp) == 1 {
		rag += "以下为与你的问题相关的回答，请你在回答时参考这个回答作为参考信息：\n"
		rag += "{用户输入:\"" + ragResp[0].Question + "\",结果:\"表达:calm\n回复内容:" + ragResp[0].Answer + "sug:\n1、一起聊聊吧？\n2、你在干嘛？\n3、我有个事要跟你说。\"}"

		return rag
	}

	rag += "以下为与你的问题相关的回答，请你在回答时参考这几个回答作为参考信息："
	for i, item := range ragResp {
		if i == 0 {
			rag += "{"
		}
		rag += "用户输入:\"" + item.Question + "\",结果:\"表达:calm\n回复内容:" + item.Answer + "sug:\n1、一起聊聊吧？\n2、你在干嘛？\n3、我有个事要跟你说。\""
		if i == len(ragResp)-1 {
			rag += "}"
		} else {
			rag += ";"
		}
	}

	return rag
}

// formatXiaosiRag
func formatXiaosiRag(_ context.Context, ragResp []*dto.RagRecallItem) string {
	rag := ""
	if len(ragResp) == 0 {
		return rag
	}

	rag += "* 相关回答：\n"
	if len(ragResp) == 1 {
		rag += "以下为与你的问题相关的回答，请你在回答时参考这个回答作为参考信息："
		rag += "{用户输入:\"" + ragResp[0].Question + "\",结果:\"" + ragResp[0].Answer + "\"}"

		return rag
	}

	rag += "以下为与你的问题相关的回答，请你在回答时参考这几个回答作为参考信息："
	for i, item := range ragResp {
		if i == 0 {
			rag += "{"
		}
		rag += "用户输入:\"" + item.Question + "\",结果:\"" + item.Answer + "\""
		if i == len(ragResp)-1 {
			rag += "}"
		} else {
			rag += "；"
		}
	}

	return rag
}

func formatXiaosiRagV2(_ context.Context, ragResp []*dto.RagRecallItem) string {
	rag := ""
	if len(ragResp) == 0 {
		return rag
	}

	rag += "相关回答：\n"
	if len(ragResp) == 1 {
		rag += "{用户输入:\"" + ragResp[0].Question + "\",结果:\"" + ragResp[0].Answer + "\"}"

		return rag
	}

	for i, item := range ragResp {
		if i == 0 {
			rag += "{"
		}
		rag += "用户输入:\"" + item.Question + "\",结果:\"" + item.Answer + "\""
		if i == len(ragResp)-1 {
			rag += "}"
		} else {
			rag += "；"
		}
	}

	return rag
}

// FetchHeaderMap
func (s *LuiService) FetchHeaderMap(r *http.Request, sessionId string) map[string]string {
	headerMap := make(map[string]string)
	for key, values := range r.Header {
		if len(values) > 0 {
			headerMap[key] = values[0]
		}
	}

	delete(headerMap, "X-Genie-Appid")
	headerMap["X-Genie-TraceId"] = sessionId
	headerMap["X-Genie-AppId"] = s.thirdConf.LlmPrompt.AppId

	return headerMap
}

// FetchUserProfile
func (s *LuiService) FetchUserProfile(ctx context.Context, talId string) string {
	userInfoParse := &biz.UserInfoParse{
		ClientID: "552403",
		TalID:    talId,
	}
	userProfile, err := s.uCenterBiz.GetUserProfile(ctx, userInfoParse)
	if err != nil {
		return ""
	}

	var str string
	if userProfile.Nickname != "" {
		str += fmt.Sprintf("昵称:%s", userProfile.Nickname+";")
	}

	if userProfile.GradeName != "" {
		str += fmt.Sprintf("年级:%s", userProfile.GradeName+";")
	}

	if str == "" {
		return ""
	}

	return str[:len(str)-1]
}

// FetchHistory 上下文
func (s *LuiService) FetchHistory(ctx context.Context, sessionId, uid string) []dto.LlmHistoryMessage {
	var llmHistoryMessage []dto.LlmHistoryMessage
	llmContextResp, err := s.memoryBiz.GetLlmMemoryContext(ctx, sessionId, uid)
	if err != nil {
		return llmHistoryMessage
	}

	for _, v := range llmContextResp {
		llmHistoryMessage = append(llmHistoryMessage, dto.LlmHistoryMessage{
			Role:    v.Role,
			Content: v.Content,
		})
	}

	return llmHistoryMessage
}

func (s *LuiService) FetchFullViewLLmHistory(ctx context.Context, sessionId, uid string, llmSkill []string) []dto.LlmHistoryMessage {
	var llmHistoryMessage []dto.LlmHistoryMessage
	llmContextResp, err := s.memoryBiz.GetFullViewLlmMemoryContext(ctx, sessionId, uid, llmSkill)
	if err != nil {
		return llmHistoryMessage
	}

	for _, v := range llmContextResp {
		llmHistoryMessage = append(llmHistoryMessage, dto.LlmHistoryMessage{
			Role:    v.Role,
			Content: v.Content,
		})
	}

	return llmHistoryMessage
}

func (s *LuiService) FetchAgentHistory(ctx context.Context, sessionId, uid string) []dto.LlmHistoryMessage {
	var llmHistoryMessage []dto.LlmHistoryMessage
	llmContextResp, err := s.memoryBiz.GetAgentMemoryContext(ctx, sessionId, uid)
	if err != nil {
		return llmHistoryMessage
	}

	for _, v := range llmContextResp {
		llmHistoryMessage = append(llmHistoryMessage, dto.LlmHistoryMessage{
			Role:    v.Role,
			Content: v.Content,
		})
	}

	return llmHistoryMessage
}

// FetchProfile 近况&爱好
func (s *LuiService) FetchProfile(ctx context.Context, uid string) dto.DialogueProfile {
	reply := dto.DialogueProfile{
		Recent:    "",
		Interests: "",
	}

	if len(uid) == 0 {
		return reply
	}

	req := make(map[string]interface{})
	req["user_id"] = uid

	headers := map[string]string{
		"Content-Type": "application/json",
		"Traceparent":  fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
	}
	ch := utils.CurlInit(ctx, time.Second*time.Duration(3), 0, s.log)
	resp, err := ch.ChRequest(ch.Req.SetHeaders(headers).SetBody(req)).Post(s.thirdConf.DialogueProfileUrl)
	if err != nil {
		s.log.WithContext(ctx).Infof("FetchProfile err: %+v", err)
		return reply
	}

	if resp.IsError() {
		s.log.WithContext(ctx).Infof("FetchProfile resp.IsError err: %+v", err)
		return reply
	}

	var res dto.DialogueProfileResp
	err = json.Unmarshal(resp.Body(), &res)
	if err != nil {
		return reply
	}

	s.log.WithContext(ctx).Infof("FetchProfile latency: %v, output: %+v", resp.Time().Milliseconds(), util.Marshal(res))

	var (
		recent    string
		interests string
		taskStr   string
		userName  string
	)

	for _, v := range res.Data.Emotion {
		if v.Details == "" {
			continue
		}
		queryTime := util.TimestampToDate(util.DateToTimestamp(v.QueryTime, util.TimeTemplate2), util.TimeTemplate1)
		recent += queryTime + ":情绪类型为" + v.Details + ",激烈程度:" + cast.ToString(v.Score) + ";\n"
	}

	for _, v := range res.Data.Hobby {
		if v.Details == "" {
			continue
		}
		interests += "爱好:" + v.Details + ",喜爱程度:" + cast.ToString(v.Score) + ";\n"
	}

	if len(recent) > 0 {
		recent = recent[:len(recent)-2]
	}

	if len(interests) > 0 {
		interests = interests[:len(interests)-2]
	}

	if res.Data.UserNameTrustScores >= 50 {
		userName = res.Data.UserName
	}

	if userName == "" {
		taskStr += "task1:询问用户的姓名"
	}

	if len(interests) == 0 {
		taskStr += "task2:询问用户的兴趣"
	}

	return dto.DialogueProfile{
		Recent:    recent,
		Interests: interests,
		Task:      taskStr,
	}
}

// FetchMemory 事件流
func (s *LuiService) FetchMemory(ctx context.Context, sseReq *dto.SseLuiReq) string {
	if sseReq == nil || sseReq.Intent != common.LlmChatIntent || sseReq.Uid == "" {
		return ""
	}

	fetchMemoryReq := &dto.FetchMemoryReq{
		TalId: sseReq.Uid,
		Query: sseReq.Message,
	}

	resp, err := resty.New().OnBeforeRequest(func(client *resty.Client, r *resty.Request) error {
		headers := map[string]string{
			"Content-Type": "application/json",
		}
		client.SetRetryCount(0).
			SetTimeout(time.Second * time.Duration(3)).
			SetHeaders(headers)
		return nil
	}).
		R().
		SetBody(fetchMemoryReq).
		Post(s.thirdConf.MemoryServiceUrl)

	if err != nil {
		s.log.WithContext(ctx).Warnf("获取lui-memory-service服务失败， err:%+v", err)
		return ""
	}

	if resp.IsError() {
		s.log.WithContext(ctx).Warnf("获取lui-memory-service服务失败， resp:%+v", resp)
		return ""
	}

	var rawResp dto.LuiMemoryServiceResp
	err = json.Unmarshal(resp.Body(), &rawResp)
	if err != nil {
		return ""
	}

	s.log.WithContext(ctx).Infof("FetchMemory latency: %v, output: %+v", resp.Time().Milliseconds(), util.Marshal(rawResp))

	if len(rawResp.Data.Replies) == 0 {
		return ""
	}

	memory := ""
	for _, item := range rawResp.Data.Replies {
		if item.Description == "" {
			continue
		}

		memory += "日期:" + item.EventDate + ",主题描述:" + item.Description + ";\n"
	}

	if len(memory) == 0 {
		return ""
	}

	return memory[:len(memory)-2]
}

func (s *LuiService) FetchSseByFilterPunctuation(ctx context.Context, ch <-chan *sse.Event) (<-chan *sse.Event, error) {
	outCh := make(chan *sse.Event, 1024)

	go func() {
		defer func() {
			if err := recover(); err != nil {
				errorMsg := fmt.Sprintf("go routine panic错误：%v\n %s", err, debug.Stack())
				s.log.WithContext(ctx).Error(errorMsg)
			}
			close(outCh)
		}()

		var resMsg string
		for {
			select {
			case <-ctx.Done():
				return
			case data, ok := <-ch:
				if !ok {
					return
				}
				if ctx.Err() != nil {
					return
				}

				if !strings.HasPrefix(data.Data, "data:") {
					continue
				}

				substring := strings.TrimPrefix(data.Data, "data:")
				var sseRes dto.SseRes
				err := json.Unmarshal([]byte(substring), &sseRes)
				if err != nil {
					continue
				}

				if !sseRes.IsDeepSeek {
					// 按标点分隔
					punc := util.FilterChars(sseRes.Result, s.thirdConf.LlmPrompt.FilterChatPunctuation)
					filterStr := ""
					if len(punc) > 0 || sseRes.IsEnd {
						if len(punc) > 0 {
							split := strings.SplitN(sseRes.Result, punc, 2)
							resMsg = resMsg + split[0] + punc
							splitLen := len(split)
							filterStr = strings.Join(split[1:splitLen], filterStr)
							// is_end punctuation
							if sseRes.IsEnd && len(filterStr) > 0 {
								resMsg = resMsg + filterStr
							}
						} else {
							// is_end none punctuation
							resMsg = resMsg + sseRes.Result
						}

						sseRes.Result = resMsg
						sseResByte, _ := json.Marshal(sseRes)
						outCh <- &sse.Event{Data: "data:" + string(sseResByte)}

						if len(filterStr) > 0 {
							resMsg = filterStr
							filterStr = ""
						} else {
							resMsg = ""
						}

						if sseRes.IsEnd == true {
							return
						}
					} else {
						resMsg = resMsg + sseRes.Result
					}
				} else {
					outCh <- data
				}
			}
		}
	}()

	return outCh, nil
}

func (s *LuiService) DealDialogueSugStr(sessionId, sugStr string) []string {
	s.log.WithContext(context.Background()).Infof("DialogueSugStr: %s sessionId: %s", sugStr, sessionId)
	if sugStr == "" {
		return []string{}
	}

	index := strings.Index(sugStr, "1.")
	if index > 0 {
		sugStr = sugStr[index:]
	}

	index = strings.Index(sugStr, "1、")
	if index > 0 {
		sugStr = sugStr[index:]
	}

	replacer := strings.NewReplacer(
		"1.", "",
		"2.", "",
		"3.", "",
		"1、", "",
		"2、", "",
		"3、", "")
	sugStr = replacer.Replace(sugStr)

	sugSlice := strings.Split(sugStr, "\n")
	var sugList []string
	for _, v := range sugSlice {
		if v != "" {
			sugList = append(sugList, strings.TrimSpace(v))
		}
	}
	if len(sugList) > 3 {
		sugList = sugList[:3]
	}
	return sugList
}

func FormatLlmModel(llmModel string) string {
	return ""
	//return dto.GetLLMSource(llmModel).ToString()
}

func FormatSessionId(sessionID string) string {
	parts := strings.SplitN(sessionID, "-", 2)
	if len(parts) > 1 {
		return parts[0]
	}

	return sessionID
}
