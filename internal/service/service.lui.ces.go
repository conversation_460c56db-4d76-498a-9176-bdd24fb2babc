package service

import (
	"context"
	"encoding/json"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/types/known/structpb"
	v1 "lui-api/api/lui/v1"
	"lui-api/internal/data/dto"
	"lui-api/pkg/util"
	"regexp"
)

type ZodiacContent struct {
	RecognitionResult bool   `json:"recognition_result"`
	Zodiac            string `json:"zodiac"`
}

func (s *LuiService) GetZodiac(ctx context.Context, req *v1.LuiNluRequest) (*structpb.Struct, error) {
	luiWidgetContent := ZodiacContent{
		Zodiac:            "",
		RecognitionResult: false,
	}
	re := regexp.MustCompile(`\d{4}`)
	matches := re.FindAllString(req.AsrInfo, 1)

	if len(matches) == 1 {
		zodiac := getZodiac(cast.ToInt(matches[0]))
		if zodiac != "" {
			luiWidgetContent.Zodiac = zodiac
			luiWidgetContent.RecognitionResult = true
		}
	}

	luiWidget := dto.Nlu4LuiWidget{
		Content: luiWidgetContent,
	}
	luiWidgetBt, _ := json.Marshal(luiWidget)
	nluByLui := &dto.NluData{
		UserID:    req.TalId,
		RequestID: req.RequestId,
		Skill:     "zodiac",
		Widget:    string(luiWidgetBt),
	}
	resp, err := util.ReplyAny(nluByLui)
	if err != nil {
		s.log.WithContext(ctx).Errorf("GetZodiac ReplyAny err: %+v; nlu4lui: %+v", err, nluByLui)
		return nil, v1.ErrorInternalError("GetZodiac")
	}
	s.log.WithContext(ctx).Infof("GetZodiac resp: %v", resp)

	return resp, nil
}

func getZodiac(year int) string {
	switch (year - 4) % 12 {
	case 0:
		return "鼠"
	case 1:
		return "牛"
	case 2:
		return "虎"
	case 3:
		return "兔"
	case 4:
		return "龙"
	case 5:
		return "蛇"
	case 6:
		return "马"
	case 7:
		return "羊"
	case 8:
		return "猴"
	case 9:
		return "鸡"
	case 10:
		return "狗"
	case 11:
		return "猪"
	default:
		return ""

	}
}
