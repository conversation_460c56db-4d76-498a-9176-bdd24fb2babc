package service

import (
	"context"
	"google.golang.org/protobuf/types/known/structpb"
	v1 "lui-api/api/lui/v1"
	"lui-api/internal/common"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	pkgSync "lui-api/internal/pkg/sync"
	"lui-api/pkg/util"
	"math/rand"
	"strings"
	"unicode"
)

// RejectNLU
// 0没走拒识--关掉拒识，所有query应该是这个值
// 1【异常无唤醒词】&非点击
// 2【异常有唤醒词】
// 3【异常无唤醒词】&点击
// 4【正常query】
func (s *LuiService) RejectNLU(ctx context.Context, req *v1.LuiRejectRequest) (*structpb.Struct, error) {
	var tts string
	var rejectType int
	if req.RejectResult == 1 {
		if custom_context.GetXAppId(ctx) == s.thirdConf.AppIdMap["xPad2AppId_qijian25"] || custom_context.GetXAppId(ctx) == s.thirdConf.AppIdMap["xPad2AppId_classic25"] {
			tts = ""
			rejectType = 7
		} else {
			if req.WakeupType == "action_wake_up" {
				sl := []rune(req.GetAsrInfo())
				if len(sl) == 0 || (len(sl) == 1 && unicode.Is(unicode.Han, sl[0])) {
					tts = s.thirdConf.SingleWordTts[rand.Intn(len(s.thirdConf.SingleWordTts))]
				} else {
					tts = s.thirdConf.NonsenseTts[rand.Intn(len(s.thirdConf.NonsenseTts))]
				}
				rejectType = 3
			} else if req.WakeupType == "continuous_vad" {
				tts = ""
				rejectType = 7
			} else {
				tts = ""
				rejectType = 1
			}
		}

	} else if req.RejectResult == 2 {
		sl := []rune(req.GetAsrInfo())
		if len(sl) == 0 || (len(sl) == 1 && unicode.Is(unicode.Han, sl[0])) {
			tts = s.thirdConf.SingleWordTts[rand.Intn(len(s.thirdConf.SingleWordTts))]
		} else {
			tts = s.thirdConf.NonsenseTts[rand.Intn(len(s.thirdConf.NonsenseTts))]
		}
		rejectType = 2
	}
	if strings.Contains(req.BizType, common.BizTypeAiTutor) {
		tts = strings.ReplaceAll(tts, "小思", "Bella")
	}
	return s.DefaultTTS(ctx, req, tts, rejectType)
}

func (s *LuiService) DefaultTTS(ctx context.Context, req *v1.LuiRejectRequest, tts string, rejectRecType int) (*structpb.Struct, error) {

	if custom_context.GetXAppId(ctx) == s.thirdConf.XPad1AppId {
		// 一代结构
		res := &dto.NluData{
			UserID:    "",
			RequestID: "",
			Skill:     "no_skill",
			Task:      "",
			TtsShow:   tts,
			TtsNorm:   tts,
		}

		resp, _ := util.ReplyAny(res)
		return resp, nil
	}

	// 二代结构
	res := dto.QueryPad2Resp{
		TtsShow: tts,
		TtsNorm: tts,
		Data: []*dto.Pad2SkillData{
			{
				Skill: "no_skill",
				Data: &dto.NLUSkillItem{
					Task:          "",
					TtsShow:       tts,
					TtsNorm:       tts,
					Count:         0,
					IsResultValid: true,
				},
			},
		},
		ModelOutputIntent: "异常query",
		RejectRecType:     rejectRecType,
	}
	resp, _ := util.ReplyAny(res)

	pkgSync.Go(ctx, s.log, func() {
		var sceneCode common.SceneCode
		if strings.HasPrefix(req.BizType, common.BizTypeAiTutor) {
			sceneCode = common.SceneAiTutor
			//} else if strings.HasPrefix(req.BizType, common.BizTypeHomeWork) && custom_context.GetVersion(ctx) == common.PlatVersionOriginalT {
			//	sceneCode = common.SceneHomeWork
		} else {
			sceneCode = common.SceneXiaoSi
		}
		memoryContextReq := dto.MemoryContextReq{
			UserTalID: req.TalId,
			SessionId: req.RequestId,
			AsrInfo:   req.AsrInfo,
			SceneCode: int(sceneCode),
		}
		_ = s.memoryBiz.AddXPadMemoryContext(context.TODO(), memoryContextReq, resp, nil)

		// 设置拒识标记
		_ = s.memoryBiz.SetAddMemoryContextRejectFlag(req.RequestId)
	})

	return resp, nil
}
