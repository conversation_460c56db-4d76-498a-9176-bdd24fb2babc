package service

import (
	"context"
	"git.100tal.com/jituan_genie_server/mqs"
	"github.com/google/uuid"
	"google.golang.org/protobuf/types/known/structpb"
	v1 "lui-api/api/lui/v1"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	"lui-api/pkg/util"
	"strings"
)

// AIEyeFingerImageJudge 智慧眼图片识别逻辑
func (s *LuiService) AIEyeFingerImageJudge(ctx context.Context, req *v1.AIEyeFingerImageJudgeRequest) (*structpb.Struct, error) {
	header := &mqs.Header{
		XGeniePlatform:  custom_context.GetXPlatform(ctx),
		XGenieVersion:   custom_context.GetXVersion(ctx),
		XGenieOsVersion: custom_context.GetXOSVersion(ctx),
		XGenieAppId:     custom_context.GetXAppId(ctx),
		XGenieDeviceId:  custom_context.GetXDeviceId(ctx),
		XGenieNonce:     custom_context.GetXNonce(ctx),
	}
	traceId := custom_context.GetTraceId(ctx)
	if traceId == "" {
		traceId = uuid.NewString()
	}
	hasImage, text, err := s.bizNlu.CheckForNestedImagesWithOCR(ctx, header, traceId, req.ImageUrl, "")
	if err != nil {
		s.log.WithContext(ctx).Errorf("CheckForNestedImagesWithOCR err: %v", err)
		return nil, err
	}
	text = strings.TrimSpace(text)
	if text == "" {
		judgeResp := AIEyeFingerImageJudgeResp{
			ResultType: 1,
			TtsNorm:    "未识别到题目，重新拍一下题目吧",
		}
		resp, _ := util.ReplyAny(judgeResp)
		s.log.WithContext(ctx).Infof("AIEyeFingerImageJudge resp: %+v", resp)
		return resp, nil
	}

	isMath, err := s.bizNlu.MathGptAskIsMath(ctx, header, traceId, text)
	if err != nil {
		return nil, err
	}

	if !isMath {
		judgeResp := AIEyeFingerImageJudgeResp{
			ResultType: 3,
			TtsNorm:    "小思现在只能讲数学题，有不会的数学题可以问小思哦~",
		}
		resp, _ := util.ReplyAny(judgeResp)
		s.log.WithContext(ctx).Infof("AIEyeFingerImageJudge resp: %+v", resp)
		return resp, nil
	}

	if hasImage {
		judgeResp := AIEyeFingerImageJudgeResp{
			ResultType: 2,
			TtsNorm:    "",
			Command: dto.CommendTmp{
				API: "functional.application",
				Param: dto.CommendParamTmp{
					Appid:       "1025",
					Method:      "rule",
					Operate:     "open",
					PackageName: "com.tal.pad.cameralesson",
					Value:       "教辅答疑",
					Scheme:      "tal://cameralesson/main",
				},
			},
			OcrText: text,
		}
		resp, _ := util.ReplyAny(judgeResp)
		s.log.WithContext(ctx).Infof("AIEyeFingerImageJudge resp: %+v", resp)
		return resp, nil
	}

	judgeResp := AIEyeFingerImageJudgeResp{
		ResultType: 4,
		OcrText:    text,
	}
	resp, _ := util.ReplyAny(judgeResp)
	s.log.WithContext(ctx).Infof("AIEyeFingerImageJudge resp: %+v", resp)
	return resp, nil
}

type AIEyeFingerImageJudgeResp struct {
	ResultType int            `json:"result_type"`
	OcrText    string         `json:"ocr_text"`
	TtsNorm    string         `json:"tts_norm,omitempty"`
	Command    dto.CommendTmp `json:"command,omitempty"`
}
