/*
* Author:  <PERSON><PERSON>ongQiang
* Date:    2024/4/9 11:24
* Description:
 */

package service

import (
	"context"
	"encoding/json"
	"github.com/go-kratos/kratos/v2/log"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/sse"
	"strings"
	"time"
)

func (s *LuiService) LuiLLMSugTail(userId string, traceId string, questInfo, traceparent string) {
	ctx := context.Background()
	var (
		headerMap = make(map[string]string)        // 请求头部
		apiConf   = s.Api.Mapping["dialogue_llm"]  // api配置
		tmpId     = s.thirdConf.LlmPrompt.SugTmpId // prompt 模版ID
		args      = []string{questInfo}
		sseRes    dto.SseRes
		allRes    = ""
	)

	headerMap["X-Genie-TraceId"] = traceId
	headerMap["X-Genie-AppId"] = s.thirdConf.LlmPrompt.AppId
	headerMap["Traceparent"] = traceparent
	log.Infof("LuiLLMSugTail_headerMapInfo traceId:%+v,traceparent:%+v,questInfo:%+v", traceId, traceparent, questInfo)

	if apiConf == nil {
		log.Errorf("LuiLLMSugTail apiConf is nil")
		return
	}

	//构造请求参数
	gptReqParam := dto.SseGptReq{
		Biz:       1,
		Message:   "请开始输出",
		SessionId: traceId,
		TmpId:     tmpId,
		Args:      args,
	}
	body, err := json.Marshal(gptReqParam)
	if err != nil {
		log.Errorf("Marshal-err %+v, body %+v,traceId:%+v", err, string(body), traceId)
		return
	}

	//建立连接
	c := sse.NewClient(apiConf.Url, time.Second*time.Duration(apiConf.Timeout))
	eventChan, err := c.Send(ctx, sse.PostMethod, headerMap, body)
	if err != nil {
		log.Errorf("Send-err %+v, body %+v,traceId:%+v", err, string(body), traceId)
		return
	}

	//接收数据
	for data := range eventChan {
		if strings.HasPrefix(data.Data, "data:") {

			substring := strings.TrimPrefix(data.Data, "data:")
			err = json.Unmarshal([]byte(substring), &sseRes)
			if err == nil {
				//llmModel = sseRes.Model
				if sseRes.ErrorCode == 0 && sseRes.Result != "" {
					allRes += sseRes.Result
				}

				// 记录错误日志
				if sseRes.ErrorCode != 0 && sseRes.ErrorCode != 120014 {
					log.Infof("LuiLLMSugTail sseRes:%+v, body:%+v,traceId:%+v", sseRes, string(body), traceId)
				}

			}
		}
	}

	log.Infof("LuiLLMSugTail questInfo:%+v,allRes:%+v,traceId:%+v", questInfo, allRes, traceId)

	//记录redis,超时时间24小时
	if allRes != "" {
		dataList := s.DealSugTailsResStr(allRes, traceId)
		if len(dataList) > 1 {
			res := s.sugRepo.RdbSetKey(ctx, userId, dataList[0], 24*3600)
			log.Infof("LuiLLMSugTail RdbSetKeyRes:%+v,traceId:%+v", res, traceId)
			res = s.sugRepo.RdbSetTailsKey(ctx, userId, dataList[1], 24*3600)
			log.Infof("LuiLLMSugTail RdbSetTailsKey:%+v,traceId:%+v", res, traceId)
		}
	}

	return
}

func (s *LuiService) DealSugTailsResStr(allRes string, traceId string) (dataList []string) {
	dataList = strings.Split(allRes, "<tail>:")
	if len(dataList) >= 2 {
		replacer := strings.NewReplacer(
			"1.", "",
			"2.", "",
			"3.", "",
			"1、", "",
			"2、", "",
			"3、", "",
			"<tail>:", "",
			"<sug>:", "",
		)

		dataList[0] = replacer.Replace(dataList[0])
		dataList[1] = replacer.Replace(dataList[1])

		//去除首尾的\n
		dataList[0] = strings.Trim(dataList[0], "\n")
		dataList[1] = strings.Trim(dataList[1], "\n")

		if strings.Count(dataList[0], "\n") != 2 {
			log.Infof("SugTails res wrong, DealSugTailsResStr dataList.len>2 allRes:%+v,dataList[0]:%+v,traceId:%+v", allRes, dataList[0], traceId)
		} else if strings.Count(dataList[1], "\n") != 2 {
			log.Infof("SugTails res wrong, DealSugTailsResStr dataList.len>2 allRes:%+v,dataList[1]:%+v,traceId:%+v", allRes, dataList[1], traceId)
		}
	} else {
		log.Infof("SugTails res wrong,DealSugTailsResStr dataList.len<2 allRes:%+v,traceID:%+v", allRes, traceId)
		dataList = []string{}
	}

	return
}
