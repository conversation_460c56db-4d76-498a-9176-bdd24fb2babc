package service

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/spf13/cast"
	"go.opentelemetry.io/otel/trace"
	v1 "lui-api/api/lui/v1"
	"lui-api/internal/biz"
	"lui-api/internal/common"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	"lui-api/internal/pkg/sse"
	"lui-api/internal/pkg/sync"
	"net/http"
	"regexp"
	"runtime/debug"
	"strings"
	"time"
)

func (s *LuiService) requestMathGPT(ctx context.Context, sessionId string, message string, ch chan string) (resChan chan dto.DialogueSseRes) {

	// 获取所有的请求头部信息
	headerMap := make(map[string]string)
	headerMap["X-Genie-TraceId"] = sessionId
	headerMap["X-Genie-AppId"] = s.thirdConf.LlmPrompt.AppId

	apiConf := s.Api.Mapping["dialogue_llm"]
	if apiConf == nil {
		s.log.WithContext(ctx).Errorf("Mapping-Empty %+v", headerMap)
		ch <- ""
		return
	}
	targetUrl := apiConf.Url
	tmpId := s.thirdConf.MathAgent.MathGptTmpId
	gptReqParam := dto.LlmServiceCommonChatReq{
		Biz:        1,
		TmpId:      tmpId,
		Args:       []string{},
		Message:    message,
		SessionId:  sessionId,
		MsgNoCheck: true, // 入参在nlu接口已经走过风控，调模型不需重复
	}
	body, err := json.Marshal(gptReqParam)
	c := sse.NewClient(targetUrl, time.Second*time.Duration(apiConf.Timeout))
	eventChan, err := c.Send(ctx, sse.PostMethod, headerMap, body)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Send-err %+v, body %+v", err, string(body))
		ch <- ""
		return
	} else {
		s.log.WithContext(ctx).Infof("requestMathGPT body %s", string(body))
	}

	resChan = make(chan dto.DialogueSseRes)

	go func() {

		var (
			sseRes       dto.SseRes
			analysis     string
			analysisFlag bool
		)

		defer func() {
			close(resChan)
			if !analysisFlag {
				ch <- ""
			}

			if err := recover(); err != nil {
				errorMsg := fmt.Sprintf("go routine panic错误：%v\n %s", err, debug.Stack())
				s.log.WithContext(ctx).Error(errorMsg)
				return
			}
		}()

		for data := range eventChan {
			if strings.HasPrefix(data.Data, "data:") {

				eventData := strings.TrimPrefix(data.Data, "data:")
				err = json.Unmarshal([]byte(eventData), &sseRes)
				if err := json.Unmarshal([]byte(eventData), &sseRes); err != nil {
					continue
				}

				if sseRes.ErrorCode == 0 {
					s.log.WithContext(ctx).Infof("requestMathGPT sseRes: %s", sseRes.Result)
					if !analysisFlag {
						analysis += sseRes.Result
						if strings.Contains(sseRes.Result, "详解") {
							analysisFlag = true
							ch <- strings.Replace(analysis, "【详解】", "", 1)
						}
					}
				}
			}
		}
	}()

	return resChan
}

type MathAgentMethodRequest struct {
	sessionId   string
	talId       string
	question    string
	message     string
	historyList []*dto.LlmMemoryContextResp
	rag         string
	videoUrl    string
	outputTitle string
}

func splitChineseText(text string) []string {
	// 使用正则表达式匹配中文句子结束的标点符号以及换行符
	var hanSplitRe = regexp.MustCompile(`。|？|！|；|，|：|“|”|‘|’|、|（|）|【|】|……|\n`)
	// 找到所有匹配的标点符号位置
	indexes := hanSplitRe.FindAllStringIndex(text, -1)

	lastStart := 0
	var sentences []string
	for _, loc := range indexes {
		// loc[1] 是匹配到的标点符号的结束位置，我们需要包括这个标点符号
		sentence := text[lastStart:loc[1]]
		if sentence != "" {
			sentences = append(sentences, sentence)
		}
		lastStart = loc[1]
	}
	// 检查是否有剩余的文本没有结束的标点符号
	if lastStart < len(text) {
		sentences = append(sentences, text[lastStart:])
	}
	return sentences
}

func endsWithPunctuation(text string) bool {
	re := regexp.MustCompile(`。|？|！|；|，|：|“|”|‘|’|、|（|）|【|】|……|\n$`)
	return re.MatchString(text)
}

func (s *LuiService) requestMathExplainAgentByQwenMax(ctx context.Context, request MathAgentMethodRequest) (validMathChan chan bool, resChan chan dto.CommonSseOutputRes) {
	s.log.WithContext(ctx).Infof("requestMathExplainAgent request: %+v", request)

	apiConf := s.Api.Mapping["dialogue_llm"]
	if apiConf == nil {
		s.log.WithContext(ctx).Errorf("Mapping-Empty %")
		return
	}
	var (
		targetUrl     = apiConf.Url
		tmpId         = s.thirdConf.MathAgent.MathAgentTmpId
		questionParam string
		nickName      string
	)

	if request.rag == "" {
		questionParam = fmt.Sprintf("【题干】%s", request.question)
	} else {
		questionParam = fmt.Sprintf("【题干】%s\n%s", request.question, request.rag)
	}

	nickName = s.FetchUserNickname(ctx, request.talId)

	llmHistory := make([]dto.LlmHistoryMessage, 0)
	if request.historyList != nil {
		for _, h := range request.historyList {
			llmHistory = append(llmHistory, dto.LlmHistoryMessage{
				Role:    h.Role,
				Content: h.Content,
			})
		}
	}

	gptReqParam := dto.LlmServiceCommonChatReq{
		Biz:   1,
		TmpId: tmpId,
		Args: []string{
			questionParam,
			nickName,
		},
		Message:    request.message,
		SessionId:  request.sessionId,
		History:    llmHistory,
		MsgNoCheck: true, // 入参在nlu接口已经走过风控，调模型不需重复
	}
	body, err := json.Marshal(gptReqParam)

	// 获取所有的请求头部信息
	headerMap := map[string]string{
		"X-Genie-TraceId": request.sessionId,
		"X-Genie-AppId":   s.thirdConf.LlmPrompt.AppId,
		"Traceparent":     fmt.Sprintf("00-%s-%s-01", custom_context.GetTraceId(ctx), trace.SpanFromContext(ctx).SpanContext().SpanID().String()),
	}
	c := sse.NewClient(targetUrl, time.Second*time.Duration(apiConf.Timeout))
	eventChan, err := c.Send(ctx, sse.PostMethod, headerMap, body)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Send-err %+v, body %s", err, string(body))
		return
	} else {
		s.log.WithContext(ctx).Infof("mathExplainAgent req body: %s", string(body))
	}

	validMathChan = make(chan bool)
	resChan = make(chan dto.CommonSseOutputRes)
	go func() {
		defer func() {
			close(validMathChan)
			close(resChan)
			if err := recover(); err != nil {
				errorMsg := fmt.Sprintf("go routine panic错误：%v\n %s", err, debug.Stack())
				s.log.WithContext(ctx).Error(errorMsg)
				return
			}
		}()

		var (
			sseRes      dto.SseRes
			validMath   bool
			preReplyBuf string
			sugStart    bool
			sugBuffer   string
			sugList     = make([]string, 0)
			sugRawResp  string
			replyBuffer string
		)

		for data := range eventChan {

			if strings.HasPrefix(data.Data, "data:") {

				eventData := strings.TrimPrefix(data.Data, "data:")
				if err := json.Unmarshal([]byte(eventData), &sseRes); err != nil {
					s.log.WithContext(ctx).Errorf("mathExplainAgent Unmarshal err: %v; eventData: %v", err, eventData)
					break
				}

				if sseRes.ErrorCode != 0 {
					// 超时屏蔽
					if sseRes.ErrorCode == 150000 || sseRes.ErrorCode == 120014 {
						s.log.WithContext(ctx).Warnf("mathExplainAgent resp errCode: %s", sseRes.ErrorCode)
					} else {
						s.log.WithContext(ctx).Errorf("mathExplainAgent resp errCode: %s", sseRes.ErrorCode)
					}
					continue
				}

				s.log.WithContext(ctx).Infof("mathExplainAgent resp data %s", data.Data)

				if !validMath {
					preReplyBuf += sseRes.Result
				}

				if strings.Contains(preReplyBuf, "是否是题目：否") {
					resChan <- dto.CommonSseOutputRes{
						ErrorCode:    -1,
						ErrorMsg:     "QWen不是题目",
						TraceId:      sseRes.TraceId,
						Created:      sseRes.Created,
						SentenceId:   sseRes.SentenceId,
						IsEnd:        true,
						Result:       "",
						IsReplyStart: true,
						IsReplyEnd:   true,
						Source:       FormatLlmModel(sseRes.Model),
						Sug:          sugList,
						RawResult:    preReplyBuf,
					}
					validMathChan <- false

					return
				} else if strings.Contains(preReplyBuf, "回复：") && !validMath {
					validMath = true
					lineMsg := strings.Split(preReplyBuf, "回复：")[1]
					resChan <- dto.CommonSseOutputRes{
						ErrorCode:    sseRes.ErrorCode,
						ErrorMsg:     sseRes.ErrorMsg,
						TraceId:      sseRes.TraceId,
						Created:      sseRes.Created,
						SentenceId:   sseRes.SentenceId,
						IsEnd:        sseRes.IsEnd,
						Result:       lineMsg,
						IsReplyStart: true,
						IsReplyEnd:   false,
						Source:       FormatLlmModel(sseRes.Model),
						Sug:          sugList,
						RawResult:    preReplyBuf,
					}
					validMathChan <- true
					continue
				}

				if !validMath {
					continue
				}

				if sugStart {
					sugBuffer += sseRes.Result
					sugRawResp += sseRes.Result

					if sseRes.IsEnd {

						re := regexp.MustCompile(`1\.|2\.|3\.`)
						// 使用正则表达式分割字符串
						sugs := re.Split(sugBuffer, -1)
						for index, sug := range sugs {
							if index > 0 {
								sugList = append(sugList, strings.TrimSpace(sug))
							}
						}
						resChan <- dto.CommonSseOutputRes{
							ErrorCode:    sseRes.ErrorCode,
							ErrorMsg:     sseRes.ErrorMsg,
							TraceId:      sseRes.TraceId,
							Created:      sseRes.Created,
							SentenceId:   sseRes.SentenceId,
							IsEnd:        sseRes.IsEnd,
							Result:       "",
							IsReplyStart: false,
							IsReplyEnd:   false,
							Source:       FormatLlmModel(sseRes.Model),
							Sug:          sugList,
							RawResult:    sugRawResp,
						}
					}

					continue
				}

				replyBuffer += sseRes.Result
				replyArr := splitChineseText(replyBuffer)
				var lastSentence string
				if len(replyArr) > 0 {
					lastSentence = strings.TrimSpace(replyArr[len(replyArr)-1])
				}

				if endsWithPunctuation(lastSentence) {
					replyBuffer = ""
				} else {
					if len(replyArr) > 1 {
						replyArr = replyArr[:len(replyArr)-1]
					} else {
						replyArr = []string{}
					}
					replyBuffer = lastSentence
				}

				for index, reply := range replyArr {
					if sugStart {
						sugBuffer += reply
						if index == len(replyArr)-1 {
							sugBuffer += replyBuffer
						}
					} else if strings.Contains(reply, "预测学生下") {
						sugBuffer += reply
						if index == len(replyArr)-1 {
							sugBuffer += replyBuffer
						}
						sugStart = true
						resChan <- dto.CommonSseOutputRes{
							ErrorCode:    sseRes.ErrorCode,
							ErrorMsg:     sseRes.ErrorMsg,
							TraceId:      sseRes.TraceId,
							Created:      sseRes.Created,
							SentenceId:   sseRes.SentenceId,
							IsEnd:        sseRes.IsEnd,
							Result:       "",
							IsReplyStart: false,
							IsReplyEnd:   true,
							Source:       FormatLlmModel(sseRes.Model),
							Sug:          sugList,
							RawResult:    reply + replyBuffer,
						}

						if request.videoUrl != "" {
							if strings.HasPrefix(request.videoUrl, "http:") {
								request.videoUrl = strings.Replace(request.videoUrl, "http:", "https:", 1)
							}
							linkParam := struct {
								SessionId string `json:"session_id"`
								VideoURL  string `json:"video_url"`
							}{
								SessionId: request.sessionId,
								VideoURL:  request.videoUrl,
							}
							jsonData, _ := json.Marshal(linkParam)
							encodedData := base64.StdEncoding.EncodeToString(jsonData)
							resChan <- dto.CommonSseOutputRes{
								ErrorCode:  sseRes.ErrorCode,
								ErrorMsg:   sseRes.ErrorMsg,
								TraceId:    sseRes.TraceId,
								Created:    sseRes.Created,
								SentenceId: sseRes.SentenceId,
								IsEnd:      sseRes.IsEnd,
								Result:     "",
								Media: dto.Media{
									Type:       3,
									Cover:      s.thirdConf.MathAgent.PaiImageUrl,
									Url:        fmt.Sprintf(s.thirdConf.MathAgent.SuishiwenH5Url, encodedData),
									CoverTitle: request.outputTitle,
								},
								ResType:      1,
								IsReplyStart: false,
								IsReplyEnd:   false,
								Source:       FormatLlmModel(sseRes.Model),
								Sug:          sugList,
								RawResult:    "",
							}
						}
					} else {
						resChan <- dto.CommonSseOutputRes{
							ErrorCode:    sseRes.ErrorCode,
							ErrorMsg:     sseRes.ErrorMsg,
							TraceId:      sseRes.TraceId,
							Created:      sseRes.Created,
							SentenceId:   sseRes.SentenceId,
							IsEnd:        sseRes.IsEnd,
							Result:       reply,
							IsReplyStart: false,
							IsReplyEnd:   sseRes.IsEnd && index == len(replyArr)-1,
							Source:       FormatLlmModel(sseRes.Model),
							Sug:          sugList,
							RawResult:    reply,
						}
					}
				}
			}
		}
	}()

	return
}

func (s *LuiService) requestMathExplainAgentByGpt4(ctx context.Context, request MathAgentMethodRequest) (validMathChan chan bool, resChan chan dto.CommonSseOutputRes) {
	s.log.WithContext(ctx).Infof("requestMathExplainAgent request: %+v", request)
	// 获取所有的请求头部信息
	headerMap := make(map[string]string)
	headerMap["X-Genie-TraceId"] = request.sessionId
	headerMap["X-Genie-AppId"] = s.thirdConf.LlmPrompt.AppId
	apiConf := s.Api.Mapping["dialogue_llm"]
	if apiConf == nil {
		s.log.WithContext(ctx).Errorf("Mapping-Empty %+v", headerMap)
		return
	}
	var (
		targetUrl     = apiConf.Url
		tmpId         = s.thirdConf.MathAgent.MathAgentTmpId
		questionParam string
	)

	if request.rag == "" {
		questionParam = fmt.Sprintf("【题干】%s", request.question)
	} else {
		questionParam = fmt.Sprintf("【题干】%s; %s", request.question, request.rag)
	}

	llmHistory := make([]dto.LlmHistoryMessage, 0)
	if request.historyList != nil {
		for _, h := range request.historyList {
			llmHistory = append(llmHistory, dto.LlmHistoryMessage{
				Role:    h.Role,
				Content: h.Content,
			})
		}
	}

	gptReqParam := dto.LlmServiceCommonChatReq{
		Biz:   1,
		TmpId: tmpId,
		Args: []string{
			questionParam,
			questionParam,
		},
		Message:    request.message,
		SessionId:  request.sessionId,
		History:    llmHistory,
		MsgNoCheck: true, // 入参在nlu接口已经走过风控，调模型不需重复
	}

	body, err := json.Marshal(gptReqParam)

	c := sse.NewClient(targetUrl, time.Second*time.Duration(apiConf.Timeout))
	eventChan, err := c.Send(ctx, sse.PostMethod, headerMap, body)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Send-err %+v, body %s", err, string(body))
		return
	} else {
		s.log.WithContext(ctx).Infof("mathExplainAgent req body: %s", string(body))
	}

	validMathChan = make(chan bool)
	resChan = make(chan dto.CommonSseOutputRes)
	go func() {
		defer func() {
			close(validMathChan)
			close(resChan)
			if err := recover(); err != nil {
				errorMsg := fmt.Sprintf("go routine panic错误：%v\n %s", err, debug.Stack())
				s.log.WithContext(ctx).Error(errorMsg)
				return
			}
		}()

		var (
			sseRes     dto.SseRes
			validMath  bool
			dataBuf    string
			sugStart   bool
			sugBuffer  string
			sugList    = make([]string, 0)
			sugRawResp string
		)

		for data := range eventChan {

			if strings.HasPrefix(data.Data, "data:") {
				s.log.WithContext(ctx).Infof("mathExplainAgent resp data %s", data.Data)
				eventData := strings.TrimPrefix(data.Data, "data:")
				if err := json.Unmarshal([]byte(eventData), &sseRes); err != nil {
					break
				}

				if sseRes.ErrorCode != 0 {
					s.log.WithContext(ctx).Warnf("mathExplainAgent resp errCode %s", sseRes.ErrorCode)
					continue
				}

				dataBuf += sseRes.Result
				if strings.Contains(dataBuf, "是否是题目：否") {
					resChan <- dto.CommonSseOutputRes{
						ErrorCode:    -1,
						ErrorMsg:     "Gpt4不是题目",
						TraceId:      sseRes.TraceId,
						Created:      sseRes.Created,
						SentenceId:   sseRes.SentenceId,
						IsEnd:        true,
						Result:       "",
						IsReplyStart: true,
						IsReplyEnd:   true,
						Source:       FormatLlmModel(sseRes.Model),
						Sug:          sugList,
					}
					validMathChan <- false
					return
				} else if strings.Contains(dataBuf, "回复：") && !validMath {
					validMath = true
					lineMsg := strings.Split(dataBuf, "回复：")[1]
					resChan <- dto.CommonSseOutputRes{
						ErrorCode:    sseRes.ErrorCode,
						ErrorMsg:     sseRes.ErrorMsg,
						TraceId:      sseRes.TraceId,
						Created:      sseRes.Created,
						SentenceId:   sseRes.SentenceId,
						IsEnd:        sseRes.IsEnd,
						Result:       lineMsg,
						IsReplyStart: true,
						IsReplyEnd:   false,
						Source:       FormatLlmModel(sseRes.Model),
						Sug:          sugList,
						RawResult:    dataBuf,
					}
					validMathChan <- true
					continue
				}

				if !validMath {
					continue
				}
				if strings.Contains(sseRes.Result, "预测的问题：") {
					sugStart = true

					resChan <- dto.CommonSseOutputRes{
						ErrorCode:    sseRes.ErrorCode,
						ErrorMsg:     sseRes.ErrorMsg,
						TraceId:      sseRes.TraceId,
						Created:      sseRes.Created,
						SentenceId:   sseRes.SentenceId,
						IsEnd:        sseRes.IsEnd,
						Result:       "",
						IsReplyStart: false,
						IsReplyEnd:   true,
						Source:       FormatLlmModel(sseRes.Model),
						Sug:          sugList,
						RawResult:    sseRes.Result,
					}

					if request.videoUrl != "" {

						type H5LinkParam struct {
							SessionId string `json:"session_id"`
							VideoURL  string `json:"video_url"`
						}
						linkParam := H5LinkParam{
							VideoURL:  request.videoUrl,
							SessionId: request.sessionId,
						}
						jsonData, _ := json.Marshal(linkParam)
						encodedData := base64.StdEncoding.EncodeToString(jsonData)
						resChan <- dto.CommonSseOutputRes{
							ErrorCode:  sseRes.ErrorCode,
							ErrorMsg:   sseRes.ErrorMsg,
							TraceId:    sseRes.TraceId,
							Created:    sseRes.Created,
							SentenceId: sseRes.SentenceId,
							IsEnd:      sseRes.IsEnd,
							Result:     "",
							Media: dto.Media{
								Type:       3,
								Cover:      s.thirdConf.MathAgent.PaiImageUrl,
								Url:        fmt.Sprintf(s.thirdConf.MathAgent.SuishiwenH5Url, encodedData),
								CoverTitle: request.outputTitle,
							},
							ResType:      1,
							IsReplyStart: false,
							IsReplyEnd:   false,
							Source:       FormatLlmModel(sseRes.Model),
							Sug:          sugList,
						}
					}

					continue
				}

				if sugStart {
					if !sseRes.IsEnd {
						sugBuffer += sseRes.Result
						sugRawResp += sseRes.Result
						var sugSplitArr []string
						if strings.Contains(sugBuffer, "\n") {
							sugSplitArr = strings.Split(sugBuffer, "\n")
						} else if strings.Contains(sugBuffer, "\\n") {
							sugSplitArr = strings.Split(sugBuffer, "\\n")
						}

						if len(sugSplitArr) > 1 {
							if len(sugSplitArr[0]) > 0 {
								sugList = append(sugList, sugSplitArr[0])
							}
							sugBuffer = sugSplitArr[1]
						} else {
							if len(sugBuffer) > 0 {
								sugList = append(sugList, sugBuffer)
								sugBuffer = ""
							}
						}
					} else {
						resChan <- dto.CommonSseOutputRes{
							ErrorCode:    sseRes.ErrorCode,
							ErrorMsg:     sseRes.ErrorMsg,
							TraceId:      sseRes.TraceId,
							Created:      sseRes.Created,
							SentenceId:   sseRes.SentenceId,
							IsEnd:        sseRes.IsEnd,
							Result:       "",
							IsReplyStart: false,
							IsReplyEnd:   false,
							Source:       FormatLlmModel(sseRes.Model),
							Sug:          sugList,
							RawResult:    sugRawResp,
						}
					}

				} else {
					resChan <- dto.CommonSseOutputRes{
						ErrorCode:    sseRes.ErrorCode,
						ErrorMsg:     sseRes.ErrorMsg,
						TraceId:      sseRes.TraceId,
						Created:      sseRes.Created,
						SentenceId:   sseRes.SentenceId,
						IsEnd:        sseRes.IsEnd,
						Result:       sseRes.Result,
						IsReplyStart: false,
						IsReplyEnd:   sseRes.IsEnd,
						Source:       FormatLlmModel(sseRes.Model),
						Sug:          sugList,
						RawResult:    sseRes.Result,
					}
				}
			}
		}
	}()

	return
}

// QueryMathSceneNlu 查题意图
func (s *LuiService) QueryMathSceneNlu(ctx context.Context, req *v1.LuiNluRequest) (isMath bool, resp *dto.QueryPad2Resp, nluData *dto.NLUQueryPad2Resp) {

	nluQuery := &dto.NLUQuery{
		RequestID: req.RequestId,
		AsrInput: dto.AsrInput{
			AsrInfo:   req.AsrInfo,
			AsrLen:    len(req.AsrInfo),
			AsrPinyin: req.AsrPinyin,
		},
		UserSystem: dto.UserSystemV2{
			Location: req.Location,
			Grade:    cast.ToInt(req.GradeId),
			Semester: dto.GetSemester(),
			TalId:    req.TalId,
			DeviceId: custom_context.GetXDeviceId(ctx),
		},
		SlotFillList: nil,
		Source:       "pad2",
		Version:      custom_context.GetXVersion(ctx),
		LlmAgent: dto.LlmAgent{
			Name:   common.SkillSearchQuestion.ToString(),
			Status: 0,
		},
	}
	controllerResp, err := s.bizNlu.ControllerCall(ctx, nluQuery)
	if err != nil {
		return
	}

	if controllerResp.Data.Intent == "查题" {

		return true, &dto.QueryPad2Resp{

				TtsShow: "",
				TtsNorm: "",
				Data: []*dto.Pad2SkillData{
					{
						Skill: common.SkillSearchQuestion.ToString(),
						Data: &dto.NLUSkillItem{
							Task:           "",
							TtsShow:        "",
							TtsNorm:        "",
							Count:          0,
							AsrInfo:        req.AsrInfo,
							SessionId:      req.RequestId,
							SentenceId:     req.SentenceId,
							SceneMode:      dto.SceneModeFullView,
							IsAccessingLLM: true,
							IsResultValid:  true,
						},
					},
				},
				ModelOutputIntent: controllerResp.Data.Intent,
				RejectRecType:     4,
			}, &dto.NLUQueryPad2Resp{
				FunctionList: controllerResp.Data.FunctionCall.Functions,
			}
	}

	return
}

func ConvertHTTPHeaderToKratosHeader(ctx context.Context, httpHeader http.Header) transport.Header {
	if kratosHeader, ok := transport.FromServerContext(ctx); ok {
		for key, values := range httpHeader {
			if len(values) > 0 {
				kratosHeader.RequestHeader().Set(key, values[0])
			}
		}
		return kratosHeader.RequestHeader()
	}

	return nil
}

type MathExplainAgentRequest struct {
	Entrance   int32  `json:"entrance"`
	ImageUrl   string `json:"image_url"`
	Text       string `json:"text"`
	Query      string `json:"query"`
	SessionId  string `json:"session_id"`
	SentenceId int32  `json:"sentence_id"`
	Continuous bool   `json:"continuous"`
	Uid        string `json:"uid"`
}

func (s *LuiService) UploadMathAgentHistory(ctx context.Context, reqParam MathExplainAgentRequest, paiVideoUrl string, allMathAgentMsgData, rawMathAgentMsgData string, mathUuid string, mediaMsg *dto.Media) {
	s.log.WithContext(ctx).Infof("reqParam: %+v; paiVideoUrl: %s; allMathAgentMsgData: %s; rawMathAgentMsgData: %s; mathUuid: %s", reqParam, paiVideoUrl, allMathAgentMsgData, rawMathAgentMsgData, mathUuid)
	// 上报历史记录
	sync.Go(ctx, s.log, func() {
		var err error
		if reqParam.Entrance != 2 {
			// 新增
			err = s.memoryBiz.AddMemoryContext(context.TODO(), reqParam.Uid, reqParam.SessionId, dto.MemoryContext{
				SessionId:   reqParam.SessionId,
				TimeStamp:   time.Now().UnixMilli(),
				AsrInfo:     reqParam.Text,
				ImageUrl:    reqParam.ImageUrl,
				VideoUrl:    paiVideoUrl,
				IsLlm:       true,
				LlmSkill:    common.SkillSearchQuestion.ToString(),
				Response:    []string{allMathAgentMsgData},
				LlmResponse: rawMathAgentMsgData,
				DialogueId:  mathUuid,
				Media:       mediaMsg,
			})
		} else {
			// 更新
			err = s.memoryBiz.UpdateMemoryContextLlmResponse(reqParam.SessionId, dto.UpdateMemoryContextLlmResponse{
				ImageUrl:    reqParam.ImageUrl,
				VideoUrl:    paiVideoUrl,
				LlmSkill:    common.SkillSearchQuestion.ToString(),
				LlmModel:    "",
				Response:    allMathAgentMsgData,
				LlmResponse: rawMathAgentMsgData,
				DialogueId:  mathUuid,
				Media:       mediaMsg,
			})
		}

		if err != nil {
			s.log.WithContext(ctx).Errorf("AddMemoryContext err: %v", err)
		}
	})
}

// DefaultSearchQuestionSkill 返回随时问app
func (s *LuiService) DefaultSearchQuestionSkill(_ context.Context) dto.QueryPad2Resp {
	skill := dto.NLUSkillItem{
		Skill:         "functional",
		Task:          "functional.application",
		TtsNorm:       "",
		TtsShow:       "",
		IsResultValid: true,
		Command: &dto.CommendTmp{
			API: "functional.application",
			Param: dto.CommendParamTmp{
				Appid:       "1025",
				Method:      "rule",
				Operate:     "open",
				PackageName: "com.tal.pad.cameralesson",
				Value:       "教辅答疑",
				Scheme:      "tal://cameralesson/main",
			},
		},
	}

	return dto.QueryPad2Resp{

		TtsShow: "",
		TtsNorm: "",
		Data: []*dto.Pad2SkillData{
			{
				Skill: common.SkillSearchQuestion.ToString(),
				Data:  &skill,
			},
		},
		ModelOutputIntent: "应用打开关闭",
		RejectRecType:     4,
	}
}

// FetchUserNickname 用户昵称
func (s *LuiService) FetchUserNickname(ctx context.Context, talId string) string {
	userInfoParse := &biz.UserInfoParse{
		ClientID: "552403",
		TalID:    talId,
	}
	userProfile, err := s.uCenterBiz.GetUserProfile(ctx, userInfoParse)
	if err != nil {
		return ""
	}

	return userProfile.Nickname
}
