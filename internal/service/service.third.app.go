package service

import "lui-api/internal/data/dto"

func (s *LuiService) GetRealResourceList(resourceList []dto.Resource) ([]dto.Resource, string) {
	var realResourceList []dto.Resource
	for i, resource := range resourceList {
		if scheme, ok := s.thirdConf.MobbyScheme[resource.ResourceId]; ok {
			if i == 0 {
				return nil, scheme
			}
		} else {
			realResourceList = append(realResourceList, resourceList[i])
		}
	}
	return realResourceList, ""
}
