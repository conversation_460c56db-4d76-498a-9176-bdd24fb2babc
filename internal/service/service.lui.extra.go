package service

import (
	"context"
	v1 "lui-api/api/lui/v1"
	"lui-api/internal/common"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	pkgSync "lui-api/internal/pkg/sync"
	"lui-api/internal/pkg/utils"
	"lui-api/pkg/util"
	"strings"
	"time"

	"github.com/mitchellh/mapstructure"
	"google.golang.org/protobuf/types/known/structpb"
)

func (s *LuiService) HandleDeepSeek(ctx context.Context, req *v1.LuiNluRequest, nlu4lui *dto.NLUQueryPad2Resp) (*structpb.Struct, error) {
	// 是否在深度思考模式中
	if nlu4lui != nil && nlu4lui.DeepSeekInfo != nil {
		if !nlu4lui.DeepSeekInfo.DeepSeekFlag { // 意图识别不在白名单中返回兜底
			deepSeekResp, _ := s.DeepseekUnderNluResp(ctx, req)

			pkgSync.Go(ctx, s.log, func() {
				_ = s.memoryBiz.AddXPadMemoryContext(context.TODO(), dto.MemoryContextReq{
					UserTalID: req.TalId,
					SessionId: req.RequestId,
					AsrInfo:   req.AsrInfo,
				}, deepSeekResp, nil)
			})
			s.log.WithContext(ctx).Infof("DeepseekUnderNluResp: %v", util.Marshal(deepSeekResp))

			return deepSeekResp, nil
		}

		_ = s.memoryBiz.SetDeepSeekFlag(ctx, req.TalId)
	} else {
		deepSeekFlag := s.memoryBiz.GetDeepSeekFlag(ctx, req.TalId)
		if deepSeekFlag == "" {
			return nil, nil
		}
	}

	// 构建深度思考的NLU响应
	deepSeekResp, _ := s.DeepseekNluResp(ctx, req)
	if deepSeekResp == nil {
		return nil, nil
	}

	// 保存深度思考的上下文
	pkgSync.Go(ctx, s.log, func() {
		_ = s.memoryBiz.AddXPadMemoryContext(context.TODO(), dto.MemoryContextReq{
			UserTalID: req.TalId,
			SessionId: req.RequestId,
			AsrInfo:   req.AsrInfo,
		}, deepSeekResp, nlu4lui)
	})

	s.log.WithContext(ctx).Infof("DeepseekNluResp: %v", util.Marshal(deepSeekResp))

	return deepSeekResp, nil
}

func (s *LuiService) DeepseekNluResp(ctx context.Context, req *v1.LuiNluRequest) (*structpb.Struct, error) {
	aiAgent := common.AiAgentDeepseek
	if req.From == common.SuggestExercisesTool {
		aiAgent = common.AiAgentDeepseekExercises
	}

	res := &dto.QueryPad2Resp{
		TtsShow: "",
		TtsNorm: "",
		Data: []*dto.Pad2SkillData{
			{
				Skill: common.SkillDeepseek.ToString(),
				Data: &dto.NLUSkillItem{
					Skill:          common.SkillDeepseek.ToString(),
					Task:           "",
					TtsShow:        "",
					TtsNorm:        "",
					Count:          0,
					AsrInfo:        req.AsrInfo,
					SessionId:      req.RequestId,
					SentenceId:     req.SentenceId,
					SceneMode:      dto.SceneModeFullView,
					AiAgent:        aiAgent,
					IsAccessingLLM: true,
					IsResultValid:  true,
				},
			},
		},
		ModelOutputIntent: "百科问答",
	}
	resp, _ := util.ReplyAny(res)

	return resp, nil
}

func (s *LuiService) DeepseekUnderNluResp(ctx context.Context, req *v1.LuiNluRequest) (*structpb.Struct, error) {
	tts := "神秘能力即将来袭，惊喜回复敬请期待～"

	res := &dto.QueryPad2Resp{
		TtsShow: tts,
		TtsNorm: tts,
		Data: []*dto.Pad2SkillData{
			{
				Skill: "no_task",
				Data: &dto.NLUSkillItem{
					Task:          "",
					TtsShow:       tts,
					TtsNorm:       tts,
					Count:         0,
					AsrInfo:       req.AsrInfo,
					SessionId:     req.RequestId,
					SentenceId:    req.SentenceId,
					IsResultValid: true,
				},
			},
		},
		ModelOutputIntent: "百科问答",
	}
	res.BizType = req.BizType
	resp, _ := util.ReplyAny(res)

	return resp, nil
}

func (s *LuiService) UnderScreenExercises(ctx context.Context, req *v1.LuiNluRequest, nlu4lui *dto.NLUQueryPad2Resp) (*structpb.Struct, error) {
	tts := "小思即将为你查询这道题"

	pad2SkillData := []*dto.Pad2SkillData{
		{
			Skill: common.SkillExercisesTool.ToString(),
			Data: &dto.NLUSkillItem{
				Skill:         common.SkillExercisesTool.ToString(),
				Task:          "",
				TtsShow:       tts,
				TtsNorm:       tts,
				Count:         0,
				AsrInfo:       req.AsrInfo,
				SessionId:     req.RequestId,
				SentenceId:    req.SentenceId,
				IsResultValid: true,
				MixedModalData: map[string]interface{}{
					"image_url":              utils.ReplaceURLDomain(nlu4lui.ExtraResp.MultiModalInfo.ImageURL),
					"trace_box":              nlu4lui.ExtraResp.MultiModalInfo.TraceBox,
					"under_screen_exercises": true,
				},
			},
		},
	}

	var (
		showType  int
		dispatch  *dto.Dispatch
		sceneCode = s.bizNlu.GetSceneCode(ctx, req.BizType)
	)

	if strings.Contains(s.thirdConf.AppIdMap["xPad25AppIds"], custom_context.GetXAppId(ctx)) {
		dispatch, showType = s.rnBiz.GetSetDispatch(ctx, sceneCode, req.RnVersion, req.BizType, pad2SkillData)
	}

	res := &dto.QueryPad2Resp{
		TtsShow:           tts,
		TtsNorm:           tts,
		Data:              pad2SkillData,
		ModelOutputIntent: "查题",
		Dispatch:          dispatch,
		ShowType:          showType,
	}

	resp, _ := util.ReplyAny(res)

	return resp, nil
}

func (s *LuiService) HandleWorkshop(ctx context.Context, req *v1.LuiNluRequest, nlu4lui *dto.NLUQueryPad2Resp) (*structpb.Struct, error) {
	if nlu4lui == nil || len(nlu4lui.SkillList) == 0 || len(nlu4lui.SkillList) > 1 {
		return nil, nil
	}

	if req.BizType == common.BizTypeWorkshop {
		return nil, nil
	}

	command := nlu4lui.SkillList[0].Command
	var commandTmp dto.CommendTmp
	_ = mapstructure.Decode(command, &commandTmp)
	if commandTmp.API != "functional.application" || commandTmp.Param.Operate != "open" || commandTmp.Param.Value != "小思工作坊" {
		return nil, nil
	}

	// 设置进入小思工作坊标识
	// _ = s.memoryBiz.SetWorkshopFlag(ctx, req.TalId)

	tts := "小思工作坊加载完成！"
	res := &dto.QueryPad2Resp{
		TtsShow: tts,
		TtsNorm: tts,
		Data: []*dto.Pad2SkillData{
			{
				Skill: nlu4lui.SkillList[0].Skill,
				Data: &dto.NLUSkillItem{
					Skill:         nlu4lui.SkillList[0].Skill,
					Task:          nlu4lui.SkillList[0].Task,
					TtsShow:       tts,
					TtsNorm:       tts,
					AsrInfo:       req.AsrInfo,
					SessionId:     req.RequestId,
					SentenceId:    req.SentenceId,
					Command:       command,
					IsResultValid: true,
				},
			},
		},
		BizType:           req.BizType,
		ModelOutputIntent: nlu4lui.ModelOutputIntent,
	}

	resp, _ := util.ReplyAny(res)
	s.log.WithContext(ctx).Infof("Workshop: %v", util.Marshal(resp))

	return resp, nil
}

func (s *LuiService) HandleWorkshopCreation(ctx context.Context, req *v1.LuiNluRequest, nlu4lui *dto.NLUQueryPad2Resp) (*structpb.Struct, error) {
	if nlu4lui == nil || len(nlu4lui.SkillList) == 0 || len(nlu4lui.SkillList) > 1 {
		return nil, nil
	}

	// workshopFlag := s.memoryBiz.GetWorkshopFlag(ctx, req.TalId)
	// if workshopFlag == "" || req.BizType != common.BizTypeWorkshop {
	// 	return nil, nil
	// }

	if req.BizType != common.BizTypeWorkshop {
		return nil, nil
	}

	noContinueIntents := []string{
		"系统控制",
	}
	if util.SliceContainStr(noContinueIntents, nlu4lui.ModelOutputIntent) {
		// _ = s.memoryBiz.DelWorkshopFlag(ctx, req.TalId)
		return nil, nil
	}

	tts := ""
	res := &dto.QueryPad2Resp{
		TtsShow: tts,
		TtsNorm: tts,
		Data: []*dto.Pad2SkillData{
			{
				Skill: common.SkillWorkshop.ToString(),
				Data: &dto.NLUSkillItem{
					Skill:          common.SkillWorkshop.ToString(),
					Task:           "",
					TtsShow:        tts,
					TtsNorm:        tts,
					Count:          0,
					AsrInfo:        req.AsrInfo,
					SessionId:      req.RequestId,
					SentenceId:     req.SentenceId,
					SceneMode:      dto.SceneModeFullView,
					AiAgent:        common.AiAgentWorkshop,
					IsAccessingLLM: true,
					IsResultValid:  true,
				},
			},
		},
		BizType:           req.BizType,
		ModelOutputIntent: "小思工作坊",
	}

	resp, _ := util.ReplyAny(res)
	s.log.WithContext(ctx).Infof("WorkshopCreation: %v", util.Marshal(resp))

	return resp, nil
}

func (s *LuiService) AtmsExplainNluResp(ctx context.Context, req *v1.LuiNluRequest) (*structpb.Struct, error) {
	aiAgent := common.AiAgentExplainScripts
	atmsExplainType := s.memoryBiz.GetAtmsExplainType(ctx, req.TalId)
	if atmsExplainType == common.AtmsExplainTypeSocrates {
		aiAgent = common.AiAgentExplainSocrates
	}

	res := &dto.QueryPad2Resp{
		TtsShow: "",
		TtsNorm: "",
		Data: []*dto.Pad2SkillData{
			{
				Skill: common.SkillExercisesTool.ToString(),
				Data: &dto.NLUSkillItem{
					Skill:             common.SkillExercisesTool.ToString(),
					Task:              "",
					TtsShow:           "",
					TtsNorm:           "",
					Count:             0,
					AsrInfo:           req.AsrInfo,
					SessionId:         req.RequestId,
					SentenceId:        req.SentenceId,
					SceneMode:         dto.SceneModeFullView,
					AiAgent:           aiAgent,
					IsAccessingLLM:    true,
					IsResultValid:     true,
					AsrEndTimestamp:   req.AsrEndTimestamp,
					AsrStartTimestamp: req.AsrStartTimestamp,
				},
			},
		},
		BizType:           req.BizType,
		ModelOutputIntent: "讲题",
	}
	resp, _ := util.ReplyAny(res)

	return resp, nil
}

// 记录讲题意图的日志
func (s *LuiService) HandleExplainQuestion(ctx context.Context, req *v1.LuiNluRequest, nlu4lui *dto.NLUQueryPad2Resp) error {
	if nlu4lui == nil || len(nlu4lui.SkillList) == 0 || len(nlu4lui.SkillList) > 1 {
		return nil
	}

	exercisesToolSkillMap := map[string]struct{}{
		"exercises_tool":        {},
		"exercises_tool_answer": {},
		"exercises_tool_step":   {},
	}

	skill := nlu4lui.SkillList[0]
	if _, ok := exercisesToolSkillMap[skill.Skill]; !ok {
		return nil
	}

	log := &dto.QuestionExplainLog{
		Timestamp:       time.Now().UnixMilli(),
		BizPrimaryKey:   dto.QuestionExplainLogPrimaryKeyTraceId,
		BizPrimaryValue: req.RequestId,
		Biz:             dto.QuestionQueryLogBiz,
		BizFunc:         dto.QuestionQueryLogBizFunc,
		Message: map[string]interface{}{
			"asr_text":      req.AsrInfo,
			"biz_type":      req.BizType,
			"session_id":    req.RequestId,
			"skill_list":    nlu4lui.SkillList,
			"function_list": nlu4lui.FunctionList,
			"vision_input":  nlu4lui.VisionInput,
		},
	}

	s.log.WithContext(ctx).Info(util.Marshal(log))

	return nil
}

func (s *LuiService) ExitLuiScene(ctx context.Context, req *v1.ExitLuiSceneRequest) (*v1.ExitLuiSceneReply, error) {
	if req.BizType == common.BizTypeDpSkClose {
		err := s.memoryBiz.DelDeepSeekFlag(ctx, req.TalId)
		if err != nil {
			s.log.WithContext(ctx).Warnf("[ExitLuiScene] req:%v, DelDeepSeekFlag is err : %v", req.String(), err)
		}
		s.log.WithContext(ctx).Infof("[ExitLuiScene] req:%v, DelDeepSeekFlag is success", req.String())
	}
	return &v1.ExitLuiSceneReply{}, nil
}
