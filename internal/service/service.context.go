package service

import (
	"context"
	"encoding/json"
	v1 "lui-api/api/lui/v1"
	"lui-api/internal/common"
	"lui-api/internal/data/dto"
	"lui-api/pkg/util"

	"github.com/spf13/cast"
	"google.golang.org/protobuf/types/known/structpb"
)

func (s *LuiService) MemoryContext(ctx context.Context, req *v1.MemoryContextRequest) (*v1.MemoryContextReply, error) {
	if req.TalId == "" {
		return &v1.MemoryContextReply{}, nil
	}

	ctx = dto.SetCtxSceneCode(ctx, req.BizType)
	memoryContext, err := s.memoryBiz.GetMemoryContext(ctx, req.TalId, dto.LuiPad2MaxLen)
	if err != nil {
		return nil, err
	}

	list := make([]*v1.MemoryContextReply_MemoryContextItem, 0)
	for _, v := range memoryContext {
		response := ""
		if v.Response != nil && v.LlmSkill != dto.MemoryContextRnCardType {
			responseByte, _ := json.Marshal(v.Response)
			response = string(responseByte)
		}

		item := &v1.MemoryContextReply_MemoryContextItem{
			TimeStamp:    v.TimeStamp,
			Intent:       v.Intent,
			AsrInfo:      v.AsrInfo,
			TtsInfo:      v.TtsInfo,
			TtsNorm:      v.TtsNorm,
			Response:     response,
			RewriteQuery: v.RewriteQuery,
		}

		list = append(list, item)
	}

	reply := &v1.MemoryContextReply{
		List: list,
	}

	return reply, nil
}

// FullViewContext
func (s *LuiService) FullViewContext(ctx context.Context, req *v1.FullViewContextRequest) (*v1.FullViewContextReply, error) {
	if req.TalId == "" {
		return &v1.FullViewContextReply{}, nil
	}

	ctx = dto.SetCtxSceneCode(ctx, req.BizType)
	limit := dto.LuiPad2MaxLen
	if req.BizType == common.BizTypeAiTutorLearn {
		limit = 1
	}
	memoryContext, err := s.memoryBiz.GetMemoryContext(ctx, req.TalId, limit)
	if err != nil {
		return nil, err
	}

	list := make([]*v1.FullViewContextReply_FullViewContextItem, 0)
	var rnCardNum int
	for i := len(memoryContext) - 1; i >= 0; i-- {
		v := memoryContext[i]
		if v.AsrInfo == "" && v.LlmSkill != dto.MemoryContextXiaoSiCommandLLmSkill {
			continue
		}

		if req.BizType == common.BizTypeAiTutorNormal && v.Intent == common.IntentClarification {
			continue
		}

		var response string
		var rnResponse *structpb.Struct
		if v.IsLlm || v.Intent == common.IntentKnowledge {
			v.TtsInfo = ""
			v.TtsNorm = ""
			responseSlice, ok := v.Response.([]interface{})
			if ok && len(responseSlice) > 0 {
				response = responseSlice[0].(string)
			}
		} else if v.LlmSkill == dto.MemoryContextRnCardType {
			// rn卡片类型的元素，取后5个
			if rnCardNum >= 5 {
				continue
			}
			bt, _ := json.Marshal(v.Response)
			var rnr dto.QueryPad2RespSimple
			_ = json.Unmarshal(bt, &rnr)

			rnResponse, _ = util.ReplyAny(rnr)
			rnCardNum++
		}

		cardType := dto.GetFullViewCardType(v.LlmSkill)
		if v.Source == dto.ContextSourceKnowledge {
			cardType = dto.GetFullViewCardType(common.Baike.ToString())
		}

		media := &v1.FullViewContextReply_Media{}
		if v.Media != nil {
			media = &v1.FullViewContextReply_Media{
				Type:       cast.ToInt32(v.Media.Type),
				Cover:      v.Media.Cover,
				Url:        v.Media.Url,
				CoverTitle: v.Media.CoverTitle,
			}
		}

		xiaosiCommandList := make([]*v1.FullViewContextReply_XiaosiCommands, 0)
		for _, xiaosiCommandItem := range v.XiaosiCommandList {
			apps := make([]*v1.FullViewContextReply_CommandApp, 0)
			if xiaosiCommandItem.Apps != nil {
				for _, app := range xiaosiCommandItem.Apps {
					appItem := &v1.FullViewContextReply_CommandApp{
						Name: app.Name,
						Icon: app.Icon,
					}
					apps = append(apps, appItem)
				}
			}
			xiaosiCommand := &v1.FullViewContextReply_XiaosiCommands{
				Title: xiaosiCommandItem.Title,
				Query: xiaosiCommandItem.Query,
				Apps:  apps,
			}
			xiaosiCommandList = append(xiaosiCommandList, xiaosiCommand)
		}

		item := &v1.FullViewContextReply_FullViewContextItem{
			SessionId:         v.SessionId,
			AsrInfo:           v.AsrInfo,
			TtsInfo:           v.TtsInfo,
			TtsNorm:           v.TtsNorm,
			Response:          response,
			Source:            "",
			ImageUrl:          v.ImageUrl,
			VideoUrl:          v.VideoUrl,
			IsLlm:             v.IsLlm,
			CardType:          cardType,
			Media:             media,
			XiaosiCommandList: xiaosiCommandList,
			RnResponse:        rnResponse,
			ReasoningContent:  v.ReasoningContent,
		}

		list = append(list, item)
	}

	// 顺序重新转换
	util.ReverseArray(list)

	reply := &v1.FullViewContextReply{
		List: list,
	}

	return reply, nil
}

func (s *LuiService) UpdateMemoryContextLlmResponse(ctx context.Context, data *v1.LlmResponse) (*structpb.Struct, error) {
	req := dto.UpdateMemoryContextLlmResponse{
		LlmSkill:    data.LlmSkill,
		LlmModel:    data.LlmModel,
		Response:    data.Response,
		ImageUrl:    data.ImageUrl,
		VideoUrl:    data.VideoUrl,
		LlmResponse: data.LlmResponse,
		DialogueId:  data.DialogueId,
		Media: &dto.Media{
			Type:       int(data.Type),
			Cover:      data.Cover,
			CoverTitle: data.CoverTitle,
			Url:        data.Url,
		},
		TtsInfo:            data.TtsInfo,
		MixedModalQuery:    data.MixedModalQuery,
		MixedModalResponse: data.MixedModalResponse,
	}

	_ = s.memoryBiz.UpdateMemoryContextLlmResponse(data.SessionId, req)
	res := map[string]string{
		"result": "success",
	}
	return util.ReplyAny(res)
}

// GetLLMMemoryContext
func (s *LuiService) GetLLMMemoryContext(ctx context.Context, req *v1.LLMMemoryContextRequest) (*v1.LLMMemoryContextReply, error) {
	ctx = dto.SetCtxSceneCode(ctx, req.BizType)

	var (
		res []*dto.LlmMemoryContextResp
		err error
	)
	if req.Version == "v2" {
		res, err = s.memoryBiz.GetAgentMemoryContext(ctx, req.CurrentSessionId, req.TalId)
	} else {
		res, err = s.memoryBiz.GetFullViewLlmMemoryContext(ctx, req.CurrentSessionId, req.TalId, req.NeedLlmSkill)
	}
	if err != nil {
		return nil, err
	}

	list := make([]*v1.LLMMemoryContextReply_ContextItem, 0)
	for _, v := range res {
		item := &v1.LLMMemoryContextReply_ContextItem{
			Role:       v.Role,
			Content:    v.Content,
			DialogueId: v.DialogueId,
		}
		list = append(list, item)
	}

	reply := &v1.LLMMemoryContextReply{
		List: list,
	}

	return reply, nil
}

func (s *LuiService) AddLLMMemoryContext(ctx context.Context, req *v1.AddLLMMemoryContextRequest) (*v1.AddLLMMemoryContextReply, error) {

	var asrInfo, llmResponse string
	for _, message := range req.Messages {
		if message.Role == "user" {
			asrInfo = message.Content
		} else if message.Role == "assistant" {
			llmResponse = message.Content
		}
	}
	memoryContext := dto.MemoryContext{
		Intent:      "闲聊",
		SessionId:   req.SessionId,
		IsLlm:       true,
		AsrInfo:     asrInfo,
		Response:    []string{llmResponse},
		LlmResponse: llmResponse,
	}
	err := s.memoryBiz.AddMemoryContext(ctx, req.TalId, req.SessionId, memoryContext)
	if err != nil {
		return nil, err
	}

	return &v1.AddLLMMemoryContextReply{}, nil
}
