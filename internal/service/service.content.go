package service

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cast"
	"lui-api/internal/biz"
	"lui-api/internal/common"
	"lui-api/internal/conf"
	"lui-api/internal/data/dto"
	"lui-api/pkg/util"
	"net/url"
	"sort"
	"strings"
)

type ContentService struct {
	bizBase          *biz.BaseUsecase
	bizSubject       *biz.SubjectUsecase
	bizCultivate     *biz.CultivationUsecase
	bizPaper         *biz.PaperUsecase
	log              *log.Helper
	bizBookReading   *biz.BookReadingUsecase
	bizEmbeddingSync *biz.EmbeddingSyncBiz
	subjectConfig    *conf.SubjectConfig
}

func NewContentService(logger log.Logger, bizBase *biz.BaseUsecase, bizSubject *biz.SubjectUsecase, bizCultivate *biz.CultivationUsecase, bizPaper *biz.PaperUsecase, bizBookReading *biz.BookReadingUsecase, bizEmbeddingSync *biz.EmbeddingSyncBiz, subjectConfig *conf.SubjectConfig) *ContentService {
	return &ContentService{
		log:              log.NewHelper(logger),
		bizBase:          bizBase,
		bizSubject:       bizSubject,
		bizCultivate:     bizCultivate,
		bizPaper:         bizPaper,
		bizBookReading:   bizBookReading,
		bizEmbeddingSync: bizEmbeddingSync,
		subjectConfig:    subjectConfig,
	}
}

func (s *ContentService) buildSubjectSlotLuiNluData(ctx context.Context, originHit bool, slotDict dto.SlotDict) (bool, string, error) {
	//学科必传
	subjectName := slotDict.SubjectName
	if len(subjectName) == 0 || subjectName == "学习广场" {
		switch slotDict.GradeName {
		case "小班", "中班", "大班", "启蒙":
			subjectName = "思维训练"
		default:
			subjectName = "数学"
		}
	}
	switch slotDict.GradeName {
	case "小班", "中班", "大班", "启蒙":
		if subjectName == "数学" {
			subjectName = "思维训练"
		} else if subjectName == "语文" {
			subjectName = "口语启蒙"
		} else if subjectName == "英语" {
			subjectName = "表达启蒙"
		}
	}

	dictMap, err := s.bizBase.SlotDictTransfer(ctx, dto.SlotDict{
		SubjectName:      subjectName,
		GradeName:        slotDict.GradeName,
		SemesterName:     slotDict.SemesterName,
		CourseSystemName: slotDict.CourseSystemName,
		VersionName:      slotDict.VersionName,
	})
	if err != nil {
		return false, "", err
	}

	var hitStatus = false
	// 如果算法返回false，则一定false； 如果true，则需二次判断
	if originHit {
		// 学科在年级下可见逻辑
		visible, err := s.bizBase.VisibleSubject(ctx, dictMap.GradeId, dictMap.SubjectId)
		s.log.WithContext(ctx).Infof("VisibleSubject GradeId: %d; SubjectId: %d; visible: %v", dictMap.GradeId, dictMap.SubjectId, visible)
		if err != nil {
			s.log.WithContext(ctx).Errorf("VisibleSubject err: %+v", err)
		}
		if visible {
			// 如果学科在年级下可见，再查向量库
			hitStatus, err = s.bizEmbeddingSync.HintStatus(ctx, dto.SlotCombineCheckReq{
				Category:       common.Subject,
				GradeId:        dictMap.GradeId,
				SemesterId:     0, // 把学期省掉 query 语文有哪些作文精讲课程 向量库里没上学期的
				SubjectId:      dictMap.SubjectId,
				VersionId:      dictMap.VersionId,
				CourseSystemId: dictMap.CourseSystemId,
			})
			if err != nil {
				s.log.WithContext(ctx).Errorf("HintStatus err: %+v", err)
				hitStatus = true
			}
		}
	}

	schemeType := 1
	columnId := dictMap.SubjectId
	if dictMap.GradeId <= 0 && dictMap.ColumnId > 0 {
		schemeType = 3
		columnId = dictMap.ColumnId
	}
	req := &dto.SlotSchemeReq{
		SchemeType:     schemeType,
		ChannelId:      5,
		ColumnId:       columnId,
		Grade:          dictMap.GradeId,
		SemesterId:     dictMap.SemesterId,
		CourseType:     dictMap.CourseSystemId,
		VersionId:      dictMap.VersionId,
		PlayAfterVoice: 1,
		ChildColumnId:  dictMap.ChildColumnId,
	}
	scheme, err := s.bizSubject.QuerySlotScheme(ctx, req)
	if err != nil {
		return false, "", err
	}
	if !hitStatus && (dictMap.GradeId == 0 || dictMap.SubjectId == 0) {
		hitStatus = true
	}
	return hitStatus, scheme.SchemeUrl, nil
}

func (s *ContentService) buildCultivationSlotLuiNluData(ctx context.Context, originHit bool, slotDict dto.SlotDict) (bool, string, error) {

	//专辑必传
	catalogName := slotDict.CatalogName
	if len(catalogName) == 0 || catalogName == "素养天地" {
		catalogName = "历史文化"
		switch slotDict.GradeName {
		case "小班", "中班", "大班", "启蒙":
			catalogName = "熊猫博士"
		}
	}

	dictMap, err := s.bizBase.SlotDictTransfer(ctx, dto.SlotDict{
		CatalogName:  catalogName,
		GradeName:    slotDict.GradeName,
		SemesterName: slotDict.SemesterName,
	})
	if err != nil {
		return false, "", err
	}

	hitStatus, err := s.bizEmbeddingSync.HintStatus(ctx, dto.SlotCombineCheckReq{
		Category:  common.Cultivation,
		GradeId:   dictMap.GradeId,
		CatalogId: dictMap.CatalogId,
	})
	if err != nil {
		s.log.WithContext(ctx).Errorf("HintStatus err: %+v", err)
		// 兜底 默认true
		hitStatus = true
	}

	schemeType := 1
	columnId := dictMap.CatalogId
	if dictMap.GradeId <= 0 && dictMap.ColumnId > 0 {
		schemeType = 4
		columnId = dictMap.ColumnId
	}
	req := &dto.SlotSchemeReq{
		SchemeType:     schemeType,
		ChannelId:      7,
		ColumnId:       columnId,
		Grade:          dictMap.GradeId,
		SemesterId:     dictMap.SemesterId,
		ChildColumnId:  dictMap.ChildColumnId,
		PlayAfterVoice: 1,
	}
	scheme, err := s.bizSubject.QuerySlotScheme(ctx, req)
	if err != nil {
		return false, "", err
	}

	return hitStatus, scheme.SchemeUrl, nil
}

func (s *ContentService) buildPaperSlotLuiNluData(ctx context.Context, originHit bool, slotDict dto.SlotDict) (bool, string, error) {

	dictMap, err := s.bizBase.SlotDictTransfer(ctx, dto.SlotDict{
		SubjectName:    slotDict.SubjectName,
		GradeName:      slotDict.GradeName,
		SemesterName:   slotDict.SemesterName,
		YearName:       slotDict.YearName,
		PaperTypeName:  slotDict.PaperTypeName,
		CupTypeName:    slotDict.CupTypeName,
		ProvinceName:   slotDict.ProvinceName,
		DifficultyName: slotDict.DifficultyName,
		IsFamousSchool: slotDict.IsFamousSchool,
	})
	if err != nil {
		return false, "", err
	}

	// 学科 要有值，规避用户二次确认瞎说一个非学科名字的情况
	if dictMap.SubjectId == 0 {
		dictMap.SubjectId = 2
	}

	var hitStatus = false
	// 学科在年级下可见逻辑
	visible, err := s.bizBase.VisibleSubject(ctx, dictMap.GradeId, dictMap.SubjectId)
	s.log.WithContext(ctx).Infof("VisibleSubject GradeId: %d; SubjectId: %d; visible: %v", dictMap.GradeId, dictMap.SubjectId, visible)
	if err != nil {
		s.log.WithContext(ctx).Errorf("VisibleSubject err: %+v", err)
	}
	if visible {
		hitStatus, err = s.bizEmbeddingSync.HintStatus(ctx, dto.SlotCombineCheckReq{
			Category:   common.ExamPaper,
			GradeId:    dictMap.GradeId,
			SemesterId: dictMap.SemesterId,
			SubjectId:  dictMap.SubjectId,
		})
		if err != nil {
			s.log.WithContext(ctx).Errorf("HintStatus err: %+v", err)
			hitStatus = true
		}
	}

	req := &dto.SlotSchemeReq{
		SchemeType:     2,
		ChannelId:      6,
		ColumnId:       dictMap.SubjectId,
		Grade:          dictMap.GradeId,
		SemesterId:     dictMap.SemesterId,
		PlayAfterVoice: 0,
		Text: dto.PaperText{
			Entrance:   "",
			Province:   dictMap.ProvinceId,
			PaperType:  dictMap.PaperType,
			PaperScope: dictMap.PaperScope,
			Difficulty: dictMap.Difficulty,
			Years:      dictMap.YearName,
			CupType:    dictMap.CupType,
		},
	}
	scheme, err := s.bizSubject.QuerySlotScheme(ctx, req)
	if err != nil {
		return false, "", err
	}

	return hitStatus, scheme.SchemeUrl, nil
}

func (s *ContentService) buildSubjectClassResourceLuiNluData(ctx context.Context, slot dto.SlotDict, resourceList []dto.Resource) ([]*dto.ResourceSortRespItem, error) {

	courseIds := make([]dto.CourseId, 0)
	classIds := make([]dto.SubjectClassesId, 0)

	for _, resource := range resourceList {
		switch resource.ResourceType {
		case common.Course.ToString(), common.JzxCourse.ToString(), common.JzxV15Course.ToString():
			courseIds = append(courseIds, dto.CourseId{
				Type: resource.ResourceType,
				Id:   resource.ResourceId,
			})
		default:
			classIds = append(classIds, dto.SubjectClassesId{
				Type:     resource.ResourceType,
				CourseId: resource.CourseId,
				NodeId:   resource.ResourceId,
				Limit:    3,
			})
		}
	}

	dictMap, err := s.bizBase.SlotDictTransfer(ctx, dto.SlotDict{
		GradeName:    slot.GradeName,
		SemesterName: slot.SemesterName,
		SubjectName:  slot.SubjectName,
		VersionName:  slot.VersionName,
	})
	if err != nil {
		return nil, err
	}

	req := &dto.SubjectClassesIdQueryReq{
		Category:          common.Subject,
		CourseIds:         courseIds,
		SubjectClassesIds: classIds,
		Grade:             dictMap.GradeId,
		Semester:          dictMap.SemesterId,
		Subject:           dictMap.SubjectId,
		Version:           dictMap.VersionId,
	}

	course, err := s.bizSubject.QuerySubjectClass(ctx, req)
	if err != nil {
		return nil, err
	}
	resp := s.dealSubjectClassSort(ctx, resourceList, course, dictMap)
	return resp, nil
}

func (s *ContentService) buildAttainmentResourceLuiNluData(ctx context.Context, slot dto.SlotDict, resourceList []dto.Resource) ([]*dto.ResourceSortRespItem, error) {

	albumList := make([]dto.Resource, 0)
	attainmentList := make([]dto.Resource, 0)
	for _, resource := range resourceList {
		if resource.ResourceType == common.Attainment.ToString() {
			attainmentList = append(attainmentList, resource)
		} else if resource.ResourceType == common.Album.ToString() {
			albumList = append(albumList, resource)
		}
	}

	albumIds := make([]int, 0)
	for _, album := range albumList {
		albumIds = append(albumIds, cast.ToInt(album.ResourceId))
	}

	attainmentIds := make([]dto.AttainmentId, 0)
	for _, attainment := range attainmentList {
		attainmentIds = append(attainmentIds, dto.AttainmentId{
			AttainmentAlbumId: attainment.AlbumId,
			AttainmentId:      cast.ToInt(attainment.ResourceId),
		})
	}

	dictMap, err := s.bizBase.SlotDictTransfer(ctx, dto.SlotDict{
		GradeName:    slot.GradeName,
		SemesterName: slot.SemesterName,
		SubjectName:  slot.SubjectName,
		VersionName:  slot.VersionName,
	})
	if err != nil {
		return nil, err
	}

	req := &dto.AttainmentAlbumQueryReq{
		Category:           common.Cultivation,
		AttainmentAlbumIds: albumIds,
		AttainmentIds:      attainmentIds,
		Grade:              dictMap.GradeId,
		Semester:           dictMap.SemesterId,
		Subject:            dictMap.SubjectId,
		Version:            dictMap.VersionId,
	}

	attainments, err := s.bizCultivate.QueryAttainmentAlbum(ctx, req)
	if err != nil {
		return nil, err
	}

	resp := s.dealAttainmentSort(ctx, resourceList, attainments, dictMap)
	return resp, nil
}

func (s *ContentService) buildPaperResourceLuiNluData(ctx context.Context, slot dto.SlotDict, resourceList []dto.Resource) ([]*dto.ResourceSortRespItem, error) {

	paperIds := make([]string, 0)

	for _, resource := range resourceList {
		paperIds = append(paperIds, resource.ResourceId)
	}

	dictMap, err := s.bizBase.SlotDictTransfer(ctx, dto.SlotDict{
		GradeName:    slot.GradeName,
		SemesterName: slot.SemesterName,
		SubjectName:  slot.SubjectName,
		VersionName:  slot.VersionName,
	})
	if err != nil {
		return nil, err
	}
	req := &dto.PaperIdsQueryReq{
		Category: common.ExamPaper,
		PaperIds: paperIds,
		Grade:    dictMap.GradeId,
		Semester: dictMap.SemesterId,
		Subject:  dictMap.SubjectId,
		Version:  dictMap.VersionId,
	}

	papers, err := s.bizPaper.QueryExamPaper(ctx, req)
	if err != nil {
		return nil, err
	}

	resp := s.dealPaperSort(ctx, resourceList, papers)
	return resp, nil
}

// 按照resource_id的顺序对返回的课程资源重新排序
func (s *ContentService) dealSubjectClassSort(ctx context.Context, resourceList []dto.Resource, data *dto.SubjectClassesIdQueryResp, slot dto.SlotDict) []*dto.ResourceSortRespItem {

	courseMap := make(map[string]dto.Resource)
	knowledgePointMap := make(map[string]dto.Resource)
	lessonMap := make(map[string]dto.Resource)
	planMap := make(map[string]dto.Resource)
	unitMap := make(map[string]dto.Resource)

	for index, resource := range resourceList {

		resource.Sort = index
		switch resource.ResourceType {
		case common.Course.ToString(), common.JzxCourse.ToString(), common.JzxV15Course.ToString():
			courseMap[resource.ResourceId] = resource
		case common.Jzx.ToString(), common.JzxV15.ToString(), common.RAZ.ToString():

			if resource.ResourceType == common.RAZ.ToString() {
				ids := strings.Split(resource.ResourceId, ":")
				if len(ids) == 2 {
					resource.ResourceId = ids[1]
				}
			}
			unitMap[resource.ResourceId] = resource
			planMap[resource.ResourceId] = resource
			lessonMap[resource.ResourceId] = resource
			knowledgePointMap[resource.ResourceId] = resource
		case common.Unit.ToString():
			unitMap[resource.ResourceId] = resource
		case common.Plan.ToString(), common.JzxV15Plan.ToString():
			planMap[resource.ResourceId] = resource
		case common.Lesson.ToString(), common.JzxV15Lesson.ToString():
			lessonMap[resource.ResourceId] = resource
		case common.Knowledge.ToString(), common.SubKnowledge.ToString(), common.JzxV15Knowledge.ToString(), common.JzxV15Kd.ToString():
			knowledgePointMap[resource.ResourceId] = resource
		}
	}

	dataExtras := make([]*dto.ResourceSortRespItem, 0)
	for _, course := range data.CourseList {

		var courseExtra dto.CourseExtra
		err := util.ConvertInterface(course, &courseExtra)
		if err != nil {
			s.log.WithContext(ctx).Errorf("ConvertInterface err: %+v; course: %+v", err, course)
			continue
		}
		fontColor, xpad2Cover, overlayColor := s.GetResourceConfig(ctx, courseExtra.CourseSystem, courseExtra.Subject, courseExtra.Xpad2Cover)

		resource := courseMap[courseExtra.CourseId]
		dataExtras = append(dataExtras, &dto.ResourceSortRespItem{
			Type:         common.CourseCard.ToString(),
			Item:         course,
			Attr:         GetSubjectFilter(courseExtra.Semester, resource),
			Sort:         resource.Sort,
			CourseSystem: courseExtra.CourseSystem,
			FontColor:    fontColor,
			Xpad2Cover:   xpad2Cover,
			OverlayColor: overlayColor,
		})
	}

	for _, unit := range data.UnitList {

		var unitExtra dto.UnitExtra
		err := util.ConvertInterface(unit, &unitExtra)
		if err != nil {
			s.log.WithContext(ctx).Errorf("ConvertInterface err: %+v; unit: %+v", err, unit)
			continue
		}
		fontColor, xpad2Cover, overlayColor := s.GetResourceConfig(ctx, unitExtra.CourseSystem, unitExtra.Subject, unitExtra.Xpad2Cover)
		resource := unitMap[unitExtra.UnitId]
		dataExtras = append(dataExtras, &dto.ResourceSortRespItem{
			Type:         common.UnitCard.ToString(),
			Item:         unit,
			Attr:         GetSubjectFilter(unitExtra.Semester, resource),
			Sort:         resource.Sort,
			CourseSystem: unitExtra.CourseSystem,
			FontColor:    fontColor,
			Xpad2Cover:   xpad2Cover,
			OverlayColor: overlayColor,
		})
	}

	for _, plan := range data.PlanList {

		var planExtra dto.PlanExtra
		err := util.ConvertInterface(plan, &planExtra)
		if err != nil {
			s.log.WithContext(ctx).Errorf("ConvertInterface err: %+v; plan: %+v", err, plan)
			continue
		}
		fontColor, xpad2Cover, overlayColor := s.GetResourceConfig(ctx, planExtra.CourseSystem, planExtra.Subject, planExtra.Xpad2Cover)
		resource := planMap[planExtra.PlanId]
		dataExtras = append(dataExtras, &dto.ResourceSortRespItem{
			Type:         common.PlanCard.ToString(),
			Item:         plan,
			Attr:         GetSubjectFilter(planExtra.Semester, resource),
			Sort:         resource.Sort,
			CourseSystem: planExtra.CourseSystem,
			FontColor:    fontColor,
			Xpad2Cover:   xpad2Cover,
			OverlayColor: overlayColor,
		})
	}

	for _, lesson := range data.LessonList {

		var lessonExtra dto.LessonExtra
		err := util.ConvertInterface(lesson, &lessonExtra)
		if err != nil {
			s.log.WithContext(ctx).Errorf("ConvertInterface err: %+v; lesson: %+v", err, lesson)
			continue
		}
		fontColor, xpad2Cover, overlayColor := s.GetResourceConfig(ctx, lessonExtra.CourseSystem, lessonExtra.Subject, lessonExtra.Xpad2Cover)
		resource := lessonMap[lessonExtra.LessonId]
		dataExtras = append(dataExtras, &dto.ResourceSortRespItem{
			Type:         common.LessonCard.ToString(),
			Item:         lesson,
			Attr:         GetSubjectFilter(lessonExtra.Semester, resource),
			Sort:         resource.Sort,
			CourseSystem: lessonExtra.CourseSystem,
			FontColor:    fontColor,
			Xpad2Cover:   xpad2Cover,
			OverlayColor: overlayColor,
		})
	}

	for _, knowledge := range data.KnowledgePoints {

		var knowledgeExtra dto.KnowledgePointExtra
		err := util.ConvertInterface(knowledge, &knowledgeExtra)
		if err != nil {
			s.log.WithContext(ctx).Errorf("ConvertInterface err: %+v; knowledge: %+v", err, knowledge)
			continue
		}
		fontColor, xpad2Cover, overlayColor := s.GetResourceConfig(ctx, knowledgeExtra.CourseSystem, knowledgeExtra.Subject, knowledgeExtra.Xpad2Cover)
		resource := knowledgePointMap[knowledgeExtra.ModuleId]
		dataExtras = append(dataExtras, &dto.ResourceSortRespItem{
			Type:         common.KnowledgeCourseCard.ToString(),
			Item:         knowledge,
			IconType:     knowledgeExtra.IconType,
			Attr:         GetSubjectFilter(knowledgeExtra.Semester, resource),
			Sort:         resource.Sort,
			CourseSystem: knowledgeExtra.CourseSystem,
			FontColor:    fontColor,
			Xpad2Cover:   xpad2Cover,
			OverlayColor: overlayColor,
		})
	}

	sort.Sort(dto.BySort(dataExtras))
	return dataExtras
}

// 按照resource_id的顺序对返回的素养资源重新排序
func (s *ContentService) dealAttainmentSort(ctx context.Context, resourceList []dto.Resource, data *dto.AlbumAttainmentIdQueryResp, slot dto.SlotDict) []*dto.ResourceSortRespItem {

	albumMap := make(map[string]dto.Resource)
	attainmentMap := make(map[string]dto.Resource)

	for index, resource := range resourceList {
		resource.Sort = index
		switch resource.ResourceType {
		case common.Album.ToString():
			albumMap[resource.ResourceId] = resource
		case common.Attainment.ToString():
			attainmentMap[resource.ResourceId] = resource
		}
	}

	dataExtras := make([]*dto.ResourceSortRespItem, 0)
	for _, album := range data.AttainmentAlbumList {
		album.Grade = slot.GradeId
		var albumExtra dto.AttainmentAlbumExtra
		err := util.ConvertInterface(album, &albumExtra)
		if err != nil {
			s.log.WithContext(ctx).Errorf("ConvertInterface err: %+v; album: %+v", err, album)
			continue
		}
		xPad2Cover := albumExtra.Xpad2Cover
		resource := albumMap[cast.ToString(albumExtra.AlbumId)]
		dataExtras = append(dataExtras, &dto.ResourceSortRespItem{
			Type:         common.AttainmentAlbumCard.ToString(),
			Item:         album,
			Attr:         GetSubjectFilter(albumExtra.Semester, resource),
			Sort:         resource.Sort,
			Xpad2Cover:   xPad2Cover,
			CourseSystem: 100,
		})
	}

	for _, attainment := range data.AttainmentList {
		attainment.Grade = slot.GradeId
		var attainmentExtra dto.AttainmentExtra
		err := util.ConvertInterface(attainment, &attainmentExtra)
		if err != nil {
			s.log.WithContext(ctx).Errorf("ConvertInterface err: %+v; attainment: %+v", err, attainment)
			continue
		}
		xPad2Cover := attainmentExtra.Xpad2Cover

		resource := attainmentMap[cast.ToString(attainmentExtra.AttainmentId)]
		dataExtras = append(dataExtras, &dto.ResourceSortRespItem{
			Type:         common.AttainmentCard.ToString(),
			Item:         attainment,
			Attr:         GetSubjectFilter(attainmentExtra.Semester, resource),
			Sort:         resource.Sort,
			Xpad2Cover:   xPad2Cover,
			CourseSystem: 100,
		})
	}

	sort.Sort(dto.BySort(dataExtras))
	return dataExtras
}

func (s *ContentService) dealPaperSort(ctx context.Context, resourceList []dto.Resource, data *dto.PaperIdQueryResp) []*dto.ResourceSortRespItem {

	paperMap := make(map[string]dto.Resource)

	for index, resource := range resourceList {
		resource.Sort = index
		switch resource.ResourceType {
		case common.Paper.ToString(), common.SchoolName.ToString():
			paperMap[resource.ResourceId] = resource
		}
	}

	dataExtras := make([]*dto.ResourceSortRespItem, 0)
	for _, paper := range data.Papers {

		var paperExtra dto.PaperExtra
		err := util.ConvertInterface(paper, &paperExtra)
		if err != nil {
			s.log.WithContext(ctx).Errorf("ConvertInterface err: %+v; paper: %+v", err, paper)
			continue
		}

		resource := paperMap[paperExtra.PaperId]
		dataExtras = append(dataExtras, &dto.ResourceSortRespItem{
			Type: common.PaperCard.ToString(),
			Item: paper,
			Attr: GetPaperFilter(paperExtra.Semester, resource),
			Sort: resource.Sort,
		})
	}

	sort.Sort(dto.BySort(dataExtras))
	return dataExtras
}

// GetSubjectFilter 学科 筛选条件 学期动态获取
func GetSubjectFilter(semester int, resource dto.Resource) []dto.Attr {
	if semester == 0 {
		semester = resource.SemesterId
	}
	return []dto.Attr{
		{
			Key: "subject",
			Id:  cast.ToString(resource.SubjectId),
		},
		{
			Key: "grade",
			Id:  cast.ToString(resource.GradeId),
		},
		{
			Key: "semester", // 学期
			Id:  cast.ToString(semester),
		},
	}
}

// GetPaperFilter 试卷筛选条件 学期动态获取
func GetPaperFilter(semester int, resource dto.Resource) []dto.Attr {
	if semester == 0 {
		semester = resource.SemesterId
	}
	return []dto.Attr{
		{
			Key: "grade", // 年级
			Id:  cast.ToString(resource.GradeId),
		},
		{
			Key: "semester", // 学期
			Id:  cast.ToString(semester),
		},
		{
			Key: "paperType", // 试卷类型
			Id:  cast.ToString(resource.PaperType),
		},
		{
			Key: "province", // 地区
			Id:  cast.ToString(resource.ProvinceId),
		},
		{
			Key: "isFamousSchool", // 只看名校
			Id:  cast.ToString(resource.IsFamousSchool),
		},
		{
			Key: "year", // 年份
			Id:  cast.ToString(resource.Year),
		},
		{
			Key: "difficulty", // 难度
			Id:  cast.ToString(resource.DifficultyId),
		},
	}
}

// GetStudyLogFilter 继续学筛选条件
func GetStudyLogFilter(resource *dto.StudyLogDetail) []dto.Attr {
	return []dto.Attr{
		{
			Key: "subject",
			Id:  cast.ToString(resource.Subject),
		},
		{
			Key: "grade",
			Id:  cast.ToString(resource.Grade),
		},
		{
			Key: "semester", // 学期
			Id:  cast.ToString(resource.Semester),
		},
	}
}

func (s *ContentService) getSubjectFontColor(subjectId int) string {
	color := s.subjectConfig.SubjectFontColor[int32(subjectId)]

	return color
}

func (s *ContentService) getJzxCover(systemID int) string {
	cover := s.subjectConfig.SubjectJzxCover[int32(systemID)]

	if cover == "" {
		cover = s.subjectConfig.SubjectJzxCover[999999]
	}
	return cover
}

func (s *ContentService) getSubjectOverlayColor(subjectId int) string {
	color := s.subjectConfig.SubjectOverlayColor[int32(subjectId)]

	return color
}

func (s *ContentService) buildJzxV15QuestionLuiNluData(ctx context.Context, slot dto.SlotDict, resourceList []dto.Resource) ([]*dto.ResourceSortRespItem, error) {
	resp := make([]*dto.ResourceSortRespItem, 0)
	for i, jzxV15 := range resourceList {
		resourceExtra := dto.JzxV15QuestionExtra{
			Cover:        s.subjectConfig.GetJzxV15QuestionsCover(),
			GradeName:    jzxV15.GradeName,
			SemesterName: jzxV15.SemesterName,
			SubjectName:  jzxV15.SubjectName,
			VersionName:  jzxV15.VersionName,
			QuesNum:      slot.ProblemCount,
			ResourceName: jzxV15.ResourceName,
			ResourceId:   jzxV15.ResourceId,
		}
		baseUrl, urlErr := url.Parse(s.subjectConfig.GetJzxV15Scheme())
		if urlErr != nil {
			s.log.WithContext(ctx).Errorf("buildClickReadResp Parse err: %+v", urlErr)
			return nil, urlErr
		}
		params := baseUrl.Query()

		params.Add("source_id", jzxV15.ResourceId)
		params.Add("question_count", cast.ToString(slot.ProblemCount))
		params.Add("subject", cast.ToString(jzxV15.SubjectId))
		params.Add("grade", cast.ToString(jzxV15.GradeId))
		baseUrl.RawQuery = params.Encode()
		resourceExtra.Scheme = baseUrl.String()
		parentNodeNames := strings.Split(jzxV15.ParentNodeNames, "<|>")
		hierarchyLevel := map[int]string{
			0: "单元：",
			1: "课时：",
		}
		hierarchyMiddleLevel := map[int]string{
			0: "章：",
			1: "节：",
		}
		if len(parentNodeNames) == 2 || len(parentNodeNames) == 1 {
			for i2, name := range parentNodeNames {
				hierarchy := ""
				if jzxV15.GradeId > 6 {
					hierarchy = hierarchyMiddleLevel[i2]
				} else {
					hierarchy = hierarchyLevel[i2]
				}

				if hierarchy != "" && name != "" {
					parentNodeNames[i2] = hierarchy + name
				}
			}
		}
		resourceExtra.ParentNodeNames = parentNodeNames
		resp = append(resp, &dto.ResourceSortRespItem{
			Type: common.SearchExercise.ToString(),
			Item: resourceExtra,
			Sort: i,
		})
	}

	return resp, nil
}

func (s *ContentService) GetResourceConfig(_ context.Context, CourseSystem, Subject int, Cover string) (FontColor, Xpad2Cover, overlayColor string) {
	if CourseSystem == common.SystemTongBu {
		FontColor = s.getSubjectFontColor(Subject)
		overlayColor = s.getSubjectOverlayColor(Subject)
	}

	if CourseSystem == common.SystemJZX || CourseSystem == common.SystemJZXV15 {
		Xpad2Cover = s.getJzxCover(CourseSystem)
	}
	if CourseSystem == 22 {
		Xpad2Cover = Cover
	}
	if Xpad2Cover == "" {
		Xpad2Cover = Cover + "?x-oss-process=image/resize,p_50"
	}
	return
}

func (s *ContentService) buildEBookResourceLuiNluData(ctx context.Context, slot dto.SlotDict, resourceList []dto.Resource, skill, defaultCover string) ([]*dto.ResourceSortRespItem, error) {

	reqItems := make([]dto.EbookResourceReqItem, 0)

	for _, resource := range resourceList {
		item := dto.EbookResourceReqItem{
			ResourceId:   resource.ResourceId,
			ResourceType: "resource",
		}
		reqItems = append(reqItems, item)
	}

	req := &dto.EbookResourceReq{
		List: reqItems,
	}

	eBook, err := s.bizPaper.QueryEBook(ctx, req)
	if err != nil {
		return nil, err
	}

	resp := s.dealEBookSort(ctx, resourceList, eBook, skill, defaultCover)
	return resp, nil
}

func (s *ContentService) dealEBookSort(ctx context.Context, resourceList []dto.Resource, data *dto.EbookResourceRes, skill, defaultCover string) []*dto.ResourceSortRespItem {

	eBookMap := make(map[string]dto.EbookResource)

	for index, resource := range resourceList {
		ebook := dto.EbookResource{
			VipLevel:     resource.VipLevel,
			Cover:        resource.Cover,
			Author:       resource.Author,
			Introduction: resource.Introduction,
			ResourceType: resource.ResourceType,
			ResourceId:   resource.ResourceId,
			ResourceName: resource.ResourceName,
			Sort:         index,
		}
		// 判断是不是没有封面，没有封面补充一个封面
		if ebook.Cover == "" {
			ebook.Cover = defaultCover
		}
		eBookMap[resource.ResourceId] = ebook
	}

	dataExtras := make([]*dto.ResourceSortRespItem, 0)
	for _, eBookPage := range data.List {
		resource := eBookMap[eBookPage.ResourceId]
		resource.Scheme = eBookPage.LandingPage
		cardType := ""
		switch skill {
		case common.SkillDocumentary.ToString():
			cardType = common.DocumentaryCard.ToString()
		case common.SearchBook.ToString():
			cardType = common.BookCard.ToString()
		case common.SkillNewspaper.ToString():
			cardType = common.NewspaperCard.ToString()
		}
		dataExtras = append(dataExtras, &dto.ResourceSortRespItem{
			Type: cardType,
			Item: resource,
			Sort: resource.Sort,
		})
	}

	sort.Sort(dto.BySort(dataExtras))
	return dataExtras
}
