package service

import (
	"context"
	"fmt"
	"lui-api/internal/common"
	"lui-api/internal/pkg/custom_context"
)

// QualityGrades 素养、继续学素养
var QualityGrades = []string{"小班", "中班", "大班", "一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级"}

// PaperGrades 搜试卷
var PaperGrades = []string{"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"}

// PointReadingGrades 点读
var PointReadingGrades = []string{"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级"}

// DictationGrades 听写
var DictationGrades = []string{"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级"}

// RecitationGrades 背诵
var RecitationGrades = []string{"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级"}

// PrimarySchool 小学年级
var PrimarySchool = []string{"一年级", "二年级", "三年级", "四年级", "五年级", "六年级"}

// JuniorHighSchool 初中年级
var JuniorHighSchool = []string{"七年级", "八年级", "九年级"}

// HighSchool 高中年级
var HighSchool = []string{"高一", "高二", "高三", "十年级", "十一年级", "十二年级"}

var AppGrades = map[string][]string{
	//"1000":  {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	"10100": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	"10101": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	"1001":  {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	"1003":  {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//"1006":  {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	"1007": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	"1008": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	"1009": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	"1010": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//"1011": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//我的计划
	"1012": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//计划广场
	"1013": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//错题本
	"1014": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//听一听
	//"1015": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//语文-课本点读
	"1016": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级"},
	//语文-字词听写
	"1017": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级"},
	//语文-课文背诵
	"1018": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级"},
	//大语文阅读
	"1019": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级"},
	//全国中小学图书馆（室）推荐书目
	"1020": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//英语-单词听写
	"1021": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级"},
	//英语-课本点读
	"1022": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级"},
	//口算练习
	"1024": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级"},
	//教辅答疑
	"1025": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//指尖查词
	//"1026": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//智能图书
	"1027": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//AI口算批改
	//"1028": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//智慧眼
	//"1029": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//国家中小学智慧教育平台
	"1030": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//作文助手
	"1036": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级"},
	//小π练习
	"1038": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级"},
	//纸屏互动
	"1037": {"小班", "中班", "大班"},
	//指尖翻译
	"1041": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//优选专区
	"10102": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级"},
	//素养天地
	"20005": {"小班", "中班", "大班"},
	//学习广场
	"20004": {"小班", "中班", "大班"},
	//摩比爱写字
	//"1048": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//AI对话
	//"1088": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//小思伴学
	"1042": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	// 每天五分钟/小计划（当前仅支持跳转至计划广场）
	"20006": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级"},
	// 写作引导
	"1044": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级"},
}

var AppGradesXPad1 = map[string][]string{
	"1000":  {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	"10100": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	"10101": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	"1001":  {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	"1003":  {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//"1006":  {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	"1007": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	"1008": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	"1009": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	"1010": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//"1011": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//我的计划
	"1012": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//计划广场
	"1013": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//错题本
	"1014": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//听一听
	//"1015": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//语文-课本点读
	"1016": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级"},
	//语文-字词听写
	"1017": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级"},
	//语文-课文背诵
	"1018": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级"},
	//大语文阅读
	"1019": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级"},
	//全国中小学图书馆（室）推荐书目
	"1020": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//英语-单词听写
	"1021": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级"},
	//英语-课本点读
	"1022": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级"},
	//口算练习
	"1024": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级"},
	//教辅答疑
	"1025": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//指尖查词
	//"1026": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//智能图书
	"1027": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//AI口算批改
	//"1028": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//智慧眼
	//"1029": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//国家中小学智慧教育平台
	"1030": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//作文助手
	"1036": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级"},
	//小π练习
	"1038": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级"},
	//纸屏互动
	"1037": {"小班", "中班", "大班"},
	//指尖翻译
	//"1041": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//优选专区
	"10102": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级"},
	//素养天地
	"20005": {"小班", "中班", "大班"},
	//学习广场
	"20004": {"小班", "中班", "大班"},
	//摩比爱写字
	//"1048": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//AI对话
	//"1088": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	//小思伴学
	"1042": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级", "七年级", "八年级", "九年级", "高一", "高二", "高三", "十年级", "十一年级", "十二年级"},
	// 每天五分钟/小计划（当前仅支持跳转至计划广场）
	"20006": {"一年级", "二年级", "三年级", "四年级", "五年级", "六年级"},
}

const (
	CompanionLearning      = "小思伴学"
	HomeWorkLearning       = "作业模式"
	HomeWorkLearningWrite  = "番茄钟"
	CompanionLearningAppID = "1042"
	HomeWorkLearningAppID  = "1043"
	XiaoSiZiXiAPP          = "小思自习室"
	DiagnosticPaper        = "试卷诊断"
)

var appNameMap = map[string]string{
	"10102": "优选专区",
}

// CheckIntercept 年级是否支持该资源类别
func (s *LuiService) CheckIntercept(ctx context.Context, gradeName, skill string) (bool, string) {

	if custom_context.GetXAppId(ctx) == s.thirdConf.XPad1AppId && (gradeName == "小班" || gradeName == "中班" || gradeName == "大班") {
		return true, common.TtsTplInterceptCnhtenment
	}

	if skill == common.ContinueLearnQuality.ToString() || skill == common.SearchQuality.ToString() {
		for _, grade := range QualityGrades {
			if gradeName == grade {
				return false, ""
			}
		}
		return true, common.TtsTplIntercept
	}

	if skill == common.SearchPaper.ToString() {
		for _, grade := range PaperGrades {
			if gradeName == grade {
				return false, ""
			}
		}
		return true, common.TtsTplIntercept
	}

	if skill == common.ClickRead.ToString() {
		for _, grade := range PointReadingGrades {
			if gradeName == grade {
				return false, ""
			}
		}
		return true, common.TtsTplIntercept
	}

	if skill == common.ListenWrite.ToString() {
		for _, grade := range DictationGrades {
			if gradeName == grade {
				return false, ""
			}
		}
		return true, common.TtsTplIntercept
	}

	if skill == common.Recite.ToString() {
		for _, grade := range RecitationGrades {
			if gradeName == grade {
				return false, ""
			}
		}
		return true, common.TtsTplIntercept
	}
	return false, ""
}

func CheckInterceptApp(gradeName, appId, value string) (bool, string) {
	sName := ""
	if gradeName == "小班" || gradeName == "中班" || gradeName == "大班" {
		sName = "启蒙屏"
		if value == CompanionLearning {
			return true, "启蒙模式还不支持，请切换到小学使用吧"
		}
		if value == HomeWorkLearning {
			return true, "启蒙模式还不支持，请切换到小学使用吧"
		}
		if value == HomeWorkLearningWrite {
			return true, "启蒙模式还不支持，请切换到小学使用吧"
		}
	}
	for _, grade := range PrimarySchool {
		if gradeName == grade {
			sName = "小学屏"
		}
	}
	middle := append(JuniorHighSchool, HighSchool...)
	for _, grade := range middle {
		if gradeName == grade {
			sName = "中学屏"
		}
	}
	if sName == "" {
		return false, ""
	}
	grades := AppGrades[appId]
	if grades == nil {
		return false, ""
	}
	for _, grade := range grades {
		if gradeName == grade {
			return false, ""
		}
	}
	appName := appNameMap[appId]

	if appName == "" {
		appName = "该应用"
	}
	return true, fmt.Sprintf(common.TtsInterceptApp, sName, appName)
}

func CheckInterceptAppXPad1(gradeName, appId string) (bool, string) {

	grades := AppGradesXPad1[appId]
	if grades == nil {
		return false, ""
	}

	for _, grade := range grades {
		if gradeName == grade {
			return false, ""
		}
	}

	sName := ""
	if gradeName == "小班" || gradeName == "中班" || gradeName == "大班" {
		sName = "启蒙屏"
	}
	for _, grade := range PrimarySchool {
		if gradeName == grade {
			sName = "小学屏"
			break
		}
	}
	middle := append(JuniorHighSchool, HighSchool...)
	for _, grade := range middle {
		if gradeName == grade {
			sName = "中学屏"
			break
		}
	}
	if sName == "" {
		return false, ""
	}

	appName := appNameMap[appId]

	if appName == "" {
		appName = "该应用"
	}
	return true, fmt.Sprintf(common.TtsInterceptApp, sName, appName)
}
