package service

import (
	"context"
	"encoding/json"
	"github.com/pkg/errors"
	v1 "lui-api/api/skill/v1"
	"lui-api/internal/conf"
	"lui-api/internal/pkg/custom_context"
	"lui-api/pkg/util"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/protobuf/ptypes/empty"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/structpb"
)

type SkillService struct {
	v1.UnimplementedSkillApiServer
	log       *log.Helper
	thirdConf *conf.Third
	skills    map[string]*conf.Skills
}

func NewSkillService(logger log.Logger, thirdConf *conf.Third, skills map[string]*conf.Skills) *SkillService {
	return &SkillService{
		log:       log.NewHelper(logger),
		thirdConf: thirdConf,
		skills:    skills,
	}
}

type SkillList struct {
	TagID     string `json:"tag_id"`
	TagName   string `json:"tag_name"`
	SkillID   string `json:"skill_id"`
	SkillName string `json:"skill_name"`
	SkillIcon string `json:"skill_icon"`
}

type Skill struct {
	SkillTagID string `json:"skill_tag_id"`
	SkillTag   string `json:"skill_tag"`
	CommandID  string `json:"command_id"`
	Command    string `json:"command"`
	Status     int    `json:"status"`
}

func (s *SkillService) SkillList(ctx context.Context, _ *empty.Empty) (*structpb.Struct, error) {

	var skills *conf.Skills
	if val, ok := s.skills[custom_context.GetXAppId(ctx)]; ok {
		skills = val
	} else {
		skills = s.skills["default"]
	}

	if skills == nil {
		return nil, errors.New("conf not found")
	}

	response := &structpb.Struct{}

	skillConf, _ := json.Marshal(skills.Skill)
	err := protojson.Unmarshal(skillConf, response)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (s *SkillService) SkillDetail(ctx context.Context, req *v1.SkillDetailRequest) (*structpb.Struct, error) {

	var skills *conf.Skills
	if val, ok := s.skills[custom_context.GetXAppId(ctx)]; ok {
		skills = val
	} else {
		skills = s.skills["default"]
	}

	if skills == nil {
		return nil, errors.New("conf not found")
	}

	respMap := map[string]interface{}{}
	if val, ok := skills.SkillDetail.SkillMap[req.SkillId]; ok {
		respMap = map[string]interface{}{
			"detail": val.DetailList,
		}
	}

	response, _ := util.ReplyAny(respMap)
	return response, nil
}
