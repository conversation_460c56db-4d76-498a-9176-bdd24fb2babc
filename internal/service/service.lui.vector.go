package service

import (
	"context"
	"math/rand"
	"strconv"
	"strings"
	"unicode"

	"github.com/goccy/go-json"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/types/known/structpb"
	v1 "lui-api/api/lui/v1"
	"lui-api/internal/common"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	"lui-api/pkg/util"
)

func (s *LuiService) QueryPadHotfix2Vdb(ctx context.Context, req *v1.LuiNluRequest) (*structpb.Struct, bool, error) {
	if len(req.GetAsrInfo()) == 0 {
		return nil, false, nil
	}

	hotFixDo, err := s.bizEmbeddingSync.QueryHotfix(ctx, req.GetAsrInfo())
	if hotFixDo == nil || err != nil {
		return nil, false, err
	}

	s.log.WithContext(ctx).Infof("QueryPadHotfix2Vdb hotFixDo: %+v", hotFixDo)

	var snList []string
	_ = json.Unmarshal([]byte(hotFixDo.SnList), &snList)

	if hotFixDo.SnType == dto.HotfixSnTypeAssign && !util.InSliceString(custom_context.GetXDeviceId(ctx), snList) {
		return nil, false, nil
	}

	if hotFixDo.HotfixType != dto.HotfixTypeText {
		return nil, false, nil
	}

	nlu4lui, hotfixDO, err := s.dealHotfixTypeText(ctx, req, hotFixDo)
	if nlu4lui == nil || err != nil {
		return nil, false, err
	}

	res := &dto.NluData{
		UserID:       nlu4lui.UserID,
		RequestID:    nlu4lui.RequestID,
		Skill:        nlu4lui.SkillList[0].Skill,
		Task:         nlu4lui.SkillList[0].Task,
		TtsShow:      nlu4lui.SkillList[0].TtsShow,
		TtsNorm:      nlu4lui.SkillList[0].TtsNorm,
		HotfixVector: hotfixDO,
	}

	resp, err := util.ReplyAny(res)
	if err != nil {
		return nil, false, v1.ErrorInternalError("nlu")
	}
	return resp, true, nil
}

func (s *LuiService) QueryHotfix2Vdb(ctx context.Context, req *v1.LuiNluRequest) (*dto.NLUQueryPad2Resp, *dto.HotfixVectorDo, error) {
	if len(req.GetAsrInfo()) == 0 {
		return nil, nil, nil
	}

	hotFixDo, err := s.bizEmbeddingSync.QueryHotfix(ctx, req.GetAsrInfo())
	if hotFixDo == nil || err != nil {
		return nil, nil, err
	}

	s.log.WithContext(ctx).Infof("QueryHotfixByVdb hotFixDo: %+v", hotFixDo)

	var (
		snList     []string
		agentList  []string
		deviceList []string
	)
	_ = json.Unmarshal([]byte(hotFixDo.SnList), &snList)
	_ = json.Unmarshal([]byte(hotFixDo.AgentList), &agentList)
	_ = json.Unmarshal([]byte(hotFixDo.DeviceList), &deviceList)

	if hotFixDo.SnType == dto.HotfixSnTypeAssign && !util.InSliceString(custom_context.GetXDeviceId(ctx), snList) {
		return nil, nil, nil
	}

	// bella和学练机不支持指定技能快修
	if hotFixDo.HotfixType == dto.HotfixTypeSkill {
		if strings.HasPrefix(req.BizType, common.BizTypeAiTutor) ||
			strings.Contains(s.thirdConf.AppIdMap["xueLianJiAppIds"], custom_context.GetXAppId(ctx)) {
			return nil, nil, nil
		}
	}

	// 支持设备判断
	if len(deviceList) > 0 && !util.InSliceString(dto.GetHotfixDeviceID(custom_context.GetXAppId(ctx), req.GetScreenMode()), deviceList) {
		return nil, nil, nil
	}

	// 智能体判断
	if len(agentList) > 0 && !util.InSliceString(dto.GetHotfixAgentID(req.BizType), agentList) {
		return nil, nil, nil
	}

	switch hotFixDo.HotfixType {
	case dto.HotfixTypeText:
		return s.dealHotfixTypeText(ctx, req, hotFixDo)
	case dto.HotfixTypeSkill:
		resp, err := s.dealHotfixTypeSkill(ctx, req, hotFixDo)
		s.log.WithContext(ctx).Infof("QueryHotfixByVdb resp: %+v", resp)
		if resp == nil || err != nil {
			return nil, nil, err
		}
		return resp, hotFixDo, err

	case dto.HotfixTypeSimilarQuery:
		if hotFixDo.Score < cast.ToFloat64(s.dataConf.Embedding.DbHotfix.Threshold) {
			return nil, nil, nil
		}

		dataM := map[string]interface{}{}
		err := json.Unmarshal([]byte(hotFixDo.Data), &dataM)
		if err != nil {
			return nil, nil, err
		}

		query, ok := dataM["query"].(string)
		if !ok {
			return nil, nil, nil
		}

		hotFixDo.AsrInfo = req.AsrInfo
		req.AsrInfo = query
		req.AsrPinyin = ""
		return nil, hotFixDo, nil
	default:
		return nil, nil, nil
	}
}

// dealHotfixTypeText
func (s *LuiService) dealHotfixTypeText(ctx context.Context, req *v1.LuiNluRequest, hotFixDo *dto.HotfixVectorDo) (*dto.NLUQueryPad2Resp, *dto.HotfixVectorDo, error) {
	if hotFixDo.Score < cast.ToFloat64(s.dataConf.Embedding.DbHotfix.FaqThreshold) {
		return nil, nil, nil
	}

	var answer []string
	_ = json.Unmarshal([]byte(hotFixDo.Data), &answer)

	doAnswer := ""
	if len(answer) > 0 {
		doAnswer = answer[rand.Intn(len(answer))]
	}

	if doAnswer == "" {
		return nil, nil, nil
	}

	//var ttsParam dto.VdbTextDataInfoDO
	//_ = json.Unmarshal([]byte(hotFixDo.TextDataInfo), &ttsParam)

	var ttsConfig dto.NluTtsConfig
	/*switch ttsParam.Item {
	case "emotion":
		ttsConfig.ExpressStyle = ttsParam.Value
	case "speech_speed":
		ttsConfig.Rate = ttsParam.Value
	case "tone":
		ttsConfig.Pitch = ttsParam.Value
	case "volume":
		ttsConfig.Volume = ttsParam.Value
	}*/
	resp := &dto.NLUQueryPad2Resp{
		RequestID:         req.RequestId,
		Version:           custom_context.GetXVersion(ctx),
		Input:             req.AsrInfo,
		SessionId:         req.RequestId,
		SkillList:         make([]dto.NLUSkillItem, 0),
		ModelOutputIntent: "闲聊",
	}

	var skillItem dto.NLUSkillItem
	if len(hotFixDo.Url) > 0 {
		if strings.HasPrefix(hotFixDo.Url, "http:") {
			hotFixDo.Url = strings.Replace(hotFixDo.Url, "http:", "https:", 1)
		}
		command := &dto.CommendTmp{
			API: "functional.application",
			Param: dto.CommendParamTmp{
				Appid:       "jumph5",
				Method:      "rule",
				Operate:     "open",
				PackageName: "com.tal.pad.lui",
				Value:       "",
				Scheme:      hotFixDo.Url,
			},
		}
		skillItem = dto.NLUSkillItem{
			Skill:         "functional",
			Task:          "functional.application",
			Count:         1,
			IsResultValid: true,
			TtsNorm:       doAnswer,
			TtsShow:       doAnswer,
			NluTtsConfig:  ttsConfig,
			Command:       command,
		}
	} else {
		skillItem = dto.NLUSkillItem{
			Skill:         "chat",
			Task:          "chat.small_talk",
			Count:         1,
			IsResultValid: true,
			TtsNorm:       doAnswer,
			TtsShow:       doAnswer,
			NluTtsConfig:  ttsConfig,
		}
	}

	resp.SkillList = append(resp.SkillList, skillItem)
	resp.HotfixVector = hotFixDo

	return resp, hotFixDo, nil
}

func (s *LuiService) dealHotfixTypeSkill(ctx context.Context, req *v1.LuiNluRequest, hotFixDo *dto.HotfixVectorDo) (*dto.NLUQueryPad2Resp, error) {
	if hotFixDo.Score < cast.ToFloat64(s.dataConf.Embedding.DbHotfix.Threshold) {
		return nil, nil
	}

	defaultSlot := dto.DefaultSlot{
		GradeName:    req.Grade,
		SemesterName: dto.GetSemester(),
	}

	resp := &dto.NLUQueryPad2Resp{
		RequestID:         req.RequestId,
		Version:           custom_context.GetXVersion(ctx),
		Input:             req.AsrInfo,
		SessionId:         req.RequestId,
		SkillList:         make([]dto.NLUSkillItem, 0),
		ModelOutputIntent: hotFixDo.Intent,
	}

	switch hotFixDo.Domain {
	case common.HotfixDomainAppOperation:
		fallthrough
	case common.HotfixDomainSystemOperation:
		command := map[string]interface{}{}
		err := json.Unmarshal([]byte(hotFixDo.Data), &command)
		if err != nil {
			return nil, err
		}
		task := "functional.application"
		commandApi, ok := command["api"].(string)
		if ok {
			task = commandApi
		}
		skillItem := dto.NLUSkillItem{
			Skill:         "functional",
			Task:          task,
			Count:         1,
			IsResultValid: true,
			Command:       command,
			DefaultSlots:  defaultSlot,
			TtsNorm:       hotFixDo.TtsNorm,
			TtsShow:       hotFixDo.TtsShow,
		}
		resp.SkillList = append(resp.SkillList, skillItem)
		resp.HotfixVector = hotFixDo
	case common.HotfixDomainStudyResources:
		if hotFixDo.Operate != common.HotfixSkillSearchCourse && hotFixDo.Operate != common.HotfixSkillSearchQuality {
			return nil, nil
		}

		skillItem := dto.NLUSkillItem{
			Skill:         hotFixDo.Operate,
			Count:         1,
			IsResultValid: true,
			DefaultSlots:  defaultSlot,
			ResourceList:  make([]dto.Resource, 0),
			Word:          make([]string, 0),
		}

		var resource []dto.Resource
		var hotfixVectorResourceDO []dto.HotfixVectorResourceDO
		err := json.Unmarshal([]byte(hotFixDo.Data), &hotfixVectorResourceDO)
		if err != nil {
			s.log.WithContext(ctx).Infof("Unmarshal hotFixDo.Data: %+v, err: %+v", hotFixDo.Data, err)
			return nil, err
		}

		for _, item := range hotfixVectorResourceDO {
			albumId := 0
			if len(item.AlbumIDs) > 0 {
				albumIDs := strings.Split(item.AlbumIDs[0], ":")
				if len(albumIDs) > 1 {
					albumId = cast.ToInt(albumIDs[0])
				}
			}

			resource = append(resource, dto.Resource{
				ResourceName:   item.ResourceName,
				ResourceType:   item.ResourceType,
				ResourceId:     item.ResourceId,
				SubjectId:      item.SubjectId,
				SubjectName:    item.SubjectName,
				VersionId:      item.VersionId,
				VersionName:    item.VersionName,
				CourseSystemId: item.CourseSystemId,
				CourseId:       item.CourseId,
				AlbumId:        albumId,
			})
		}

		if len(resource) == 0 {
			s.log.WithContext(ctx).Infof("resource is empty, hotfixVectorResourceDO: %+v, defaultSlot: %+v", hotfixVectorResourceDO, defaultSlot)
			return nil, nil
		}

		skillItem.ResourceList = resource
		resp.SkillList = append(resp.SkillList, skillItem)
		resp.HotfixVector = hotFixDo
	case common.HotfixDomainStudyTools:
		//获取nluQuery
		gradeId, _ := strconv.Atoi(req.GradeId)
		nluQuery := &dto.NLUQuery{
			AsrInput: dto.AsrInput{
				AsrInfo:   req.AsrInfo,
				AsrLen:    len(req.AsrInfo),
				AsrPinyin: req.AsrPinyin,
			},
			UserSystem: dto.UserSystemV2{
				Location: req.Location,
				Grade:    gradeId,
				Semester: dto.GetSemester(),
				TalId:    req.TalId,
				DeviceId: custom_context.GetXDeviceId(ctx),
			},
			RequestID: req.RequestId,
		}

		if req.BizType == common.BizTypeCompanionLearn {
			nluQuery.SceneCode = common.SceneCompanionLearn
		}
		//调用Skill-Hub /v2/call
		funcReq := &dto.FunctionCallReq{
			RequestId:  nluQuery.RequestID,
			UserSystem: nluQuery.UserSystem,
			AsrInput:   nluQuery.AsrInput,
			SceneCode:  nluQuery.SceneCode,
		}
		switch hotFixDo.Operate {
		case common.HotfixFunctionPoemAuthor:
			vdbPoetAuthorInfo := dto.VdbPoetAuthorInfo{}
			err := json.Unmarshal([]byte(hotFixDo.Data), &vdbPoetAuthorInfo)
			if err != nil {
				return nil, err
			}
			searchAuthorInformationFunc := dto.SearchAuthorInformationFunc{
				Author:                      vdbPoetAuthorInfo.Author,
				Title:                       vdbPoetAuthorInfo.PoetName,
				Dynasty:                     vdbPoetAuthorInfo.AuthorDynasty,
				Sentence:                    vdbPoetAuthorInfo.PoetContent,
				SearchAuthorInformationType: vdbPoetAuthorInfo.AuthorAttribute,
			}
			funcReq.Intent = common.IntentSearchPoem
			funcReq.FunctionCall = dto.FunctionCall{
				Functions: []dto.Function{
					{
						FuncName:       "search_author_information",
						FuncParamaters: searchAuthorInformationFunc,
					},
				},
			}
			funcResp, err := s.bizNlu.FunctionCall(ctx, funcReq, req)
			if err != nil {
				return nil, err
			}
			resp.SkillList = funcResp.SkillList
			resp.ModelOutputIntent = funcResp.ModelOutputIntent

			isSingleSkill := len(funcResp.SkillList) == 1
			isSpecialSkill := isSingleSkill && (funcResp.SkillList[0].Skill == "no_skill" || funcResp.SkillList[0].Skill == "functional")
			if !isSpecialSkill {
				resp.HotfixVector = hotFixDo
			}

		case common.HotfixFunctionPoemContent:
			vdbPoeticLines := dto.VdbPoeticLines{}
			err := json.Unmarshal([]byte(hotFixDo.Data), &vdbPoeticLines)
			if err != nil {
				return nil, err
			}

			searchPoetryFormsFunc := dto.SearchPoetryFormsFunc{
				Author:  vdbPoeticLines.Author,
				Title:   vdbPoeticLines.PoetName,
				Dynasty: vdbPoeticLines.AuthorDynasty,
				Tags:    vdbPoeticLines.PoetTags,
				Keyword: vdbPoeticLines.PoetKeyword,
				Types:   vdbPoeticLines.PoetForm,
			}
			funcReq.Intent = common.IntentSearchPoem
			funcReq.FunctionCall = dto.FunctionCall{
				Functions: []dto.Function{
					{
						FuncName:       "search_poetry_forms",
						FuncParamaters: searchPoetryFormsFunc,
					},
				},
			}
			funcResp, err := s.bizNlu.FunctionCall(ctx, funcReq, req)
			if err != nil {
				return nil, err
			}
			resp.SkillList = funcResp.SkillList
			resp.ModelOutputIntent = funcResp.ModelOutputIntent

			isSingleSkill := len(funcResp.SkillList) == 1
			isSpecialSkill := isSingleSkill && (funcResp.SkillList[0].Skill == "no_skill" || funcResp.SkillList[0].Skill == "functional")
			if !isSpecialSkill {
				resp.HotfixVector = hotFixDo
			}
		case common.HotfixFunctionPoemAnalysis:
			vdbPoetInterpretationAppreciationSource := dto.VdbPoetInterpretationAppreciationSource{}
			err := json.Unmarshal([]byte(hotFixDo.Data), &vdbPoetInterpretationAppreciationSource)
			if err != nil {
				return nil, err
			}

			analysisOfPoemFunc := dto.AnalysisOfPoemFunc{
				Author:             vdbPoetInterpretationAppreciationSource.Author,
				Title:              vdbPoetInterpretationAppreciationSource.PoetName,
				Dynasty:            vdbPoetInterpretationAppreciationSource.AuthorDynasty,
				Sentence:           vdbPoetInterpretationAppreciationSource.PoetContent,
				SearchAnalysisType: vdbPoetInterpretationAppreciationSource.AnalysisType,
			}
			funcReq.Intent = common.IntentSearchPoem
			funcReq.FunctionCall = dto.FunctionCall{
				Functions: []dto.Function{
					{
						FuncName:       "analysis_of_poem",
						FuncParamaters: analysisOfPoemFunc,
					},
				},
			}
			funcResp, err := s.bizNlu.FunctionCall(ctx, funcReq, req)
			if err != nil {
				return nil, err
			}
			resp.SkillList = funcResp.SkillList
			resp.ModelOutputIntent = funcResp.ModelOutputIntent

			isSingleSkill := len(funcResp.SkillList) == 1
			isSpecialSkill := isSingleSkill && (funcResp.SkillList[0].Skill == "no_skill" || funcResp.SkillList[0].Skill == "functional")
			if !isSpecialSkill {
				resp.HotfixVector = hotFixDo
			}

		case common.HotfixFunctionTranslate:
			translateSentence := dto.TranslateSentence{}
			err := json.Unmarshal([]byte(hotFixDo.Data), &translateSentence)
			if err != nil {
				return nil, err
			}

			translationLookupParametersFunc := dto.TranslationLookupParametersFunc{
				SourceText:        translateSentence.TranslateContent,
				TranslationMethod: translateSentence.TranslateType,
			}

			funcReq.Intent = common.IntentTranslation
			funcReq.FunctionCall = dto.FunctionCall{
				Functions: []dto.Function{
					{
						FuncName:       "translation_lookup",
						FuncParamaters: translationLookupParametersFunc,
					},
				},
			}
			funcResp, err := s.bizNlu.FunctionCall(ctx, funcReq, req)
			if err != nil {
				return nil, err
			}
			resp.SkillList = funcResp.SkillList
			resp.ModelOutputIntent = funcResp.ModelOutputIntent

			isSingleSkill := len(funcResp.SkillList) == 1
			isSpecialSkill := isSingleSkill && (funcResp.SkillList[0].Skill == "no_skill" || funcResp.SkillList[0].Skill == "functional")
			if !isSpecialSkill {
				resp.HotfixVector = hotFixDo
			}
		}

	default:
		return nil, nil
	}

	return resp, nil
}

func (s *LuiService) AccessingVectorLib(ctx context.Context, req *v1.LuiNluRequest) (*structpb.Struct, bool, error) {
	if len(req.GetAsrInfo()) == 0 {
		return nil, false, nil
	}

	faqVectorDo, err := s.bizEmbeddingSync.QueryFaqAnswer(ctx, req.GetAsrInfo())

	if faqVectorDo == nil || err != nil {
		return nil, false, err
	}
	s.log.WithContext(ctx).Infof("FetchVectorNLU faqVectorDo: %+v", faqVectorDo)

	// 二代结构
	if (strings.Contains(s.thirdConf.XPad2AppId, custom_context.GetXAppId(ctx)) || strings.Contains(s.thirdConf.XPad1V2AppId, custom_context.GetXAppId(ctx))) && s.confWhiteOsVersion.Status == 1 {
		res := dto.QueryPad2Resp{
			TtsShow: faqVectorDo.FaqAnswer,
			TtsNorm: faqVectorDo.FaqAnswer,
			Data: []*dto.Pad2SkillData{
				{
					Skill: "chat",
					Data: &dto.NLUSkillItem{
						Task:          "chat.small_talk",
						TtsShow:       faqVectorDo.FaqAnswer,
						TtsNorm:       faqVectorDo.FaqAnswer,
						Count:         1,
						IsResultValid: true,
					},
				},
			},
			ModelOutputIntent: "闲聊",
			FaqVector:         faqVectorDo,
		}
		resp, err := util.ReplyAny(res)
		if err != nil {
			s.log.WithContext(ctx).Errorf("FetchVectorNLU ReplyAny err: %+v", err)
			return nil, false, v1.ErrorInternalError("nlu")
		}

		return resp, true, nil
	}

	osVersion := custom_context.GetXOSVersion(ctx)
	pkgTime := 0
	if len(osVersion) > 2 {
		pkgTime = cast.ToInt(osVersion[1:])
	}

	// 一代结构
	res := &dto.NluData{
		UserID:    "",
		RequestID: "",
		Skill:     "chat",
		Task:      "chat.small_talk",
		TtsShow:   faqVectorDo.FaqAnswer,
		TtsNorm:   faqVectorDo.FaqAnswer,
		FaqVector: faqVectorDo,
	}
	if pkgTime != 0 && pkgTime < common.PaiVersionFlag {
		res.TtsNorm = strings.Replace(res.TtsNorm, "小思", "小派", -1)
		res.TtsShow = strings.Replace(res.TtsShow, "小思", "小派", -1)
	}

	resp, err := util.ReplyAny(res)
	if err != nil {
		s.log.WithContext(ctx).Errorf("FetchVectorNLU ReplyAny err: %+v", err)
		return nil, false, v1.ErrorInternalError("nlu")
	}

	return resp, true, nil
}

func (s *LuiService) SingleAsrVectorLib(ctx context.Context, req *v1.LuiNluRequest) (*structpb.Struct, bool, error) {
	if len(req.AsrInfo) == 0 {
		return nil, false, nil
	}

	// 连续对话 非首轮
	if req.Continuous && req.SentenceId > 0 {
		return nil, false, nil
	}

	sl := []rune(req.AsrInfo)
	if len(sl) == 1 && unicode.Is(unicode.Han, sl[0]) {
		tts := s.thirdConf.SingleWordTts[rand.Intn(len(s.thirdConf.SingleWordTts))]
		// 二代结构
		if (strings.Contains(s.thirdConf.XPad2AppId, custom_context.GetXAppId(ctx)) || strings.Contains(s.thirdConf.XPad1V2AppId, custom_context.GetXAppId(ctx))) && s.confWhiteOsVersion.Status == 1 {
			res := dto.QueryPad2Resp{
				TtsShow: tts,
				TtsNorm: tts,
				Data: []*dto.Pad2SkillData{
					{
						Skill: "no_skill",
						Data: &dto.NLUSkillItem{
							Task:          "",
							TtsShow:       tts,
							TtsNorm:       tts,
							Count:         1,
							IsResultValid: true,
						},
					},
				},
				ModelOutputIntent: "异常query",
			}
			resp, err := util.ReplyAny(res)
			if err != nil {
				s.log.WithContext(ctx).Errorf("FetchVectorSingleAsrNLU ReplyAny err: %+v", err)
				return nil, false, v1.ErrorInternalError("nlu")
			}

			return resp, true, nil
		}

		// 一代结构
		res := &dto.NluData{
			UserID:    "",
			RequestID: "",
			Skill:     "no_skill",
			Task:      "",
			TtsShow:   tts,
			TtsNorm:   tts,
		}

		resp, err := util.ReplyAny(res)
		if err != nil {
			s.log.WithContext(ctx).Errorf("FetchVectorSingleAsrNLU ReplyAny err: %+v", err)
			return nil, false, v1.ErrorInternalError("nlu")
		}

		return resp, true, nil
	} else {
		return nil, false, nil
	}
}

func isMatchingGradeAndSemester(do dto.HotfixVectorResourceDO, grade, semester, hotfixSkill string) bool {
	var gradeFound, semesterFound bool

	for _, g := range do.GradeIDs {
		if strings.Contains(g, grade) {
			gradeFound = true
			break
		}
	}

	if hotfixSkill == common.HotfixSkillSearchQuality {
		return gradeFound
	}

	for _, s := range do.SemesterIDs {
		if strings.Contains(s, semester) {
			semesterFound = true
			break
		}
	}

	return gradeFound && semesterFound
}
