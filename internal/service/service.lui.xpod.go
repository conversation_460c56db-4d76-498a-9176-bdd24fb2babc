package service

import (
	"context"
	"encoding/json"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/types/known/structpb"
	v1 "lui-api/api/lui/v1"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	pkgSync "lui-api/internal/pkg/sync"
	"lui-api/pkg/util"
)

type CheckFuncParams func(context.Context, interface{}, string) (bool, *dto.Pad2SkillData)

func (s *LuiService) GetFunc(name string) (fn CheckFuncParams, ok bool) {
	var FuncMap = map[string]CheckFuncParams{
		"make_call": s.CheckMakeCallFuncParams,
		"send_msg":  s.CheckSendMsgFuncParams,
		"contact":   s.CheckContactFuncParams,
	}
	fn, ok = FuncMap[name]
	return
}

func (s *LuiService) LuiNluControllerResult(ctx context.Context, req *v1.LuiNluRequest) (*structpb.Struct, error) {
	s.log.WithContext(ctx).Infof("LuiNluControllerResult request: %v", req)

	memoryContexts, _ := s.memoryBiz.GetXPodMemoryContext(ctx, custom_context.GetXDeviceId(ctx))

	funcs, err := s.luiControllerBiz.GetLuiFunctions(ctx, req, memoryContexts)
	if err != nil || len(funcs) == 0 {
		return s.getErrResult(ctx, err), nil
	}
	res := dto.QueryPad2Resp{}
	//槽位校验
	for _, function := range funcs {
		fn, ok := s.GetFunc(function.FuncName)
		if ok {
			paramsStr, _ := json.Marshal(function.FuncParamaters)
			checked, skill := fn(ctx, req, cast.ToString(paramsStr))
			if checked {
				res.Data = append(res.Data, skill)
			}
		} else if len(res.Data) == 0 && function.IsSee2say == "1" {
			paramsStr, _ := json.Marshal(function.FuncParamaters)
			skillData := &dto.Pad2SkillData{
				Skill: "see_2_say",
				Data: &dto.NLUSkillItem{
					Widget:    cast.ToString(paramsStr),
					IsSee2say: function.IsSee2say,
				},
			}
			res.Data = append(res.Data, skillData)
		}
	}
	if len(res.Data) == 0 {
		nlu4lui, FetchNLU4LUIErr := s.skillHub.FetchNLU4LUI(ctx, funcs, req.TalId, custom_context.GetXDeviceId(ctx))
		if FetchNLU4LUIErr != nil {
			s.log.WithContext(ctx).Warnf("FetchPadV2NLU4LUI err: %+v", FetchNLU4LUIErr)
			return s.getErrResult(ctx, FetchNLU4LUIErr), nil
		}

		for i, skill := range nlu4lui.SkillList {
			res.Data = append(res.Data, &dto.Pad2SkillData{
				Skill: skill.Skill,
				Sort:  i,
				Data:  &skill,
			})
		}
		if len(res.Data) == 1 {
			skill := res.Data[0]
			res.TtsNorm = skill.Data.TtsNorm
			res.TtsShow = skill.Data.TtsShow
			res.ModelOutputIntent = nlu4lui.ModelOutputIntent
		}
	}

	if len(res.Data) == 0 {
		return s.getErrResult(ctx, nil), nil
	}

	pkgSync.Go(ctx, s.log, func() {
		memoryContextReq := dto.MemoryContextReq{
			UserTalID: req.TalId,
			SessionId: req.RequestId,
			AsrInfo:   req.AsrInfo,
		}
		_ = s.memoryBiz.AddXPodMemoryContext(ctx, memoryContextReq, funcs, &res)
	})

	resp, err := util.ReplyAny(res)
	if err != nil {
		s.log.WithContext(ctx).Warnf("LuiNluControllerResult ReplyAny err: %+v; req: %+v", err, req)
		return s.getErrResult(ctx, err), nil
	}
	s.log.WithContext(ctx).Infof("LuiNluControllerResult resp: %v", resp)

	return resp, nil
}

func (s *LuiService) getErrResult(ctx context.Context, error error) *structpb.Struct {
	s.log.WithContext(ctx).Warnf("getErrResult error: %v", error)
	res := dto.QueryPad2Resp{}
	luiWidgetContent := dto.Nlu4LuiWidgetContent{
		Category: 0,
		Title:    "",
		Data:     nil,
		Scheme:   "",
	}

	luiWidget := dto.Nlu4LuiWidget{
		Content: luiWidgetContent,
	}
	luiWidgetBt, _ := json.Marshal(luiWidget)

	nlu4lui := &dto.NLUSkillItem{}
	nlu4lui.TtsNorm = "这个小思还在学习，不过我可以帮你打电话、发消息，还能回答你的好奇问题哦"
	nlu4lui.TtsShow = "这个小思还在学习，不过我可以帮你打电话、发消息，还能回答你的好奇问题哦"
	nlu4lui.Widget = string(luiWidgetBt)
	skillData := &dto.Pad2SkillData{
		Skill: "no_skill",
		Data:  nlu4lui,
	}
	res.Data = append(res.Data, skillData)
	res.TtsNorm = nlu4lui.TtsNorm
	res.TtsShow = nlu4lui.TtsShow
	resp, _ := util.ReplyAny(res)
	return resp

}

func (s *LuiService) CheckMakeCallFuncParams(ctx context.Context, _ interface{}, paramsStr string) (bool, *dto.Pad2SkillData) {
	matchRzt := dto.XpodContactMatchRzt{}
	err := json.Unmarshal([]byte(paramsStr), &matchRzt)
	if err != nil {
		s.log.WithContext(ctx).Errorf("CheckFuncParams param convert error: %v, params:%+v", err, paramsStr)
		return false, nil
	}

	if matchRzt.ContactName == "" {
		skillData := &dto.Pad2SkillData{
			Skill: "make_call",
			Data: &dto.NLUSkillItem{
				TtsNorm: "打电话给谁呢？",
				TtsShow: "打电话给谁呢？",
			},
		}
		return true, skillData
	}
	return false, nil
}

func (s *LuiService) CheckSendMsgFuncParams(ctx context.Context, _ interface{}, paramsStr string) (bool, *dto.Pad2SkillData) {
	matchRzt := dto.XpodContactMatchRzt{}
	err := json.Unmarshal([]byte(paramsStr), &matchRzt)
	if err != nil {
		s.log.WithContext(ctx).Errorf("CheckSendMsgFuncParams param convert error: %v, params:%+v", err, paramsStr)
		return false, nil
	}

	if matchRzt.ContactName == "" {
		skillData := &dto.Pad2SkillData{
			Skill: "send_msg",
			Data: &dto.NLUSkillItem{
				TtsNorm: "发消息给谁呢？",
				TtsShow: "发消息给谁呢？",
			},
		}
		return true, skillData
	}
	return false, nil
}

func (s *LuiService) CheckContactFuncParams(_ context.Context, _ interface{}, paramsStr string) (bool, *dto.Pad2SkillData) {

	skillData := &dto.Pad2SkillData{
		Skill: "contact",
		Data: &dto.NLUSkillItem{
			TtsNorm: "需要小思帮你打电话还是发消息呢？",
			TtsShow: "需要小思帮你打电话还是发消息呢？",
			Widget:  paramsStr,
		},
	}
	return true, skillData

}
