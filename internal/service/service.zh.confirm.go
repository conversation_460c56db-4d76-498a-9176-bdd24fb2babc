/*
* Author:  <PERSON><PERSON><PERSON>Q<PERSON>g
* Date:    2024/7/9 18:32
* Description:
 */

package service

import (
	"context"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/structpb"
	v1 "lui-api/api/lui/v1"
	"lui-api/pkg/util"
)

func (s *LuiService) ZhWordConfirm(ctx context.Context, req *v1.LuiNluConfirmRequest) (*structpb.Struct, error) {
	if req.AsrInput == nil {
		return nil, errors.New("asrInput is nil")
	}
	if req.UserSystem == nil {
		return nil, errors.New("userSystem is nil")
	}

	res, err := s.bizNlu.FetchZhConfirmNLU4LUI(ctx, req)
	if err != nil {
		return nil, err
	}
	resp, _ := util.ReplyAny(res)
	return resp, nil
}
