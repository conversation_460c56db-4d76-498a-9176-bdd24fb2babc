package service

import (
	"context"
	"encoding/json"
	"lui-api/internal/biz"
	"lui-api/internal/pkg/utils"
	"reflect"

	//"math/rand"
	"sort"
	"strings"
	"sync"
	"time"

	v1 "lui-api/api/lui/v1"
	"lui-api/internal/common"
	"lui-api/internal/data/dto"
	"lui-api/internal/pkg/custom_context"
	pkgSync "lui-api/internal/pkg/sync"
	"lui-api/pkg/util"

	"github.com/mitchellh/mapstructure"
	"google.golang.org/protobuf/types/known/structpb"
)

const (
	Version0606 = "240606"
	Version0701 = "240701"
)

// 找一类学科课程 有学科 无结果
// 检索有该槽位组合
var tssSlice = []string{
	"哎呀，这个课程我没找到，不过别担心，我已经为你打开全部课程资源，快去看看有没有需要的课程吧！",
	"你想要的资源我没有找到，我已经把其他所有课程资源准备好了，快来选你的心头好！",
	"你想要的资源我没有找到。不过，我已经把所有课程资源准备好了，自由挑选哦！",
	"你想要的资源我没有找到呢，不过全部课程资源已经就位，等你来一探究竟！",
}

// LuiNluPad2Result 垂域混排结果返回
func (s *LuiService) LuiNluPad2Result(ctx context.Context, req *v1.LuiNluRequest) (*structpb.Struct, error) {
	s.log.WithContext(ctx).Infof("LuiNluPad2Result req: %v", util.Marshal(req))

	if req.GetTalId() == "" {
		s.log.WithContext(ctx).Warnf("LuiNluPad2Result req talId is empty")
	}

	if req.SceneMode != dto.SceneModeFullView {
		_ = s.memoryBiz.DelDeepSeekFlag(ctx, req.TalId)
	}

	nlu4lui, hotfixDO, err := s.QueryHotfix2Vdb(ctx, req)
	if nlu4lui == nil || err != nil {
		// 获取上下文
		ctx = dto.SetCtxSceneCode(ctx, req.BizType)
		memoryContexts, _ := s.memoryBiz.GetXPadMemoryContext(ctx, req.TalId, dto.LuiPad2MaxLen)
		//调用nlu
		nlu4lui, err = s.bizNlu.FetchPadV3NLU4LUI(ctx, req, memoryContexts)
		if err != nil {
			if strings.Contains(err.Error(), "100001") {
				s.log.WithContext(ctx).Warnf("FetchPadV2NLU4LUI err: %+v", err)
				return nil, nil
			}
			s.log.WithContext(ctx).Warnf("FetchPadV2NLU4LUI err: %+v", err)
			return nil, v1.ErrorInternalError("LuiNluPad2Result" + err.Error())
		}

		//可见即可说
		if nlu4lui != nil && nlu4lui.SceneResult != nil && nlu4lui.SceneResult.ClickArea != nil {
			vsResp, _ := s.VisibleSaidResp(ctx, req, nlu4lui)
			if vsResp != nil {
				pkgSync.Go(ctx, s.log, func() {
					memoryContextReq := dto.MemoryContextReq{
						UserTalID:   req.TalId,
						SessionId:   req.RequestId,
						AsrInfo:     req.AsrInfo,
						IllegalType: "",
						Hotfix:      hotfixDO,
						SceneCode:   nlu4lui.SceneCode,
					}
					_ = s.memoryBiz.AddXPadMemoryContext(context.TODO(), memoryContextReq, vsResp, nlu4lui)
				})
				s.log.WithContext(ctx).Infof("LuiNluPad2Result vsResp: %+v", util.Marshal(vsResp))
				return vsResp, nil
			}
		}

		// 多模意图处理
		multiResp, _ := s.MultiModeResp(ctx, req, nlu4lui)
		if multiResp != nil {
			pkgSync.Go(ctx, s.log, func() {
				memoryContextReq := dto.MemoryContextReq{
					UserTalID:   req.TalId,
					SessionId:   req.RequestId,
					AsrInfo:     req.AsrInfo,
					IllegalType: "",
					Hotfix:      hotfixDO,
					SceneCode:   nlu4lui.SceneCode,
				}
				err = s.memoryBiz.AddXPadMemoryContext(context.TODO(), memoryContextReq, multiResp, nlu4lui)
				s.log.WithContext(ctx).Infof("MultiMode memoryBiz.AddXPadMemoryContext err: %+v", err)
			})
			s.log.WithContext(ctx).Infof("LuiNluPad2Result multiResp: %+v", util.Marshal(multiResp))
			return multiResp, nil
		}

		// 深度思考处理
		if nlu4lui != nil && nlu4lui.DeepSeekInfo != nil {
			deepSeekResp, _ := s.HandleDeepSeek(ctx, req, nlu4lui)
			if deepSeekResp != nil {
				return deepSeekResp, nil
			}
		}

		if nlu4lui != nil && nlu4lui.ExtraResp != nil {
			if nlu4lui.ExtraResp.UnderScreenExercises && nlu4lui.ExtraResp.MultiModalInfo != nil {
				underScreenExercises, _ := s.UnderScreenExercises(ctx, req, nlu4lui)
				if underScreenExercises != nil {
					pkgSync.Go(ctx, s.log, func() {
						memoryContextReq := dto.MemoryContextReq{
							UserTalID:   req.TalId,
							SessionId:   req.RequestId,
							AsrInfo:     req.AsrInfo,
							IllegalType: "",
							Hotfix:      hotfixDO,
							SceneCode:   nlu4lui.SceneCode,
						}
						_ = s.memoryBiz.AddXPadMemoryContext(context.TODO(), memoryContextReq, underScreenExercises, nlu4lui)
					})
					s.log.WithContext(ctx).Infof("LuiNluPad2Result underScreenExercises: %+v", util.Marshal(underScreenExercises))
					return underScreenExercises, nil
				}
			}
		}
	}

	// 进入小思工作坊
	workshopResp, _ := s.HandleWorkshop(ctx, req, nlu4lui)
	if workshopResp != nil {
		return workshopResp, nil
	}

	// 小思工作坊创造作品
	workshopCreationResp, _ := s.HandleWorkshopCreation(ctx, req, nlu4lui)
	if workshopCreationResp != nil {
		return workshopCreationResp, nil
	}

	// 小思滤镜处理,走agent
	if nlu4lui != nil && nlu4lui.FiltersInfo != nil && !nlu4lui.FiltersInfo.ToSkill {
		filterResp, _ := s.HandleFilters(ctx, req, nlu4lui)
		if filterResp != nil {
			return filterResp, nil
		}
	}

	// 记录讲题意图的日志
	_ = s.HandleExplainQuestion(ctx, req, nlu4lui)

	var wg sync.WaitGroup
	taskCount := len(nlu4lui.SkillList)
	rc := make(chan *dto.Pad2SkillData, taskCount)
	defer close(rc)

	//遍历skill列表 老nlu直接append 新nlu并发调用pad2接口
	skillNum := len(nlu4lui.SkillList)
	for i, skill := range nlu4lui.SkillList {
		skill.AsrInfo = req.AsrInfo
		skill.SessionId = req.RequestId
		skill.SentenceId = req.SentenceId
		switch skill.Skill {
		case common.SearchCourse.ToString():
			wg.Add(1)
			go func(skill2 dto.NLUSkillItem, sort int) {
				defer wg.Done()
				resp, respErr := s.buildPadV2SearchCourseNLU4LuiResp(ctx, &skill2, sort, skillNum, req.AsrInfo, req.AsrPinyin, req.TalId)
				if respErr == nil {
					rc <- resp
				} else {
					rc <- nil
				}
			}(skill, i)
		case common.SearchQuality.ToString():
			wg.Add(1)
			go func(skill2 dto.NLUSkillItem, sort int) {
				defer wg.Done()
				resp, respErr := s.buildPadV2SearchCultivationResp(ctx, &skill2, sort, skillNum)
				if respErr == nil {
					rc <- resp
				} else {
					rc <- nil
				}
			}(skill, i)
		case common.ContinueLearnSubject.ToString():
			wg.Add(1)
			go func(skill2 dto.NLUSkillItem, sort int) {
				defer wg.Done()
				resp, respErr := s.buildPadV2CourseLearnTrackResp(ctx, &skill2, req.TalId, sort)
				if respErr == nil {
					rc <- resp
				} else {
					rc <- nil
				}
			}(skill, i)
		case common.ContinueLearnQuality.ToString():
			wg.Add(1)
			go func(skill2 dto.NLUSkillItem, sort int) {
				defer wg.Done()
				resp, respErr := s.buildPadV2CultivationLearnTrackResp(ctx, &skill2, req.TalId, sort)
				if respErr == nil {
					rc <- resp
				} else {
					rc <- nil
				}
			}(skill, i)
		case common.PaperSubjectConfirm.ToString():
			wg.Add(1)
			go func(skill2 dto.NLUSkillItem, sort int) {
				defer wg.Done()
				resp, respErr := s.buildPadV2PaperConfirmResp(ctx, &skill2, req.AsrInfo, req.AsrPinyin)
				if respErr == nil {
					rc <- resp
				} else {
					rc <- nil
				}
			}(skill, i)
		case common.SearchPaper.ToString():
			wg.Add(1)
			go func(skill2 dto.NLUSkillItem, sort int) {
				defer wg.Done()
				resp, respErr := s.buildV2SearchPaperResp(ctx, &skill2, req.AsrInfo, req.AsrPinyin, req.TalId)
				if respErr == nil {
					rc <- resp
				} else {
					rc <- nil
				}
			}(skill, i)
		case common.ListenWrite.ToString():
			wg.Add(1)
			go func(skill2 dto.NLUSkillItem, sort int) {
				defer wg.Done()
				resp, respErr := s.buildV2DictationResp(ctx, &skill2, req.TalId, sort)
				if respErr == nil {
					rc <- resp
				} else {
					rc <- nil
				}
			}(skill, i)
		case common.ClickRead.ToString(), common.SearchCourseBook.ToString():
			wg.Add(1)
			go func(skill2 dto.NLUSkillItem, sort int) {
				defer wg.Done()
				resp, respErr := s.buildPadV2ClickReadResp(ctx, &skill2, sort, req.TalId)
				if respErr == nil {
					rc <- resp
				} else {
					rc <- nil
				}
			}(skill, i)
		case common.Recite.ToString():
			wg.Add(1)
			go func(skill2 dto.NLUSkillItem, sort int) {
				defer wg.Done()
				resp, respErr := s.buildPadV2RecitationResp(ctx, &skill2, req.TalId, sort)
				if respErr == nil {
					rc <- resp
				} else {
					rc <- nil
				}
			}(skill, i)
		case common.CheckTypeConfirm.ToString():
			wg.Add(1)
			go func(skill2 dto.NLUSkillItem, sort int) {
				defer wg.Done()
				resp, respErr := s.buildPadV2AppTypeConfirmResp(ctx, &skill2, req.AsrInfo, req.AsrPinyin)
				if respErr == nil {
					rc <- resp
				} else {
					rc <- nil
				}
			}(skill, i)
		case common.CheckDictateTypeConfirm.ToString():
			wg.Add(1)
			go func(skill2 dto.NLUSkillItem, sort int) {
				defer wg.Done()
				resp, respErr := s.buildPadV2DictateTypeConfirmResp(ctx, &skill2, req.AsrInfo, req.AsrPinyin, req.GradeId)
				if respErr == nil {
					rc <- resp
				} else {
					rc <- nil
				}
			}(skill, i)
		case common.ConfirmInvalid.ToString():
			wg.Add(1)
			go func(skill2 dto.NLUSkillItem, sort int) {
				defer wg.Done()
				resp, respErr := s.buildPadV2ConfirmInvalidResp(ctx, &skill2)
				if respErr == nil {
					rc <- resp
				} else {
					rc <- nil
				}
			}(skill, i)
		case common.JzxV15QuestionSkill.ToString():

			pkgTime := custom_context.GetXOsPkgDate(ctx)
			if pkgTime != 0 && pkgTime > common.SearchExerciseVersionFlag {
				wg.Add(1)
				go func(skill2 dto.NLUSkillItem, sort int) {
					defer wg.Done()
					resp, respErr := s.buildPadV2JzxV15QuestionResp(ctx, &skill2, sort)
					if respErr == nil {
						rc <- resp
					} else {
						rc <- nil
					}
				}(skill, i)
			} else {
				rc <- nil
			}
		case common.SearchBook.ToString(), common.SkillNewspaper.ToString(), common.SkillDocumentary.ToString():
			wg.Add(1)
			go func(skill2 dto.NLUSkillItem, sort int) {
				defer wg.Done()
				//找课本目前走点读逻辑
				resp, respErr := s.buildPadV2EBookResp(ctx, &skill2, sort)
				if respErr == nil {
					rc <- resp
				} else {
					rc <- nil
				}
			}(skill, i)
		default:
			wg.Add(1)
			go func(skill2 dto.NLUSkillItem, sort int) {
				defer wg.Done()
				resp, respErr := s.buildAppFuncNLU4LuiResp(ctx, &skill2, sort, req.Grade, req.BizType, nlu4lui.ModelOutputIntent)
				if respErr == nil {
					rc <- resp
				} else {
					rc <- nil
				}
			}(skill, i)
		}
	}
	wg.Wait()

	var (
		skillCount     = taskCount
		isFullView     = false
		isAccessingLLM = false
		isSessionEnd   = false
		endReason      = ""
		//skillDataList  = make([]*dto.Pad2SkillData, 0)
		res = dto.QueryPad2Resp{}
	)

	for i := taskCount; i > 0; i-- {
		queryData := <-rc
		if queryData != nil {
			if queryData.Data.TtsNorm == common.TtsTpl431 {
				skillCount--
				if skillCount > 0 {
					continue
				}
			}

			queryData.Data.SceneCode = nlu4lui.SceneCode
			if req.BizType == common.BizTypeCompanionLearn {
				tts := s.GetCompanionLearningTTS(queryData.Skill)
				if tts != "" {
					queryData.Data.TtsNorm = tts
				}
			}

			switch queryData.Skill {
			case common.SearchCourse.ToString(), common.SearchQuality.ToString(), common.ContinueLearnSubject.ToString(), common.ContinueLearnQuality.ToString():
				// 播放视频终止连续对话
				if queryData.Data.Count == 1 {
					isSessionEnd = true
					endReason = "video"
				}
			}

			if queryData.Data.FuncName == "search_progress" {
				isSessionEnd = true
				endReason = queryData.Data.FuncName
			}

			// 连续对话下的session终止
			if req.Continuous && queryData.Data.Skill == "functional" {
				if queryData.Data.Task == "functional.shutDown" || queryData.Data.Task == "functional.closeScreen" || queryData.Data.Task == "functional.exit" {
					isSessionEnd = true
					endReason = queryData.Data.Task
				}

				// 查题打开视频页面
				if nlu4lui.ModelOutputIntent == "查题" && queryData.Data.Command != nil {
					commandM := map[string]interface{}{}
					command, _ := json.Marshal(queryData.Data.Command)
					_ = json.Unmarshal(command, &commandM)
					if commandM != nil {
						appid, ok := commandM["param"].(map[string]interface{})["appid"].(string)
						if ok && appid == "jumph5" {
							isSessionEnd = true
							endReason = appid
						}
					}
				}
			}

			res.Data = append(res.Data, queryData)
		}
	}

	// 如果混排，过滤掉没有资源的数据
	if len(res.Data) > 1 {
		res.Data = s.filterResourceEmptyDataInMultiSkillData(ctx, res.Data)
	}

	slice := res.Data
	sort.Slice(slice, func(i, j int) bool {
		return slice[i].Sort < slice[j].Sort
	})

	var (
		showType      int
		dispatch      *dto.Dispatch
		sceneCode     = s.bizNlu.GetSceneCode(ctx, req.BizType)
		pad2SkillData = make([]*dto.Pad2SkillData, 0)
	)
	for i, data := range slice {
		if skillNum > 1 && (strings.Contains(data.Data.TtsNorm, common.TtsInterceptAppTmp) || strings.Contains(data.Data.TtsNorm, common.TtsTplInterceptCnhtenment) || strings.Contains(data.Data.TtsNorm, common.TtsTplIntercept)) {
			continue
		}
		//更新skill枚举映射
		dataSkill := data.Skill
		if data.Data != nil && data.Data.Task == common.BaiKeKnowledge {
			dataSkill = common.BaiKeKnowledge
			slice[i].Data.Task = common.Baike.ToString()
		}

		if skillInfo, ok := dto.WebSkillInfoMap[dataSkill]; ok {
			//找课程二次确认临时使用paper_subject_confirm的skill,上次已处理VerticalDomain
			if !(dataSkill == common.PaperSubjectConfirm.ToString() && slice[i].VerticalDomain == "课程学习") {
				slice[i].VerticalDomain = skillInfo.VerticalDomain
				slice[i].ModuleId = skillInfo.ModuleId
				slice[i].ParserId = skillInfo.ParserId
			}
		}

		if !strings.Contains(s.thirdConf.AppIdMap["xPad25AppIds"], custom_context.GetXAppId(ctx)) && len(res.Data) == 1 {
			dispatchXlj, showType := s.rnBiz.GetRn(ctx, req.SceneMode, req.RnVersion, data.Skill, data.Data, sceneCode)
			if dispatchXlj != nil {
				dispatch = dispatchXlj
				res.ShowType = showType
			}
		}

		if data.Skill == common.Poem.ToString() {
			poemInfo := dto.PoemInfo{}
			if err := json.Unmarshal([]byte(data.Data.Widget), &poemInfo); err == nil {
				if len(poemInfo.Content) > 0 {
					content := poemInfo.Content[0]
					if content.ShowType == common.PoemShowTypeAuthor {
						skillInfo := dto.WebSkillInfoMap[common.Baike.ToString()]
						if skillInfo != nil {
							slice[i].VerticalDomain = skillInfo.VerticalDomain
							slice[i].ModuleId = skillInfo.ModuleId
							slice[i].ParserId = skillInfo.ParserId
						}
					}
				}
			}

			if strings.Contains(s.thirdConf.AppIdMap["xiaosi2AppId"], custom_context.GetXAppId(ctx)) && !strings.Contains(s.thirdConf.AppIdMap["xPad25AppIds"], custom_context.GetXAppId(ctx)) && len(res.Data) == 1 {
				bundle := s.rnBiz.GetSkillRn(ctx, req.RnVersion, data.Skill, sceneCode)
				if len(bundle) > 0 {
					dispatchTmp := &dto.Dispatch{
						OpenType: 2,
						Router:   dto.Router{Bundle: bundle},
					}
					if dispatch == nil {
						dispatch = dispatchTmp
						res.ShowType = 2
					} else {
						data.Data.Dispatch = dispatchTmp
					}
				}
			}
		}

		if data.Skill == common.PaperSubjectConfirm.ToString() || data.Skill == common.CheckTypeConfirm.ToString() || data.Skill == common.CheckDictateTypeConfirm.ToString() {
			res.ConfirmSkill = true
		}

		pad2SkillData = append(pad2SkillData, slice[i])
		if data.Skill == "functional" && data.Data.Command != nil {
			url := s.thirdConf.AllSkillsWebUrl
			if strings.Contains(s.thirdConf.AppIdMap["xueLianJiAppIds"], custom_context.GetXAppId(ctx)) {
				url = s.thirdConf.XueLianJiAllSkillsWebUrl
			}
			commandM := map[string]interface{}{}
			command, _ := json.Marshal(data.Data.Command)
			_ = json.Unmarshal(command, &commandM)
			if commandM == nil {
				continue
			}
			v, ok := commandM["param"].(map[string]interface{})["value"].(string)
			if !ok || v != "技能中心" {
				continue
			}

			if sceneCode == common.SceneXiaoSi {
				dispatch = &dto.Dispatch{
					OpenType: 1,
					Router: dto.Router{
						Web: dto.Web{
							Url: url,
						}},
				}
			}
		}
	}

	// skill 是否走大模型 ｜ 是否全屏页打开
	for _, dataItem := range pad2SkillData {
		isFullView = s.IsFullView(ctx, nlu4lui.ModelOutputIntent, nlu4lui.FunctionList, dataItem, len(pad2SkillData), req, hotfixDO)
		isAccessingLLM = s.IsAccessingLLM(ctx, nlu4lui.ModelOutputIntent, dataItem, len(pad2SkillData), req, hotfixDO)

		s.SetAiAgent(ctx, req, dataItem)
		if strings.Contains(s.thirdConf.AppIdMap["xPad25AppIds"], custom_context.GetXAppId(ctx)) && (sceneCode == common.SceneXiaoSi) {
			dataItem.Data.SceneMode = dto.SceneModeLightweight
		}
	}

	res.Data = pad2SkillData
	res.ModelOutputIntent = nlu4lui.ModelOutputIntent
	res.HotfixVector = hotfixDO
	res.LegalCheckType = nlu4lui.LegalCheckType
	res.ControllerVersion = nlu4lui.ControllerVersion
	res.RewriteInfo = nlu4lui.RewriteInfo
	res.VisionInput = nlu4lui.VisionInput
	if !(res.ModelOutputIntent == common.IntentChaTi && isFullView) {
		res.IsSessionEnd = isSessionEnd
	}
	res.EndReason = endReason

	// 通过 rn 服务设置技能的分发信息
	if strings.Contains(s.thirdConf.AppIdMap["xPad25AppIds"], custom_context.GetXAppId(ctx)) {
		dispatch, showType = s.rnBiz.GetSetDispatch(ctx, sceneCode, req.RnVersion, req.BizType, pad2SkillData)
		res.ShowType = showType
		res.Dispatch = dispatch
	} else {
		res.Dispatch = dispatch
	}

	if res.Dispatch != nil {
		res.IsSessionEnd = false
	}

	if taskCount == 1 && len(pad2SkillData) == 1 {
		skill := pad2SkillData[0]
		res.TtsNorm = skill.Data.TtsNorm
		res.TtsShow = skill.Data.TtsShow
	}

	if req.BizType != common.BizTypeCompanionLearn {
		if len(pad2SkillData) == 0 {
			res.TtsNorm = common.TtsTpl131
			res.TtsShow = common.TtsTpl131
		} else if len(pad2SkillData) > 1 || res.TtsNorm == "" {
			res.TtsNorm = common.TtsTplT121
			res.TtsShow = common.TtsTplT121
		}
	}

	// 使用滤镜音色走skill
	if nlu4lui != nil && nlu4lui.FiltersInfo != nil && nlu4lui.FiltersInfo.ToSkill {
		// 设置滤镜音色
		voiceId, _ := s.thirdConf.Filters.FiltersNameMapVideoid[nlu4lui.FiltersInfo.FiltersName]
		res.TtsParam = &dto.TtsParam{VoiceId: voiceId}
	}

	illegalType := ""
	var checkInfo biz.LegalCheckResp
	text := s.legalBiz.CheckPad2IfNeed(&res)
	if s.thirdConf.LegalCheck.CheckEnable && text != "" {
		illegalType, _ = s.legalBiz.LegalCheck(ctx, req.TalId, text, "lui_output")
		if illegalType != "" {
			res = s.legalBiz.BuildPad2DefaultIllegalResp(illegalType, res)
		}
	}

	if s.thirdConf.LegalCheck.CheckEnable && (isAccessingLLM || util.InSliceString(res.ModelOutputIntent, s.thirdConf.LegalCheck.CheckIntent)) {
		illegalType, checkInfo, _ = s.legalBiz.LegalCheckInfo(ctx, req.TalId, req.AsrInfo, "lui_input")
		if illegalType != "" {
			res = s.legalBiz.BuildPad2QueryIllegalResp(illegalType, req.BizType, res)
		}
	}
	res.BizType = req.BizType
	resp, err := util.ReplyAny(res)
	if err != nil {
		s.log.WithContext(ctx).Errorf("LuiNluPad2Result ReplyAny err: %+v; nlu4lui: %+v", err, nlu4lui)
		return nil, v1.ErrorInternalError("LuiNluPad2Result")
	}

	startTime := ctx.Value(common.StartTime).(time.Time)
	if res.VisionInput != nil && (res.VisionInput.DesktopImageInfo != nil || res.VisionInput.TraceImageInfo != nil) {
		s.log.WithContext(ctx).Infof("LuiNluPad2Result_MultiModal_Latency: %v", time.Since(startTime).Seconds())
	} else {
		s.log.WithContext(ctx).Infof("LuiNluPad2Result_Latency: %v", time.Since(startTime).Seconds())
	}

	s.log.WithContext(ctx).Infof("LuiNluPad2Result resp: %v", util.Marshal(resp))

	//风控一级领导人
	if !(checkInfo.Data.RiskLabel1 == common.LegalCheckRiskPolitics && illegalType == "") {
		pkgSync.Go(ctx, s.log, func() {
			memoryContextReq := dto.MemoryContextReq{
				UserTalID:   req.TalId,
				SessionId:   req.RequestId,
				AsrInfo:     req.AsrInfo,
				IllegalType: illegalType,
				Hotfix:      hotfixDO,
				SceneCode:   nlu4lui.SceneCode,
			}
			_ = s.memoryBiz.AddXPadMemoryContext(context.TODO(), memoryContextReq, resp, nlu4lui)
		})
	} else {
		s.log.WithContext(ctx).Infof("checkInfo.Data.RiskLabel1 not add memory checkInfo:%+v", checkInfo)
	}

	return resp, nil
}

func (s *LuiService) filterResourceEmptyDataInMultiSkillData(ctx context.Context, skillDataList []*dto.Pad2SkillData) []*dto.Pad2SkillData {
	filteredSkillDataList := make([]*dto.Pad2SkillData, 0)
	// 涉及资源的skill
	resourceSkills := []string{common.SearchCourse.ToString(), common.SearchQuality.ToString(), common.SearchBook.ToString(), common.SkillNewspaper.ToString(), common.SkillDocumentary.ToString(), common.ClickRead.ToString(), common.PaperSubjectConfirm.ToString()}
	for _, skillData := range skillDataList {
		flag := false
		for _, resourceSkill := range resourceSkills {
			if resourceSkill == skillData.Skill {
				s.log.WithContext(ctx).Infof("resourceSkill: %v", util.Marshal(skillData))
				// 过滤掉资源为空的数据
				if skillData.Data == nil || skillData.Data.Widget == "" {
					flag = true
					break
				}
				widget := &dto.Nlu4LuiWidget{}
				err := json.Unmarshal([]byte(skillData.Data.Widget), &widget)
				if err != nil {
					s.log.WithContext(ctx).Errorf("filterResourceEmptyDataInMultiSkillData json.Unmarshal err: %+v", err)
				}
				if widget.Content == nil || widget.Content == "" {
					flag = true
					break
				} else {
					widgetContent := &dto.Nlu4LuiWidgetContent{}
					jsonData, err := json.Marshal(widget.Content)
					if err != nil {
						s.log.WithContext(ctx).Errorf("filterResourceEmptyDataInMultiSkillData Marshal%v", err)
					}
					err = json.Unmarshal(jsonData, &widgetContent)
					if err != nil {
						s.log.WithContext(ctx).Errorf("filterResourceEmptyDataInMultiSkillData json.Unmarshal err: %+v", err)
					}
					s.log.WithContext(ctx).Infof("widgetContent.Data: %v", util.Marshal(widgetContent))
					if widgetContent.Data == nil {
						flag = true
						break
					} else {
						val := reflect.ValueOf(widgetContent.Data)
						// 检查是否为切片且长度为空
						if val.Kind() == reflect.Slice && val.Len() == 0 {
							flag = true
							break
						}
					}
				}
			}
		}
		if flag {
			continue
		}
		filteredSkillDataList = append(filteredSkillDataList, skillData)
		// 如果resource list 都为空 则返回第一个， 走槽位
	}
	if len(filteredSkillDataList) == 0 && len(skillDataList) > 0 {
		filteredSkillDataList = append(filteredSkillDataList, skillDataList[0])
	}
	s.log.WithContext(ctx).Infof("filterResourceEmptyDataInMultiSkillData filteredSkillDataList: %v", util.Marshal(filteredSkillDataList))
	return filteredSkillDataList
}

func convertNLUSkillItemToNlu4LuiData(b *dto.NLUSkillItem) *dto.Nlu4LuiData {
	b.AllSlots = dto.MergeStructs(b.DefaultSlots, b.SlotDict)
	return &dto.Nlu4LuiData{
		RequestID:        b.SessionId,
		ModuleName:       "",
		IsValidWidget:    b.IsResultValid,
		ResultConfidence: b.Count,
		Skill:            b.Skill,
		SubIntent:        0,
		DefaultSlots:     b.DefaultSlots,
		SlotDict:         b.SlotDict,
		RawSlotDict:      b.RawSlotDict,
		AllSlots:         b.AllSlots,
		Category:         b.Category,
		ResourceList:     b.ResourceList,
		Word:             b.Word,
		Hit:              b.Hit,
		SimScore:         b.SimScore,
		SearchFlag:       b.SearchFlag,
	}
}

// 找学科课程
func (s *LuiService) buildPadV2SearchCourseNLU4LuiResp(ctx context.Context, nlu4lui *dto.NLUSkillItem, sort int, skillNum int, asrInfo, asrPinyin, talId string) (*dto.Pad2SkillData, error) {

	s.log.WithContext(ctx).Infof("buildPadV2SearchCourseNLU4LuiResp nlu4lui: %s", util.Marshal(nlu4lui))

	//旗舰款 澄清追问逻辑
	appId := custom_context.GetXAppId(ctx)
	version := utils.GetVersionNum(custom_context.GetXOSVersion(ctx))
	if strings.Contains(s.thirdConf.AppIdMap["xiaosi2AppId"], appId) && version >= Version0606 {
		if nlu4lui.MultiDialogSubject {
			// 获取上下文
			memoryContexts, _ := s.memoryBiz.GetXPadMemoryContext(ctx, talId, 1)
			tts := common.TTSFollowUpSubject
			if len(memoryContexts) > 0 && memoryContexts[len(memoryContexts)-1].TtsInfo == tts {
				tts := common.TTSOtherIntent
				res := &dto.Pad2SkillData{
					Skill: "no_skill",
					Data: &dto.NLUSkillItem{
						TtsNorm: tts,
						TtsShow: tts,
					},
				}
				return res, nil

			} else {
				res, err := s.buildPadV2PaperConfirmResp(ctx, nlu4lui, asrInfo, asrPinyin)
				if err != nil {
					s.log.WithContext(ctx).Errorf("buildPadV2PaperConfirmResp err: %+v; nlu4lui: %+v", err, nlu4lui)
					return nil, err
				}

				skillInfo := dto.WebSkillInfoMap[common.SearchCourse.ToString()]
				res.VerticalDomain = skillInfo.VerticalDomain
				res.ModuleId = skillInfo.ModuleId
				res.ParserId = skillInfo.ParserId
				return res, nil
			}

		} else if nlu4lui.MultiDialogVersion {
			tts := common.TTSFollowUpVersion
			memoryContexts, _ := s.memoryBiz.GetXPadMemoryContext(ctx, talId, 1) // 获取上下文
			if len(memoryContexts) > 0 && memoryContexts[len(memoryContexts)-1].TtsInfo == tts {
				tts = common.TTSOtherIntent
				res := &dto.Pad2SkillData{
					Skill: "no_skill",
					Data: &dto.NLUSkillItem{
						TtsNorm: tts,
						TtsShow: tts,
					},
				}
				return res, nil
			} else {

				luiWidgetContent := dto.Nlu4LuiWidgetContent{
					Category: 0,
					SlotDict: nlu4lui.AllSlots,
					Title:    "",
					Data:     nil,
					Word:     nlu4lui.Word,
					Scheme:   "",
					Confirm: &dto.Confirm{
						AsrInfo:   common.TTSFollowUpVersion,
						AsrPinyin: common.TTSFollowUpVersion,
						Key:       "",
						Name:      "",
						Options:   nil,
					},
				}
				luiWidget := dto.Nlu4LuiWidget{
					Content: luiWidgetContent,
				}
				luiWidgetBt, _ := json.Marshal(luiWidget)
				nlu4lui.Widget = string(luiWidgetBt)
				nlu4lui.TtsNorm = tts
				nlu4lui.TtsShow = tts

				skillInfo := dto.WebSkillInfoMap[common.SearchCourse.ToString()]

				return &dto.Pad2SkillData{
					Skill:          common.PaperSubjectConfirm.ToString(), //临时方案 支持二次追问唤醒
					Sort:           sort,
					Data:           nlu4lui,
					VerticalDomain: skillInfo.VerticalDomain,
					ModuleId:       skillInfo.ModuleId,
					ParserId:       skillInfo.ParserId,
				}, nil
			}
		}
	}

	nlu4luiReq := convertNLUSkillItemToNlu4LuiData(nlu4lui)
	interceptFlag, interceptTts := s.CheckIntercept(ctx, nlu4lui.AllSlots.GradeName, nlu4lui.Skill)

	if interceptFlag {
		luiWidgetContent := dto.Nlu4LuiWidgetContent{
			Category: 0,
			SlotDict: nlu4lui.AllSlots,
			Title:    "",
			Data:     nil,
			Word:     nlu4lui.Word,
			Scheme:   "",
		}

		luiWidget := dto.Nlu4LuiWidget{
			ModuleName:       nlu4luiReq.ModuleName,
			IsValidWidget:    nlu4luiReq.IsValidWidget,
			ResultConfidence: nlu4luiReq.ResultConfidence,
			NeedConfirm:      nlu4luiReq.NeedConfirm,
			Content:          luiWidgetContent,
		}
		luiWidgetBt, _ := json.Marshal(luiWidget)
		nlu4lui.TtsNorm = interceptTts
		nlu4lui.Widget = string(luiWidgetBt)
		return &dto.Pad2SkillData{
			Skill: nlu4lui.Skill,
			Sort:  sort,
			Data:  nlu4lui,
		}, nil
	}

	go s.UpdateUserProfileSubjectVersion(ctx, talId, nlu4lui.AllSlots.GradeName, nlu4lui.AllSlots.SubjectName, nlu4lui.AllSlots.VersionName)
	nluData, err := s.buildSearchCourseNLU4LuiResp(ctx, nlu4luiReq, skillNum, talId)
	if err != nil {
		s.log.WithContext(ctx).Errorf("buildPadV2SearchCourseNLU4LuiResp err: %+v; nlu4lui: %+v", err, nlu4lui)
		return nil, err
	}

	nlu4lui.TtsNorm = nluData.TtsNorm
	nlu4lui.Widget = nluData.Widget
	nlu4lui.TtsShow = nluData.TtsShow
	nlu4lui.Count = nluData.Count
	nlu4lui.ResourceList = nil
	return &dto.Pad2SkillData{
		Skill: nlu4lui.Skill,
		Sort:  sort,
		Data:  nlu4lui,
	}, nil
}

// 找素养课程
func (s *LuiService) buildPadV2SearchCultivationResp(ctx context.Context, nlu4lui *dto.NLUSkillItem, sort int, skillNum int) (*dto.Pad2SkillData, error) {

	nlu4luiReq := convertNLUSkillItemToNlu4LuiData(nlu4lui)
	interceptFlag, interceptTts := s.CheckIntercept(ctx, nlu4lui.AllSlots.GradeName, nlu4lui.Skill)

	if interceptFlag {
		luiWidgetContent := dto.Nlu4LuiWidgetContent{
			Category: 0,
			SlotDict: nlu4lui.AllSlots,
			Title:    "",
			Data:     nil,
			Word:     nlu4lui.Word,
			Scheme:   "",
		}

		luiWidget := dto.Nlu4LuiWidget{
			ModuleName:       nlu4luiReq.ModuleName,
			IsValidWidget:    nlu4luiReq.IsValidWidget,
			ResultConfidence: nlu4luiReq.ResultConfidence,
			NeedConfirm:      nlu4luiReq.NeedConfirm,
			Content:          luiWidgetContent,
		}
		luiWidgetBt, _ := json.Marshal(luiWidget)
		nlu4lui.TtsNorm = interceptTts
		nlu4lui.Widget = string(luiWidgetBt)

		return &dto.Pad2SkillData{
			Skill: nlu4lui.Skill,
			Sort:  sort,
			Data:  nlu4lui,
		}, nil
	}
	nluData, err := s.buildSearchCultivationResp(ctx, nlu4luiReq, skillNum)
	if err != nil {
		s.log.WithContext(ctx).Errorf("buildPadV2SearchCultivationResp err: %+v; nlu4lui: %+v", err, nlu4lui)
		return nil, err
	}
	nlu4lui.TtsNorm = nluData.TtsNorm
	nlu4lui.Widget = nluData.Widget
	nlu4lui.TtsShow = nluData.TtsShow
	nlu4lui.Count = nluData.Count
	nlu4lui.ResourceList = nil

	return &dto.Pad2SkillData{
		Skill: nlu4lui.Skill,
		Sort:  sort,
		Data:  nlu4lui,
	}, nil
}

func (s *LuiService) buildPadV2CourseLearnTrackResp(ctx context.Context, nlu4lui *dto.NLUSkillItem, talId string, sort int) (*dto.Pad2SkillData, error) {

	nlu4luiReq := convertNLUSkillItemToNlu4LuiData(nlu4lui)
	interceptFlag, interceptTts := s.CheckIntercept(ctx, nlu4lui.AllSlots.GradeName, nlu4lui.Skill)

	if interceptFlag || talId == "" {
		if talId == "" {
			interceptTts = common.TtsTpl431
		}
		luiWidgetContent := dto.Nlu4LuiWidgetContent{
			Category: 0,
			SlotDict: nlu4lui.AllSlots,
			Title:    "",
			Data:     nil,
			Word:     nlu4lui.Word,
			Scheme:   "",
		}

		luiWidget := dto.Nlu4LuiWidget{
			ModuleName:       nlu4luiReq.ModuleName,
			IsValidWidget:    nlu4luiReq.IsValidWidget,
			ResultConfidence: nlu4luiReq.ResultConfidence,
			NeedConfirm:      nlu4luiReq.NeedConfirm,
			Content:          luiWidgetContent,
		}
		luiWidgetBt, _ := json.Marshal(luiWidget)
		nlu4lui.TtsNorm = interceptTts
		nlu4lui.Widget = string(luiWidgetBt)

		return &dto.Pad2SkillData{
			Skill: nlu4lui.Skill,
			Sort:  sort,
			Data:  nlu4lui,
		}, nil
	}

	go s.UpdateUserProfileSubjectVersion(ctx, talId, nlu4lui.AllSlots.GradeName, nlu4lui.AllSlots.SubjectName, nlu4lui.AllSlots.VersionName)
	nluData, err := s.buildCourseLearnTrackResp(ctx, nlu4luiReq, talId)
	if err != nil {
		s.log.WithContext(ctx).Errorf("buildPadV2CourseLearnTrackResp err: %+v; nlu4lui: %+v", err, nlu4lui)
		return nil, err
	}
	nlu4lui.TtsNorm = nluData.TtsNorm
	nlu4lui.Widget = nluData.Widget
	nlu4lui.TtsShow = nluData.TtsShow
	nlu4lui.Count = nluData.Count
	nlu4lui.ResourceList = nil

	return &dto.Pad2SkillData{
		Skill: nlu4lui.Skill,
		Sort:  sort,
		Data:  nlu4lui,
	}, nil
}

func (s *LuiService) buildPadV2CultivationLearnTrackResp(ctx context.Context, nlu4lui *dto.NLUSkillItem, talId string, sort int) (*dto.Pad2SkillData, error) {

	nlu4luiReq := convertNLUSkillItemToNlu4LuiData(nlu4lui)
	interceptFlag, interceptTts := s.CheckIntercept(ctx, nlu4lui.AllSlots.GradeName, nlu4lui.Skill)

	if interceptFlag || talId == "" {
		if talId == "" {
			interceptTts = common.TtsTpl431
		}
		luiWidgetContent := dto.Nlu4LuiWidgetContent{
			Category: 0,
			SlotDict: nlu4lui.AllSlots,
			Title:    "",
			Data:     nil,
			Word:     nlu4lui.Word,
			Scheme:   "",
		}

		luiWidget := dto.Nlu4LuiWidget{
			ModuleName:       nlu4luiReq.ModuleName,
			IsValidWidget:    nlu4luiReq.IsValidWidget,
			ResultConfidence: nlu4luiReq.ResultConfidence,
			NeedConfirm:      nlu4luiReq.NeedConfirm,
			Content:          luiWidgetContent,
		}
		luiWidgetBt, _ := json.Marshal(luiWidget)
		nlu4lui.TtsNorm = interceptTts
		nlu4lui.Widget = string(luiWidgetBt)

		return &dto.Pad2SkillData{
			Skill: nlu4lui.Skill,
			Sort:  sort,
			Data:  nlu4lui,
		}, nil
	}

	nluData, err := s.buildCultivationLearnTrackResp(ctx, nlu4luiReq, talId)
	if err != nil {
		s.log.WithContext(ctx).Errorf("buildPadV2CultivationLearnTrackResp err: %+v; nlu4lui: %+v", err, nlu4lui)
		return nil, err
	}

	nlu4lui.TtsNorm = nluData.TtsNorm
	nlu4lui.Widget = nluData.Widget
	nlu4lui.Count = nluData.Count
	nlu4lui.TtsShow = nluData.TtsShow
	nlu4lui.ResourceList = nil

	return &dto.Pad2SkillData{
		Skill: nlu4lui.Skill,
		Sort:  sort,
		Data:  nlu4lui,
	}, nil
}

// 听写
func (s *LuiService) buildV2DictationResp(ctx context.Context, nlu4lui *dto.NLUSkillItem, talId string, sort int) (*dto.Pad2SkillData, error) {

	nlu4luiReq := convertNLUSkillItemToNlu4LuiData(nlu4lui)
	interceptFlag, interceptTts := s.CheckIntercept(ctx, nlu4lui.AllSlots.GradeName, nlu4lui.Skill)

	if interceptFlag {
		luiWidgetContent := dto.Nlu4LuiWidgetContent{
			Category: 0,
			SlotDict: nlu4lui.AllSlots,
			Title:    "",
			Data:     nil,
			Word:     nlu4lui.Word,
			Scheme:   "",
		}

		luiWidget := dto.Nlu4LuiWidget{
			ModuleName:       nlu4luiReq.ModuleName,
			IsValidWidget:    nlu4luiReq.IsValidWidget,
			ResultConfidence: nlu4luiReq.ResultConfidence,
			NeedConfirm:      nlu4luiReq.NeedConfirm,
			Content:          luiWidgetContent,
		}
		luiWidgetBt, _ := json.Marshal(luiWidget)
		nlu4lui.TtsNorm = interceptTts
		nlu4lui.Widget = string(luiWidgetBt)
		return &dto.Pad2SkillData{
			Skill: nlu4lui.Skill,
			Sort:  sort,
			Data:  nlu4lui,
		}, nil
	}

	go s.UpdateUserProfileSubjectVersion(ctx, talId, nlu4lui.AllSlots.GradeName, nlu4lui.AllSlots.SubjectName, nlu4lui.AllSlots.VersionName)
	nluData, err := s.buildDictationResp(ctx, nlu4luiReq, talId)
	if err != nil {
		s.log.WithContext(ctx).Errorf("buildV2DictationResp err: %+v; nlu4lui: %+v", err, nlu4lui)
		return nil, err
	}

	nlu4lui.TtsNorm = nluData.TtsNorm
	nlu4lui.Widget = nluData.Widget
	nlu4lui.TtsShow = nluData.TtsShow
	nlu4lui.ResourceList = nil
	return &dto.Pad2SkillData{
		Skill: nlu4lui.Skill,
		Sort:  sort,
		Data:  nlu4lui,
	}, nil
}

func (s *LuiService) buildPadV2RecitationResp(ctx context.Context, nlu4lui *dto.NLUSkillItem, talId string, sort int) (*dto.Pad2SkillData, error) {

	nlu4luiReq := convertNLUSkillItemToNlu4LuiData(nlu4lui)
	interceptFlag, interceptTts := s.CheckIntercept(ctx, nlu4lui.AllSlots.GradeName, nlu4lui.Skill)

	if interceptFlag {
		luiWidgetContent := dto.Nlu4LuiWidgetContent{
			Category: 0,
			SlotDict: nlu4lui.AllSlots,
			Title:    "",
			Data:     nil,
			Word:     nlu4lui.Word,
			Scheme:   "",
		}

		luiWidget := dto.Nlu4LuiWidget{
			ModuleName:       nlu4luiReq.ModuleName,
			IsValidWidget:    nlu4luiReq.IsValidWidget,
			ResultConfidence: nlu4luiReq.ResultConfidence,
			NeedConfirm:      nlu4luiReq.NeedConfirm,
			Content:          luiWidgetContent,
		}
		luiWidgetBt, _ := json.Marshal(luiWidget)
		nlu4lui.TtsNorm = interceptTts
		nlu4lui.Widget = string(luiWidgetBt)
		return &dto.Pad2SkillData{
			Skill: nlu4lui.Skill,
			Sort:  sort,
			Data:  nlu4lui,
		}, nil
	}
	go s.UpdateUserProfileSubjectVersion(ctx, talId, nlu4lui.AllSlots.GradeName, nlu4lui.AllSlots.SubjectName, nlu4lui.AllSlots.VersionName)
	nluData, err := s.buildRecitationResp(ctx, nlu4luiReq, talId)
	if err != nil {
		s.log.WithContext(ctx).Errorf("buildPadV2RecitationResp err: %+v; nlu4lui: %+v", err, nlu4lui)
		return nil, err
	}
	nlu4lui.TtsNorm = nluData.TtsNorm
	nlu4lui.Widget = nluData.Widget
	nlu4lui.TtsShow = nluData.TtsShow
	nlu4lui.ResourceList = nil
	return &dto.Pad2SkillData{
		Skill: nlu4lui.Skill,
		Sort:  sort,
		Data:  nlu4lui,
	}, nil
}

func (s *LuiService) buildPadV2ClickReadResp(ctx context.Context, nlu4lui *dto.NLUSkillItem, sort int, talId string) (*dto.Pad2SkillData, error) {

	nlu4luiReq := convertNLUSkillItemToNlu4LuiData(nlu4lui)
	interceptFlag, interceptTts := s.CheckIntercept(ctx, nlu4lui.AllSlots.GradeName, nlu4lui.Skill)

	if interceptFlag {
		luiWidgetContent := dto.Nlu4LuiWidgetContent{
			Category: 0,
			SlotDict: nlu4lui.AllSlots,
			Title:    "",
			Data:     nil,
			Word:     nlu4lui.Word,
			Scheme:   "",
		}

		luiWidget := dto.Nlu4LuiWidget{
			ModuleName:       nlu4luiReq.ModuleName,
			IsValidWidget:    nlu4luiReq.IsValidWidget,
			ResultConfidence: nlu4luiReq.ResultConfidence,
			NeedConfirm:      nlu4luiReq.NeedConfirm,
			Content:          luiWidgetContent,
		}
		luiWidgetBt, _ := json.Marshal(luiWidget)
		nlu4lui.TtsNorm = interceptTts
		nlu4lui.Widget = string(luiWidgetBt)
		return &dto.Pad2SkillData{
			Skill: nlu4lui.Skill,
			Sort:  sort,
			Data:  nlu4lui,
		}, nil
	}
	go s.UpdateUserProfileSubjectVersion(ctx, talId, nlu4lui.AllSlots.GradeName, nlu4lui.AllSlots.SubjectName, nlu4lui.AllSlots.VersionName)
	nluData, err := s.buildClickReadResp(ctx, nlu4luiReq)
	if err != nil {
		s.log.WithContext(ctx).Errorf("buildPadV2ClickReadResp err: %+v; nlu4lui: %s", err, util.Marshal(nlu4lui))
		return nil, err
	}
	nlu4lui.TtsNorm = nluData.TtsNorm
	nlu4lui.Widget = nluData.Widget
	nlu4lui.TtsShow = nluData.TtsShow
	nlu4lui.ResourceList = nil
	return &dto.Pad2SkillData{
		Skill: nlu4lui.Skill,
		Sort:  sort,
		Data:  nlu4lui,
	}, nil
}

func (s *LuiService) buildPadV2PaperConfirmResp(ctx context.Context, nlu4lui *dto.NLUSkillItem, asrInfo, asrPinyin string) (*dto.Pad2SkillData, error) {

	nlu4luiReq := convertNLUSkillItemToNlu4LuiData(nlu4lui)
	interceptFlag, interceptTts := s.CheckIntercept(ctx, nlu4lui.AllSlots.GradeName, nlu4lui.Skill)

	if interceptFlag {
		luiWidgetContent := dto.Nlu4LuiWidgetContent{
			Category: 0,
			SlotDict: nlu4lui.AllSlots,
			Title:    "",
			Data:     nil,
			Word:     nlu4lui.Word,
			Scheme:   "",
		}

		luiWidget := dto.Nlu4LuiWidget{
			ModuleName:       nlu4luiReq.ModuleName,
			IsValidWidget:    nlu4luiReq.IsValidWidget,
			ResultConfidence: nlu4luiReq.ResultConfidence,
			NeedConfirm:      nlu4luiReq.NeedConfirm,
			Content:          luiWidgetContent,
		}
		luiWidgetBt, _ := json.Marshal(luiWidget)
		nlu4lui.Widget = string(luiWidgetBt)
		nlu4lui.TtsNorm = interceptTts
		nlu4lui.TtsShow = interceptTts
		nlu4lui.Skill = "no_skill"
		return &dto.Pad2SkillData{
			Skill: nlu4lui.Skill,
			Sort:  0,
			Data:  nlu4lui,
		}, nil
	}

	nluData, err := s.buildPaperConfirmResp(ctx, nlu4luiReq, asrInfo, asrPinyin)
	if err != nil {
		s.log.WithContext(ctx).Errorf("buildPadV2PaperConfirmResp err: %+v; nlu4lui: %s", err, util.Marshal(nlu4lui))
		return nil, err
	}

	nlu4lui.Widget = nluData.Widget
	nlu4lui.TtsShow = nluData.TtsShow
	nlu4lui.TtsNorm = nluData.TtsNorm
	nlu4lui.ResourceList = nil
	nlu4lui.Skill = nluData.Skill
	return &dto.Pad2SkillData{
		Skill: nluData.Skill,
		Sort:  0,
		Data:  nlu4lui,
	}, nil
}

func (s *LuiService) buildV2SearchPaperResp(ctx context.Context, nlu4lui *dto.NLUSkillItem, asrInfo, asrPinyin, talId string) (*dto.Pad2SkillData, error) {
	//旗舰款 澄清追问逻辑
	appId := custom_context.GetXAppId(ctx)
	version := utils.GetVersionNum(custom_context.GetXOSVersion(ctx))
	if strings.Contains(s.thirdConf.AppIdMap["xiaosi2AppId"], appId) && version >= Version0606 {
		if nlu4lui.MultiDialogSubject {
			memoryContexts, _ := s.memoryBiz.GetXPadMemoryContext(ctx, talId, 1) // 获取上下文
			mc, _ := json.Marshal(memoryContexts)
			s.log.WithContext(ctx).Infof("memoryContexts:%+v", string(mc))
			if len(memoryContexts) > 0 && memoryContexts[len(memoryContexts)-1].TtsInfo == common.TTSFollowUpSubject {
				res := &dto.Pad2SkillData{
					Skill: "no_skill",
					Data: &dto.NLUSkillItem{
						TtsNorm: common.TTSOtherIntent,
						TtsShow: common.TTSOtherIntent,
					},
				}
				return res, nil
			} else {
				res, err := s.buildPadV2PaperConfirmResp(ctx, nlu4lui, asrInfo, asrPinyin)
				if err != nil {
					s.log.WithContext(ctx).Errorf("buildV2SearchPaperResp err: %+v; nlu4lui: %s", err, util.Marshal(nlu4lui))
					return nil, err
				}
				res.Skill = common.PaperSubjectConfirm.ToString()
				res.Data.TtsNorm = common.TTSFollowUpSubject
				res.Data.TtsShow = common.TTSFollowUpSubject
				return res, nil
			}
		}
	}

	nlu4luiReq := convertNLUSkillItemToNlu4LuiData(nlu4lui)
	interceptFlag, interceptTts := s.CheckIntercept(ctx, nlu4lui.AllSlots.GradeName, nlu4lui.Skill)

	if interceptFlag {
		luiWidgetContent := dto.Nlu4LuiWidgetContent{
			Category: 0,
			SlotDict: nlu4lui.AllSlots,
			Title:    "",
			Data:     nil,
			Word:     nlu4lui.Word,
			Scheme:   "",
		}

		luiWidget := dto.Nlu4LuiWidget{
			ModuleName:       nlu4luiReq.ModuleName,
			IsValidWidget:    nlu4luiReq.IsValidWidget,
			ResultConfidence: nlu4luiReq.ResultConfidence,
			NeedConfirm:      nlu4luiReq.NeedConfirm,
			Content:          luiWidgetContent,
		}
		luiWidgetBt, _ := json.Marshal(luiWidget)
		nlu4lui.TtsNorm = interceptTts
		nlu4lui.Widget = string(luiWidgetBt)
		return &dto.Pad2SkillData{
			Skill: nlu4lui.Skill,
			Sort:  0,
			Data:  nlu4lui,
		}, nil
	}
	nluData, err := s.buildSearchPaperResp(ctx, nlu4luiReq, talId)
	if err != nil {
		s.log.WithContext(ctx).Errorf("buildPadV2PaperConfirmResp err: %+v; nlu4lui: %s", err, util.Marshal(nlu4lui))
		return nil, err
	}
	nlu4lui.TtsNorm = nluData.TtsNorm
	nlu4lui.Widget = nluData.Widget
	nlu4lui.TtsShow = nluData.TtsShow
	nlu4lui.ResourceList = nil
	return &dto.Pad2SkillData{
		Skill: nlu4lui.Skill,
		Sort:  0,
		Data:  nlu4lui,
	}, nil
}

func (s *LuiService) buildAppFuncNLU4LuiResp(ctx context.Context, nlu4lui *dto.NLUSkillItem, sort int, gradeName, bizType, modelOutputIntent string) (*dto.Pad2SkillData, error) {

	hit, defaultNlu4lui := s.bizGrayBiz.NLU4ExamJudgeGrayHitPad2(ctx, nlu4lui)
	if !hit {
		return &dto.Pad2SkillData{
			Skill: defaultNlu4lui.Skill,
			Sort:  sort,
			Data:  defaultNlu4lui,
		}, nil
	}

	if bizType != common.BizTypeCompanionLearn {
		// 新版查题app跳转
		s.bizGrayBiz.MathExplainAppSchemeGray(ctx, nlu4lui, modelOutputIntent)
	}

	nlu4luiReq := convertNLUSkillItemToNlu4LuiData(nlu4lui)
	tmp := dto.CommendTmp{}

	_ = mapstructure.Decode(nlu4lui.Command, &tmp)
	appID := tmp.Param.Appid
	value := tmp.Param.Value
	apId := custom_context.GetXAppId(ctx)
	if bizType != common.BizTypeCompanionLearn {
		interceptFlag, interceptTts := CheckInterceptApp(gradeName, appID, value)
		if !interceptFlag {
			appId := custom_context.GetXAppId(ctx)
			deviceId := custom_context.GetXDeviceId(ctx)
			if appId == s.thirdConf.XPad1V2AppId && (value == DiagnosticPaper || value == CompanionLearning || appID == CompanionLearningAppID || value == HomeWorkLearning || appID == HomeWorkLearningAppID) {
				interceptFlag = true
				interceptTts = common.TTSDefault
				if value == CompanionLearning || appID == CompanionLearningAppID || value == HomeWorkLearning || appID == HomeWorkLearningAppID {
					interceptTts = common.TTSBANXUENEICE
				}
			} else if (value == CompanionLearning || appID == CompanionLearningAppID) && appId != s.thirdConf.XPad2AppIdQijian {
				interceptFlag = true
				interceptTts = common.TTSBANXUENEICE
			} else if value == XiaoSiZiXiAPP && !s.bizGrayBiz.XSZixishiJudgeGrayHit(ctx, deviceId) {
				interceptFlag = true
				interceptTts = common.TTSBZiXiNEICE
			} else if s.checkForNoApp(ctx, appId, deviceId, tmp) {
				interceptFlag = true
				interceptTts = common.TtsInterceptAppTmp
			}

			if modelOutputIntent == "应用快捷操作" && appID == "" && (tmp.Param.Operate == "next" || tmp.Param.Operate == "pause") && (bizType != common.BizTypeHomeWork && apId != s.thirdConf.AppIdMap["xPad2AppId_qijian25"]) {
				interceptFlag = true
				interceptTts = common.TTSTouchScreen
			}
			//uninstall
			if modelOutputIntent == "应用下载卸载" && tmp.Param.Operate == "uninstall" {
				interceptFlag = true
				interceptTts = common.TTSUninstall
			}
		}
		if interceptFlag {
			luiWidgetContent := dto.Nlu4LuiWidgetContent{
				Category: 0,
				SlotDict: nlu4lui.AllSlots,
				Title:    "",
				Data:     nil,
				Word:     nlu4lui.Word,
				Scheme:   "",
			}

			luiWidget := dto.Nlu4LuiWidget{
				ModuleName:       nlu4luiReq.ModuleName,
				IsValidWidget:    nlu4luiReq.IsValidWidget,
				ResultConfidence: nlu4luiReq.ResultConfidence,
				NeedConfirm:      nlu4luiReq.NeedConfirm,
				Content:          luiWidgetContent,
			}
			luiWidgetBt, _ := json.Marshal(luiWidget)
			nlu4lui.TtsNorm = interceptTts
			nlu4lui.TtsShow = ""
			nlu4lui.Widget = string(luiWidgetBt)
			nlu4lui.Command = nil
			return &dto.Pad2SkillData{
				Skill: nlu4lui.Skill,
				Sort:  0,
				Data:  nlu4lui,
			}, nil
		}
	} else {
		tts := s.GetAppCompanionLearningTTS(appID)
		if tts != "" {
			nlu4lui.TtsNorm = tts
		}
	}
	if appID == "20002" {
		nlu4lui.TtsNorm = common.TTSHuYanMoShi
		nlu4lui.TtsShow = common.TTSHuYanMoShi
	}
	return &dto.Pad2SkillData{
		Skill: nlu4lui.Skill,
		Sort:  sort,
		Data:  nlu4lui,
	}, nil
}

// 返回没有应用话术的场景
func (s *LuiService) checkForNoApp(ctx context.Context, appId, deviceId string, tmp dto.CommendTmp) bool {
	if tmp.Param.PackageName == "com.tal.pad.writing_guide" && !s.bizGrayBiz.XSXieZuoYinDaoJudgeGrayHit(ctx, appId, deviceId) {
		return true
	}
	if tmp.Param.PackageName == "com.tal.pad.at_cn_reading" && !s.bizGrayBiz.XSYueDuLiJieJudgeGrayHit(ctx, deviceId) {
		return true
	}
	if tmp.Param.PackageName == "com.tal.genie.hybrid" && !s.bizGrayBiz.XSAtEnglishWordJudgeGrayHit(ctx, deviceId) {
		return true
	}

	return false
}

// 作业类型二次确认
func (s *LuiService) buildPadV2AppTypeConfirmResp(ctx context.Context, nlu4lui *dto.NLUSkillItem, asrInfo, asrPinyin string) (*dto.Pad2SkillData, error) {

	nlu4luiReq := convertNLUSkillItemToNlu4LuiData(nlu4lui)
	ttsNorm := common.TtsTplTAppType

	dictMap, _ := s.contentService.bizBase.SlotDictTransfer(ctx, nlu4luiReq.AllSlots)

	confirm := &dto.Confirm{
		AsrInfo:   asrInfo,
		AsrPinyin: asrPinyin,
		Key:       "check_type",
		Name:      "口算/语文作文/英语作文",
		Options:   dto.HomeworkType,
	}

	luiWidgetContent := dto.Nlu4LuiWidgetContent{
		Category:      common.HomeworkType.Int(),
		SlotDict:      dictMap,
		Toast:         "",
		ToastDuration: 0,
		Title:         ttsNorm,
		Data:          nil,
		Word:          nlu4luiReq.Word,
		Filters:       nil,
		Confirm:       confirm,
	}

	luiWidget := dto.Nlu4LuiWidget{
		ModuleName:       nlu4luiReq.ModuleName,
		IsValidWidget:    nlu4luiReq.IsValidWidget,
		ResultConfidence: nlu4luiReq.ResultConfidence,
		NeedConfirm:      nlu4luiReq.NeedConfirm,
		Content:          luiWidgetContent,
	}
	luiWidgetBt, _ := json.Marshal(luiWidget)
	nluData := &dto.NluData{
		UserID:    "",
		RequestID: nlu4luiReq.RequestID,
		Skill:     nlu4luiReq.Skill,
		TtsNorm:   ttsNorm,
		Widget:    string(luiWidgetBt),
	}

	nlu4lui.TtsNorm = nluData.TtsNorm
	nlu4lui.Widget = nluData.Widget
	nlu4lui.TtsShow = nluData.TtsShow
	nlu4lui.ResourceList = nil
	return &dto.Pad2SkillData{
		Skill: nlu4lui.Skill,
		Sort:  0,
		Data:  nlu4lui,
	}, nil
}

// 作业类型二次确认
func (s *LuiService) buildPadV2DictateTypeConfirmResp(ctx context.Context, nlu4lui *dto.NLUSkillItem, asrInfo, asrPinyin, gradeId string) (*dto.Pad2SkillData, error) {

	nlu4luiReq := convertNLUSkillItemToNlu4LuiData(nlu4lui)
	ttsNorm := common.TtsTplTDictateType

	dictMap, _ := s.contentService.bizBase.SlotDictTransfer(ctx, nlu4luiReq.AllSlots)

	confirm := &dto.Confirm{
		AsrInfo:   asrInfo,
		AsrPinyin: asrPinyin,
		Key:       "dictate_type",
		Name:      "英语/语文",
		Options:   dto.DictateType,
	}

	luiWidgetContent := dto.Nlu4LuiWidgetContent{
		Category:      common.DictateType.Int(),
		SlotDict:      dictMap,
		Toast:         "",
		ToastDuration: 0,
		Title:         ttsNorm,
		Data:          nil,
		Word:          nlu4luiReq.Word,
		Filters:       nil,
		Confirm:       confirm,
	}

	luiWidget := dto.Nlu4LuiWidget{
		ModuleName:       nlu4luiReq.ModuleName,
		IsValidWidget:    nlu4luiReq.IsValidWidget,
		ResultConfidence: nlu4luiReq.ResultConfidence,
		NeedConfirm:      nlu4luiReq.NeedConfirm,
		Content:          luiWidgetContent,
	}
	luiWidgetBt, _ := json.Marshal(luiWidget)
	nluData := &dto.NluData{
		UserID:    "",
		RequestID: nlu4luiReq.RequestID,
		Skill:     nlu4luiReq.Skill,
		TtsNorm:   ttsNorm,
		Widget:    string(luiWidgetBt),
	}

	nlu4lui.TtsNorm = nluData.TtsNorm
	nlu4lui.Widget = nluData.Widget
	nlu4lui.TtsShow = nluData.TtsShow
	nlu4lui.ResourceList = nil
	return &dto.Pad2SkillData{
		Skill: nlu4lui.Skill,
		Sort:  0,
		Data:  nlu4lui,
	}, nil
}

// 作业类型二次确认
func (s *LuiService) buildPadV2ConfirmInvalidResp(ctx context.Context, nlu4lui *dto.NLUSkillItem) (*dto.Pad2SkillData, error) {

	nlu4luiReq := convertNLUSkillItemToNlu4LuiData(nlu4lui)
	ttsNorm := common.TtsTPLConfirmInvalid

	luiWidgetContent := dto.Nlu4LuiWidgetContent{
		Category:      common.DictateType.Int(),
		Toast:         "",
		ToastDuration: 0,
		Title:         ttsNorm,
		Data:          nil,
		Word:          nlu4luiReq.Word,
		Filters:       nil,
	}

	luiWidget := dto.Nlu4LuiWidget{
		ModuleName:       nlu4luiReq.ModuleName,
		IsValidWidget:    nlu4luiReq.IsValidWidget,
		ResultConfidence: nlu4luiReq.ResultConfidence,
		NeedConfirm:      nlu4luiReq.NeedConfirm,
		Content:          luiWidgetContent,
	}
	luiWidgetBt, _ := json.Marshal(luiWidget)
	nluData := &dto.NluData{
		UserID:    "",
		RequestID: nlu4luiReq.RequestID,
		Skill:     nlu4lui.Skill,
		TtsNorm:   ttsNorm,
		Widget:    string(luiWidgetBt),
	}

	nlu4lui.TtsNorm = nluData.TtsNorm
	nlu4lui.Widget = nluData.Widget
	nlu4lui.ResourceList = nil
	nlu4lui.TtsShow = nluData.TtsNorm
	return &dto.Pad2SkillData{
		Skill: nlu4lui.Skill,
		Sort:  0,
		Data:  nlu4lui,
	}, nil
}
func (s *LuiService) GetCompanionLearningTTS(skill string) string {
	switch skill {
	case common.ListenWrite.ToString():
		return "好的。请选择要听写的内容，我会陪你一起听写～"
	case common.Recite.ToString():
		return "好的。请选择要背诵的内容，我会陪你一起背诵～"
	default:
		return ""
	}
}

func (s *LuiService) GetAppCompanionLearningTTS(appID string) string {
	switch appID {
	case "1028":
		return "好的。请拍摄口算，我会帮你进行检查～"
	case "1036":
		return "好的。"
	case "1017":
		return "好的。请选择要听写的内容，我会陪你一起听写～"
	case "1021":
		return "好的。请选择要听写的内容，我会陪你一起听写～"
	case "1018":
		return "好的。请选择要背诵的内容，我会陪你一起背诵～"
	default:
		return ""
	}
}

func (s *LuiService) buildPadV2JzxV15QuestionResp(ctx context.Context, nlu4lui *dto.NLUSkillItem, sort int) (*dto.Pad2SkillData, error) {

	nlu4luiReq := convertNLUSkillItemToNlu4LuiData(nlu4lui)
	interceptFlag, interceptTts := s.CheckIntercept(ctx, nlu4lui.AllSlots.GradeName, nlu4lui.Skill)

	if interceptFlag {
		luiWidgetContent := dto.Nlu4LuiWidgetContent{
			Category: 0,
			SlotDict: nlu4lui.AllSlots,
			Title:    "",
			Data:     nil,
			Word:     nlu4lui.Word,
			Scheme:   "",
		}

		luiWidget := dto.Nlu4LuiWidget{
			ModuleName:       nlu4luiReq.ModuleName,
			IsValidWidget:    nlu4luiReq.IsValidWidget,
			ResultConfidence: nlu4luiReq.ResultConfidence,
			NeedConfirm:      nlu4luiReq.NeedConfirm,
			Content:          luiWidgetContent,
		}
		luiWidgetBt, _ := json.Marshal(luiWidget)
		nlu4lui.TtsNorm = interceptTts
		nlu4lui.TtsShow = interceptTts
		nlu4lui.Widget = string(luiWidgetBt)
		return &dto.Pad2SkillData{
			Skill: nlu4lui.Skill,
			Sort:  0,
			Data:  nlu4lui,
		}, nil
	}
	nluData, err := s.contentService.buildJzxV15QuestionLuiNluData(ctx, nlu4lui.AllSlots, nlu4lui.ResourceList)
	if err != nil {
		s.log.WithContext(ctx).Errorf("buildJzxV15QuestionLuiNluData err: %+v", err)
		return nil, err
	}

	luiWidgetContent := dto.Nlu4LuiWidgetContent{
		Category:      common.JzxV15Question.Int(),
		SlotDict:      nlu4lui.AllSlots,
		Toast:         "",
		ToastDuration: 0,
		Title:         nlu4lui.TtsNorm,
		Data:          nluData,
		Word:          nlu4lui.Word,
		Confirm:       nil,
	}

	luiWidget := dto.Nlu4LuiWidget{
		ModuleName:       nlu4luiReq.ModuleName,
		IsValidWidget:    nlu4luiReq.IsValidWidget,
		ResultConfidence: nlu4luiReq.ResultConfidence,
		NeedConfirm:      nlu4luiReq.NeedConfirm,
		Content:          luiWidgetContent,
	}
	luiWidgetBt, err := json.Marshal(luiWidget)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Marshal err: %+v", err)
		return nil, err
	}
	nlu4lui.Widget = string(luiWidgetBt)
	nlu4lui.ResourceList = nil
	nlu4lui.Count = len(nluData)
	return &dto.Pad2SkillData{
		Skill: nlu4lui.Skill,
		Sort:  sort,
		Data:  nlu4lui,
	}, nil
}

func (s *LuiService) SetAiAgent(ctx context.Context, req *v1.LuiNluRequest, data *dto.Pad2SkillData) {
	if data.Data == nil || !data.Data.IsAccessingLLM {
		return
	}

	// tutor
	if strings.HasPrefix(req.BizType, common.BizTypeAiTutor) {
		switch data.Data.Skill {
		case common.SkillTutorQA.ToString():
			data.Data.AiAgent = common.AiAgentKetTutorQA
		case common.Baike.ToString():
			data.Data.AiAgent = common.AiAgentKetTutorWiki
		case common.SkillChat.ToString():
			data.Data.AiAgent = common.AiAgentKetTutorWiki
		}

		return
	}

	if data.Data.SceneMode != dto.SceneModeFullView {
		return
	}

	switch data.Data.Skill {
	case common.SkillSearchQuestion.ToString():
		data.Data.AiAgent = common.AiAgentChaTi
	case common.Baike.ToString():
		data.Data.AiAgent = common.AiAgentXiaoSi2
	case common.SkillChat.ToString():
		data.Data.AiAgent = common.AiAgentXiaoSi2
	case common.SkillExamQuestion.ToString():
		data.Data.AiAgent = common.AiExamQuestion
	case common.SkillReadQuestion.ToString():
		data.Data.AiAgent = common.AiReadQuestion
	}

	// 多模百科
	if data.Data.Skill == common.Baike.ToString() && data.Data.MixedModalData != nil {
		data.Data.AiAgent = common.AiAgentMixedModal
	}

	if req.BizType == common.BizTypeTopicDialogue && data.Data.MixedModalData == nil {
		data.Data.AiAgent = common.AiAgentExplainQuestionTopic
	}

	return
}

// 是否走大模型
func (s *LuiService) IsAccessingLLM(ctx context.Context, intent string, data *dto.Pad2SkillData, lenData int, req *v1.LuiNluRequest, hotfixDO *dto.HotfixVectorDo) bool {
	if hotfixDO != nil && hotfixDO.HotfixType != dto.HotfixTypeSimilarQuery {
		return false
	}

	// 混排不走大模型
	if lenData > 1 {
		return false
	}

	// 伴学不走大模型
	if req.BizType == common.BizTypeCompanionLearn {
		return false
	}
	if req.BizType == common.BizTypeHomeWork && (data.Data.Skill == "functional" || data.Data.IsResultValid) {
		return false
	}

	if !s.thirdConf.LlmPrompt.Enable {
		return false
	}

	pkgTime := custom_context.GetXOsPkgDate(ctx)
	if pkgTime != 0 && pkgTime < common.CheckIsLLMVersion {
		return false
	}

	// 学练机
	if strings.Contains(s.thirdConf.AppIdMap["xueLianJiAppIds"], custom_context.GetXAppId(ctx)) {
		if data.Data != nil && data.Data.Skill == "no_skill" {
			return false
		}
	}

	if data.Skill == string(common.SkillExamQuestion) || data.Skill == string(common.SkillReadQuestion) {
		return true
	}
	// 意图白名单
	intents := s.thirdConf.Llm.Intent

	// 全屏模式
	if data.Data != nil && data.Data.SceneMode == dto.SceneModeFullView {
		data.Data.Command = nil
		data.Data.IsAccessingLLM = true
		if intent == common.IntentChaTi && !strings.HasPrefix(req.BizType, common.BizTypeAiTutor) && !strings.HasPrefix(req.BizType, common.BizTypeHomeWork) && !strings.HasPrefix(req.BizType, common.BizTypeTopicDialogue) {
			data.Skill = common.SkillSearchQuestion.ToString()
			data.Data.Skill = common.SkillSearchQuestion.ToString()
			return true
		}

		if intent == common.IntentKnowledge {
			if data.Data.IsResultValid {
				data.Data.IsAccessingLLM = false
				return false
			} else {
				data.Skill = common.Baike.ToString()
				data.Data.Skill = common.Baike.ToString()
				return true
			}
		}

		if intent == common.IntentChat || intent == common.IntentSearchStory {
			data.Skill = common.SkillChat.ToString()
			data.Data.Skill = common.SkillChat.ToString()
			return true
		}

		if intent == common.IntentBaiKeWenDa && data.Data.IsResultValid {
			data.Data.IsAccessingLLM = false
			return false
		}

		if intent == common.IntentBaiKeWenDa || (util.SliceContainStr(intents, intent) && data.Data != nil && !data.Data.IsResultValid) {
			data.Skill = common.Baike.ToString()
			data.Data.Skill = common.Baike.ToString()
			return true
		}
	}

	if intent == common.IntentChat || intent == common.IntentSearchStory {
		data.Data.IsAccessingLLM = true
		return true
	}

	return false
}

// IsFullView 是否全屏展示
func (s *LuiService) IsFullView(ctx context.Context, intent string, functionList []dto.Function, data *dto.Pad2SkillData, lenData int, req *v1.LuiNluRequest, hotfixDO *dto.HotfixVectorDo) bool {
	// 1、开关
	if !s.thirdConf.LlmPrompt.FullViewEnable {
		return false
	}

	// 2、白名单 查题类的
	if util.InSliceString(string(common.CurrentEnv), []string{"gray", "online"}) && util.SliceContainStr([]string{common.IntentChaTi}, intent) && !s.bizGrayBiz.MathExplainJudgeGrayHit(ctx, custom_context.GetXDeviceId(ctx), custom_context.GetXAppId(ctx), custom_context.GetXOsPkgDate(ctx)) {
		return false
	}

	// 2、白名单 闲聊、百科、找故事
	if util.InSliceString(string(common.CurrentEnv), []string{"gray", "online"}) &&
		(util.SliceContainStr([]string{common.IntentChat, common.IntentBaiKeWenDa, common.IntentKnowledge, common.IntentSearchStory}, intent) || util.SliceContainStr(s.thirdConf.Llm.Intent, intent)) &&
		!s.bizGrayBiz.XS20AgentJudgeGrayHit(ctx, custom_context.GetXDeviceId(ctx)) {
		return false
	}

	// 3、版本
	pkgTime := custom_context.GetXOsPkgDate(ctx)
	if pkgTime != 0 && pkgTime < common.CheckIsFullViewVersion && custom_context.GetXAppId(ctx) != s.thirdConf.XPad2LiteAppId {
		return false
	}

	if pkgTime != 0 && pkgTime < common.CheckLiteIsFullViewVersion && custom_context.GetXAppId(ctx) == s.thirdConf.XPad2LiteAppId {
		return false
	}

	// 4、小思2.0应用ID
	if !strings.Contains(s.thirdConf.AppIdMap["xiaosi2AppId"], custom_context.GetXAppId(ctx)) {
		return false
	}

	// 学联机条件
	if strings.Contains(s.thirdConf.AppIdMap["xueLianJiAppIds"], custom_context.GetXAppId(ctx)) {
		if data.Data != nil && data.Data.Skill == "no_skill" {
			return false
		}
	}

	// 5、意图
	if hotfixDO != nil && hotfixDO.HotfixType == dto.HotfixTypeSkill {
		return false
	}

	if lenData > 1 {
		return false
	}

	if req.BizType == common.BizTypeCompanionLearn {
		return false
	}

	if req.BizType == common.BizTypeTopicDialogue && util.SliceContainStr([]string{common.IntentChaTi}, intent) {
		data.Data.SceneMode = dto.SceneModeFullView
		return true
	}

	if data.Data.IsNoImage {
		return false
	}

	var chaTiFullView bool
	if util.SliceContainStr([]string{common.IntentChaTi}, intent) && !(len(functionList) > 0 && functionList[0].FuncName == "problem_tools") && !(len(functionList) > 0 && functionList[0].FuncName == common.SkillExercisesTool.ToString()) {

		if data.Data.Command != nil {
			commandM := map[string]interface{}{}
			command, _ := json.Marshal(data.Data.Command)
			_ = json.Unmarshal(command, &commandM)
			if commandM != nil {
				appid, ok := commandM["param"].(map[string]interface{})["appid"].(string)
				if ok && appid != "jumph5" {
					chaTiFullView = true
				}
			}
		}
	}

	intents := s.thirdConf.Llm.Intent
	if chaTiFullView || util.SliceContainStr([]string{common.IntentChat, common.IntentBaiKeWenDa, common.IntentKnowledge, common.IntentSearchStory, common.IntentClarification}, intent) ||
		(util.SliceContainStr(intents, intent) && data.Data != nil && !data.Data.IsResultValid) {

		data.Data.SceneMode = dto.SceneModeFullView
		if intent == common.IntentClarification {
			data.Data.SceneMode = dto.SceneModeNotUnderstand
		}

		// 没听懂闲聊执行场景
		if intent == common.IntentClarification && data.Data.Skill == common.SkillChat.ToString() {
			data.Data.SceneMode = dto.SceneModeFullView
		}

		s.log.WithContext(ctx).Infof("IsFullView deviceID: %s, intent: %s", custom_context.GetXDeviceId(ctx), intent)
		return true
	}

	return false
}

func (s *LuiService) UpdateUserProfileSubjectVersion(ctx context.Context, talID, grade, subject, Version string) {
	headers := make(map[string]string)
	headers["Content-Type"] = "application/json"
	req := map[string]interface{}{
		"grade":   grade,
		"subject": subject,
		"version": Version,
		"tal_id":  talID,
	}
	ch := utils.CurlInit(context.Background(), time.Second*time.Duration(3), 0, s.log)
	_, err := ch.ChRequest(ch.Req.SetHeaders(headers).SetBody(req)).Post(s.thirdConf.UpdateProfileSubjectVersionUrl)
	if err != nil {
		s.log.WithContext(ctx).Warnf("UpdateUserProfileSubjectVersion err: %+v", err)
		return
	}
}

// 多模意图
func (s *LuiService) MultiModeResp(ctx context.Context, req *v1.LuiNluRequest, nlu4lui *dto.NLUQueryPad2Resp) (*structpb.Struct, error) {
	if nlu4lui == nil || nlu4lui.FunctionList == nil || len(nlu4lui.FunctionList) == 0 {
		return nil, nil
	}

	if nlu4lui.MixedModalInfo == nil || !nlu4lui.MixedModalInfo.MixedModal {
		return nil, nil
	}

	sessionId := req.RequestId
	if strings.Index(sessionId, "-") > 0 {
		sessionId = sessionId[:strings.Index(sessionId, "-")]
	}

	res := dto.QueryPad2Resp{
		MixedModal: common.MixedModalUnderScreen,
		OcrType:    nlu4lui.MixedModalInfo.OcrType,
		MixedModalData: &dto.MixedModalData{
			TalId:      req.TalId,
			SessionId:  sessionId,
			SentenceId: req.SentenceId,
			GradeId:    req.GradeId,
			AsrInfo:    req.AsrInfo,
			Intent:     nlu4lui.ModelOutputIntent,
			RnVersion:  req.RnVersion,
			FunctionCall: &dto.FunctionCall{
				Functions: nlu4lui.FunctionList,
			},
			OcrType: nlu4lui.MixedModalInfo.OcrType,
		},
		ModelOutputIntent: nlu4lui.ModelOutputIntent,
	}
	res.BizType = req.BizType
	resp, _ := util.ReplyAny(res)

	return resp, nil
}

func (s *LuiService) MultiModeUnderResp(ctx context.Context, tts, intent string) (*structpb.Struct, error) {
	res := dto.QueryPad2Resp{
		TtsShow: tts,
		TtsNorm: tts,
		Data: []*dto.Pad2SkillData{
			{
				Skill: "no_skill",
				Data: &dto.NLUSkillItem{
					Skill:         "no_skill",
					TtsShow:       tts,
					TtsNorm:       tts,
					Count:         1,
					IsResultValid: true,
				},
			},
		},
		ModelOutputIntent: intent,
	}

	resp, _ := util.ReplyAny(res)

	return resp, nil
}

// 可见即可说
func (s *LuiService) VisibleSaidResp(ctx context.Context, req *v1.LuiNluRequest, nlu4lui *dto.NLUQueryPad2Resp) (*structpb.Struct, error) {
	if nlu4lui == nil || nlu4lui.SceneResult == nil {
		return nil, nil
	}

	tts := "好的"
	skillData := &dto.Pad2SkillData{
		Data: &dto.NLUSkillItem{
			SceneResult:   nlu4lui.SceneResult,
			TtsNorm:       tts,
			TtsShow:       tts,
			IsResultValid: true,
			Skill:         common.SkillSee2Say.ToString(),
			AsrInfo:       req.AsrInfo,
			SessionId:     req.RequestId,
			SentenceId:    req.SentenceId,
		},
		Skill: common.SkillSee2Say.ToString(),
	}
	res := dto.QueryPad2Resp{
		TtsNorm:           tts,
		TtsShow:           tts,
		Data:              []*dto.Pad2SkillData{skillData},
		ModelOutputIntent: nlu4lui.ModelOutputIntent,
		ControllerVersion: nlu4lui.ControllerVersion,
	}
	res.BizType = req.BizType
	resp, _ := util.ReplyAny(res)

	return resp, nil
}

func (s *LuiService) buildPadV2EBookResp(ctx context.Context, nlu4lui *dto.NLUSkillItem, sort int) (*dto.Pad2SkillData, error) {

	nlu4luiReq := convertNLUSkillItemToNlu4LuiData(nlu4lui)
	interceptFlag, interceptTts := s.CheckIntercept(ctx, nlu4lui.AllSlots.GradeName, nlu4lui.Skill)

	if interceptFlag {
		luiWidgetContent := dto.Nlu4LuiWidgetContent{
			Category: 0,
			SlotDict: nlu4lui.AllSlots,
			Title:    "",
			Data:     nil,
			Word:     nlu4lui.Word,
			Scheme:   "",
		}

		luiWidget := dto.Nlu4LuiWidget{
			ModuleName:       nlu4luiReq.ModuleName,
			IsValidWidget:    nlu4luiReq.IsValidWidget,
			ResultConfidence: nlu4luiReq.ResultConfidence,
			NeedConfirm:      nlu4luiReq.NeedConfirm,
			Content:          luiWidgetContent,
		}
		luiWidgetBt, _ := json.Marshal(luiWidget)
		nlu4lui.TtsNorm = interceptTts
		nlu4lui.Widget = string(luiWidgetBt)
		return &dto.Pad2SkillData{
			Skill: nlu4lui.Skill,
			Sort:  sort,
			Data:  nlu4lui,
		}, nil
	}

	nluData, err := s.buildEBookNLU4LuiResp(ctx, nlu4luiReq)
	if err != nil {
		s.log.WithContext(ctx).Errorf("buildPadV2EBookResp err: %+v; nlu4lui: %+v", err, nlu4lui)
		return nil, err
	}

	nlu4lui.TtsNorm = nluData.TtsNorm
	nlu4lui.Widget = nluData.Widget
	nlu4lui.TtsShow = nluData.TtsShow
	nlu4lui.ResourceList = nil
	return &dto.Pad2SkillData{
		Skill: nlu4lui.Skill,
		Sort:  sort,
		Data:  nlu4lui,
	}, nil
}
