package service

import "time"

// 学科优先级映射
var subjectPriority = map[int]int{
	2:  0, //数学
	1:  1, //语文
	3:  2, //英语
	4:  3, //物理
	5:  4, //化学
	6:  5, //生物
	21: 6, //科学
	7:  7, //道德与政治
	9:  8, //历史
	8:  9, //地理
}

var CourseSysTemPriority = map[int]int{
	10:  6, //智慧学推荐
	11:  2, //作文课
	3:   4, //提升S
	5:   0, //同步
	6:   4, //提升A
	7:   2, //道德与政治
	9:   3, //专题
	8:   5, //奥数
	2:   1, //精准学
	23:  1, //精准学1.5
	100: 6, //素养
}

// 根据当前月份确定学期的优先级
func semesterPriority(semester int) int {
	month := time.Now().Month()
	realSemester := 5
	if month >= time.August || month <= time.January {
		realSemester = 5
	} else {
		realSemester = 6
	}
	if semester == realSemester {
		return 1
	}
	return 0 // 下学期
}

// 计算年级距离
func gradeDistance(currentGrade, resourceGrade int) int {
	distance := currentGrade - resourceGrade
	if distance < 0 {
		return -distance*2 - 1
	}
	return distance * 2
}

// RecourseScore 计算资源的排序分值
func RecourseScore(grade, subject, semester, courseSystem, slotGrade, sort int) int {
	// 计算年级距离分值
	gradeScore := gradeDistance(slotGrade, grade)

	// 计算学科优先级分值
	subjectScore, ok := subjectPriority[subject]
	if !ok {
		subjectScore = 10 // 如果学科不在列表中，则给一个较高的分值
	}
	courseSystemScore, ok := CourseSysTemPriority[courseSystem]
	if !ok {
		courseSystemScore = 10 // 如果学科不在列表中，则给一个较高的分值
	}
	// 计算学期优先级分值
	semesterScore := semesterPriority(semester)
	score := gradeScore*100000 + sort*10000 + subjectScore*100 + semesterScore*10 + courseSystemScore
	// 综合所有分值
	return score
}
