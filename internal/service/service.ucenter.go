package service

import (
	"context"
	v1 "lui-api/api/lui/v1"
	"lui-api/internal/biz"
	"lui-api/internal/conf"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
)

type UCenterService struct {
	v1.UCenterApiHTTPServer
	log   *log.Helper
	third *conf.Third
	biz   *biz.UCenterBiz
}

func NewUCenterService(logger log.Logger, third *conf.Third, biz *biz.UCenterBiz) *UCenterService {
	return &UCenterService{log: log.NewHelper(logger), third: third, biz: biz}
}

func (us *UCenterService) LuiUserInfo(ctx context.Context, req *v1.LuiUserInfoRequest) (*v1.LuiUserInfoReply, error) {
	res := v1.LuiUserInfoReply{}
	userInfoParse, err := us.biz.TokenParse(ctx, req.Token)
	if err != nil {
		return nil, err
	}
	if userInfoParse.TalID == "" {
		return nil, errors.New(400, "param", "err")
	}
	userProfile, err := us.biz.GetUserProfile(ctx, userInfoParse)
	if err != nil {
		return nil, err
	}
	res.TalId = userProfile.TalID
	res.Grade = userProfile.Grade
	res.GradeName = userProfile.GradeName
	res.Nickname = userProfile.Nickname
	//缓存
	go func() {
		set := us.biz.Repo.RdbSetKey(context.TODO(), userInfoParse.TalID, userInfoParse.ClientID, 120)
		_, err = set.Result()
		if err != nil {
			us.log.Errorf("set clientId cache err:%s", err.Error())
		}
	}()

	return &res, nil
}
