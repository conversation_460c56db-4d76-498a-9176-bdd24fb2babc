package service

import (
	"context"
	"google.golang.org/protobuf/types/known/structpb"
	v1 "lui-api/api/lui/v1"
	"lui-api/internal/pkg/custom_context"
)

func (s *LuiService) LuiSimulateQuery(ctx context.Context, req *v1.LuiSimulateQueryRequest) (*structpb.Struct, error) {
	luiNluReq := &v1.LuiNluRequest{
		RequestId:    custom_context.GetTraceId(ctx),
		AsrInfo:      req.GetAsrInfo(),
		AsrPinyin:    req.GetAsrPinyin(),
		Location:     req.GetLocation(),
		Grade:        req.GetGrade(),
		GradeId:      req.GetGradeId(),
		TalId:        req.GetTalId(),
		BizType:      req.GetBizType(),
		SlotFillList: req.GetSlotFillList(),
	}

	return s.<PERSON><PERSON>(ctx, luiNluReq)
}
