// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"lui-api/internal/biz"
	"lui-api/internal/conf"
	"lui-api/internal/data"
	"lui-api/internal/server"
	"lui-api/internal/service"
)

// Injectors from wire.go:

// initApp init kratos application.
func initApp(confServer *conf.Server, errorHandle *conf.ErrorHandle, confData *conf.Data, arg []*conf.Data_Kafka, logger log.Logger, third *conf.Third, sign *conf.Sign, whiteOsVersion *conf.WhiteOsVersion, api *conf.Api, subjectConfig *conf.SubjectConfig, arg2 map[string]*conf.Skills, arg3 map[string]*conf.VoiceInteractionCommands) (*kratos.App, func(), error) {
	dataData, cleanup, err := data.NewData(confData, logger)
	if err != nil {
		return nil, nil, err
	}
	greeterRepo := data.NewGreeterRepo(dataData, logger)
	greeterUsecase := biz.NewGreeterUsecase(greeterRepo, logger)
	greeterService := service.NewGreeterService(greeterUsecase, logger)
	uCenterRepo := data.NewUCenterRepo(dataData)
	baseUsecase := biz.NewBaseUsecase(logger, third, uCenterRepo)
	ipAnalysisRepo := data.NewIPAnalysisRepo(dataData)
	iVisionRepo := data.NewVisionRepo(dataData, logger)
	iMemoryContextRepo := data.NewMemoryContext(dataData, logger)
	memoryContextBiz := biz.NewMemoryContextBiz(logger, iMemoryContextRepo, third)
	nluUsecase := biz.NewNluUsecase(logger, third, sign, ipAnalysisRepo, iVisionRepo, memoryContextBiz)
	subjectUsecase := biz.NewSubjectUsecase(logger, third)
	cultivationUsecase := biz.NewCultivationUsecase(logger, third)
	paperUsecase := biz.NewPaperUsecase(logger, third)
	bookReadingUsecase := biz.NewBookReadingUsecase(logger, third)
	embeddingDBRepo := data.NewEmbeddingRepo(dataData, logger, confData)
	embeddingSyncBiz := biz.NewEmbeddingSyncBiz(logger, embeddingDBRepo, confData)
	contentService := service.NewContentService(logger, baseUsecase, subjectUsecase, cultivationUsecase, paperUsecase, bookReadingUsecase, embeddingSyncBiz, subjectConfig)
	uCenterBiz := biz.NewUCenterBiz(uCenterRepo, logger, third)
	studyLogBiz := biz.NewStudyLogBiz(logger, third, uCenterBiz)
	toolsBiz := biz.NewToolsBiz(logger, third)
	legalBizUsecase := biz.NewLegalBizUsecase(logger, third)
	kafkaProducerRepo := data.NewKafkaProducerRepo(dataData, logger, arg)
	traceUsecase := biz.NewTraceUsecase(logger, kafkaProducerRepo)
	grayBizUsecase := biz.NewGrayBizUsecase(logger, third)
	contactRepo := data.NewContactRepo(dataData, logger, third)
	luiControllerBiz := biz.NewLuiControllerBiz(logger, third, contactRepo)
	xsSkillHubBiz := biz.NewXSSkillHubBiz(logger, third, contactRepo)
	xiaosiRagBiz := biz.NewXiaosiRagBiz(logger, third)
	sugRepo := data.NewSugRepo(dataData)
	msgUsecase := biz.NewMsgUsecase(logger, third)
	rnUsecase := biz.NewRnUsecase(logger, third, nluUsecase)
	luiService := service.NewLuiService(logger, baseUsecase, nluUsecase, contentService, studyLogBiz, toolsBiz, third, uCenterBiz, legalBizUsecase, whiteOsVersion, embeddingSyncBiz, traceUsecase, api, grayBizUsecase, luiControllerBiz, xsSkillHubBiz, confData, xiaosiRagBiz, memoryContextBiz, sugRepo, msgUsecase, rnUsecase, arg3)
	uCenterService := service.NewUCenterService(logger, third, uCenterBiz)
	skillService := service.NewSkillService(logger, third, arg2)
	httpServer := server.NewHTTPServer(confServer, errorHandle, greeterService, luiService, logger, uCenterService, skillService)
	grpcServer := server.NewGRPCServer(confServer, greeterService, logger)
	app := newApp(logger, httpServer, grpcServer)
	return app, func() {
		cleanup()
	}, nil
}
