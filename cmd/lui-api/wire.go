//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"lui-api/internal/biz"
	"lui-api/internal/conf"
	"lui-api/internal/data"
	"lui-api/internal/server"
	"lui-api/internal/service"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// initApp init kratos application.
func initApp(*conf.Server, *conf.ErrorHandle, *conf.Data, []*conf.Data_Kafka, log.Logger, *conf.Third, *conf.Sign, *conf.WhiteOsVersion, *conf.Api, *conf.SubjectConfig, map[string]*conf.Skills, map[string]*conf.VoiceInteractionCommands) (*kratos.App, func(), error) {
	panic(wire.Build(server.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, newApp))
}
