package main

import (
	"flag"
	"fmt"
	"os"
	"time"

	"lui-api/internal/common"

	"github.com/aliyun-sls/opentelemetry-go-provider-sls/provider"

	"lui-api/internal/conf"
	"lui-api/internal/data"
	"lui-api/internal/pkg/dayu_trace"
	"lui-api/internal/pkg/header_logger"
	"lui-api/pkg/tcm"
	"lui-api/pkg/zlog"

	feature "git.100tal.com/znxx_xpp/feature-ab-client-go"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name string = "lui-api" // TODO:默认为当前项目名称，会影响服务发现注册等功能，一旦项目发布不要后续修改，除非知道自己在做什么！！！
	// Version is the version of the compiled software.
	Version string = "1.0.0"
	// App Env
	env string
	// Hostname
	id, _ = os.Hostname()
)

func init() {
	flag.StringVar(&env, "env", string(common.LocalEnv), "app env, eg: -env local")
	flag.StringVar(&Name, "name", Name, "app name, eg: -name kratos-layout")

	flag.Parse()

	//灰度策略
	fabConfig := feature.FabConfig{
		Env:       feature.TestEnv,
		StartWait: 2 * time.Second, //默认0秒，建议设置为2秒，防止启动后灰度判断时无配置
	}
	switch env {
	case "test":
		fabConfig.Env = feature.TestEnv
		break
	case "gray":
		fabConfig.Env = feature.GrayEnv
		break
	case "online":
		fabConfig.Env = feature.OnlineEnv
		break
	default:
		fabConfig.Env = feature.TestEnv
	}
	feature.Register(fabConfig, []string{common.XsdhXiaosi20AgentSnList, common.XsdhOralAppSnList, common.XsdhMathExplainSnList, common.XszixishiSnList, common.XsxiezuoyindaoSnList, common.XsMrIntentSnList, common.XsYueDuLiJieSnList, common.XsAtEnglishWordSnList, common.XsUnclearIntentUpgrade, common.XsDeepseek, common.XsExplainQuestionAgentList})
}

func newApp(logger log.Logger, hs *http.Server, gs *grpc.Server) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		kratos.Server(
			hs,
			gs,
		),
	)
}

func main() {
	// 设置环境变量到全局变量
	common.CurrentEnv = common.Env(env)
	c, err := tcm.NewConfig(Name, common.Env(env), "config.yaml", "app.yaml")
	if err != nil {
		panic(err)
	}
	defer c.Close()

	if err := c.Load(); err != nil {
		panic(err)
	}

	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}

	zlog.Init(Name, bc.Log.Filename, int(bc.Log.MaxSize), int(bc.Log.MaxBackup), int(bc.Log.MaxAge), bc.Log.Compress)
	defer zlog.Sync()
	logger := log.With(zlog.NewZapLogger(zlog.STDInstance()),
		"ts", log.Timestamp(time.RFC3339Nano),
		"caller", log.DefaultCaller,
		"service.id", id,
		"service.name", Name,
		"service.version", Version,
		"trace_id", tracing.TraceID(),
		"span_id", tracing.SpanID(),
		"dayu_trace_id", dayu_trace.TraceID(),
		"header", header_logger.Header(),
		// 可以添加额外k,v,满足基本日志需求
	)

	// 开启阿里云tracing
	slsConfig, err := data.NewTrace(env, Name, Version)
	if err != nil {
		panic(err)
	}
	defer provider.Shutdown(slsConfig)

	// watch 监听需要动态改变的配置
	c.Watch("error", func(key string, value config.Value) {
		value.Scan(&bc.Error)
		fmt.Printf("error changed: %+v", bc.Error)
	})
	c.Watch("sign", func(key string, value config.Value) {
		value.Scan(&bc.Sign)
		fmt.Printf("sign changed: %+v", bc.Sign)
	})

	c.Watch("third", func(key string, value config.Value) {
		value.Scan(&bc.Third)
		fmt.Printf("third changed: %+v", bc.Third)
	})
	c.Watch("white_os_version", func(key string, value config.Value) {
		value.Scan(&bc.WhiteOsVersion)
		fmt.Printf("white_os_version changed: %+v", bc.WhiteOsVersion)
	})

	c.Watch("data.embedding", func(key string, value config.Value) {
		value.Scan(&bc.Data.Embedding)
		fmt.Printf("embedding changed: %+v", bc.Data.Embedding)
	})
	c.Watch("api", func(key string, value config.Value) {
		value.Scan(&bc.Api)
		fmt.Printf("api changed: %+v", bc.Api)
	})
	c.Watch("subject_config", func(key string, value config.Value) {
		value.Scan(&bc.SubjectConfig)
		fmt.Printf("subject_config changed: %+v", bc.SubjectConfig)
	})

	_ = c.Watch("skills", func(key string, value config.Value) {
		_ = value.Scan(&bc.Skills)
		fmt.Printf("skills changed: %+v", bc.Skills)
	})

	_ = c.Watch("voice_interaction_tips", func(key string, value config.Value) {
		_ = value.Scan(&bc.VoiceInteractionTips)
		fmt.Printf("voice_interaction_tips changed: %+v", bc.VoiceInteractionTips)
	})

	app, cleanup, err := initApp(bc.Server, bc.Error, bc.Data, bc.Data.QueryKafkaList, logger, bc.Third, bc.Sign, bc.WhiteOsVersion, bc.Api, bc.SubjectConfig, bc.Skills, bc.VoiceInteractionTips)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}
