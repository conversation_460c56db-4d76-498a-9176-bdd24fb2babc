<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LuiNluPad2Result 方法文档</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        header {
            background-color: #1e88e5;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
            box-shadow: 0 3px 5px rgba(0,0,0,0.1);
        }
        h1 {
            margin: 0;
            font-size: 2.2em;
        }
        h2 {
            color: #1e88e5;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        h3 {
            color: #0d47a1;
            margin-top: 25px;
        }
        h4 {
            color: #1565c0;
            margin-top: 20px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border-left: 4px solid #1e88e5;
        }
        code {
            font-family: 'Courier New', Courier, monospace;
            color: #0d47a1;
        }
        pre code {
            color: #333;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 3px rgba(0,0,0,0.05);
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
            padding: 12px;
            text-align: left;
        }
        td {
            padding: 12px;
        }
        .method-signature {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
            box-shadow: 0 2px 3px rgba(0,0,0,0.05);
        }
        .flow-diagram {
            background-color: #fff;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 20px 0;
            overflow-x: auto;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .flow-diagram pre {
            background-color: #fff;
            border-left: none;
        }
        .highlight {
            background-color: #fff8e1;
            padding: 3px 5px;
            border-radius: 3px;
        }
        .note {
            background-color: #e8f5e9;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #4caf50;
            margin: 20px 0;
            box-shadow: 0 2px 3px rgba(0,0,0,0.05);
        }
        .warning {
            background-color: #fff3e0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ff9800;
            margin: 20px 0;
            box-shadow: 0 2px 3px rgba(0,0,0,0.05);
        }
        .section {
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .function-section {
            margin: 40px 0;
        }
        .mermaid {
            margin: 20px 0;
            text-align: center;
        }
        .flow-chart-container {
            overflow-x: auto;
            max-width: 100%;
            margin: 20px 0;
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .flow-step {
            padding: 8px;
            margin: 5px 0;
            border-radius: 5px;
            background: #e3f2fd;
            color: #0d47a1;
            font-weight: bold;
        }
        .flow-arrow {
            text-align: center;
            padding: 5px;
            font-weight: bold;
        }
        .grid-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 20px 0;
        }
        .grid-item {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #1e88e5;
        }
        .code-comment {
            color: #4caf50;
            font-style: italic;
        }
        .toc {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .toc ul {
            margin: 0;
            padding-left: 20px;
        }
        .toc li {
            margin: 5px 0;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                flowchart: {
                    useMaxWidth: true,
                    htmlLabels: true,
                    curve: 'basis'
                }
            });
        });
    </script>
</head>
<body>
    <header>
        <h1>LuiNluPad2Result 方法详细文档</h1>
        <p>垂域混排结果返回处理流程详解</p>
    </header>

    <div class="toc section">
        <h2>目录</h2>
        <ul>
            <li><a href="#basic-info">1. 基本信息</a></li>
            <li><a href="#flow-chart">2. 流程图</a></li>
            <li><a href="#detailed-process">3. 详细执行流程</a>
                <ul>
                    <li><a href="#process-1">3.1 日志记录与参数校验</a></li>
                    <li><a href="#process-2">3.2 场景模式处理</a></li>
                    <li><a href="#process-3">3.3 NLU处理流程</a></li>
                    <li><a href="#process-4">3.4 特殊场景处理</a></li>
                    <li><a href="#process-5">3.5 特殊意图处理</a></li>
                    <li><a href="#process-6">3.6 技能处理（并发）</a></li>
                    <li><a href="#process-7">3.7 结果收集与处理</a></li>
                    <li><a href="#process-8">3.8 多技能数据过滤与排序</a></li>
                    <li><a href="#process-9">3.9 技能数据处理与转换</a></li>
                    <li><a href="#process-10">3.10 法律合规检查</a></li>
                    <li><a href="#process-11">3.11 构建最终响应</a></li>
                </ul>
            </li>
            <li><a href="#helper-functions">4. 辅助功能函数</a></li>
            <li><a href="#core-logic">5. 核心业务逻辑要点</a></li>
        </ul>
    </div>

    <div id="basic-info" class="section">
        <h2>1. 基本信息</h2>
        <table>
            <tr>
                <th>属性</th>
                <th>详情</th>
            </tr>
            <tr>
                <td>方法名称</td>
                <td>LuiNluPad2Result</td>
            </tr>
            <tr>
                <td>所属服务</td>
                <td>LuiService</td>
            </tr>
            <tr>
                <td>文件路径</td>
                <td>/Users/<USER>/work/lui-api/internal/service/service.lui.pad2.go</td>
process_each --> check_fullview[判断是否全屏显示]
    check_fullview --> check_llm[判断是否访问大模型]
    check_llm --> set_agent[设置AI代理]
    set_agent --> legal_check[法律合规检查]
    legal_check --> legal_result{是否合规?}
    legal_result -- 否 --> replace_illegal[替换为合规响应]
    legal_result -- 是 --> build_resp[构建最终响应]
    replace_illegal --> build_resp
    build_resp --> set_fields[设置响应字段]
    set_fields --> convert_resp[转换为API响应]
    convert_resp --> log_latency[记录延迟统计]
    log_latency --> async_memory[异步保存记忆上下文]
    async_memory --> return_final([返回最终结果])
</div>
        </div>

        <div class="flow-chart-container">
            <h3>详细流程图 - 并发技能处理部分</h3>
            <div class="mermaid">
flowchart TD
    start([开始]) --> create_wg[创建等待组与结果通道]
    create_wg --> iterate[遍历技能列表]
    iterate --> skill_type{技能类型?}
    
    skill_type -- 搜索课程 --> course_go[开启并发处理搜索课程]
    course_go --> course_build[构建课程搜索响应]
    course_build --> course_check{处理成功?}
    course_check -- 是 --> course_send[发送结果到通道]
    course_check -- 否 --> course_nil[发送nil到通道]
    
    skill_type -- 搜索素养 --> quality_go[开启并发处理搜索素养]
    quality_go --> quality_build[构建素养搜索响应]
    quality_build --> quality_check{处理成功?}
    quality_check -- 是 --> quality_send[发送结果到通道]
    quality_check -- 否 --> quality_nil[发送nil到通道]
    
    skill_type -- 继续学习 --> learn_go[开启并发处理继续学习]
    learn_go --> learn_build[构建继续学习响应]
    learn_build --> learn_check{处理成功?}
    learn_check -- 是 --> learn_send[发送结果到通道]
    learn_check -- 否 --> learn_nil[发送nil到通道]
    
    skill_type -- 其他技能 --> other_go[开启并发处理其他技能]
    other_go --> other_build[构建相应技能响应]
    other_build --> other_check{处理成功?}
    other_check -- 是 --> other_send[发送结果到通道]
    other_check -- 否 --> other_nil[发送nil到通道]
    
    iterate --> wait[等待所有技能处理完成]
    course_send --> collect[收集处理结果]
    quality_send --> collect
    learn_send --> collect
    other_send --> collect
    course_nil --> collect
    quality_nil --> collect
    learn_nil --> collect
    other_nil --> collect
    
    wait --> finish([处理完成])
            </div>
        </div>

        <div class="flow-chart-container">
            <h3>功能组件关系图</h3>
            <div class="mermaid">
flowchart LR
    LuiNluPad2Result --> QueryHotfix2Vdb[查询向量数据库]
    LuiNluPad2Result --> FetchPadV3NLU4LUI[调用NLU服务]
    LuiNluPad2Result --> VisibleSaidResp[可见即可说处理]
    LuiNluPad2Result --> MultiModeResp[多模态处理]
    LuiNluPad2Result --> HandleDeepseek[深度思考处理]
    LuiNluPad2Result --> UnderScreenExercises[屏幕下练习处理]
    LuiNluPad2Result --> HandleWorkshop[工作坊处理]
    LuiNluPad2Result --> HandleFilters[滤镜处理]
    LuiNluPad2Result --> SkillProcessing{技能处理}
    
    SkillProcessing --> SearchCourse[搜索课程]
    SkillProcessing --> SearchQuality[搜索素养]
    SkillProcessing --> ContinueLearn[继续学习]
    SkillProcessing --> PaperConfirm[试卷确认]
    SkillProcessing --> SearchPaper[搜索试卷]
    SkillProcessing --> ListenWrite[听写]
    SkillProcessing --> DefaultSkill[其他功能]
    
    LuiNluPad2Result --> LegalCheck[法律合规检查]
    LuiNluPad2Result --> MemoryContext[记忆上下文存储]
            </div>
        </div>
    </div>

    <div id="detailed-process" class="section">
        <h2>3. 详细执行流程</h2>

        <div id="process-1" class="function-section">
            <h3>3.1 日志记录与参数校验</h3>
            <pre><code>s.log.WithContext(ctx).Infof("LuiNluPad2Result req: %v", util.Marshal(req))

if req.GetTalId() == "" {
    s.log.WithContext(ctx).Warnf("LuiNluPad2Result req talId is empty")
}</code></pre>
            <ul>
                <li>记录输入请求详情，便于问题排查和日志跟踪</li>
                <li>检查用户ID是否存在，若为空则记录警告日志</li>
                <li>使用Marshal函数将请求对象序列化为JSON字符串便于日志记录</li>
            </ul>
        </div>

        <div id="process-2" class="function-section">
            <h3>3.2 场景模式处理</h3>
            <pre><code>if req.SceneMode != dto.SceneModeFullView {
    _ = s.memoryBiz.DelDeepSeekFlag(ctx, req.TalId)
}</code></pre>
            <ul>
                <li>如果当前不是全屏视图模式，删除深度思考标记</li>
                <li>确保在非全屏模式下不会触发深度思考功能</li>
                <li>DeepSeekFlag用于控制是否启用深度思考能力</li>
            </ul>
            <div class="note">
                <strong>场景模式说明：</strong>SceneModeFullView表示全屏视图模式，在这种模式下才允许使用深度思考功能。当用户切换到其他场景模式时，需要清除相关标记，避免触发不适合当前场景的功能。
            </div>
        </div>

        <div id="process-3" class="function-section">
            <h3>3.3 NLU处理流程</h3>
            <pre><code>nlu4lui, hotfixDO, err := s.QueryHotfix2Vdb(ctx, req)
if nlu4lui == nil || err != nil {
    // 获取上下文
    ctx = dto.SetCtxSceneCode(ctx, req.BizType)
    memoryContexts, _ := s.memoryBiz.GetXPadMemoryContext(ctx, req.TalId, dto.LuiPad2MaxLen)
    //调用nlu
    nlu4lui, err = s.bizNlu.FetchPadV3NLU4LUI(ctx, req, memoryContexts)
    if err != nil {
        if strings.Contains(err.Error(), "100001") {
            s.log.WithContext(ctx).Warnf("FetchPadV2NLU4LUI err: %+v", err)
            return nil, nil
        }
        s.log.WithContext(ctx).Warnf("FetchPadV2NLU4LUI err: %+v", err)
        return nil, v1.ErrorInternalError("LuiNluPad2Result" + err.Error())
    }
}</code></pre>
            <ul>
                <li>首先尝试从热修复/向量数据库查询NLU结果
                    <ul>
                        <li>热修复机制允许系统快速修复和调整NLU结果，无需重新部署</li>
                        <li>向量数据库可以存储常见问题的预处理结果，提高响应速度</li>
                    </ul>
                </li>
                <li>如果没有结果或出错，则执行以下步骤：
                    <ul>
                        <li>设置上下文场景代码，基于业务类型</li>
                        <li>获取用户的历史记忆上下文，用于上下文理解</li>
                        <li>调用NLU服务获取自然语言理解结果</li>
                        <li>处理可能的错误，包括特定错误码的处理</li>
                    </ul>
                </li>
            </ul>
            <div class="note">
                <strong>注意：</strong>错误码"100001"被特殊处理，遇到该错误时直接返回空结果而不是错误对象。此错误可能表示用户输入过短或无法理解等非关键错误情况。
            </div>
        </div>

        <div id="process-4" class="function-section">
            <h3>3.4 特殊场景处理</h3>
            
            <h4>3.4.1 可见即可说处理</h4>
            <pre><code>//可见即可说
if nlu4lui != nil && nlu4lui.SceneResult != nil && nlu4lui.SceneResult.ClickArea != nil {
    vsResp, _ := s.VisibleSaidResp(ctx, req, nlu4lui)
    if vsResp != nil {
        pkgSync.Go(ctx, s.log, func() {
            memoryContextReq := dto.MemoryContextReq{
                UserTalID:   req.TalId,
                SessionId:   req.RequestId,
                AsrInfo:     req.AsrInfo,
                IllegalType: "",
                Hotfix:      hotfixDO,
                SceneCode:   nlu4lui.SceneCode,
            }
            _ = s.memoryBiz.AddXPadMemoryContext(context.TODO(), memoryContextReq, vsResp, nlu4lui)
        })
        s.log.WithContext(ctx).Infof("LuiNluPad2Result vsResp: %+v", util.Marshal(vsResp))
        return vsResp, nil
    }
}</code></pre>
            <ul>
                <li>检查是否有场景结果和点击区域信息</li>
                <li>调用<code>VisibleSaidResp</code>处理，生成可见即可说响应</li>
                <li>异步保存记忆上下文，不阻塞主流程</li>
                <li>记录日志并提前返回处理结果</li>
            </ul>
            <div class="note">
                <strong>"可见即可说"功能：</strong>允许用户通过语言指令操作屏幕上可见的元素，如"点击这个"、"选择那个"等。系统通过识别用户指令和屏幕区域的结合，确定用户意图并执行相应操作。
            </div>

            <h4>3.4.2 多模意图处理</h4>
            <pre><code>// 多模意图处理
multiResp, _ := s.MultiModeResp(ctx, req, nlu4lui)
if multiResp != nil {
    pkgSync.Go(ctx, s.log, func() {
        memoryContextReq := dto.MemoryContextReq{
            UserTalID:   req.TalId,
            SessionId:   req.RequestId,
            AsrInfo:     req.AsrInfo,
            IllegalType: "",
            Hotfix:      hotfixDO,
            SceneCode:   nlu4lui.SceneCode,
<h4>3.4.3 深度思考处理</h4>
            <pre><code>// 深度思考处理
if nlu4lui != nil && nlu4lui.DeepSeekInfo != nil {
    deepSeekResp, _ := s.HandleDeepseek(ctx, req, nlu4lui)
    if deepSeekResp != nil {
        return deepSeekResp, nil
    }
}</code></pre>
            <ul>
                <li>检查是否有深度思考信息</li>
                <li>调用<code>HandleDeepseek</code>进行处理</li>
                <li>若返回非空结果，提前返回</li>
            </ul>
            <div class="note">
                <strong>"深度思考"功能：</strong>处理需要更复杂逻辑和推理的用户请求，可能会调用大模型或其他复杂算法进行深度处理。
            </div>

            <h4>3.4.4 屏幕下方练习处理</h4>
            <pre><code>if nlu4lui != nil && nlu4lui.ExtraResp != nil {
    if nlu4lui.ExtraResp.UnderScreenExercises && nlu4lui.ExtraResp.MultiModalInfo != nil {
        underScreenExercises, _ := s.UnderScreenExercises(ctx, req, nlu4lui)
        if underScreenExercises != nil {
            pkgSync.Go(ctx, s.log, func() {
                memoryContextReq := dto.MemoryContextReq{
                    UserTalID:   req.TalId,
                    SessionId:   req.RequestId,
                    AsrInfo:     req.AsrInfo,
                    IllegalType: "",
                    Hotfix:      hotfixDO,
                    SceneCode:   nlu4lui.SceneCode,
                }
                _ = s.memoryBiz.AddXPadMemoryContext(context.TODO(), memoryContextReq, underScreenExercises, nlu4lui)
            })
            s.log.WithContext(ctx).Infof("LuiNluPad2Result underScreenExercises: %+v", util.Marshal(underScreenExercises))
            return underScreenExercises, nil
        }
    }
}</code></pre>
            <ul>
                <li>检查是否有屏幕下方练习信息</li>
                <li>调用<code>UnderScreenExercises</code>处理</li>
                <li>异步保存记忆上下文</li>
                <li>提前返回处理结果</li>
            </ul>
            <div class="note">
                <strong>"屏幕下方练习"功能：</strong>处理屏幕下方区域的练习题或互动内容，通常是需要用户交互完成的任务。
            </div>
        </div>

        <div id="process-5" class="function-section">
            <h3>3.5 特殊意图处理</h3>
            <pre><code>// 进入小思工作坊
workshopResp, _ := s.HandleWorkshop(ctx, req, nlu4lui)
if workshopResp != nil {
    return workshopResp, nil
}

// 小思工作坊创造作品
workshopCreationResp, _ := s.HandleWorkshopCreation(ctx, req, nlu4lui)
if workshopCreationResp != nil {
    return workshopCreationResp, nil
}

// 小思滤镜处理,走agent
if nlu4lui != nil && nlu4lui.FiltersInfo != nil && !nlu4lui.FiltersInfo.ToSkill {
    filterResp, _ := s.HandleFilters(ctx, req, nlu4lui)
    if filterResp != nil {
        return filterResp, nil
    }
}

// 记录讲题意图的日志
_ = s.HandleExplainQuestion(ctx, req, nlu4lui)</code></pre>
            <ul>
                <li>依次处理以下特殊场景：
                    <ul>
                        <li><strong>小思工作坊</strong>：用<code>HandleWorkshop</code>处理创意工作坊相关功能</li>
                        <li><strong>创造作品</strong>：用<code>HandleWorkshopCreation</code>处理工作坊中创建作品的功能</li>
                        <li><strong>滤镜处理</strong>：用<code>HandleFilters</code>处理语音滤镜效果，但仅限非ToSkill情况</li>
                        <li><strong>讲题意图</strong>：记录讲题相关的日志，帮助追踪和分析用户学习行为</li>
                    </ul>
                </li>
                <li>如果任一处理返回非空结果，则提前返回</li>
            </ul>
            <div class="warning">
                <strong>注意：</strong>特殊意图处理顺序有严格优先级，按照代码中定义的顺序执行。
            </div>
        </div>

        <div id="process-6" class="function-section">
            <h3>3.6 技能处理（并发）</h3>
            <pre><code>var wg sync.WaitGroup
taskCount := len(nlu4lui.SkillList)
rc := make(chan *dto.Pad2SkillData, taskCount)
defer close(rc)

//遍历skill列表 老nlu直接append 新nlu并发调用pad2接口
skillNum := len(nlu4lui.SkillList)
for i, skill := range nlu4lui.SkillList {
    skill.AsrInfo = req.AsrInfo
    skill.SessionId = req.RequestId
    skill.SentenceId = req.SentenceId
    
    // 根据不同技能类型进行处理...
    switch skill.Skill {
    case common.SearchCourse.ToString():
        wg.Add(1)
        go func(skill2 dto.NLUSkillItem, sort int) {
            defer wg.Done()
            resp, respErr := s.buildPadV2SearchCourseNLU4LuiResp(ctx, &skill2, sort, skillNum, req.AsrInfo, req.AsrPinyin, req.TalId)
            if respErr == nil {
                rc <- resp
            } else {
                rc <- nil
            }
        }(skill, i)
    case common.SearchQuality.ToString():
        wg.Add(1)
        go func(skill2 dto.NLUSkillItem, sort int) {
            defer wg.Done()
            resp, respErr := s.buildPadV2SearchCultivationResp(ctx, &skill2, sort, skillNum)
            if respErr == nil {
                rc <- resp
            } else {
                rc <- nil
            }
        }(skill, i)
    // ... 更多技能类型处理
    default:
        wg.Add(1)
        go func(skill2 dto.NLUSkillItem, sort int) {
            defer wg.Done()
            resp, respErr := s.buildAppFuncNLU4LuiResp(ctx, &skill2, sort, req.Grade, req.BizType, nlu4lui.ModelOutputIntent)
            if respErr == nil {
                rc <- resp
            } else {
                rc <- nil
            }
        }(skill, i)
    }
}
wg.Wait()</code></pre>
            <ul>
                <li>创建等待组和结果通道，用于并发处理和结果收集</li>
                <li>遍历技能列表，为每个技能启动一个goroutine进行处理：
                    <ul>
                        <li>搜索课程（SearchCourse）：调用<code>buildPadV2SearchCourseNLU4LuiResp</code></li>
                        <li>搜索素养（SearchQuality）：调用<code>buildPadV2SearchCultivationResp</code></li>
                        <li>其他各种技能：调用对应的处理函数</li>
                        <li>默认技能：调用通用的<code>buildAppFuncNLU4LuiResp</code></li>
                    </ul>
                </li>
                <li>每个goroutine处理结束后，将结果发送到通道</li>
                <li>使用等待组确保所有技能处理完成后再继续</li>
            </ul>
            <div class="note">
                <strong>技能类型：</strong>系统支持多种技能类型，包括：搜索课程、搜索素养、继续学习学科、继续学习素养、试卷学科确认、搜索试卷、听写、点读、搜索课本、背诵、类型确认、听写类型确认、确认无效、练习题技能、搜索书籍/报纸/纪录片等。每种技能有专门的处理逻辑。
            </div>
            <div class="warning">
                <strong>并发处理：</strong>使用goroutine并发处理多个技能，提高系统吞吐量，但需要注意goroutine的异常处理和资源释放。
            </div>
        </div>

        <div id="process-7" class="function-section">
            <h3>3.7 结果收集与处理</h3>
            <pre><code>var (
    skillCount     = taskCount
    isFullView     = false
    isAccessingLLM = false
    isSessionEnd   = false
    endReason      = ""
    skillDataList  = make([]*dto.Pad2SkillData, 0)
    res            = dto.QueryPad2Resp{}
)

for i := taskCount; i > 0; i-- {
    queryData := <-rc
    if queryData != nil {
        if queryData.Data.TtsNorm == common.TtsTpl431 {
            skillCount--
            if skillCount > 0 {
                continue
            }
        }

        queryData.Data.SceneCode = nlu4lui.SceneCode
        if req.BizType == common.BizTypeCompanionLearn {
            tts := s.GetCompanionLearningTTS(queryData.Skill)
            if tts != "" {
                queryData.Data.TtsNorm = tts
            }
        }

        // 处理会话终止条件
        switch queryData.Skill {
        case common.SearchCourse.ToString(), common.SearchQuality.ToString(), common.ContinueLearnSubject.ToString(), common.ContinueLearnQuality.ToString():
            // 播放视频终止连续对话
            if queryData.Data.Count == 1 {
                isSessionEnd = true
                endReason = "video"
            }
        }

        // 更多会话终止条件判断...
        
        skillDataList = append(skillDataList, queryData)
    }
}</code></pre>
            <ul>
                <li>定义结果处理所需的变量</li>
                <li>从通道中收集处理结果：
                    <ul>
                        <li>处理特殊TTS模板情况</li>
                        <li>设置场景代码</li>
                        <li>针对特定业务类型（如伴学）设置特殊TTS</li>
                        <li>处理会话终止条件</li>
                    </ul>
                </li>
                <li>收集有效的技能数据到列表</li>
            </ul>
            <div class="note">
                <strong>会话终止条件：</strong>
                <ul>
                    <li>播放视频时终止连续对话（如搜索课程、素养等）</li>
                    <li>特定功能操作时终止（如关机、退出、关闭屏幕）</li>
                    <li>查找进度等特定功能名称时终止</li>
                </ul>
            </div>
        </div>

        <div id="process-8" class="function-section">
            <h3>3.8 多技能数据过滤与排序</h3>
            <pre><code>// 如果混排，过滤掉没有资源的数据
if len(skillDataList) > 1 {
    skillDataList = s.filterResourceEmptyDataInMultiSkillData(ctx, skillDataList)
}

sort.Slice(skillDataList, func(i, j int) bool {
    return skillDataList[i].Sort < skillDataList[j].Sort
})</code></pre>
            <ul>
                <li>当有多个技能结果时，过滤掉没有资源的数据</li>
                <li>根据排序值（Sort）对结果进行排序</li>
            </ul>
            <div class="note">
                <strong>过滤逻辑：</strong><code>filterResourceEmptyDataInMultiSkillData</code>方法会检查各技能数据是否含有资源，包括Widget内容和资源列表是否为空，确保只返回有效的结果。这对于混排多种技能结果时，保证UI展示的合理性非常重要。
            </div>
        </div>

        <div id="process-9" class="function-section">
            <h3>3.9 技能数据处理与转换</h3>
            <pre><code>var (
    showType      int
    dispatch      *dto.Dispatch
    sceneCode     = s.bizNlu.GetSceneCode(ctx, req.BizType)
    pad2SkillData = make([]*dto.Pad2SkillData, 0)
)
for i, data := range skillDataList {
    // 跳过特定TTS模板
    if skillNum > 1 && (strings.Contains(data.Data.TtsNorm, common.TtsInterceptAppTmp) || 
        strings.Contains(data.Data.TtsNorm, common.TtsTplInterceptCnhtenment) || 
        strings.Contains(data.Data.TtsNorm, common.TtsTplIntercept)) {
        continue
// 更新skill枚举映射
    dataSkill := data.Skill
    if data.Data != nil && data.Data.Task == common.BaiKeKnowledge {
        dataSkill = common.BaiKeKnowledge
        skillDataList[i].Data.Task = common.Baike.ToString()
    }

    // 设置技能信息
    if skillInfo, ok := dto.WebSkillInfoMap[dataSkill]; ok {
        //找课程二次确认临时使用paper_subject_confirm的skill,上次已处理VerticalDomain
        if !(dataSkill == common.PaperSubjectConfirm.ToString() && skillDataList[i].VerticalDomain == "课程学习") {
            skillDataList[i].VerticalDomain = skillInfo.VerticalDomain
            skillDataList[i].ModuleId = skillInfo.ModuleId
            skillDataList[i].ParserId = skillInfo.ParserId
        }
    }

    // RN服务设置
    if !strings.Contains(s.thirdConf.AppIdMap["xPad25AppIds"], custom_context.GetXAppId(ctx)) && len(res.Data) == 1 {
        dispatchXlj, showType := s.rnBiz.GetRn(ctx, req.SceneMode, req.RnVersion, data.Skill, data.Data, sceneCode)
        if dispatchXlj != nil {
            dispatch = dispatchXlj
            res.ShowType = showType
        }
    }

    // 添加到结果列表
    pad2SkillData = append(pad2SkillData, skillDataList[i])
}

// skill 是否走大模型 | 是否全屏页打开
for _, dataItem := range pad2SkillData {
    isFullView = s.IsFullView(ctx, nlu4lui.ModelOutputIntent, nlu4lui.FunctionList, dataItem, len(pad2SkillData), req, hotfixDO)
    isAccessingLLM = s.IsAccessingLLM(ctx, nlu4lui.ModelOutputIntent, dataItem, len(pad2SkillData), req, hotfixDO)
    
    s.SetAiAgent(ctx, req, dataItem)
    // 场景模式设置
    if strings.Contains(s.thirdConf.AppIdMap["xPad25AppIds"], custom_context.GetXAppId(ctx)) && (sceneCode == common.SceneXiaoSi) {
        dataItem.Data.SceneMode = dto.SceneModeLightweight
    }
}</code></pre>
            <ul>
                <li>遍历处理后的技能数据，执行以下操作：
                    <ul>
                        <li>过滤特定TTS模板，避免显示特殊提示文本</li>
                        <li>更新技能枚举映射，处理特殊技能类型（如百科知识）</li>
                        <li>设置垂直领域、模块ID和解析器ID</li>
                        <li>设置RN服务配置和分发信息</li>
                    </ul>
                </li>
                <li>对于每个最终技能数据：
                    <ul>
                        <li>判断是否需要全屏显示</li>
                        <li>判断是否需要访问大语言模型</li>
                        <li>设置AI代理信息</li>
                        <li>根据应用ID设置场景模式</li>
                    </ul>
                </li>
            </ul>
            <div class="note">
                <strong>技能数据转换：</strong>这个阶段主要是对技能数据进行最终处理和转换，确保它们包含正确的元数据，并按照应用要求进行适当配置。
            </div>
        </div>

        <div id="process-10" class="function-section">
            <h3>3.10 法律合规检查</h3>
            <pre><code>illegalType := ""
var checkInfo biz.LegalCheckResp
text := s.legalBiz.CheckPad2IfNeed(&res)
if s.thirdConf.LegalCheck.CheckEnable && text != "" {
    illegalType, _ = s.legalBiz.LegalCheck(ctx, req.TalId, text, "lui_output")
    if illegalType != "" {
        res = s.legalBiz.BuildPad2DefaultIllegalResp(illegalType, res)
    }
}

// 检查输入合规性
if s.thirdConf.LegalCheck.CheckEnable && (isAccessingLLM || util.InSliceString(res.ModelOutputIntent, s.thirdConf.LegalCheck.CheckIntent)) {
    illegalType, checkInfo, _ = s.legalBiz.LegalCheckInfo(ctx, req.TalId, req.AsrInfo, "lui_input")
    if illegalType != "" {
        res = s.legalBiz.BuildPad2QueryIllegalResp(illegalType, req.BizType, res)
    }
}</code></pre>
            <ul>
                <li>提取需要检查的文本内容</li>
                <li>如果启用了合规检查功能：
                    <ul>
                        <li>对输出内容进行合规检查</li>
                        <li>如果检测到不合规内容，替换为默认合规响应</li>
                        <li>对特定情况（访问大模型或特定意图）下的输入内容进行合规检查</li>
                        <li>如果输入不合规，也替换为合规响应</li>
                    </ul>
                </li>
            </ul>
            <div class="warning">
                <strong>法律合规：</strong>合规检查是面向青少年的应用必须执行的步骤，确保输入和输出内容不包含敏感、不良或违规信息。尤其是在使用大模型时，合规检查尤为重要。
            </div>
        </div>

        <div id="process-11" class="function-section">
            <h3>3.11 构建最终响应</h3>
            <pre><code>res.Data = pad2SkillData
res.ModelOutputIntent = nlu4lui.ModelOutputIntent
res.HotfixVector = hotfixDO
res.LegalCheckType = nlu4lui.LegalCheckType
res.ControllerVersion = nlu4lui.ControllerVersion
res.RewriteInfo = nlu4lui.RewriteInfo
res.VisionInput = nlu4lui.VisionInput

// 会话结束判断
if !(res.ModelOutputIntent == common.IntentChaTi && isFullView) {
    res.IsSessionEnd = isSessionEnd
}
res.EndReason = endReason

// 通过 rn 服务设置技能的分发信息
if strings.Contains(s.thirdConf.AppIdMap["xPad25AppIds"], custom_context.GetXAppId(ctx)) {
    dispatch, showType = s.rnBiz.GetSetDispatch(ctx, sceneCode, req.RnVersion, req.BizType, pad2SkillData)
    res.ShowType = showType
    res.Dispatch = dispatch
} else {
    res.Dispatch = dispatch
}

if res.Dispatch != nil {
    res.IsSessionEnd = false
}

// 设置TTS信息
if taskCount == 1 && len(pad2SkillData) == 1 {
    skill := pad2SkillData[0]
    res.TtsNorm = skill.Data.TtsNorm
    res.TtsShow = skill.Data.TtsShow
}

// 设置默认TTS
if req.BizType != common.BizTypeCompanionLearn {
    if len(pad2SkillData) == 0 {
        res.TtsNorm = common.TtsTpl131
        res.TtsShow = common.TtsTpl131
    } else if len(pad2SkillData) > 1 || res.TtsNorm == "" {
        res.TtsNorm = common.TtsTplT121
        res.TtsShow = common.TtsTplT121
    }
}

// 处理滤镜音色
if nlu4lui != nil && nlu4lui.FiltersInfo != nil && nlu4lui.FiltersInfo.ToSkill {
    voiceId, _ := s.thirdConf.Filters.FiltersNameMapVideoid[nlu4lui.FiltersInfo.FiltersName]
    res.TtsParam = &dto.TtsParam{VoiceId: voiceId}
}

// 最终转换并返回
res.BizType = req.BizType
resp, err := util.ReplyAny(res)
if err != nil {
    s.log.WithContext(ctx).Errorf("LuiNluPad2Result ReplyAny err: %+v; nlu4lui: %+v", err, nlu4lui)
    return nil, v1.ErrorInternalError("LuiNluPad2Result")
}

// 记录延迟统计
startTime := ctx.Value(common.StartTime).(time.Time)
if res.VisionInput != nil && (res.VisionInput.DesktopImageInfo != nil || res.VisionInput.TraceImageInfo != nil) {
    s.log.WithContext(ctx).Infof("LuiNluPad2Result_MultiModal_Latency: %v", time.Since(startTime).Seconds())
} else {
    s.log.WithContext(ctx).Infof("LuiNluPad2Result_Latency: %v", time.Since(startTime).Seconds())
}

// 异步保存记忆上下文
pkgSync.Go(ctx, s.log, func() {
    memoryContextReq := dto.MemoryContextReq{
        UserTalID:   req.TalId,
        SessionId:   req.RequestId,
        AsrInfo:     req.AsrInfo,
        IllegalType: illegalType,
        Hotfix:      hotfixDO,
        SceneCode:   nlu4lui.SceneCode,
    }
    _ = s.memoryBiz.AddXPadMemoryContext(context.TODO(), memoryContextReq, resp, nlu4lui)
})

return resp, nil</code></pre>
            <ul>
                <li>设置响应结构的各个字段：
                    <ul>
                        <li>技能数据</li>
                        <li>模型输出意图</li>
                        <li>热修复向量</li>
                        <li>合规检查类型</li>
                        <li>控制器版本</li>
                        <li>重写信息</li>
                        <li>视觉输入信息</li>
                    </ul>
                </li>
                <li>处理会话结束条件，但查题意图下的全屏模式有特殊处理</li>
                <li>通过RN服务设置分发信息，不同的应用ID有不同处理</li>
                <li>设置TTS信息：
                    <ul>
                        <li>单技能情况下使用技能自带TTS</li>
                        <li>多技能或无技能时使用默认TTS</li>
                        <li>伴学模式有特殊TTS处理</li>
                    </ul>
                </li>
                <li>处理滤镜音色，设置特殊语音参数</li>
                <li>转换为最终API响应结构</li>
filteredSkillDataList = append(filteredSkillDataList, skillData)
    }
    // 如果resource list 都为空 则返回第一个， 走槽位
    if len(filteredSkillDataList) == 0 && len(skillDataList) > 0 {
        filteredSkillDataList = append(filteredSkillDataList, skillDataList[0])
    }
    s.log.WithContext(ctx).Infof("filterResourceEmptyDataInMultiSkillData filteredSkillDataList: %v", util.Marshal(filteredSkillDataList))
    return filteredSkillDataList
}</code></pre>
            <ul>
                <li><strong>功能</strong>：过滤掉没有资源的技能数据，确保UI展示资源完整</li>
                <li><strong>处理流程</strong>：
                    <ul>
                        <li>定义资源相关技能列表</li>
                        <li>遍历技能数据检查资源完整性</li>
                        <li>检查Widget内容是否为空</li>
                        <li>使用反射检查数据结构</li>
                        <li>如所有资源都为空，返回第一个技能数据作为默认</li>
                    </ul>
                </li>
                <li><strong>应用场景</strong>：在多技能混排展示时，优先展示有完整资源的结果</li>
            </ul>
            <div class="note">
                <strong>优化点：</strong>该函数使用了较为复杂的JSON解析和反射操作，在高并发场景下可能会影响性能，未来可考虑优化解析逻辑或使用更高效的结构。
            </div>
        </div>

        <div class="function-section">
            <h3>4.2 convertNLUSkillItemToNlu4LuiData</h3>
            <pre><code>func convertNLUSkillItemToNlu4LuiData(b *dto.NLUSkillItem) *dto.Nlu4LuiData {
    b.AllSlots = dto.MergeStructs(b.DefaultSlots, b.SlotDict)
    return &dto.Nlu4LuiData{
        RequestID:        b.SessionId,
        ModuleName:       "",
        IsValidWidget:    b.IsResultValid,
        ResultConfidence: b.Count,
        Skill:            b.Skill,
        SubIntent:        0,
        DefaultSlots:     b.DefaultSlots,
        SlotDict:         b.SlotDict,
        RawSlotDict:      b.RawSlotDict,
        AllSlots:         b.AllSlots,
        Category:         b.Category,
        ResourceList:     b.ResourceList,
        Word:             b.Word,
        Hit:              b.Hit,
        SimScore:         b.SimScore,
        SearchFlag:       b.SearchFlag,
    }
}</code></pre>
            <ul>
                <li><strong>功能</strong>：将NLU技能项转换为Lui数据格式</li>
                <li><strong>处理流程</strong>：
                    <ul>
                        <li>合并默认槽位和槽位字典</li>
                        <li>创建并填充Nlu4LuiData结构</li>
                        <li>保留原始数据的关键信息</li>
                    </ul>
                </li>
                <li><strong>应用场景</strong>：用于数据模型转换，适配不同服务间的数据格式要求</li>
            </ul>
        </div>

        <div class="function-section">
            <h3>4.3 其他关键辅助方法</h3>
            <div class="grid-container">
                <div class="grid-item">
                    <strong>IsFullView</strong>
                    <p>判断是否需要全屏页显示，根据意图和技能类型决定</p>
                </div>
                <div class="grid-item">
                    <strong>IsAccessingLLM</strong>
                    <p>判断是否需要访问大语言模型，影响合规检查逻辑</p>
                </div>
                <div class="grid-item">
                    <strong>SetAiAgent</strong>
                    <p>设置AI代理信息，用于处理交互式对话</p>
                </div>
                <div class="grid-item">
                    <strong>VisibleSaidResp</strong>
                    <p>处理可见即可说场景，返回对应响应数据</p>
                </div>
                <div class="grid-item">
                    <strong>MultiModeResp</strong>
                    <p>处理多模态意图，整合多种输入类型</p>
                </div>
                <div class="grid-item">
                    <strong>HandleDeepseek</strong>
                    <p>处理深度思考请求，可能涉及大模型调用</p>
                </div>
            </div>
        </div>
    </div>

    <div id="core-logic" class="section">
        <h2>5. 核心业务逻辑要点</h2>
        
        <h3>5.1 主要设计思路</h3>
        <ol>
            <li><strong>多技能并发处理</strong>：使用goroutine并发调用不同技能处理逻辑，提高处理效率</li>
            <li><strong>场景分流机制</strong>：对不同场景（可见即可说、多模意图、深度思考等）进行分流处理，专门化处理流程</li>
            <li><strong>技能混排排序</strong>：支持多种技能结果混合排序展示，根据优先级和相关性排序</li>
            <li><strong>合规检查机制</strong>：对输入输出进行法律合规性检查，确保内容安全</li>
            <li><strong>会话管理</strong>：根据不同条件判断是否结束当前会话，支持连续对话</li>
            <li><strong>记忆上下文</strong>：保存交互上下文以支持后续对话，提升对话连贯性</li>
            <li><strong>RN服务集成</strong>：与RN服务交互设置技能分发信息，支持跨平台能力</li>
        </ol>

        <h3>5.2 处理流程总结</h3>
        <p>LuiNluPad2Result 方法整体处理流程可以概括为以下几个主要阶段：</p>
        <div class="flow-chart-container">
            <div class="flow-step">1. 初始化与校验</div>
            <div class="flow-arrow">↓</div>
            <div class="flow-step">2. NLU结果获取（优先从缓存/向量库获取，失败则调用NLU服务）</div>
            <div class="flow-arrow">↓</div>
            <div class="flow-step">3. 特殊场景处理（可见即可说、多模意图、深度思考等）</div>
            <div class="flow-arrow">↓</div>
            <div class="flow-step">4. 技能并发处理（多技能同时处理）</div>
            <div class="flow-arrow">↓</div>
            <div class="flow-step">5. 结果收集与过滤（排除空资源结果）</div>
            <div class="flow-arrow">↓</div>
            <div class="flow-step">6. 技能数据加工与转换</div>
            <div class="flow-arrow">↓</div>
            <div class="flow-step">7. 法律合规检查</div>
            <div class="flow-arrow">↓</div>
            <div class="flow-step">8. 响应组装与返回</div>
        </div>

        <h3>5.3 性能优化考虑</h3>
        <ul>
            <li><strong>并发处理</strong>：使用goroutine进行并发技能处理，提高吞吐量</li>
            <li><strong>缓存机制</strong>：优先从向量数据库查询结果，减少NLU服务调用</li>
            <li><strong>异步操作</strong>：记忆上下文的保存采用异步方式，不阻塞主流程</li>
            <li><strong>延迟统计</strong>：针对不同类型请求（普通/多模态）分别记录延迟，便于性能分析</li>
        </ul>

        <h3>5.4 优化方向建议</h3>
        <div class="warning">
            <ul>
                <li>进一步优化并发处理参数，根据系统负载动态调整并发度</li>
                <li>完善错误处理和日志记录，提高系统可观测性和稳定性</li>
                <li>增强记忆上下文的精确性，优化多轮对话体验</li>
                <li>优化垂域混排算法，使用机器学习方法提供更精准的响应排序</li>
                <li>减少JSON序列化/反序列化操作，使用更高效的数据传输格式</li>
                <li>为高频技能添加专门的缓存机制，进一步提高响应速度</li>
            </ul>
        </div>
    </div>
</body>
</html>
                <li>记录延迟统计，多模态和普通请求分别记录</li>
                <li>异步保存记忆上下文，确保不阻塞主流程</li>
                <li>返回最终结果</li>
            </ul>
        </div>
    </div>

    <div id="helper-functions" class="section">
        <h2>4. 辅助功能函数</h2>

        <div class="function-section">
            <h3>4.1 filterResourceEmptyDataInMultiSkillData</h3>
            <pre><code>func (s *LuiService) filterResourceEmptyDataInMultiSkillData(ctx context.Context, skillDataList []*dto.Pad2SkillData) []*dto.Pad2SkillData {
    filteredSkillDataList := make([]*dto.Pad2SkillData, 0)
    // 涉及资源的skill
    resourceSkills := []string{common.SearchCourse.ToString(), common.SearchQuality.ToString(), common.SearchBook.ToString(), 
                               common.SkillNewspaper.ToString(), common.SkillDocumentary.ToString(), common.ClickRead.ToString(), 
                               common.PaperSubjectConfirm.ToString()}
    for _, skillData := range skillDataList {
        flag := false
        for _, resourceSkill := range resourceSkills {
            // 过滤掉资源为空的数据
            if resourceSkill == skillData.Skill && (skillData.Data == nil || skillData.Data.Widget == "") {
                flag = true
                break
            }

            widget := &dto.Nlu4LuiWidget{}
            err := json.Unmarshal([]byte(skillData.Data.Widget), &widget)
            if err != nil {
                s.log.WithContext(ctx).Errorf("filterResourceEmptyDataInMultiSkillData json.Unmarshal err: %+v", err)
            }

            if widget.Content == nil || widget.Content == "" {
                flag = true
                break
            } else {
                widgetContent := &dto.Nlu4LuiWidgetContent{}
                jsonData, err := json.Marshal(widget.Content)
                if err != nil {
                    s.log.WithContext(ctx).Errorf("filterResourceEmptyDataInMultiSkillData Marshal%v", err)
                }
                err = json.Unmarshal(jsonData, &widgetContent)
                if err != nil {
                    s.log.WithContext(ctx).Errorf("filterResourceEmptyDataInMultiSkillData json.Unmarshal err: %+v", err)
                }
                if widgetContent.Data == nil {
                    flag = true
                    break
                } else {
                    val := reflect.ValueOf(widgetContent.Data)
                    // 检查是否为切片且长度为空
                    if val.Kind() == reflect.Slice && val.Len() == 0 {
                        flag = true
                        break
                    }
                }
            }
        }
        if flag {
            continue
        }
        filteredSkillDataList =
    }
        }
        err = s.memoryBiz.AddXPadMemoryContext(context.TODO(), memoryContextReq, multiResp, nlu4lui)
        s.log.WithContext(ctx).Infof("MultiMode memoryBiz.AddXPadMemoryContext err: %+v", err)
    })
    s.log.WithContext(ctx).Infof("LuiNluPad2Result multiResp: %+v", util.Marshal(multiResp))
    return multiResp, nil
}</code></pre>
            <ul>
                <li>调用<code>MultiModeResp</code>进行多模态处理</li>
                <li>如返回非空结果，异步保存记忆上下文</li>
                <li>记录日志并提前返回处理结果</li>
            </ul>
            <div class="note">
                <strong>"多模意图"功能：</strong>处理涉及多种模态（如语音、图像、文本等）的复合意图，例如用户同时使用语音和手势指令的情况。
            </div>

            <h4>3.4.3 深度思考处理</h4>
            <pre><code>// 深度思考处理
if nlu4lui != nil && nlu4lui.DeepSeekInfo != nil {
    deepSeekResp, _ := s.HandleDeepseek(ctx, req, nlu4lui)
    if deepSeekResp != nil {
        return deepSeekResp, nil
    }
}</code></pre>
            <ul>
                <li>检查是否有深度思考信息</li>
                <li>调用<code>HandleDeepseek</code>进行处理</li>
                <li>若返回非空结果，提前返回</li>
            </ul>
            <div class="note">
                <strong>"深度思考"功能：</strong>处理需要更复杂逻辑和推理的用户请求，可能会调用大模型或其他复杂算法进行深度处理。
            </div>

            <h
            </tr>
            <tr>
                <td>行号</td>
                <td>42行</td>
            </tr>
            <tr>
                <td>功能描述</td>
                <td>垂域混排结果返回，处理用户NLU请求并返回适当的响应结构</td>
            </tr>
        </table>

        <h3>方法签名</h3>
        <div class="method-signature">
            <code>func (s *LuiService) LuiNluPad2Result(ctx context.Context, req *v1.LuiNluRequest) (*structpb.Struct, error)</code>
        </div>

        <h3>参数说明</h3>
        <table>
            <tr>
                <th>参数</th>
                <th>类型</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>ctx</td>
                <td>context.Context</td>
                <td>上下文对象，包含请求上下文信息</td>
            </tr>
            <tr>
                <td>req</td>
                <td>*v1.LuiNluRequest</td>
                <td>NLU请求对象，包含用户ID、请求ID、ASR信息、业务类型等</td>
            </tr>
        </table>

        <h3>返回值</h3>
        <table>
            <tr>
                <th>返回值</th>
                <th>类型</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>result</td>
                <td>*structpb.Struct</td>
                <td>处理后的结构化响应</td>
            </tr>
            <tr>
                <td>err</td>
                <td>error</td>
                <td>错误信息（如有）</td>
            </tr>
        </table>
    </div>

    <div id="flow-chart" class="section">
        <h2>2. 流程图</h2>
        <div class="flow-chart-container">
            <div class="mermaid">
flowchart TD
    start([开始]) --> log[日志记录与参数校验]
    log --> scene_mode[场景模式处理]
    scene_mode --> query_nlu[查询NLU结果]
    query_nlu --> nlu_check{NLU结果是否为空?}
    nlu_check -- 是 --> get_context[获取上下文]
    get_context --> fetch_nlu[调用NLU服务获取结果]
    fetch_nlu --> err_check{是否有错误?}
    err_check -- 是 --> err_handle[错误处理]
    err_handle --> return_err([返回错误])
    err_check -- 否 --> special
    nlu_check -- 否 --> special[特殊场景处理]
    
    special --> visible_check{可见即可说?}
    visible_check -- 是 --> visible_process[处理可见即可说]
    visible_process --> memory_visible[异步保存记忆]
    memory_visible --> return_visible([返回可见即可说结果])
    
    visible_check -- 否 --> multi_check{多模意图?}
    multi_check -- 是 --> multi_process[处理多模意图]
    multi_process --> memory_multi[异步保存记忆]
    memory_multi --> return_multi([返回多模意图结果])
    
    multi_check -- 否 --> deep_check{深度思考?}
    deep_check -- 是 --> deep_process[处理深度思考]
    deep_process --> return_deep([返回深度思考结果])
    
    deep_check -- 否 --> exercise_check{屏幕下方练习?}
    exercise_check -- 是 --> exercise_process[处理屏幕下方练习]
    exercise_process --> memory_exercise[异步保存记忆]
    memory_exercise --> return_exercise([返回练习结果])
    
    exercise_check -- 否 --> workshop_check{小思工作坊?}
    workshop_check -- 是 --> workshop_process[处理工作坊]
    workshop_process --> return_workshop([返回工作坊结果])
    
    workshop_check -- 否 --> creation_check{工作坊创造作品?}
    creation_check -- 是 --> creation_process[处理创造作品]
    creation_process --> return_creation([返回创造作品结果])
    
    creation_check -- 否 --> filter_check{小思滤镜?}
    filter_check -- 是 --> filter_process[处理滤镜]
    filter_process --> return_filter([返回滤镜结果])
    
    filter_check -- 否 --> log_question[记录讲题意图]
    log_question --> concurrent_skill[并发处理技能列表]
    concurrent_skill --> wait_skill[等待所有技能处理完成]
    wait_skill --> collect_result[收集技能处理结果]
    collect_result --> filter_data[过滤空资源技能]
    filter_data --> sort_data[排序技能数据]
    sort_data --> process_each[处理每个技能数据]
    process_each