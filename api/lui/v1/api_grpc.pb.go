// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: api/lui/v1/api.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	LuiContentApi_LuiNluResult_FullMethodName                   = "/api.v1.LuiContentApi/LuiNluResult"
	LuiContentApi_LuiNlu4Lui_FullMethodName                     = "/api.v1.LuiContentApi/LuiNlu4Lui"
	LuiContentApi_LuiSug2Lui_FullMethodName                     = "/api.v1.LuiContentApi/LuiSug2Lui"
	LuiContentApi_LuiUserChoice_FullMethodName                  = "/api.v1.LuiContentApi/LuiUserChoice"
	LuiContentApi_LuiToolsUserChoice_FullMethodName             = "/api.v1.LuiContentApi/LuiToolsUserChoice"
	LuiContentApi_QueryTraceFeedback_FullMethodName             = "/api.v1.LuiContentApi/QueryTraceFeedback"
	LuiContentApi_LuiSimulateQuery_FullMethodName               = "/api.v1.LuiContentApi/LuiSimulateQuery"
	LuiContentApi_RejectNLU_FullMethodName                      = "/api.v1.LuiContentApi/RejectNLU"
	LuiContentApi_MemoryContext_FullMethodName                  = "/api.v1.LuiContentApi/MemoryContext"
	LuiContentApi_LuiUserLlmSug_FullMethodName                  = "/api.v1.LuiContentApi/LuiUserLlmSug"
	LuiContentApi_FullViewContext_FullMethodName                = "/api.v1.LuiContentApi/FullViewContext"
	LuiContentApi_LuiUserLlmTails_FullMethodName                = "/api.v1.LuiContentApi/LuiUserLlmTails"
	LuiContentApi_AIEyeFingerImageJudge_FullMethodName          = "/api.v1.LuiContentApi/AIEyeFingerImageJudge"
	LuiContentApi_ZhWordConfirm_FullMethodName                  = "/api.v1.LuiContentApi/ZhWordConfirm"
	LuiContentApi_UpdateMemoryContextLlmResponse_FullMethodName = "/api.v1.LuiContentApi/UpdateMemoryContextLlmResponse"
	LuiContentApi_GetLLMMemoryContext_FullMethodName            = "/api.v1.LuiContentApi/GetLLMMemoryContext"
	LuiContentApi_AddLLMMemoryContext_FullMethodName            = "/api.v1.LuiContentApi/AddLLMMemoryContext"
	LuiContentApi_ExitLuiScene_FullMethodName                   = "/api.v1.LuiContentApi/ExitLuiScene"
)

// LuiContentApiClient is the client API for LuiContentApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LuiContentApiClient interface {
	// nlu数据
	LuiNluResult(ctx context.Context, in *LuiNluRequest, opts ...grpc.CallOption) (*structpb.Struct, error)
	// 二次确认调用
	LuiNlu4Lui(ctx context.Context, in *LuiNluRequest, opts ...grpc.CallOption) (*structpb.Struct, error)
	// sug点击调用
	LuiSug2Lui(ctx context.Context, in *LuiSugRequest, opts ...grpc.CallOption) (*structpb.Struct, error)
	// LUI资源 用户选择的版本 from学习系统
	LuiUserChoice(ctx context.Context, in *LuiUserChoiceRequest, opts ...grpc.CallOption) (*LuiUserChoiceReply, error)
	// LUI资源 用户选择的版本 from工具
	LuiToolsUserChoice(ctx context.Context, in *LuiToolsUserChoiceRequest, opts ...grpc.CallOption) (*LuiToolsUserChoiceReply, error)
	QueryTraceFeedback(ctx context.Context, in *QueryTraceFeedbackRequest, opts ...grpc.CallOption) (*QueryTraceFeedbackReply, error)
	// LuiSimulateQuery
	LuiSimulateQuery(ctx context.Context, in *LuiSimulateQueryRequest, opts ...grpc.CallOption) (*structpb.Struct, error)
	RejectNLU(ctx context.Context, in *LuiRejectRequest, opts ...grpc.CallOption) (*structpb.Struct, error)
	// 拒识接口上下文呢
	MemoryContext(ctx context.Context, in *MemoryContextRequest, opts ...grpc.CallOption) (*MemoryContextReply, error)
	// 用户大模型SUG
	LuiUserLlmSug(ctx context.Context, in *LuiUserLlmSugRequest, opts ...grpc.CallOption) (*LuiUserLlmSugReply, error)
	// 全屏对话页上下文
	FullViewContext(ctx context.Context, in *FullViewContextRequest, opts ...grpc.CallOption) (*FullViewContextReply, error)
	// 用户大模型Tails
	LuiUserLlmTails(ctx context.Context, in *LuiUserLlmTailsRequest, opts ...grpc.CallOption) (*structpb.Struct, error)
	// 用户大模型Tails
	AIEyeFingerImageJudge(ctx context.Context, in *AIEyeFingerImageJudgeRequest, opts ...grpc.CallOption) (*structpb.Struct, error)
	// 中文字词确认
	ZhWordConfirm(ctx context.Context, in *LuiNluConfirmRequest, opts ...grpc.CallOption) (*structpb.Struct, error)
	UpdateMemoryContextLlmResponse(ctx context.Context, in *LlmResponse, opts ...grpc.CallOption) (*structpb.Struct, error)
	GetLLMMemoryContext(ctx context.Context, in *LLMMemoryContextRequest, opts ...grpc.CallOption) (*LLMMemoryContextReply, error)
	AddLLMMemoryContext(ctx context.Context, in *AddLLMMemoryContextRequest, opts ...grpc.CallOption) (*AddLLMMemoryContextReply, error)
	// 用户主动退出某个场景
	ExitLuiScene(ctx context.Context, in *ExitLuiSceneRequest, opts ...grpc.CallOption) (*ExitLuiSceneReply, error)
}

type luiContentApiClient struct {
	cc grpc.ClientConnInterface
}

func NewLuiContentApiClient(cc grpc.ClientConnInterface) LuiContentApiClient {
	return &luiContentApiClient{cc}
}

func (c *luiContentApiClient) LuiNluResult(ctx context.Context, in *LuiNluRequest, opts ...grpc.CallOption) (*structpb.Struct, error) {
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, LuiContentApi_LuiNluResult_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luiContentApiClient) LuiNlu4Lui(ctx context.Context, in *LuiNluRequest, opts ...grpc.CallOption) (*structpb.Struct, error) {
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, LuiContentApi_LuiNlu4Lui_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luiContentApiClient) LuiSug2Lui(ctx context.Context, in *LuiSugRequest, opts ...grpc.CallOption) (*structpb.Struct, error) {
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, LuiContentApi_LuiSug2Lui_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luiContentApiClient) LuiUserChoice(ctx context.Context, in *LuiUserChoiceRequest, opts ...grpc.CallOption) (*LuiUserChoiceReply, error) {
	out := new(LuiUserChoiceReply)
	err := c.cc.Invoke(ctx, LuiContentApi_LuiUserChoice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luiContentApiClient) LuiToolsUserChoice(ctx context.Context, in *LuiToolsUserChoiceRequest, opts ...grpc.CallOption) (*LuiToolsUserChoiceReply, error) {
	out := new(LuiToolsUserChoiceReply)
	err := c.cc.Invoke(ctx, LuiContentApi_LuiToolsUserChoice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luiContentApiClient) QueryTraceFeedback(ctx context.Context, in *QueryTraceFeedbackRequest, opts ...grpc.CallOption) (*QueryTraceFeedbackReply, error) {
	out := new(QueryTraceFeedbackReply)
	err := c.cc.Invoke(ctx, LuiContentApi_QueryTraceFeedback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luiContentApiClient) LuiSimulateQuery(ctx context.Context, in *LuiSimulateQueryRequest, opts ...grpc.CallOption) (*structpb.Struct, error) {
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, LuiContentApi_LuiSimulateQuery_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luiContentApiClient) RejectNLU(ctx context.Context, in *LuiRejectRequest, opts ...grpc.CallOption) (*structpb.Struct, error) {
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, LuiContentApi_RejectNLU_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luiContentApiClient) MemoryContext(ctx context.Context, in *MemoryContextRequest, opts ...grpc.CallOption) (*MemoryContextReply, error) {
	out := new(MemoryContextReply)
	err := c.cc.Invoke(ctx, LuiContentApi_MemoryContext_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luiContentApiClient) LuiUserLlmSug(ctx context.Context, in *LuiUserLlmSugRequest, opts ...grpc.CallOption) (*LuiUserLlmSugReply, error) {
	out := new(LuiUserLlmSugReply)
	err := c.cc.Invoke(ctx, LuiContentApi_LuiUserLlmSug_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luiContentApiClient) FullViewContext(ctx context.Context, in *FullViewContextRequest, opts ...grpc.CallOption) (*FullViewContextReply, error) {
	out := new(FullViewContextReply)
	err := c.cc.Invoke(ctx, LuiContentApi_FullViewContext_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luiContentApiClient) LuiUserLlmTails(ctx context.Context, in *LuiUserLlmTailsRequest, opts ...grpc.CallOption) (*structpb.Struct, error) {
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, LuiContentApi_LuiUserLlmTails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luiContentApiClient) AIEyeFingerImageJudge(ctx context.Context, in *AIEyeFingerImageJudgeRequest, opts ...grpc.CallOption) (*structpb.Struct, error) {
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, LuiContentApi_AIEyeFingerImageJudge_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luiContentApiClient) ZhWordConfirm(ctx context.Context, in *LuiNluConfirmRequest, opts ...grpc.CallOption) (*structpb.Struct, error) {
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, LuiContentApi_ZhWordConfirm_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luiContentApiClient) UpdateMemoryContextLlmResponse(ctx context.Context, in *LlmResponse, opts ...grpc.CallOption) (*structpb.Struct, error) {
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, LuiContentApi_UpdateMemoryContextLlmResponse_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luiContentApiClient) GetLLMMemoryContext(ctx context.Context, in *LLMMemoryContextRequest, opts ...grpc.CallOption) (*LLMMemoryContextReply, error) {
	out := new(LLMMemoryContextReply)
	err := c.cc.Invoke(ctx, LuiContentApi_GetLLMMemoryContext_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luiContentApiClient) AddLLMMemoryContext(ctx context.Context, in *AddLLMMemoryContextRequest, opts ...grpc.CallOption) (*AddLLMMemoryContextReply, error) {
	out := new(AddLLMMemoryContextReply)
	err := c.cc.Invoke(ctx, LuiContentApi_AddLLMMemoryContext_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luiContentApiClient) ExitLuiScene(ctx context.Context, in *ExitLuiSceneRequest, opts ...grpc.CallOption) (*ExitLuiSceneReply, error) {
	out := new(ExitLuiSceneReply)
	err := c.cc.Invoke(ctx, LuiContentApi_ExitLuiScene_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LuiContentApiServer is the server API for LuiContentApi service.
// All implementations must embed UnimplementedLuiContentApiServer
// for forward compatibility
type LuiContentApiServer interface {
	// nlu数据
	LuiNluResult(context.Context, *LuiNluRequest) (*structpb.Struct, error)
	// 二次确认调用
	LuiNlu4Lui(context.Context, *LuiNluRequest) (*structpb.Struct, error)
	// sug点击调用
	LuiSug2Lui(context.Context, *LuiSugRequest) (*structpb.Struct, error)
	// LUI资源 用户选择的版本 from学习系统
	LuiUserChoice(context.Context, *LuiUserChoiceRequest) (*LuiUserChoiceReply, error)
	// LUI资源 用户选择的版本 from工具
	LuiToolsUserChoice(context.Context, *LuiToolsUserChoiceRequest) (*LuiToolsUserChoiceReply, error)
	QueryTraceFeedback(context.Context, *QueryTraceFeedbackRequest) (*QueryTraceFeedbackReply, error)
	// LuiSimulateQuery
	LuiSimulateQuery(context.Context, *LuiSimulateQueryRequest) (*structpb.Struct, error)
	RejectNLU(context.Context, *LuiRejectRequest) (*structpb.Struct, error)
	// 拒识接口上下文呢
	MemoryContext(context.Context, *MemoryContextRequest) (*MemoryContextReply, error)
	// 用户大模型SUG
	LuiUserLlmSug(context.Context, *LuiUserLlmSugRequest) (*LuiUserLlmSugReply, error)
	// 全屏对话页上下文
	FullViewContext(context.Context, *FullViewContextRequest) (*FullViewContextReply, error)
	// 用户大模型Tails
	LuiUserLlmTails(context.Context, *LuiUserLlmTailsRequest) (*structpb.Struct, error)
	// 用户大模型Tails
	AIEyeFingerImageJudge(context.Context, *AIEyeFingerImageJudgeRequest) (*structpb.Struct, error)
	// 中文字词确认
	ZhWordConfirm(context.Context, *LuiNluConfirmRequest) (*structpb.Struct, error)
	UpdateMemoryContextLlmResponse(context.Context, *LlmResponse) (*structpb.Struct, error)
	GetLLMMemoryContext(context.Context, *LLMMemoryContextRequest) (*LLMMemoryContextReply, error)
	AddLLMMemoryContext(context.Context, *AddLLMMemoryContextRequest) (*AddLLMMemoryContextReply, error)
	// 用户主动退出某个场景
	ExitLuiScene(context.Context, *ExitLuiSceneRequest) (*ExitLuiSceneReply, error)
	mustEmbedUnimplementedLuiContentApiServer()
}

// UnimplementedLuiContentApiServer must be embedded to have forward compatible implementations.
type UnimplementedLuiContentApiServer struct {
}

func (UnimplementedLuiContentApiServer) LuiNluResult(context.Context, *LuiNluRequest) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LuiNluResult not implemented")
}
func (UnimplementedLuiContentApiServer) LuiNlu4Lui(context.Context, *LuiNluRequest) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LuiNlu4Lui not implemented")
}
func (UnimplementedLuiContentApiServer) LuiSug2Lui(context.Context, *LuiSugRequest) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LuiSug2Lui not implemented")
}
func (UnimplementedLuiContentApiServer) LuiUserChoice(context.Context, *LuiUserChoiceRequest) (*LuiUserChoiceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LuiUserChoice not implemented")
}
func (UnimplementedLuiContentApiServer) LuiToolsUserChoice(context.Context, *LuiToolsUserChoiceRequest) (*LuiToolsUserChoiceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LuiToolsUserChoice not implemented")
}
func (UnimplementedLuiContentApiServer) QueryTraceFeedback(context.Context, *QueryTraceFeedbackRequest) (*QueryTraceFeedbackReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryTraceFeedback not implemented")
}
func (UnimplementedLuiContentApiServer) LuiSimulateQuery(context.Context, *LuiSimulateQueryRequest) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LuiSimulateQuery not implemented")
}
func (UnimplementedLuiContentApiServer) RejectNLU(context.Context, *LuiRejectRequest) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RejectNLU not implemented")
}
func (UnimplementedLuiContentApiServer) MemoryContext(context.Context, *MemoryContextRequest) (*MemoryContextReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MemoryContext not implemented")
}
func (UnimplementedLuiContentApiServer) LuiUserLlmSug(context.Context, *LuiUserLlmSugRequest) (*LuiUserLlmSugReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LuiUserLlmSug not implemented")
}
func (UnimplementedLuiContentApiServer) FullViewContext(context.Context, *FullViewContextRequest) (*FullViewContextReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FullViewContext not implemented")
}
func (UnimplementedLuiContentApiServer) LuiUserLlmTails(context.Context, *LuiUserLlmTailsRequest) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LuiUserLlmTails not implemented")
}
func (UnimplementedLuiContentApiServer) AIEyeFingerImageJudge(context.Context, *AIEyeFingerImageJudgeRequest) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AIEyeFingerImageJudge not implemented")
}
func (UnimplementedLuiContentApiServer) ZhWordConfirm(context.Context, *LuiNluConfirmRequest) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ZhWordConfirm not implemented")
}
func (UnimplementedLuiContentApiServer) UpdateMemoryContextLlmResponse(context.Context, *LlmResponse) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMemoryContextLlmResponse not implemented")
}
func (UnimplementedLuiContentApiServer) GetLLMMemoryContext(context.Context, *LLMMemoryContextRequest) (*LLMMemoryContextReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLLMMemoryContext not implemented")
}
func (UnimplementedLuiContentApiServer) AddLLMMemoryContext(context.Context, *AddLLMMemoryContextRequest) (*AddLLMMemoryContextReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddLLMMemoryContext not implemented")
}
func (UnimplementedLuiContentApiServer) ExitLuiScene(context.Context, *ExitLuiSceneRequest) (*ExitLuiSceneReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExitLuiScene not implemented")
}
func (UnimplementedLuiContentApiServer) mustEmbedUnimplementedLuiContentApiServer() {}

// UnsafeLuiContentApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LuiContentApiServer will
// result in compilation errors.
type UnsafeLuiContentApiServer interface {
	mustEmbedUnimplementedLuiContentApiServer()
}

func RegisterLuiContentApiServer(s grpc.ServiceRegistrar, srv LuiContentApiServer) {
	s.RegisterService(&LuiContentApi_ServiceDesc, srv)
}

func _LuiContentApi_LuiNluResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LuiNluRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuiContentApiServer).LuiNluResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LuiContentApi_LuiNluResult_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuiContentApiServer).LuiNluResult(ctx, req.(*LuiNluRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuiContentApi_LuiNlu4Lui_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LuiNluRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuiContentApiServer).LuiNlu4Lui(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LuiContentApi_LuiNlu4Lui_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuiContentApiServer).LuiNlu4Lui(ctx, req.(*LuiNluRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuiContentApi_LuiSug2Lui_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LuiSugRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuiContentApiServer).LuiSug2Lui(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LuiContentApi_LuiSug2Lui_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuiContentApiServer).LuiSug2Lui(ctx, req.(*LuiSugRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuiContentApi_LuiUserChoice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LuiUserChoiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuiContentApiServer).LuiUserChoice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LuiContentApi_LuiUserChoice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuiContentApiServer).LuiUserChoice(ctx, req.(*LuiUserChoiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuiContentApi_LuiToolsUserChoice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LuiToolsUserChoiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuiContentApiServer).LuiToolsUserChoice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LuiContentApi_LuiToolsUserChoice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuiContentApiServer).LuiToolsUserChoice(ctx, req.(*LuiToolsUserChoiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuiContentApi_QueryTraceFeedback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryTraceFeedbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuiContentApiServer).QueryTraceFeedback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LuiContentApi_QueryTraceFeedback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuiContentApiServer).QueryTraceFeedback(ctx, req.(*QueryTraceFeedbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuiContentApi_LuiSimulateQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LuiSimulateQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuiContentApiServer).LuiSimulateQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LuiContentApi_LuiSimulateQuery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuiContentApiServer).LuiSimulateQuery(ctx, req.(*LuiSimulateQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuiContentApi_RejectNLU_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LuiRejectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuiContentApiServer).RejectNLU(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LuiContentApi_RejectNLU_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuiContentApiServer).RejectNLU(ctx, req.(*LuiRejectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuiContentApi_MemoryContext_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MemoryContextRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuiContentApiServer).MemoryContext(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LuiContentApi_MemoryContext_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuiContentApiServer).MemoryContext(ctx, req.(*MemoryContextRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuiContentApi_LuiUserLlmSug_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LuiUserLlmSugRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuiContentApiServer).LuiUserLlmSug(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LuiContentApi_LuiUserLlmSug_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuiContentApiServer).LuiUserLlmSug(ctx, req.(*LuiUserLlmSugRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuiContentApi_FullViewContext_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FullViewContextRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuiContentApiServer).FullViewContext(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LuiContentApi_FullViewContext_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuiContentApiServer).FullViewContext(ctx, req.(*FullViewContextRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuiContentApi_LuiUserLlmTails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LuiUserLlmTailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuiContentApiServer).LuiUserLlmTails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LuiContentApi_LuiUserLlmTails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuiContentApiServer).LuiUserLlmTails(ctx, req.(*LuiUserLlmTailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuiContentApi_AIEyeFingerImageJudge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AIEyeFingerImageJudgeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuiContentApiServer).AIEyeFingerImageJudge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LuiContentApi_AIEyeFingerImageJudge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuiContentApiServer).AIEyeFingerImageJudge(ctx, req.(*AIEyeFingerImageJudgeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuiContentApi_ZhWordConfirm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LuiNluConfirmRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuiContentApiServer).ZhWordConfirm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LuiContentApi_ZhWordConfirm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuiContentApiServer).ZhWordConfirm(ctx, req.(*LuiNluConfirmRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuiContentApi_UpdateMemoryContextLlmResponse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LlmResponse)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuiContentApiServer).UpdateMemoryContextLlmResponse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LuiContentApi_UpdateMemoryContextLlmResponse_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuiContentApiServer).UpdateMemoryContextLlmResponse(ctx, req.(*LlmResponse))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuiContentApi_GetLLMMemoryContext_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LLMMemoryContextRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuiContentApiServer).GetLLMMemoryContext(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LuiContentApi_GetLLMMemoryContext_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuiContentApiServer).GetLLMMemoryContext(ctx, req.(*LLMMemoryContextRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuiContentApi_AddLLMMemoryContext_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddLLMMemoryContextRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuiContentApiServer).AddLLMMemoryContext(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LuiContentApi_AddLLMMemoryContext_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuiContentApiServer).AddLLMMemoryContext(ctx, req.(*AddLLMMemoryContextRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuiContentApi_ExitLuiScene_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExitLuiSceneRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuiContentApiServer).ExitLuiScene(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LuiContentApi_ExitLuiScene_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuiContentApiServer).ExitLuiScene(ctx, req.(*ExitLuiSceneRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// LuiContentApi_ServiceDesc is the grpc.ServiceDesc for LuiContentApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LuiContentApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.v1.LuiContentApi",
	HandlerType: (*LuiContentApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "LuiNluResult",
			Handler:    _LuiContentApi_LuiNluResult_Handler,
		},
		{
			MethodName: "LuiNlu4Lui",
			Handler:    _LuiContentApi_LuiNlu4Lui_Handler,
		},
		{
			MethodName: "LuiSug2Lui",
			Handler:    _LuiContentApi_LuiSug2Lui_Handler,
		},
		{
			MethodName: "LuiUserChoice",
			Handler:    _LuiContentApi_LuiUserChoice_Handler,
		},
		{
			MethodName: "LuiToolsUserChoice",
			Handler:    _LuiContentApi_LuiToolsUserChoice_Handler,
		},
		{
			MethodName: "QueryTraceFeedback",
			Handler:    _LuiContentApi_QueryTraceFeedback_Handler,
		},
		{
			MethodName: "LuiSimulateQuery",
			Handler:    _LuiContentApi_LuiSimulateQuery_Handler,
		},
		{
			MethodName: "RejectNLU",
			Handler:    _LuiContentApi_RejectNLU_Handler,
		},
		{
			MethodName: "MemoryContext",
			Handler:    _LuiContentApi_MemoryContext_Handler,
		},
		{
			MethodName: "LuiUserLlmSug",
			Handler:    _LuiContentApi_LuiUserLlmSug_Handler,
		},
		{
			MethodName: "FullViewContext",
			Handler:    _LuiContentApi_FullViewContext_Handler,
		},
		{
			MethodName: "LuiUserLlmTails",
			Handler:    _LuiContentApi_LuiUserLlmTails_Handler,
		},
		{
			MethodName: "AIEyeFingerImageJudge",
			Handler:    _LuiContentApi_AIEyeFingerImageJudge_Handler,
		},
		{
			MethodName: "ZhWordConfirm",
			Handler:    _LuiContentApi_ZhWordConfirm_Handler,
		},
		{
			MethodName: "UpdateMemoryContextLlmResponse",
			Handler:    _LuiContentApi_UpdateMemoryContextLlmResponse_Handler,
		},
		{
			MethodName: "GetLLMMemoryContext",
			Handler:    _LuiContentApi_GetLLMMemoryContext_Handler,
		},
		{
			MethodName: "AddLLMMemoryContext",
			Handler:    _LuiContentApi_AddLLMMemoryContext_Handler,
		},
		{
			MethodName: "ExitLuiScene",
			Handler:    _LuiContentApi_ExitLuiScene_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/lui/v1/api.proto",
}

const (
	UCenterApi_LuiUserInfo_FullMethodName = "/api.v1.UCenterApi/LuiUserInfo"
)

// UCenterApiClient is the client API for UCenterApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UCenterApiClient interface {
	// 用户信息
	LuiUserInfo(ctx context.Context, in *LuiUserInfoRequest, opts ...grpc.CallOption) (*LuiUserInfoReply, error)
}

type uCenterApiClient struct {
	cc grpc.ClientConnInterface
}

func NewUCenterApiClient(cc grpc.ClientConnInterface) UCenterApiClient {
	return &uCenterApiClient{cc}
}

func (c *uCenterApiClient) LuiUserInfo(ctx context.Context, in *LuiUserInfoRequest, opts ...grpc.CallOption) (*LuiUserInfoReply, error) {
	out := new(LuiUserInfoReply)
	err := c.cc.Invoke(ctx, UCenterApi_LuiUserInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UCenterApiServer is the server API for UCenterApi service.
// All implementations must embed UnimplementedUCenterApiServer
// for forward compatibility
type UCenterApiServer interface {
	// 用户信息
	LuiUserInfo(context.Context, *LuiUserInfoRequest) (*LuiUserInfoReply, error)
	mustEmbedUnimplementedUCenterApiServer()
}

// UnimplementedUCenterApiServer must be embedded to have forward compatible implementations.
type UnimplementedUCenterApiServer struct {
}

func (UnimplementedUCenterApiServer) LuiUserInfo(context.Context, *LuiUserInfoRequest) (*LuiUserInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LuiUserInfo not implemented")
}
func (UnimplementedUCenterApiServer) mustEmbedUnimplementedUCenterApiServer() {}

// UnsafeUCenterApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UCenterApiServer will
// result in compilation errors.
type UnsafeUCenterApiServer interface {
	mustEmbedUnimplementedUCenterApiServer()
}

func RegisterUCenterApiServer(s grpc.ServiceRegistrar, srv UCenterApiServer) {
	s.RegisterService(&UCenterApi_ServiceDesc, srv)
}

func _UCenterApi_LuiUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LuiUserInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UCenterApiServer).LuiUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UCenterApi_LuiUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UCenterApiServer).LuiUserInfo(ctx, req.(*LuiUserInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UCenterApi_ServiceDesc is the grpc.ServiceDesc for UCenterApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UCenterApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.v1.UCenterApi",
	HandlerType: (*UCenterApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "LuiUserInfo",
			Handler:    _UCenterApi_LuiUserInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/lui/v1/api.proto",
}
