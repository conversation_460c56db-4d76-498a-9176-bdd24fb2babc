// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.20.3
// source: api/lui/v1/api.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LlmResponse struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	LlmSkill           string                 `protobuf:"bytes,1,opt,name=llm_skill,json=llmSkill,proto3" json:"llm_skill,omitempty"`
	LlmModel           string                 `protobuf:"bytes,2,opt,name=llm_model,json=llmModel,proto3" json:"llm_model,omitempty"`
	Response           string                 `protobuf:"bytes,3,opt,name=response,proto3" json:"response,omitempty"`
	ImageUrl           string                 `protobuf:"bytes,4,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	VideoUrl           string                 `protobuf:"bytes,5,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	LlmResponse        string                 `protobuf:"bytes,6,opt,name=llm_response,json=llmResponse,proto3" json:"llm_response,omitempty"`
	DialogueId         string                 `protobuf:"bytes,7,opt,name=dialogue_id,json=dialogueId,proto3" json:"dialogue_id,omitempty"`
	SessionId          string                 `protobuf:"bytes,9,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	Type               int32                  `protobuf:"varint,10,opt,name=type,proto3" json:"type,omitempty"`
	Cover              string                 `protobuf:"bytes,11,opt,name=cover,proto3" json:"cover,omitempty"`
	CoverTitle         string                 `protobuf:"bytes,12,opt,name=cover_title,json=coverTitle,proto3" json:"cover_title,omitempty"`
	Url                string                 `protobuf:"bytes,13,opt,name=url,proto3" json:"url,omitempty"`
	TtsInfo            string                 `protobuf:"bytes,14,opt,name=tts_info,json=ttsInfo,proto3" json:"tts_info,omitempty"`
	MixedModalQuery    string                 `protobuf:"bytes,15,opt,name=mixed_modal_query,json=mixedModalQuery,proto3" json:"mixed_modal_query,omitempty"`
	MixedModalResponse *structpb.Struct       `protobuf:"bytes,16,opt,name=mixed_modal_response,json=mixedModalResponse,proto3" json:"mixed_modal_response,omitempty"`
	TtsNorm            string                 `protobuf:"bytes,17,opt,name=tts_norm,json=ttsNorm,proto3" json:"tts_norm,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *LlmResponse) Reset() {
	*x = LlmResponse{}
	mi := &file_api_lui_v1_api_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LlmResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LlmResponse) ProtoMessage() {}

func (x *LlmResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LlmResponse.ProtoReflect.Descriptor instead.
func (*LlmResponse) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{0}
}

func (x *LlmResponse) GetLlmSkill() string {
	if x != nil {
		return x.LlmSkill
	}
	return ""
}

func (x *LlmResponse) GetLlmModel() string {
	if x != nil {
		return x.LlmModel
	}
	return ""
}

func (x *LlmResponse) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

func (x *LlmResponse) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *LlmResponse) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *LlmResponse) GetLlmResponse() string {
	if x != nil {
		return x.LlmResponse
	}
	return ""
}

func (x *LlmResponse) GetDialogueId() string {
	if x != nil {
		return x.DialogueId
	}
	return ""
}

func (x *LlmResponse) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *LlmResponse) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *LlmResponse) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *LlmResponse) GetCoverTitle() string {
	if x != nil {
		return x.CoverTitle
	}
	return ""
}

func (x *LlmResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *LlmResponse) GetTtsInfo() string {
	if x != nil {
		return x.TtsInfo
	}
	return ""
}

func (x *LlmResponse) GetMixedModalQuery() string {
	if x != nil {
		return x.MixedModalQuery
	}
	return ""
}

func (x *LlmResponse) GetMixedModalResponse() *structpb.Struct {
	if x != nil {
		return x.MixedModalResponse
	}
	return nil
}

func (x *LlmResponse) GetTtsNorm() string {
	if x != nil {
		return x.TtsNorm
	}
	return ""
}

type LuiNluRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	RequestId         string                 `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	AsrInfo           string                 `protobuf:"bytes,7,opt,name=asr_info,json=asrInfo,proto3" json:"asr_info,omitempty"`
	AsrPinyin         string                 `protobuf:"bytes,8,opt,name=asr_pinyin,json=asrPinyin,proto3" json:"asr_pinyin,omitempty"`
	Location          string                 `protobuf:"bytes,9,opt,name=location,proto3" json:"location,omitempty"`
	Grade             string                 `protobuf:"bytes,10,opt,name=grade,proto3" json:"grade,omitempty"`
	TalId             string                 `protobuf:"bytes,11,opt,name=tal_id,json=talId,proto3" json:"tal_id,omitempty"`
	SlotFillList      []*SlotFill            `protobuf:"bytes,12,rep,name=slot_fill_list,json=slotFillList,proto3" json:"slot_fill_list,omitempty"`
	GradeId           string                 `protobuf:"bytes,13,opt,name=grade_id,json=gradeId,proto3" json:"grade_id,omitempty"`
	BizType           string                 `protobuf:"bytes,14,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	Continuous        bool                   `protobuf:"varint,15,opt,name=continuous,proto3" json:"continuous,omitempty"`
	SentenceId        int32                  `protobuf:"varint,16,opt,name=sentence_id,json=sentenceId,proto3" json:"sentence_id,omitempty"`
	WakeUpType        string                 `protobuf:"bytes,17,opt,name=wake_up_type,json=wakeUpType,proto3" json:"wake_up_type,omitempty"`
	PagePrompt        string                 `protobuf:"bytes,18,opt,name=page_prompt,json=pagePrompt,proto3" json:"page_prompt,omitempty"`
	SceneMode         int32                  `protobuf:"varint,19,opt,name=scene_mode,json=sceneMode,proto3" json:"scene_mode,omitempty"`
	LlmAgent          *LlmAgent              `protobuf:"bytes,20,opt,name=llm_agent,json=llmAgent,proto3" json:"llm_agent,omitempty"`
	RnVersion         string                 `protobuf:"bytes,21,opt,name=rn_version,json=rnVersion,proto3" json:"rn_version,omitempty"`
	ScreenMode        int32                  `protobuf:"varint,22,opt,name=screen_mode,json=screenMode,proto3" json:"screen_mode,omitempty"`
	ImagePrompt       []*structpb.Struct     `protobuf:"bytes,23,rep,name=image_prompt,json=imagePrompt,proto3" json:"image_prompt,omitempty"`
	BizExtra          string                 `protobuf:"bytes,24,opt,name=biz_extra,json=bizExtra,proto3" json:"biz_extra,omitempty"`
	From              string                 `protobuf:"bytes,25,opt,name=from,proto3" json:"from,omitempty"`
	AsrStartTimestamp int64                  `protobuf:"varint,26,opt,name=asr_start_timestamp,json=asrStartTimestamp,proto3" json:"asr_start_timestamp,omitempty"`
	AsrEndTimestamp   int64                  `protobuf:"varint,27,opt,name=asr_end_timestamp,json=asrEndTimestamp,proto3" json:"asr_end_timestamp,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *LuiNluRequest) Reset() {
	*x = LuiNluRequest{}
	mi := &file_api_lui_v1_api_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiNluRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiNluRequest) ProtoMessage() {}

func (x *LuiNluRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiNluRequest.ProtoReflect.Descriptor instead.
func (*LuiNluRequest) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{1}
}

func (x *LuiNluRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *LuiNluRequest) GetAsrInfo() string {
	if x != nil {
		return x.AsrInfo
	}
	return ""
}

func (x *LuiNluRequest) GetAsrPinyin() string {
	if x != nil {
		return x.AsrPinyin
	}
	return ""
}

func (x *LuiNluRequest) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *LuiNluRequest) GetGrade() string {
	if x != nil {
		return x.Grade
	}
	return ""
}

func (x *LuiNluRequest) GetTalId() string {
	if x != nil {
		return x.TalId
	}
	return ""
}

func (x *LuiNluRequest) GetSlotFillList() []*SlotFill {
	if x != nil {
		return x.SlotFillList
	}
	return nil
}

func (x *LuiNluRequest) GetGradeId() string {
	if x != nil {
		return x.GradeId
	}
	return ""
}

func (x *LuiNluRequest) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

func (x *LuiNluRequest) GetContinuous() bool {
	if x != nil {
		return x.Continuous
	}
	return false
}

func (x *LuiNluRequest) GetSentenceId() int32 {
	if x != nil {
		return x.SentenceId
	}
	return 0
}

func (x *LuiNluRequest) GetWakeUpType() string {
	if x != nil {
		return x.WakeUpType
	}
	return ""
}

func (x *LuiNluRequest) GetPagePrompt() string {
	if x != nil {
		return x.PagePrompt
	}
	return ""
}

func (x *LuiNluRequest) GetSceneMode() int32 {
	if x != nil {
		return x.SceneMode
	}
	return 0
}

func (x *LuiNluRequest) GetLlmAgent() *LlmAgent {
	if x != nil {
		return x.LlmAgent
	}
	return nil
}

func (x *LuiNluRequest) GetRnVersion() string {
	if x != nil {
		return x.RnVersion
	}
	return ""
}

func (x *LuiNluRequest) GetScreenMode() int32 {
	if x != nil {
		return x.ScreenMode
	}
	return 0
}

func (x *LuiNluRequest) GetImagePrompt() []*structpb.Struct {
	if x != nil {
		return x.ImagePrompt
	}
	return nil
}

func (x *LuiNluRequest) GetBizExtra() string {
	if x != nil {
		return x.BizExtra
	}
	return ""
}

func (x *LuiNluRequest) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *LuiNluRequest) GetAsrStartTimestamp() int64 {
	if x != nil {
		return x.AsrStartTimestamp
	}
	return 0
}

func (x *LuiNluRequest) GetAsrEndTimestamp() int64 {
	if x != nil {
		return x.AsrEndTimestamp
	}
	return 0
}

type LlmAgent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Status        int32                  `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LlmAgent) Reset() {
	*x = LlmAgent{}
	mi := &file_api_lui_v1_api_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LlmAgent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LlmAgent) ProtoMessage() {}

func (x *LlmAgent) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LlmAgent.ProtoReflect.Descriptor instead.
func (*LlmAgent) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{2}
}

func (x *LlmAgent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LlmAgent) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type SlotFill struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Id            string                 `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SlotFill) Reset() {
	*x = SlotFill{}
	mi := &file_api_lui_v1_api_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SlotFill) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlotFill) ProtoMessage() {}

func (x *SlotFill) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlotFill.ProtoReflect.Descriptor instead.
func (*SlotFill) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{3}
}

func (x *SlotFill) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *SlotFill) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SlotFill) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type LuiSugRequest struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	UserId        string                    `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	RequestId     string                    `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	SessionId     string                    `protobuf:"bytes,3,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	Timestamp     int64                     `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Source        string                    `protobuf:"bytes,5,opt,name=source,proto3" json:"source,omitempty"`
	Version       string                    `protobuf:"bytes,6,opt,name=version,proto3" json:"version,omitempty"`
	AsrInput      *LuiSugRequest_AsrInput   `protobuf:"bytes,7,opt,name=asr_input,json=asrInput,proto3" json:"asr_input,omitempty"`
	UserSystem    *LuiSugRequest_UserSystem `protobuf:"bytes,8,opt,name=user_system,json=userSystem,proto3" json:"user_system,omitempty"`
	BizType       string                    `protobuf:"bytes,9,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	SceneMode     int32                     `protobuf:"varint,10,opt,name=scene_mode,json=sceneMode,proto3" json:"scene_mode,omitempty"`
	RnVersion     string                    `protobuf:"bytes,11,opt,name=rn_version,json=rnVersion,proto3" json:"rn_version,omitempty"`
	ScreenState   string                    `protobuf:"bytes,12,opt,name=screenState,proto3" json:"screenState,omitempty"`
	BizExtra      string                    `protobuf:"bytes,13,opt,name=biz_extra,json=bizExtra,proto3" json:"biz_extra,omitempty"`
	From          string                    `protobuf:"bytes,14,opt,name=from,proto3" json:"from,omitempty"`
	ExerciseOcr   string                    `protobuf:"bytes,15,opt,name=exercise_ocr,json=exerciseOcr,proto3" json:"exercise_ocr,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LuiSugRequest) Reset() {
	*x = LuiSugRequest{}
	mi := &file_api_lui_v1_api_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiSugRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiSugRequest) ProtoMessage() {}

func (x *LuiSugRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiSugRequest.ProtoReflect.Descriptor instead.
func (*LuiSugRequest) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{4}
}

func (x *LuiSugRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *LuiSugRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *LuiSugRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *LuiSugRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *LuiSugRequest) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *LuiSugRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *LuiSugRequest) GetAsrInput() *LuiSugRequest_AsrInput {
	if x != nil {
		return x.AsrInput
	}
	return nil
}

func (x *LuiSugRequest) GetUserSystem() *LuiSugRequest_UserSystem {
	if x != nil {
		return x.UserSystem
	}
	return nil
}

func (x *LuiSugRequest) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

func (x *LuiSugRequest) GetSceneMode() int32 {
	if x != nil {
		return x.SceneMode
	}
	return 0
}

func (x *LuiSugRequest) GetRnVersion() string {
	if x != nil {
		return x.RnVersion
	}
	return ""
}

func (x *LuiSugRequest) GetScreenState() string {
	if x != nil {
		return x.ScreenState
	}
	return ""
}

func (x *LuiSugRequest) GetBizExtra() string {
	if x != nil {
		return x.BizExtra
	}
	return ""
}

func (x *LuiSugRequest) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *LuiSugRequest) GetExerciseOcr() string {
	if x != nil {
		return x.ExerciseOcr
	}
	return ""
}

type LuiNluEnsureRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LuiNluEnsureRequest) Reset() {
	*x = LuiNluEnsureRequest{}
	mi := &file_api_lui_v1_api_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiNluEnsureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiNluEnsureRequest) ProtoMessage() {}

func (x *LuiNluEnsureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiNluEnsureRequest.ProtoReflect.Descriptor instead.
func (*LuiNluEnsureRequest) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{5}
}

type LuiUserInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LuiUserInfoRequest) Reset() {
	*x = LuiUserInfoRequest{}
	mi := &file_api_lui_v1_api_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiUserInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiUserInfoRequest) ProtoMessage() {}

func (x *LuiUserInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiUserInfoRequest.ProtoReflect.Descriptor instead.
func (*LuiUserInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{6}
}

func (x *LuiUserInfoRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type LuiUserInfoReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TalId         string                 `protobuf:"bytes,2,opt,name=tal_id,json=talId,proto3" json:"tal_id,omitempty"`
	Grade         int32                  `protobuf:"varint,3,opt,name=grade,proto3" json:"grade,omitempty"`
	GradeName     string                 `protobuf:"bytes,4,opt,name=grade_name,json=gradeName,proto3" json:"grade_name,omitempty"`
	Nickname      string                 `protobuf:"bytes,5,opt,name=nickname,proto3" json:"nickname,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LuiUserInfoReply) Reset() {
	*x = LuiUserInfoReply{}
	mi := &file_api_lui_v1_api_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiUserInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiUserInfoReply) ProtoMessage() {}

func (x *LuiUserInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiUserInfoReply.ProtoReflect.Descriptor instead.
func (*LuiUserInfoReply) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{7}
}

func (x *LuiUserInfoReply) GetTalId() string {
	if x != nil {
		return x.TalId
	}
	return ""
}

func (x *LuiUserInfoReply) GetGrade() int32 {
	if x != nil {
		return x.Grade
	}
	return 0
}

func (x *LuiUserInfoReply) GetGradeName() string {
	if x != nil {
		return x.GradeName
	}
	return ""
}

func (x *LuiUserInfoReply) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

type Any struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	B             []byte                 `protobuf:"bytes,1,opt,name=b,proto3" json:"b,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Any) Reset() {
	*x = Any{}
	mi := &file_api_lui_v1_api_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Any) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Any) ProtoMessage() {}

func (x *Any) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Any.ProtoReflect.Descriptor instead.
func (*Any) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{8}
}

func (x *Any) GetB() []byte {
	if x != nil {
		return x.B
	}
	return nil
}

type LuiUserChoiceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TalId         string                 `protobuf:"bytes,1,opt,name=tal_id,json=talId,proto3" json:"tal_id,omitempty"`
	CourseSystem  int32                  `protobuf:"varint,2,opt,name=course_system,json=courseSystem,proto3" json:"course_system,omitempty"`
	Subject       int32                  `protobuf:"varint,3,opt,name=subject,proto3" json:"subject,omitempty"`
	Grade         int32                  `protobuf:"varint,4,opt,name=grade,proto3" json:"grade,omitempty"`
	DeviceId      string                 `protobuf:"bytes,5,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LuiUserChoiceRequest) Reset() {
	*x = LuiUserChoiceRequest{}
	mi := &file_api_lui_v1_api_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiUserChoiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiUserChoiceRequest) ProtoMessage() {}

func (x *LuiUserChoiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiUserChoiceRequest.ProtoReflect.Descriptor instead.
func (*LuiUserChoiceRequest) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{9}
}

func (x *LuiUserChoiceRequest) GetTalId() string {
	if x != nil {
		return x.TalId
	}
	return ""
}

func (x *LuiUserChoiceRequest) GetCourseSystem() int32 {
	if x != nil {
		return x.CourseSystem
	}
	return 0
}

func (x *LuiUserChoiceRequest) GetSubject() int32 {
	if x != nil {
		return x.Subject
	}
	return 0
}

func (x *LuiUserChoiceRequest) GetGrade() int32 {
	if x != nil {
		return x.Grade
	}
	return 0
}

func (x *LuiUserChoiceRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type LuiUserChoiceReply struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	List          []*LuiUserChoiceReplyItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LuiUserChoiceReply) Reset() {
	*x = LuiUserChoiceReply{}
	mi := &file_api_lui_v1_api_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiUserChoiceReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiUserChoiceReply) ProtoMessage() {}

func (x *LuiUserChoiceReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiUserChoiceReply.ProtoReflect.Descriptor instead.
func (*LuiUserChoiceReply) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{10}
}

func (x *LuiUserChoiceReply) GetList() []*LuiUserChoiceReplyItem {
	if x != nil {
		return x.List
	}
	return nil
}

type LuiUserChoiceReplyItem struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	CourseSystem      int32                  `protobuf:"varint,1,opt,name=course_system,json=courseSystem,proto3" json:"course_system,omitempty"`
	CourseSystemAlias string                 `protobuf:"bytes,2,opt,name=course_system_alias,json=courseSystemAlias,proto3" json:"course_system_alias,omitempty"`
	Version           int32                  `protobuf:"varint,3,opt,name=version,proto3" json:"version,omitempty"`
	VersionName       string                 `protobuf:"bytes,4,opt,name=version_name,json=versionName,proto3" json:"version_name,omitempty"`
	Subject           int32                  `protobuf:"varint,5,opt,name=subject,proto3" json:"subject,omitempty"`
	SubjectName       string                 `protobuf:"bytes,6,opt,name=subject_name,json=subjectName,proto3" json:"subject_name,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *LuiUserChoiceReplyItem) Reset() {
	*x = LuiUserChoiceReplyItem{}
	mi := &file_api_lui_v1_api_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiUserChoiceReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiUserChoiceReplyItem) ProtoMessage() {}

func (x *LuiUserChoiceReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiUserChoiceReplyItem.ProtoReflect.Descriptor instead.
func (*LuiUserChoiceReplyItem) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{11}
}

func (x *LuiUserChoiceReplyItem) GetCourseSystem() int32 {
	if x != nil {
		return x.CourseSystem
	}
	return 0
}

func (x *LuiUserChoiceReplyItem) GetCourseSystemAlias() string {
	if x != nil {
		return x.CourseSystemAlias
	}
	return ""
}

func (x *LuiUserChoiceReplyItem) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *LuiUserChoiceReplyItem) GetVersionName() string {
	if x != nil {
		return x.VersionName
	}
	return ""
}

func (x *LuiUserChoiceReplyItem) GetSubject() int32 {
	if x != nil {
		return x.Subject
	}
	return 0
}

func (x *LuiUserChoiceReplyItem) GetSubjectName() string {
	if x != nil {
		return x.SubjectName
	}
	return ""
}

type LuiToolsUserChoiceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TalId         string                 `protobuf:"bytes,1,opt,name=tal_id,json=talId,proto3" json:"tal_id,omitempty"`
	Subject       int32                  `protobuf:"varint,2,opt,name=subject,proto3" json:"subject,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LuiToolsUserChoiceRequest) Reset() {
	*x = LuiToolsUserChoiceRequest{}
	mi := &file_api_lui_v1_api_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiToolsUserChoiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiToolsUserChoiceRequest) ProtoMessage() {}

func (x *LuiToolsUserChoiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiToolsUserChoiceRequest.ProtoReflect.Descriptor instead.
func (*LuiToolsUserChoiceRequest) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{12}
}

func (x *LuiToolsUserChoiceRequest) GetTalId() string {
	if x != nil {
		return x.TalId
	}
	return ""
}

func (x *LuiToolsUserChoiceRequest) GetSubject() int32 {
	if x != nil {
		return x.Subject
	}
	return 0
}

type LuiToolsUserChoiceReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VersionName   string                 `protobuf:"bytes,1,opt,name=version_name,json=versionName,proto3" json:"version_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LuiToolsUserChoiceReply) Reset() {
	*x = LuiToolsUserChoiceReply{}
	mi := &file_api_lui_v1_api_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiToolsUserChoiceReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiToolsUserChoiceReply) ProtoMessage() {}

func (x *LuiToolsUserChoiceReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiToolsUserChoiceReply.ProtoReflect.Descriptor instead.
func (*LuiToolsUserChoiceReply) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{13}
}

func (x *LuiToolsUserChoiceReply) GetVersionName() string {
	if x != nil {
		return x.VersionName
	}
	return ""
}

type QueryTraceFeedbackRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SessionId     string                 `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	FeedbackType  int32                  `protobuf:"varint,2,opt,name=feedback_type,json=feedbackType,proto3" json:"feedback_type,omitempty"`
	Feedback      string                 `protobuf:"bytes,3,opt,name=feedback,proto3" json:"feedback,omitempty"`
	FeedbackTypes []int32                `protobuf:"varint,4,rep,packed,name=feedback_types,json=feedbackTypes,proto3" json:"feedback_types,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryTraceFeedbackRequest) Reset() {
	*x = QueryTraceFeedbackRequest{}
	mi := &file_api_lui_v1_api_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryTraceFeedbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryTraceFeedbackRequest) ProtoMessage() {}

func (x *QueryTraceFeedbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryTraceFeedbackRequest.ProtoReflect.Descriptor instead.
func (*QueryTraceFeedbackRequest) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{14}
}

func (x *QueryTraceFeedbackRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *QueryTraceFeedbackRequest) GetFeedbackType() int32 {
	if x != nil {
		return x.FeedbackType
	}
	return 0
}

func (x *QueryTraceFeedbackRequest) GetFeedback() string {
	if x != nil {
		return x.Feedback
	}
	return ""
}

func (x *QueryTraceFeedbackRequest) GetFeedbackTypes() []int32 {
	if x != nil {
		return x.FeedbackTypes
	}
	return nil
}

type QueryTraceFeedbackReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryTraceFeedbackReply) Reset() {
	*x = QueryTraceFeedbackReply{}
	mi := &file_api_lui_v1_api_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryTraceFeedbackReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryTraceFeedbackReply) ProtoMessage() {}

func (x *QueryTraceFeedbackReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryTraceFeedbackReply.ProtoReflect.Descriptor instead.
func (*QueryTraceFeedbackReply) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{15}
}

type LuiSimulateQueryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AsrInfo       string                 `protobuf:"bytes,1,opt,name=asr_info,json=asrInfo,proto3" json:"asr_info,omitempty"`
	AsrPinyin     string                 `protobuf:"bytes,2,opt,name=asr_pinyin,json=asrPinyin,proto3" json:"asr_pinyin,omitempty"`
	Location      string                 `protobuf:"bytes,3,opt,name=location,proto3" json:"location,omitempty"`
	Grade         string                 `protobuf:"bytes,4,opt,name=grade,proto3" json:"grade,omitempty"`
	GradeId       string                 `protobuf:"bytes,5,opt,name=grade_id,json=gradeId,proto3" json:"grade_id,omitempty"`
	TalId         string                 `protobuf:"bytes,6,opt,name=tal_id,json=talId,proto3" json:"tal_id,omitempty"`
	BizType       string                 `protobuf:"bytes,7,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	SlotFillList  []*SlotFill            `protobuf:"bytes,8,rep,name=slot_fill_list,json=slotFillList,proto3" json:"slot_fill_list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LuiSimulateQueryRequest) Reset() {
	*x = LuiSimulateQueryRequest{}
	mi := &file_api_lui_v1_api_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiSimulateQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiSimulateQueryRequest) ProtoMessage() {}

func (x *LuiSimulateQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiSimulateQueryRequest.ProtoReflect.Descriptor instead.
func (*LuiSimulateQueryRequest) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{16}
}

func (x *LuiSimulateQueryRequest) GetAsrInfo() string {
	if x != nil {
		return x.AsrInfo
	}
	return ""
}

func (x *LuiSimulateQueryRequest) GetAsrPinyin() string {
	if x != nil {
		return x.AsrPinyin
	}
	return ""
}

func (x *LuiSimulateQueryRequest) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *LuiSimulateQueryRequest) GetGrade() string {
	if x != nil {
		return x.Grade
	}
	return ""
}

func (x *LuiSimulateQueryRequest) GetGradeId() string {
	if x != nil {
		return x.GradeId
	}
	return ""
}

func (x *LuiSimulateQueryRequest) GetTalId() string {
	if x != nil {
		return x.TalId
	}
	return ""
}

func (x *LuiSimulateQueryRequest) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

func (x *LuiSimulateQueryRequest) GetSlotFillList() []*SlotFill {
	if x != nil {
		return x.SlotFillList
	}
	return nil
}

type LuiRejectRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestId     string                 `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	DeviceId      string                 `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Version       string                 `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	AsrInfo       string                 `protobuf:"bytes,4,opt,name=asr_info,json=asrInfo,proto3" json:"asr_info,omitempty"`
	WakeupType    string                 `protobuf:"bytes,5,opt,name=wakeup_type,json=wakeupType,proto3" json:"wakeup_type,omitempty"`
	RejectResult  int32                  `protobuf:"varint,6,opt,name=reject_result,json=rejectResult,proto3" json:"reject_result,omitempty"`
	Continuous    bool                   `protobuf:"varint,7,opt,name=continuous,proto3" json:"continuous,omitempty"`
	TalId         string                 `protobuf:"bytes,8,opt,name=tal_id,json=talId,proto3" json:"tal_id,omitempty"`
	BizType       string                 `protobuf:"bytes,9,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LuiRejectRequest) Reset() {
	*x = LuiRejectRequest{}
	mi := &file_api_lui_v1_api_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiRejectRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiRejectRequest) ProtoMessage() {}

func (x *LuiRejectRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiRejectRequest.ProtoReflect.Descriptor instead.
func (*LuiRejectRequest) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{17}
}

func (x *LuiRejectRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *LuiRejectRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *LuiRejectRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *LuiRejectRequest) GetAsrInfo() string {
	if x != nil {
		return x.AsrInfo
	}
	return ""
}

func (x *LuiRejectRequest) GetWakeupType() string {
	if x != nil {
		return x.WakeupType
	}
	return ""
}

func (x *LuiRejectRequest) GetRejectResult() int32 {
	if x != nil {
		return x.RejectResult
	}
	return 0
}

func (x *LuiRejectRequest) GetContinuous() bool {
	if x != nil {
		return x.Continuous
	}
	return false
}

func (x *LuiRejectRequest) GetTalId() string {
	if x != nil {
		return x.TalId
	}
	return ""
}

func (x *LuiRejectRequest) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

type MemoryContextRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TalId         string                 `protobuf:"bytes,1,opt,name=tal_id,json=talId,proto3" json:"tal_id,omitempty"`
	BizType       string                 `protobuf:"bytes,2,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MemoryContextRequest) Reset() {
	*x = MemoryContextRequest{}
	mi := &file_api_lui_v1_api_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemoryContextRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemoryContextRequest) ProtoMessage() {}

func (x *MemoryContextRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemoryContextRequest.ProtoReflect.Descriptor instead.
func (*MemoryContextRequest) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{18}
}

func (x *MemoryContextRequest) GetTalId() string {
	if x != nil {
		return x.TalId
	}
	return ""
}

func (x *MemoryContextRequest) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

type MemoryContextReply struct {
	state         protoimpl.MessageState                  `protogen:"open.v1"`
	List          []*MemoryContextReply_MemoryContextItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MemoryContextReply) Reset() {
	*x = MemoryContextReply{}
	mi := &file_api_lui_v1_api_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemoryContextReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemoryContextReply) ProtoMessage() {}

func (x *MemoryContextReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemoryContextReply.ProtoReflect.Descriptor instead.
func (*MemoryContextReply) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{19}
}

func (x *MemoryContextReply) GetList() []*MemoryContextReply_MemoryContextItem {
	if x != nil {
		return x.List
	}
	return nil
}

type LLMMemoryContextRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	BizType          string                 `protobuf:"bytes,1,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	CurrentSessionId string                 `protobuf:"bytes,2,opt,name=current_session_id,json=currentSessionId,proto3" json:"current_session_id,omitempty"`
	TalId            string                 `protobuf:"bytes,3,opt,name=tal_id,json=talId,proto3" json:"tal_id,omitempty"`
	NeedLlmSkill     []string               `protobuf:"bytes,4,rep,name=need_llm_skill,json=needLlmSkill,proto3" json:"need_llm_skill,omitempty"`
	Version          string                 `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *LLMMemoryContextRequest) Reset() {
	*x = LLMMemoryContextRequest{}
	mi := &file_api_lui_v1_api_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LLMMemoryContextRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LLMMemoryContextRequest) ProtoMessage() {}

func (x *LLMMemoryContextRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LLMMemoryContextRequest.ProtoReflect.Descriptor instead.
func (*LLMMemoryContextRequest) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{20}
}

func (x *LLMMemoryContextRequest) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

func (x *LLMMemoryContextRequest) GetCurrentSessionId() string {
	if x != nil {
		return x.CurrentSessionId
	}
	return ""
}

func (x *LLMMemoryContextRequest) GetTalId() string {
	if x != nil {
		return x.TalId
	}
	return ""
}

func (x *LLMMemoryContextRequest) GetNeedLlmSkill() []string {
	if x != nil {
		return x.NeedLlmSkill
	}
	return nil
}

func (x *LLMMemoryContextRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type LLMMemoryContextReply struct {
	state         protoimpl.MessageState               `protogen:"open.v1"`
	List          []*LLMMemoryContextReply_ContextItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LLMMemoryContextReply) Reset() {
	*x = LLMMemoryContextReply{}
	mi := &file_api_lui_v1_api_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LLMMemoryContextReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LLMMemoryContextReply) ProtoMessage() {}

func (x *LLMMemoryContextReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LLMMemoryContextReply.ProtoReflect.Descriptor instead.
func (*LLMMemoryContextReply) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{21}
}

func (x *LLMMemoryContextReply) GetList() []*LLMMemoryContextReply_ContextItem {
	if x != nil {
		return x.List
	}
	return nil
}

type FullViewContextRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TalId         string                 `protobuf:"bytes,1,opt,name=tal_id,json=talId,proto3" json:"tal_id,omitempty"`
	BizType       string                 `protobuf:"bytes,2,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FullViewContextRequest) Reset() {
	*x = FullViewContextRequest{}
	mi := &file_api_lui_v1_api_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FullViewContextRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FullViewContextRequest) ProtoMessage() {}

func (x *FullViewContextRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FullViewContextRequest.ProtoReflect.Descriptor instead.
func (*FullViewContextRequest) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{22}
}

func (x *FullViewContextRequest) GetTalId() string {
	if x != nil {
		return x.TalId
	}
	return ""
}

func (x *FullViewContextRequest) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

type FullViewContextReply struct {
	state         protoimpl.MessageState                      `protogen:"open.v1"`
	List          []*FullViewContextReply_FullViewContextItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FullViewContextReply) Reset() {
	*x = FullViewContextReply{}
	mi := &file_api_lui_v1_api_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FullViewContextReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FullViewContextReply) ProtoMessage() {}

func (x *FullViewContextReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FullViewContextReply.ProtoReflect.Descriptor instead.
func (*FullViewContextReply) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{23}
}

func (x *FullViewContextReply) GetList() []*FullViewContextReply_FullViewContextItem {
	if x != nil {
		return x.List
	}
	return nil
}

type LuiUserLlmSugRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LuiUserLlmSugRequest) Reset() {
	*x = LuiUserLlmSugRequest{}
	mi := &file_api_lui_v1_api_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiUserLlmSugRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiUserLlmSugRequest) ProtoMessage() {}

func (x *LuiUserLlmSugRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiUserLlmSugRequest.ProtoReflect.Descriptor instead.
func (*LuiUserLlmSugRequest) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{24}
}

func (x *LuiUserLlmSugRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type LuiUserLlmSugReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Sug           string                 `protobuf:"bytes,2,opt,name=sug,proto3" json:"sug,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LuiUserLlmSugReply) Reset() {
	*x = LuiUserLlmSugReply{}
	mi := &file_api_lui_v1_api_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiUserLlmSugReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiUserLlmSugReply) ProtoMessage() {}

func (x *LuiUserLlmSugReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiUserLlmSugReply.ProtoReflect.Descriptor instead.
func (*LuiUserLlmSugReply) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{25}
}

func (x *LuiUserLlmSugReply) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *LuiUserLlmSugReply) GetSug() string {
	if x != nil {
		return x.Sug
	}
	return ""
}

type LuiUserLlmTailsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ScreenMode    int32                  `protobuf:"varint,2,opt,name=screen_mode,json=screenMode,proto3" json:"screen_mode,omitempty"`
	BizType       string                 `protobuf:"bytes,3,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LuiUserLlmTailsRequest) Reset() {
	*x = LuiUserLlmTailsRequest{}
	mi := &file_api_lui_v1_api_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiUserLlmTailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiUserLlmTailsRequest) ProtoMessage() {}

func (x *LuiUserLlmTailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiUserLlmTailsRequest.ProtoReflect.Descriptor instead.
func (*LuiUserLlmTailsRequest) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{26}
}

func (x *LuiUserLlmTailsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *LuiUserLlmTailsRequest) GetScreenMode() int32 {
	if x != nil {
		return x.ScreenMode
	}
	return 0
}

func (x *LuiUserLlmTailsRequest) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

type AIEyeFingerImageJudgeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BizType       int32                  `protobuf:"varint,1,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	ImageUrl      string                 `protobuf:"bytes,2,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AIEyeFingerImageJudgeRequest) Reset() {
	*x = AIEyeFingerImageJudgeRequest{}
	mi := &file_api_lui_v1_api_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AIEyeFingerImageJudgeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AIEyeFingerImageJudgeRequest) ProtoMessage() {}

func (x *AIEyeFingerImageJudgeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AIEyeFingerImageJudgeRequest.ProtoReflect.Descriptor instead.
func (*AIEyeFingerImageJudgeRequest) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{27}
}

func (x *AIEyeFingerImageJudgeRequest) GetBizType() int32 {
	if x != nil {
		return x.BizType
	}
	return 0
}

func (x *AIEyeFingerImageJudgeRequest) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

// 从paas-service的/api/ai/v1/nlu接口迁移过来的，参考paas-service的NluPadReq
type LuiNluConfirmRequest struct {
	state         protoimpl.MessageState            `protogen:"open.v1"`
	UserId        string                            `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId,omitempty"`
	RequestId     string                            `protobuf:"bytes,2,opt,name=requestId,proto3" json:"requestId,omitempty"`
	SessionId     string                            `protobuf:"bytes,3,opt,name=sessionId,proto3" json:"sessionId,omitempty"`
	Version       string                            `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	AsrInput      *LuiNluConfirmRequest_AsrInput    `protobuf:"bytes,5,opt,name=asr_input,json=asrInput,proto3" json:"asr_input,omitempty"`
	UserSystem    *LuiNluConfirmRequest_UserSystem  `protobuf:"bytes,6,opt,name=user_system,json=userSystem,proto3" json:"user_system,omitempty"`
	OcrInput      *LuiNluConfirmRequest_OcrInput    `protobuf:"bytes,7,opt,name=ocr_input,json=ocrInput,proto3" json:"ocr_input,omitempty"`
	HandleInput   *LuiNluConfirmRequest_HandleInput `protobuf:"bytes,8,opt,name=handle_input,json=handleInput,proto3" json:"handle_input,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LuiNluConfirmRequest) Reset() {
	*x = LuiNluConfirmRequest{}
	mi := &file_api_lui_v1_api_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiNluConfirmRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiNluConfirmRequest) ProtoMessage() {}

func (x *LuiNluConfirmRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiNluConfirmRequest.ProtoReflect.Descriptor instead.
func (*LuiNluConfirmRequest) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{28}
}

func (x *LuiNluConfirmRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *LuiNluConfirmRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *LuiNluConfirmRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *LuiNluConfirmRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *LuiNluConfirmRequest) GetAsrInput() *LuiNluConfirmRequest_AsrInput {
	if x != nil {
		return x.AsrInput
	}
	return nil
}

func (x *LuiNluConfirmRequest) GetUserSystem() *LuiNluConfirmRequest_UserSystem {
	if x != nil {
		return x.UserSystem
	}
	return nil
}

func (x *LuiNluConfirmRequest) GetOcrInput() *LuiNluConfirmRequest_OcrInput {
	if x != nil {
		return x.OcrInput
	}
	return nil
}

func (x *LuiNluConfirmRequest) GetHandleInput() *LuiNluConfirmRequest_HandleInput {
	if x != nil {
		return x.HandleInput
	}
	return nil
}

type AddLLMMemoryContextRequest struct {
	state         protoimpl.MessageState                    `protogen:"open.v1"`
	TalId         string                                    `protobuf:"bytes,1,opt,name=tal_id,json=talId,proto3" json:"tal_id,omitempty"`
	SessionId     string                                    `protobuf:"bytes,2,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	Messages      []*AddLLMMemoryContextRequest_ContextItem `protobuf:"bytes,3,rep,name=messages,proto3" json:"messages,omitempty"`
	BizType       string                                    `protobuf:"bytes,4,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddLLMMemoryContextRequest) Reset() {
	*x = AddLLMMemoryContextRequest{}
	mi := &file_api_lui_v1_api_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddLLMMemoryContextRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLLMMemoryContextRequest) ProtoMessage() {}

func (x *AddLLMMemoryContextRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLLMMemoryContextRequest.ProtoReflect.Descriptor instead.
func (*AddLLMMemoryContextRequest) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{29}
}

func (x *AddLLMMemoryContextRequest) GetTalId() string {
	if x != nil {
		return x.TalId
	}
	return ""
}

func (x *AddLLMMemoryContextRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *AddLLMMemoryContextRequest) GetMessages() []*AddLLMMemoryContextRequest_ContextItem {
	if x != nil {
		return x.Messages
	}
	return nil
}

func (x *AddLLMMemoryContextRequest) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

type AddLLMMemoryContextReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddLLMMemoryContextReply) Reset() {
	*x = AddLLMMemoryContextReply{}
	mi := &file_api_lui_v1_api_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddLLMMemoryContextReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLLMMemoryContextReply) ProtoMessage() {}

func (x *AddLLMMemoryContextReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLLMMemoryContextReply.ProtoReflect.Descriptor instead.
func (*AddLLMMemoryContextReply) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{30}
}

type ExitLuiSceneRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BizType       string                 `protobuf:"bytes,1,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	TalId         string                 `protobuf:"bytes,2,opt,name=tal_id,json=talId,proto3" json:"tal_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExitLuiSceneRequest) Reset() {
	*x = ExitLuiSceneRequest{}
	mi := &file_api_lui_v1_api_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExitLuiSceneRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExitLuiSceneRequest) ProtoMessage() {}

func (x *ExitLuiSceneRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExitLuiSceneRequest.ProtoReflect.Descriptor instead.
func (*ExitLuiSceneRequest) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{31}
}

func (x *ExitLuiSceneRequest) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

func (x *ExitLuiSceneRequest) GetTalId() string {
	if x != nil {
		return x.TalId
	}
	return ""
}

type ExitLuiSceneReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExitLuiSceneReply) Reset() {
	*x = ExitLuiSceneReply{}
	mi := &file_api_lui_v1_api_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExitLuiSceneReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExitLuiSceneReply) ProtoMessage() {}

func (x *ExitLuiSceneReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExitLuiSceneReply.ProtoReflect.Descriptor instead.
func (*ExitLuiSceneReply) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{32}
}

type LuiSugRequest_AsrInput struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AsrLen        int32                  `protobuf:"varint,1,opt,name=asr_len,json=asrLen,proto3" json:"asr_len,omitempty"`
	AsrInfo       string                 `protobuf:"bytes,2,opt,name=asr_info,json=asrInfo,proto3" json:"asr_info,omitempty"`
	AsrPinyin     string                 `protobuf:"bytes,3,opt,name=asr_pinyin,json=asrPinyin,proto3" json:"asr_pinyin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LuiSugRequest_AsrInput) Reset() {
	*x = LuiSugRequest_AsrInput{}
	mi := &file_api_lui_v1_api_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiSugRequest_AsrInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiSugRequest_AsrInput) ProtoMessage() {}

func (x *LuiSugRequest_AsrInput) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiSugRequest_AsrInput.ProtoReflect.Descriptor instead.
func (*LuiSugRequest_AsrInput) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{4, 0}
}

func (x *LuiSugRequest_AsrInput) GetAsrLen() int32 {
	if x != nil {
		return x.AsrLen
	}
	return 0
}

func (x *LuiSugRequest_AsrInput) GetAsrInfo() string {
	if x != nil {
		return x.AsrInfo
	}
	return ""
}

func (x *LuiSugRequest_AsrInput) GetAsrPinyin() string {
	if x != nil {
		return x.AsrPinyin
	}
	return ""
}

type LuiSugRequest_UserSystem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Location      string                 `protobuf:"bytes,1,opt,name=location,proto3" json:"location,omitempty"`
	Grade         int32                  `protobuf:"varint,2,opt,name=grade,proto3" json:"grade,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LuiSugRequest_UserSystem) Reset() {
	*x = LuiSugRequest_UserSystem{}
	mi := &file_api_lui_v1_api_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiSugRequest_UserSystem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiSugRequest_UserSystem) ProtoMessage() {}

func (x *LuiSugRequest_UserSystem) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiSugRequest_UserSystem.ProtoReflect.Descriptor instead.
func (*LuiSugRequest_UserSystem) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{4, 1}
}

func (x *LuiSugRequest_UserSystem) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *LuiSugRequest_UserSystem) GetGrade() int32 {
	if x != nil {
		return x.Grade
	}
	return 0
}

type MemoryContextReply_MemoryContextItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AsrInfo       string                 `protobuf:"bytes,1,opt,name=asr_info,json=asrInfo,proto3" json:"asr_info,omitempty"`
	Intent        string                 `protobuf:"bytes,2,opt,name=intent,proto3" json:"intent,omitempty"`
	Response      string                 `protobuf:"bytes,3,opt,name=response,proto3" json:"response,omitempty"`
	TtsInfo       string                 `protobuf:"bytes,4,opt,name=tts_info,json=ttsInfo,proto3" json:"tts_info,omitempty"`
	RewriteQuery  string                 `protobuf:"bytes,5,opt,name=rewrite_query,json=rewriteQuery,proto3" json:"rewrite_query,omitempty"`
	TimeStamp     int64                  `protobuf:"varint,6,opt,name=time_stamp,json=timeStamp,proto3" json:"time_stamp,omitempty"`
	TtsNorm       string                 `protobuf:"bytes,7,opt,name=tts_norm,json=ttsNorm,proto3" json:"tts_norm,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MemoryContextReply_MemoryContextItem) Reset() {
	*x = MemoryContextReply_MemoryContextItem{}
	mi := &file_api_lui_v1_api_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemoryContextReply_MemoryContextItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemoryContextReply_MemoryContextItem) ProtoMessage() {}

func (x *MemoryContextReply_MemoryContextItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemoryContextReply_MemoryContextItem.ProtoReflect.Descriptor instead.
func (*MemoryContextReply_MemoryContextItem) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{19, 0}
}

func (x *MemoryContextReply_MemoryContextItem) GetAsrInfo() string {
	if x != nil {
		return x.AsrInfo
	}
	return ""
}

func (x *MemoryContextReply_MemoryContextItem) GetIntent() string {
	if x != nil {
		return x.Intent
	}
	return ""
}

func (x *MemoryContextReply_MemoryContextItem) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

func (x *MemoryContextReply_MemoryContextItem) GetTtsInfo() string {
	if x != nil {
		return x.TtsInfo
	}
	return ""
}

func (x *MemoryContextReply_MemoryContextItem) GetRewriteQuery() string {
	if x != nil {
		return x.RewriteQuery
	}
	return ""
}

func (x *MemoryContextReply_MemoryContextItem) GetTimeStamp() int64 {
	if x != nil {
		return x.TimeStamp
	}
	return 0
}

func (x *MemoryContextReply_MemoryContextItem) GetTtsNorm() string {
	if x != nil {
		return x.TtsNorm
	}
	return ""
}

type LLMMemoryContextReply_ContextItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Role          string                 `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	DialogueId    string                 `protobuf:"bytes,3,opt,name=dialogue_id,json=dialogueId,proto3" json:"dialogue_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LLMMemoryContextReply_ContextItem) Reset() {
	*x = LLMMemoryContextReply_ContextItem{}
	mi := &file_api_lui_v1_api_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LLMMemoryContextReply_ContextItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LLMMemoryContextReply_ContextItem) ProtoMessage() {}

func (x *LLMMemoryContextReply_ContextItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LLMMemoryContextReply_ContextItem.ProtoReflect.Descriptor instead.
func (*LLMMemoryContextReply_ContextItem) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{21, 0}
}

func (x *LLMMemoryContextReply_ContextItem) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *LLMMemoryContextReply_ContextItem) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *LLMMemoryContextReply_ContextItem) GetDialogueId() string {
	if x != nil {
		return x.DialogueId
	}
	return ""
}

type FullViewContextReply_Media struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          int32                  `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Cover         string                 `protobuf:"bytes,2,opt,name=cover,proto3" json:"cover,omitempty"`
	Url           string                 `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	CoverTitle    string                 `protobuf:"bytes,4,opt,name=cover_title,json=coverTitle,proto3" json:"cover_title,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FullViewContextReply_Media) Reset() {
	*x = FullViewContextReply_Media{}
	mi := &file_api_lui_v1_api_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FullViewContextReply_Media) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FullViewContextReply_Media) ProtoMessage() {}

func (x *FullViewContextReply_Media) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FullViewContextReply_Media.ProtoReflect.Descriptor instead.
func (*FullViewContextReply_Media) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{23, 0}
}

func (x *FullViewContextReply_Media) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *FullViewContextReply_Media) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *FullViewContextReply_Media) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *FullViewContextReply_Media) GetCoverTitle() string {
	if x != nil {
		return x.CoverTitle
	}
	return ""
}

type FullViewContextReply_XiaosiCommands struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	Title         string                             `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Query         string                             `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
	Apps          []*FullViewContextReply_CommandApp `protobuf:"bytes,3,rep,name=apps,proto3" json:"apps,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FullViewContextReply_XiaosiCommands) Reset() {
	*x = FullViewContextReply_XiaosiCommands{}
	mi := &file_api_lui_v1_api_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FullViewContextReply_XiaosiCommands) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FullViewContextReply_XiaosiCommands) ProtoMessage() {}

func (x *FullViewContextReply_XiaosiCommands) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FullViewContextReply_XiaosiCommands.ProtoReflect.Descriptor instead.
func (*FullViewContextReply_XiaosiCommands) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{23, 1}
}

func (x *FullViewContextReply_XiaosiCommands) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *FullViewContextReply_XiaosiCommands) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *FullViewContextReply_XiaosiCommands) GetApps() []*FullViewContextReply_CommandApp {
	if x != nil {
		return x.Apps
	}
	return nil
}

type FullViewContextReply_CommandApp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Icon          string                 `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FullViewContextReply_CommandApp) Reset() {
	*x = FullViewContextReply_CommandApp{}
	mi := &file_api_lui_v1_api_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FullViewContextReply_CommandApp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FullViewContextReply_CommandApp) ProtoMessage() {}

func (x *FullViewContextReply_CommandApp) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FullViewContextReply_CommandApp.ProtoReflect.Descriptor instead.
func (*FullViewContextReply_CommandApp) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{23, 2}
}

func (x *FullViewContextReply_CommandApp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FullViewContextReply_CommandApp) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

type FullViewContextReply_FullViewContextItem struct {
	state             protoimpl.MessageState                 `protogen:"open.v1"`
	SessionId         string                                 `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	AsrInfo           string                                 `protobuf:"bytes,2,opt,name=asr_info,json=asrInfo,proto3" json:"asr_info,omitempty"`
	Response          string                                 `protobuf:"bytes,3,opt,name=response,proto3" json:"response,omitempty"`
	TtsInfo           string                                 `protobuf:"bytes,4,opt,name=tts_info,json=ttsInfo,proto3" json:"tts_info,omitempty"`
	Source            string                                 `protobuf:"bytes,5,opt,name=source,proto3" json:"source,omitempty"`
	ImageUrl          string                                 `protobuf:"bytes,6,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	VideoUrl          string                                 `protobuf:"bytes,7,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	IsLlm             bool                                   `protobuf:"varint,8,opt,name=is_llm,json=isLlm,proto3" json:"is_llm,omitempty"`
	CardType          int32                                  `protobuf:"varint,9,opt,name=card_type,json=cardType,proto3" json:"card_type,omitempty"`
	Media             *FullViewContextReply_Media            `protobuf:"bytes,10,opt,name=media,proto3" json:"media,omitempty"`
	XiaosiCommandList []*FullViewContextReply_XiaosiCommands `protobuf:"bytes,11,rep,name=xiaosi_command_list,json=xiaosiCommandList,proto3" json:"xiaosi_command_list,omitempty"`
	RnResponse        *structpb.Struct                       `protobuf:"bytes,12,opt,name=rn_response,json=rnResponse,proto3" json:"rn_response,omitempty"`
	ReasoningContent  string                                 `protobuf:"bytes,13,opt,name=reasoning_content,json=reasoningContent,proto3" json:"reasoning_content,omitempty"`
	TtsNorm           string                                 `protobuf:"bytes,14,opt,name=tts_norm,json=ttsNorm,proto3" json:"tts_norm,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *FullViewContextReply_FullViewContextItem) Reset() {
	*x = FullViewContextReply_FullViewContextItem{}
	mi := &file_api_lui_v1_api_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FullViewContextReply_FullViewContextItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FullViewContextReply_FullViewContextItem) ProtoMessage() {}

func (x *FullViewContextReply_FullViewContextItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FullViewContextReply_FullViewContextItem.ProtoReflect.Descriptor instead.
func (*FullViewContextReply_FullViewContextItem) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{23, 3}
}

func (x *FullViewContextReply_FullViewContextItem) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *FullViewContextReply_FullViewContextItem) GetAsrInfo() string {
	if x != nil {
		return x.AsrInfo
	}
	return ""
}

func (x *FullViewContextReply_FullViewContextItem) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

func (x *FullViewContextReply_FullViewContextItem) GetTtsInfo() string {
	if x != nil {
		return x.TtsInfo
	}
	return ""
}

func (x *FullViewContextReply_FullViewContextItem) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *FullViewContextReply_FullViewContextItem) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *FullViewContextReply_FullViewContextItem) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *FullViewContextReply_FullViewContextItem) GetIsLlm() bool {
	if x != nil {
		return x.IsLlm
	}
	return false
}

func (x *FullViewContextReply_FullViewContextItem) GetCardType() int32 {
	if x != nil {
		return x.CardType
	}
	return 0
}

func (x *FullViewContextReply_FullViewContextItem) GetMedia() *FullViewContextReply_Media {
	if x != nil {
		return x.Media
	}
	return nil
}

func (x *FullViewContextReply_FullViewContextItem) GetXiaosiCommandList() []*FullViewContextReply_XiaosiCommands {
	if x != nil {
		return x.XiaosiCommandList
	}
	return nil
}

func (x *FullViewContextReply_FullViewContextItem) GetRnResponse() *structpb.Struct {
	if x != nil {
		return x.RnResponse
	}
	return nil
}

func (x *FullViewContextReply_FullViewContextItem) GetReasoningContent() string {
	if x != nil {
		return x.ReasoningContent
	}
	return ""
}

func (x *FullViewContextReply_FullViewContextItem) GetTtsNorm() string {
	if x != nil {
		return x.TtsNorm
	}
	return ""
}

type LuiNluConfirmRequest_AsrInput struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AsrLen        int32                  `protobuf:"varint,1,opt,name=asr_len,json=asrLen,proto3" json:"asr_len,omitempty"`
	AsrInfo       string                 `protobuf:"bytes,2,opt,name=asr_info,json=asrInfo,proto3" json:"asr_info,omitempty"`
	AsrPinyin     string                 `protobuf:"bytes,3,opt,name=asr_pinyin,json=asrPinyin,proto3" json:"asr_pinyin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LuiNluConfirmRequest_AsrInput) Reset() {
	*x = LuiNluConfirmRequest_AsrInput{}
	mi := &file_api_lui_v1_api_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiNluConfirmRequest_AsrInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiNluConfirmRequest_AsrInput) ProtoMessage() {}

func (x *LuiNluConfirmRequest_AsrInput) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiNluConfirmRequest_AsrInput.ProtoReflect.Descriptor instead.
func (*LuiNluConfirmRequest_AsrInput) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{28, 0}
}

func (x *LuiNluConfirmRequest_AsrInput) GetAsrLen() int32 {
	if x != nil {
		return x.AsrLen
	}
	return 0
}

func (x *LuiNluConfirmRequest_AsrInput) GetAsrInfo() string {
	if x != nil {
		return x.AsrInfo
	}
	return ""
}

func (x *LuiNluConfirmRequest_AsrInput) GetAsrPinyin() string {
	if x != nil {
		return x.AsrPinyin
	}
	return ""
}

type LuiNluConfirmRequest_OcrInput struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	OcrAlldocLen     int32                  `protobuf:"varint,1,opt,name=ocr_alldoc_len,json=ocrAlldocLen,proto3" json:"ocr_alldoc_len,omitempty"`
	OcrKeyLen        int32                  `protobuf:"varint,2,opt,name=ocr_key_len,json=ocrKeyLen,proto3" json:"ocr_key_len,omitempty"`
	OcrAlldocInfo    string                 `protobuf:"bytes,3,opt,name=ocr_alldoc_info,json=ocrAlldocInfo,proto3" json:"ocr_alldoc_info,omitempty"`
	OcrKeyInfo       []string               `protobuf:"bytes,4,rep,name=ocr_key_info,json=ocrKeyInfo,proto3" json:"ocr_key_info,omitempty"`
	OcrKeywordInfo   []string               `protobuf:"bytes,5,rep,name=ocr_keyword_info,json=ocrKeywordInfo,proto3" json:"ocr_keyword_info,omitempty"`
	OcrKeyInfoOffset []int32                `protobuf:"varint,6,rep,packed,name=ocr_key_info_offset,json=ocrKeyInfoOffset,proto3" json:"ocr_key_info_offset,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *LuiNluConfirmRequest_OcrInput) Reset() {
	*x = LuiNluConfirmRequest_OcrInput{}
	mi := &file_api_lui_v1_api_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiNluConfirmRequest_OcrInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiNluConfirmRequest_OcrInput) ProtoMessage() {}

func (x *LuiNluConfirmRequest_OcrInput) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiNluConfirmRequest_OcrInput.ProtoReflect.Descriptor instead.
func (*LuiNluConfirmRequest_OcrInput) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{28, 1}
}

func (x *LuiNluConfirmRequest_OcrInput) GetOcrAlldocLen() int32 {
	if x != nil {
		return x.OcrAlldocLen
	}
	return 0
}

func (x *LuiNluConfirmRequest_OcrInput) GetOcrKeyLen() int32 {
	if x != nil {
		return x.OcrKeyLen
	}
	return 0
}

func (x *LuiNluConfirmRequest_OcrInput) GetOcrAlldocInfo() string {
	if x != nil {
		return x.OcrAlldocInfo
	}
	return ""
}

func (x *LuiNluConfirmRequest_OcrInput) GetOcrKeyInfo() []string {
	if x != nil {
		return x.OcrKeyInfo
	}
	return nil
}

func (x *LuiNluConfirmRequest_OcrInput) GetOcrKeywordInfo() []string {
	if x != nil {
		return x.OcrKeywordInfo
	}
	return nil
}

func (x *LuiNluConfirmRequest_OcrInput) GetOcrKeyInfoOffset() []int32 {
	if x != nil {
		return x.OcrKeyInfoOffset
	}
	return nil
}

type LuiNluConfirmRequest_ConfirmRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	ConfirmRequestIntent string                 `protobuf:"bytes,1,opt,name=confirm_request_intent,json=confirmRequestIntent,proto3" json:"confirm_request_intent,omitempty"`
	ConfirmRequestInput  string                 `protobuf:"bytes,2,opt,name=confirm_request_input,json=confirmRequestInput,proto3" json:"confirm_request_input,omitempty"`
	ConfirmRequestFinger string                 `protobuf:"bytes,3,opt,name=confirm_request_finger,json=confirmRequestFinger,proto3" json:"confirm_request_finger,omitempty"`
	ConfirmRequestPinyin string                 `protobuf:"bytes,4,opt,name=confirm_request_pinyin,json=confirmRequestPinyin,proto3" json:"confirm_request_pinyin,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *LuiNluConfirmRequest_ConfirmRequest) Reset() {
	*x = LuiNluConfirmRequest_ConfirmRequest{}
	mi := &file_api_lui_v1_api_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiNluConfirmRequest_ConfirmRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiNluConfirmRequest_ConfirmRequest) ProtoMessage() {}

func (x *LuiNluConfirmRequest_ConfirmRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiNluConfirmRequest_ConfirmRequest.ProtoReflect.Descriptor instead.
func (*LuiNluConfirmRequest_ConfirmRequest) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{28, 2}
}

func (x *LuiNluConfirmRequest_ConfirmRequest) GetConfirmRequestIntent() string {
	if x != nil {
		return x.ConfirmRequestIntent
	}
	return ""
}

func (x *LuiNluConfirmRequest_ConfirmRequest) GetConfirmRequestInput() string {
	if x != nil {
		return x.ConfirmRequestInput
	}
	return ""
}

func (x *LuiNluConfirmRequest_ConfirmRequest) GetConfirmRequestFinger() string {
	if x != nil {
		return x.ConfirmRequestFinger
	}
	return ""
}

func (x *LuiNluConfirmRequest_ConfirmRequest) GetConfirmRequestPinyin() string {
	if x != nil {
		return x.ConfirmRequestPinyin
	}
	return ""
}

type LuiNluConfirmRequest_HandleInput struct {
	state          protoimpl.MessageState               `protogen:"open.v1"`
	HandleTaskType string                               `protobuf:"bytes,1,opt,name=handle_task_type,json=handleTaskType,proto3" json:"handle_task_type,omitempty"`
	IsEngCorrect   bool                                 `protobuf:"varint,2,opt,name=is_eng_correct,json=isEngCorrect,proto3" json:"is_eng_correct,omitempty"`
	ConfirmRequest *LuiNluConfirmRequest_ConfirmRequest `protobuf:"bytes,3,opt,name=confirm_request,json=confirmRequest,proto3" json:"confirm_request,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *LuiNluConfirmRequest_HandleInput) Reset() {
	*x = LuiNluConfirmRequest_HandleInput{}
	mi := &file_api_lui_v1_api_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiNluConfirmRequest_HandleInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiNluConfirmRequest_HandleInput) ProtoMessage() {}

func (x *LuiNluConfirmRequest_HandleInput) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiNluConfirmRequest_HandleInput.ProtoReflect.Descriptor instead.
func (*LuiNluConfirmRequest_HandleInput) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{28, 3}
}

func (x *LuiNluConfirmRequest_HandleInput) GetHandleTaskType() string {
	if x != nil {
		return x.HandleTaskType
	}
	return ""
}

func (x *LuiNluConfirmRequest_HandleInput) GetIsEngCorrect() bool {
	if x != nil {
		return x.IsEngCorrect
	}
	return false
}

func (x *LuiNluConfirmRequest_HandleInput) GetConfirmRequest() *LuiNluConfirmRequest_ConfirmRequest {
	if x != nil {
		return x.ConfirmRequest
	}
	return nil
}

type LuiNluConfirmRequest_UserSystem struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Location        string                 `protobuf:"bytes,1,opt,name=location,proto3" json:"location,omitempty"`
	Grade           int32                  `protobuf:"varint,2,opt,name=grade,proto3" json:"grade,omitempty"`
	Gender          string                 `protobuf:"bytes,3,opt,name=gender,proto3" json:"gender,omitempty"`
	Nickname        string                 `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Semester        string                 `protobuf:"bytes,5,opt,name=semester,proto3" json:"semester,omitempty"`
	TextbookVersion string                 `protobuf:"bytes,6,opt,name=textbook_version,json=textbookVersion,proto3" json:"textbook_version,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *LuiNluConfirmRequest_UserSystem) Reset() {
	*x = LuiNluConfirmRequest_UserSystem{}
	mi := &file_api_lui_v1_api_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LuiNluConfirmRequest_UserSystem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuiNluConfirmRequest_UserSystem) ProtoMessage() {}

func (x *LuiNluConfirmRequest_UserSystem) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuiNluConfirmRequest_UserSystem.ProtoReflect.Descriptor instead.
func (*LuiNluConfirmRequest_UserSystem) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{28, 4}
}

func (x *LuiNluConfirmRequest_UserSystem) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *LuiNluConfirmRequest_UserSystem) GetGrade() int32 {
	if x != nil {
		return x.Grade
	}
	return 0
}

func (x *LuiNluConfirmRequest_UserSystem) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *LuiNluConfirmRequest_UserSystem) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *LuiNluConfirmRequest_UserSystem) GetSemester() string {
	if x != nil {
		return x.Semester
	}
	return ""
}

func (x *LuiNluConfirmRequest_UserSystem) GetTextbookVersion() string {
	if x != nil {
		return x.TextbookVersion
	}
	return ""
}

type AddLLMMemoryContextRequest_ContextItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Role          string                 `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	DialogueId    string                 `protobuf:"bytes,3,opt,name=dialogue_id,json=dialogueId,proto3" json:"dialogue_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddLLMMemoryContextRequest_ContextItem) Reset() {
	*x = AddLLMMemoryContextRequest_ContextItem{}
	mi := &file_api_lui_v1_api_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddLLMMemoryContextRequest_ContextItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLLMMemoryContextRequest_ContextItem) ProtoMessage() {}

func (x *AddLLMMemoryContextRequest_ContextItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_lui_v1_api_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLLMMemoryContextRequest_ContextItem.ProtoReflect.Descriptor instead.
func (*AddLLMMemoryContextRequest_ContextItem) Descriptor() ([]byte, []int) {
	return file_api_lui_v1_api_proto_rawDescGZIP(), []int{29, 0}
}

func (x *AddLLMMemoryContextRequest_ContextItem) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *AddLLMMemoryContextRequest_ContextItem) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *AddLLMMemoryContextRequest_ContextItem) GetDialogueId() string {
	if x != nil {
		return x.DialogueId
	}
	return ""
}

var File_api_lui_v1_api_proto protoreflect.FileDescriptor

const file_api_lui_v1_api_proto_rawDesc = "" +
	"\n" +
	"\x14api/lui/v1/api.proto\x12\x06api.v1\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1cgoogle/api/annotations.proto\"\x8a\x04\n" +
	"\vLlmResponse\x12\x1b\n" +
	"\tllm_skill\x18\x01 \x01(\tR\bllmSkill\x12\x1b\n" +
	"\tllm_model\x18\x02 \x01(\tR\bllmModel\x12\x1a\n" +
	"\bresponse\x18\x03 \x01(\tR\bresponse\x12\x1b\n" +
	"\timage_url\x18\x04 \x01(\tR\bimageUrl\x12\x1b\n" +
	"\tvideo_url\x18\x05 \x01(\tR\bvideoUrl\x12!\n" +
	"\fllm_response\x18\x06 \x01(\tR\vllmResponse\x12\x1f\n" +
	"\vdialogue_id\x18\a \x01(\tR\n" +
	"dialogueId\x12\x1d\n" +
	"\n" +
	"session_id\x18\t \x01(\tR\tsessionId\x12\x12\n" +
	"\x04type\x18\n" +
	" \x01(\x05R\x04type\x12\x14\n" +
	"\x05cover\x18\v \x01(\tR\x05cover\x12\x1f\n" +
	"\vcover_title\x18\f \x01(\tR\n" +
	"coverTitle\x12\x10\n" +
	"\x03url\x18\r \x01(\tR\x03url\x12\x19\n" +
	"\btts_info\x18\x0e \x01(\tR\attsInfo\x12*\n" +
	"\x11mixed_modal_query\x18\x0f \x01(\tR\x0fmixedModalQuery\x12I\n" +
	"\x14mixed_modal_response\x18\x10 \x01(\v2\x17.google.protobuf.StructR\x12mixedModalResponse\x12\x19\n" +
	"\btts_norm\x18\x11 \x01(\tR\attsNorm\"\xfa\x05\n" +
	"\rLuiNluRequest\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\x12\x19\n" +
	"\basr_info\x18\a \x01(\tR\aasrInfo\x12\x1d\n" +
	"\n" +
	"asr_pinyin\x18\b \x01(\tR\tasrPinyin\x12\x1a\n" +
	"\blocation\x18\t \x01(\tR\blocation\x12\x14\n" +
	"\x05grade\x18\n" +
	" \x01(\tR\x05grade\x12\x15\n" +
	"\x06tal_id\x18\v \x01(\tR\x05talId\x126\n" +
	"\x0eslot_fill_list\x18\f \x03(\v2\x10.api.v1.SlotFillR\fslotFillList\x12\x19\n" +
	"\bgrade_id\x18\r \x01(\tR\agradeId\x12\x19\n" +
	"\bbiz_type\x18\x0e \x01(\tR\abizType\x12\x1e\n" +
	"\n" +
	"continuous\x18\x0f \x01(\bR\n" +
	"continuous\x12\x1f\n" +
	"\vsentence_id\x18\x10 \x01(\x05R\n" +
	"sentenceId\x12 \n" +
	"\fwake_up_type\x18\x11 \x01(\tR\n" +
	"wakeUpType\x12\x1f\n" +
	"\vpage_prompt\x18\x12 \x01(\tR\n" +
	"pagePrompt\x12\x1d\n" +
	"\n" +
	"scene_mode\x18\x13 \x01(\x05R\tsceneMode\x12-\n" +
	"\tllm_agent\x18\x14 \x01(\v2\x10.api.v1.LlmAgentR\bllmAgent\x12\x1d\n" +
	"\n" +
	"rn_version\x18\x15 \x01(\tR\trnVersion\x12\x1f\n" +
	"\vscreen_mode\x18\x16 \x01(\x05R\n" +
	"screenMode\x12:\n" +
	"\fimage_prompt\x18\x17 \x03(\v2\x17.google.protobuf.StructR\vimagePrompt\x12\x1b\n" +
	"\tbiz_extra\x18\x18 \x01(\tR\bbizExtra\x12\x12\n" +
	"\x04from\x18\x19 \x01(\tR\x04from\x12.\n" +
	"\x13asr_start_timestamp\x18\x1a \x01(\x03R\x11asrStartTimestamp\x12*\n" +
	"\x11asr_end_timestamp\x18\x1b \x01(\x03R\x0fasrEndTimestamp\"6\n" +
	"\bLlmAgent\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06status\x18\x02 \x01(\x05R\x06status\"@\n" +
	"\bSlotFill\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\"\xa4\x05\n" +
	"\rLuiSugRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1d\n" +
	"\n" +
	"request_id\x18\x02 \x01(\tR\trequestId\x12\x1d\n" +
	"\n" +
	"session_id\x18\x03 \x01(\tR\tsessionId\x12\x1c\n" +
	"\ttimestamp\x18\x04 \x01(\x03R\ttimestamp\x12\x16\n" +
	"\x06source\x18\x05 \x01(\tR\x06source\x12\x18\n" +
	"\aversion\x18\x06 \x01(\tR\aversion\x12;\n" +
	"\tasr_input\x18\a \x01(\v2\x1e.api.v1.LuiSugRequest.AsrInputR\basrInput\x12A\n" +
	"\vuser_system\x18\b \x01(\v2 .api.v1.LuiSugRequest.UserSystemR\n" +
	"userSystem\x12\x19\n" +
	"\bbiz_type\x18\t \x01(\tR\abizType\x12\x1d\n" +
	"\n" +
	"scene_mode\x18\n" +
	" \x01(\x05R\tsceneMode\x12\x1d\n" +
	"\n" +
	"rn_version\x18\v \x01(\tR\trnVersion\x12 \n" +
	"\vscreenState\x18\f \x01(\tR\vscreenState\x12\x1b\n" +
	"\tbiz_extra\x18\r \x01(\tR\bbizExtra\x12\x12\n" +
	"\x04from\x18\x0e \x01(\tR\x04from\x12!\n" +
	"\fexercise_ocr\x18\x0f \x01(\tR\vexerciseOcr\x1a]\n" +
	"\bAsrInput\x12\x17\n" +
	"\aasr_len\x18\x01 \x01(\x05R\x06asrLen\x12\x19\n" +
	"\basr_info\x18\x02 \x01(\tR\aasrInfo\x12\x1d\n" +
	"\n" +
	"asr_pinyin\x18\x03 \x01(\tR\tasrPinyin\x1a>\n" +
	"\n" +
	"UserSystem\x12\x1a\n" +
	"\blocation\x18\x01 \x01(\tR\blocation\x12\x14\n" +
	"\x05grade\x18\x02 \x01(\x05R\x05grade\"\x15\n" +
	"\x13LuiNluEnsureRequest\"*\n" +
	"\x12LuiUserInfoRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\"z\n" +
	"\x10LuiUserInfoReply\x12\x15\n" +
	"\x06tal_id\x18\x02 \x01(\tR\x05talId\x12\x14\n" +
	"\x05grade\x18\x03 \x01(\x05R\x05grade\x12\x1d\n" +
	"\n" +
	"grade_name\x18\x04 \x01(\tR\tgradeName\x12\x1a\n" +
	"\bnickname\x18\x05 \x01(\tR\bnickname\"\x13\n" +
	"\x03Any\x12\f\n" +
	"\x01b\x18\x01 \x01(\fR\x01b\"\x9f\x01\n" +
	"\x14LuiUserChoiceRequest\x12\x15\n" +
	"\x06tal_id\x18\x01 \x01(\tR\x05talId\x12#\n" +
	"\rcourse_system\x18\x02 \x01(\x05R\fcourseSystem\x12\x18\n" +
	"\asubject\x18\x03 \x01(\x05R\asubject\x12\x14\n" +
	"\x05grade\x18\x04 \x01(\x05R\x05grade\x12\x1b\n" +
	"\tdevice_id\x18\x05 \x01(\tR\bdeviceId\"H\n" +
	"\x12LuiUserChoiceReply\x122\n" +
	"\x04list\x18\x01 \x03(\v2\x1e.api.v1.LuiUserChoiceReplyItemR\x04list\"\xe7\x01\n" +
	"\x16LuiUserChoiceReplyItem\x12#\n" +
	"\rcourse_system\x18\x01 \x01(\x05R\fcourseSystem\x12.\n" +
	"\x13course_system_alias\x18\x02 \x01(\tR\x11courseSystemAlias\x12\x18\n" +
	"\aversion\x18\x03 \x01(\x05R\aversion\x12!\n" +
	"\fversion_name\x18\x04 \x01(\tR\vversionName\x12\x18\n" +
	"\asubject\x18\x05 \x01(\x05R\asubject\x12!\n" +
	"\fsubject_name\x18\x06 \x01(\tR\vsubjectName\"L\n" +
	"\x19LuiToolsUserChoiceRequest\x12\x15\n" +
	"\x06tal_id\x18\x01 \x01(\tR\x05talId\x12\x18\n" +
	"\asubject\x18\x02 \x01(\x05R\asubject\"<\n" +
	"\x17LuiToolsUserChoiceReply\x12!\n" +
	"\fversion_name\x18\x01 \x01(\tR\vversionName\"\xa2\x01\n" +
	"\x19QueryTraceFeedbackRequest\x12\x1d\n" +
	"\n" +
	"session_id\x18\x01 \x01(\tR\tsessionId\x12#\n" +
	"\rfeedback_type\x18\x02 \x01(\x05R\ffeedbackType\x12\x1a\n" +
	"\bfeedback\x18\x03 \x01(\tR\bfeedback\x12%\n" +
	"\x0efeedback_types\x18\x04 \x03(\x05R\rfeedbackTypes\"\x19\n" +
	"\x17QueryTraceFeedbackReply\"\x8a\x02\n" +
	"\x17LuiSimulateQueryRequest\x12\x19\n" +
	"\basr_info\x18\x01 \x01(\tR\aasrInfo\x12\x1d\n" +
	"\n" +
	"asr_pinyin\x18\x02 \x01(\tR\tasrPinyin\x12\x1a\n" +
	"\blocation\x18\x03 \x01(\tR\blocation\x12\x14\n" +
	"\x05grade\x18\x04 \x01(\tR\x05grade\x12\x19\n" +
	"\bgrade_id\x18\x05 \x01(\tR\agradeId\x12\x15\n" +
	"\x06tal_id\x18\x06 \x01(\tR\x05talId\x12\x19\n" +
	"\bbiz_type\x18\a \x01(\tR\abizType\x126\n" +
	"\x0eslot_fill_list\x18\b \x03(\v2\x10.api.v1.SlotFillR\fslotFillList\"\x9b\x02\n" +
	"\x10LuiRejectRequest\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\x12\x1b\n" +
	"\tdevice_id\x18\x02 \x01(\tR\bdeviceId\x12\x18\n" +
	"\aversion\x18\x03 \x01(\tR\aversion\x12\x19\n" +
	"\basr_info\x18\x04 \x01(\tR\aasrInfo\x12\x1f\n" +
	"\vwakeup_type\x18\x05 \x01(\tR\n" +
	"wakeupType\x12#\n" +
	"\rreject_result\x18\x06 \x01(\x05R\frejectResult\x12\x1e\n" +
	"\n" +
	"continuous\x18\a \x01(\bR\n" +
	"continuous\x12\x15\n" +
	"\x06tal_id\x18\b \x01(\tR\x05talId\x12\x19\n" +
	"\bbiz_type\x18\t \x01(\tR\abizType\"H\n" +
	"\x14MemoryContextRequest\x12\x15\n" +
	"\x06tal_id\x18\x01 \x01(\tR\x05talId\x12\x19\n" +
	"\bbiz_type\x18\x02 \x01(\tR\abizType\"\xb5\x02\n" +
	"\x12MemoryContextReply\x12@\n" +
	"\x04list\x18\x01 \x03(\v2,.api.v1.MemoryContextReply.MemoryContextItemR\x04list\x1a\xdc\x01\n" +
	"\x11MemoryContextItem\x12\x19\n" +
	"\basr_info\x18\x01 \x01(\tR\aasrInfo\x12\x16\n" +
	"\x06intent\x18\x02 \x01(\tR\x06intent\x12\x1a\n" +
	"\bresponse\x18\x03 \x01(\tR\bresponse\x12\x19\n" +
	"\btts_info\x18\x04 \x01(\tR\attsInfo\x12#\n" +
	"\rrewrite_query\x18\x05 \x01(\tR\frewriteQuery\x12\x1d\n" +
	"\n" +
	"time_stamp\x18\x06 \x01(\x03R\ttimeStamp\x12\x19\n" +
	"\btts_norm\x18\a \x01(\tR\attsNorm\"\xb9\x01\n" +
	"\x17LLMMemoryContextRequest\x12\x19\n" +
	"\bbiz_type\x18\x01 \x01(\tR\abizType\x12,\n" +
	"\x12current_session_id\x18\x02 \x01(\tR\x10currentSessionId\x12\x15\n" +
	"\x06tal_id\x18\x03 \x01(\tR\x05talId\x12$\n" +
	"\x0eneed_llm_skill\x18\x04 \x03(\tR\fneedLlmSkill\x12\x18\n" +
	"\aversion\x18\x05 \x01(\tR\aversion\"\xb4\x01\n" +
	"\x15LLMMemoryContextReply\x12=\n" +
	"\x04list\x18\x01 \x03(\v2).api.v1.LLMMemoryContextReply.ContextItemR\x04list\x1a\\\n" +
	"\vContextItem\x12\x12\n" +
	"\x04role\x18\x01 \x01(\tR\x04role\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\x12\x1f\n" +
	"\vdialogue_id\x18\x03 \x01(\tR\n" +
	"dialogueId\"J\n" +
	"\x16FullViewContextRequest\x12\x15\n" +
	"\x06tal_id\x18\x01 \x01(\tR\x05talId\x12\x19\n" +
	"\bbiz_type\x18\x02 \x01(\tR\abizType\"\x9b\a\n" +
	"\x14FullViewContextReply\x12D\n" +
	"\x04list\x18\x01 \x03(\v20.api.v1.FullViewContextReply.FullViewContextItemR\x04list\x1ad\n" +
	"\x05Media\x12\x12\n" +
	"\x04type\x18\x01 \x01(\x05R\x04type\x12\x14\n" +
	"\x05cover\x18\x02 \x01(\tR\x05cover\x12\x10\n" +
	"\x03url\x18\x03 \x01(\tR\x03url\x12\x1f\n" +
	"\vcover_title\x18\x04 \x01(\tR\n" +
	"coverTitle\x1ay\n" +
	"\x0eXiaosiCommands\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12\x14\n" +
	"\x05query\x18\x02 \x01(\tR\x05query\x12;\n" +
	"\x04apps\x18\x03 \x03(\v2'.api.v1.FullViewContextReply.CommandAppR\x04apps\x1a4\n" +
	"\n" +
	"CommandApp\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04icon\x18\x02 \x01(\tR\x04icon\x1a\xa5\x04\n" +
	"\x13FullViewContextItem\x12\x1d\n" +
	"\n" +
	"session_id\x18\x01 \x01(\tR\tsessionId\x12\x19\n" +
	"\basr_info\x18\x02 \x01(\tR\aasrInfo\x12\x1a\n" +
	"\bresponse\x18\x03 \x01(\tR\bresponse\x12\x19\n" +
	"\btts_info\x18\x04 \x01(\tR\attsInfo\x12\x16\n" +
	"\x06source\x18\x05 \x01(\tR\x06source\x12\x1b\n" +
	"\timage_url\x18\x06 \x01(\tR\bimageUrl\x12\x1b\n" +
	"\tvideo_url\x18\a \x01(\tR\bvideoUrl\x12\x15\n" +
	"\x06is_llm\x18\b \x01(\bR\x05isLlm\x12\x1b\n" +
	"\tcard_type\x18\t \x01(\x05R\bcardType\x128\n" +
	"\x05media\x18\n" +
	" \x01(\v2\".api.v1.FullViewContextReply.MediaR\x05media\x12[\n" +
	"\x13xiaosi_command_list\x18\v \x03(\v2+.api.v1.FullViewContextReply.XiaosiCommandsR\x11xiaosiCommandList\x128\n" +
	"\vrn_response\x18\f \x01(\v2\x17.google.protobuf.StructR\n" +
	"rnResponse\x12+\n" +
	"\x11reasoning_content\x18\r \x01(\tR\x10reasoningContent\x12\x19\n" +
	"\btts_norm\x18\x0e \x01(\tR\attsNorm\"/\n" +
	"\x14LuiUserLlmSugRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"?\n" +
	"\x12LuiUserLlmSugReply\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x10\n" +
	"\x03sug\x18\x02 \x01(\tR\x03sug\"m\n" +
	"\x16LuiUserLlmTailsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1f\n" +
	"\vscreen_mode\x18\x02 \x01(\x05R\n" +
	"screenMode\x12\x19\n" +
	"\bbiz_type\x18\x03 \x01(\tR\abizType\"V\n" +
	"\x1cAIEyeFingerImageJudgeRequest\x12\x19\n" +
	"\bbiz_type\x18\x01 \x01(\x05R\abizType\x12\x1b\n" +
	"\timage_url\x18\x02 \x01(\tR\bimageUrl\"\xd3\n" +
	"\n" +
	"\x14LuiNluConfirmRequest\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\tR\x06userId\x12\x1c\n" +
	"\trequestId\x18\x02 \x01(\tR\trequestId\x12\x1c\n" +
	"\tsessionId\x18\x03 \x01(\tR\tsessionId\x12\x18\n" +
	"\aversion\x18\x04 \x01(\tR\aversion\x12B\n" +
	"\tasr_input\x18\x05 \x01(\v2%.api.v1.LuiNluConfirmRequest.AsrInputR\basrInput\x12H\n" +
	"\vuser_system\x18\x06 \x01(\v2'.api.v1.LuiNluConfirmRequest.UserSystemR\n" +
	"userSystem\x12B\n" +
	"\tocr_input\x18\a \x01(\v2%.api.v1.LuiNluConfirmRequest.OcrInputR\bocrInput\x12K\n" +
	"\fhandle_input\x18\b \x01(\v2(.api.v1.LuiNluConfirmRequest.HandleInputR\vhandleInput\x1a]\n" +
	"\bAsrInput\x12\x17\n" +
	"\aasr_len\x18\x01 \x01(\x05R\x06asrLen\x12\x19\n" +
	"\basr_info\x18\x02 \x01(\tR\aasrInfo\x12\x1d\n" +
	"\n" +
	"asr_pinyin\x18\x03 \x01(\tR\tasrPinyin\x1a\xf3\x01\n" +
	"\bOcrInput\x12$\n" +
	"\x0eocr_alldoc_len\x18\x01 \x01(\x05R\focrAlldocLen\x12\x1e\n" +
	"\vocr_key_len\x18\x02 \x01(\x05R\tocrKeyLen\x12&\n" +
	"\x0focr_alldoc_info\x18\x03 \x01(\tR\rocrAlldocInfo\x12 \n" +
	"\focr_key_info\x18\x04 \x03(\tR\n" +
	"ocrKeyInfo\x12(\n" +
	"\x10ocr_keyword_info\x18\x05 \x03(\tR\x0eocrKeywordInfo\x12-\n" +
	"\x13ocr_key_info_offset\x18\x06 \x03(\x05R\x10ocrKeyInfoOffset\x1a\xe6\x01\n" +
	"\x0eConfirmRequest\x124\n" +
	"\x16confirm_request_intent\x18\x01 \x01(\tR\x14confirmRequestIntent\x122\n" +
	"\x15confirm_request_input\x18\x02 \x01(\tR\x13confirmRequestInput\x124\n" +
	"\x16confirm_request_finger\x18\x03 \x01(\tR\x14confirmRequestFinger\x124\n" +
	"\x16confirm_request_pinyin\x18\x04 \x01(\tR\x14confirmRequestPinyin\x1a\xb3\x01\n" +
	"\vHandleInput\x12(\n" +
	"\x10handle_task_type\x18\x01 \x01(\tR\x0ehandleTaskType\x12$\n" +
	"\x0eis_eng_correct\x18\x02 \x01(\bR\fisEngCorrect\x12T\n" +
	"\x0fconfirm_request\x18\x03 \x01(\v2+.api.v1.LuiNluConfirmRequest.ConfirmRequestR\x0econfirmRequest\x1a\xb9\x01\n" +
	"\n" +
	"UserSystem\x12\x1a\n" +
	"\blocation\x18\x01 \x01(\tR\blocation\x12\x14\n" +
	"\x05grade\x18\x02 \x01(\x05R\x05grade\x12\x16\n" +
	"\x06gender\x18\x03 \x01(\tR\x06gender\x12\x1a\n" +
	"\bnickname\x18\x04 \x01(\tR\bnickname\x12\x1a\n" +
	"\bsemester\x18\x05 \x01(\tR\bsemester\x12)\n" +
	"\x10textbook_version\x18\x06 \x01(\tR\x0ftextbookVersion\"\x97\x02\n" +
	"\x1aAddLLMMemoryContextRequest\x12\x15\n" +
	"\x06tal_id\x18\x01 \x01(\tR\x05talId\x12\x1d\n" +
	"\n" +
	"session_id\x18\x02 \x01(\tR\tsessionId\x12J\n" +
	"\bmessages\x18\x03 \x03(\v2..api.v1.AddLLMMemoryContextRequest.ContextItemR\bmessages\x12\x19\n" +
	"\bbiz_type\x18\x04 \x01(\tR\abizType\x1a\\\n" +
	"\vContextItem\x12\x12\n" +
	"\x04role\x18\x01 \x01(\tR\x04role\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\x12\x1f\n" +
	"\vdialogue_id\x18\x03 \x01(\tR\n" +
	"dialogueId\"\x1a\n" +
	"\x18AddLLMMemoryContextReply\"G\n" +
	"\x13ExitLuiSceneRequest\x12\x19\n" +
	"\bbiz_type\x18\x01 \x01(\tR\abizType\x12\x15\n" +
	"\x06tal_id\x18\x02 \x01(\tR\x05talId\"\x13\n" +
	"\x11ExitLuiSceneReply2\xea\x0f\n" +
	"\rLuiContentApi\x12V\n" +
	"\fLuiNluResult\x12\x15.api.v1.LuiNluRequest\x1a\x17.google.protobuf.Struct\"\x16\x82\xd3\xe4\x93\x02\x10:\x01*\"\v/v1/lui/nlu\x12X\n" +
	"\n" +
	"LuiNlu4Lui\x12\x15.api.v1.LuiNluRequest\x1a\x17.google.protobuf.Struct\"\x1a\x82\xd3\xe4\x93\x02\x14:\x01*\"\x0f/v1/lui/nlu-lui\x12X\n" +
	"\n" +
	"LuiSug2Lui\x12\x15.api.v1.LuiSugRequest\x1a\x17.google.protobuf.Struct\"\x1a\x82\xd3\xe4\x93\x02\x14:\x01*\"\x0f/v1/lui/sug-lui\x12d\n" +
	"\rLuiUserChoice\x12\x1c.api.v1.LuiUserChoiceRequest\x1a\x1a.api.v1.LuiUserChoiceReply\"\x19\x82\xd3\xe4\x93\x02\x13:\x01*\"\x0e/v1/lui/choice\x12y\n" +
	"\x12LuiToolsUserChoice\x12!.api.v1.LuiToolsUserChoiceRequest\x1a\x1f.api.v1.LuiToolsUserChoiceReply\"\x1f\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/v1/lui/tools/choice\x12{\n" +
	"\x12QueryTraceFeedback\x12!.api.v1.QueryTraceFeedbackRequest\x1a\x1f.api.v1.QueryTraceFeedbackReply\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/v1/lui/trace/feedback\x12o\n" +
	"\x10LuiSimulateQuery\x12\x1f.api.v1.LuiSimulateQueryRequest\x1a\x17.google.protobuf.Struct\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/v1/lui/simulate-query\x12]\n" +
	"\tRejectNLU\x12\x18.api.v1.LuiRejectRequest\x1a\x17.google.protobuf.Struct\"\x1d\x82\xd3\xe4\x93\x02\x17:\x01*\"\x12/v1/lui/nlu-reject\x12l\n" +
	"\rMemoryContext\x12\x1c.api.v1.MemoryContextRequest\x1a\x1a.api.v1.MemoryContextReply\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/v1/lui/memory-context\x12j\n" +
	"\rLuiUserLlmSug\x12\x1c.api.v1.LuiUserLlmSugRequest\x1a\x1a.api.v1.LuiUserLlmSugReply\"\x1f\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/v1/lui/user-llm-sug\x12u\n" +
	"\x0fFullViewContext\x12\x1e.api.v1.FullViewContextRequest\x1a\x1c.api.v1.FullViewContextReply\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\"\x19/v1/lui/full-view-context\x12m\n" +
	"\x0fLuiUserLlmTails\x12\x1e.api.v1.LuiUserLlmTailsRequest\x1a\x17.google.protobuf.Struct\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/v1/lui/user-llm-tails\x12}\n" +
	"\x15AIEyeFingerImageJudge\x12$.api.v1.AIEyeFingerImageJudgeRequest\x1a\x17.google.protobuf.Struct\"%\x82\xd3\xe4\x93\x02\x1f:\x01*\"\x1a/v1/lui/ai-eye-image-judge\x12j\n" +
	"\rZhWordConfirm\x12\x1c.api.v1.LuiNluConfirmRequest\x1a\x17.google.protobuf.Struct\"\"\x82\xd3\xe4\x93\x02\x1c:\x01*\"\x17/v1/lui/zh-word-confirm\x12\x81\x01\n" +
	"\x1eUpdateMemoryContextLlmResponse\x12\x13.api.v1.LlmResponse\x1a\x17.google.protobuf.Struct\"1\x82\xd3\xe4\x93\x02+:\x01*\"&/v1/lui/updateMemoryContextLlmResponse\x12|\n" +
	"\x13GetLLMMemoryContext\x12\x1f.api.v1.LLMMemoryContextRequest\x1a\x1d.api.v1.LLMMemoryContextReply\"%\x82\xd3\xe4\x93\x02\x1f:\x01*\"\x1a/v1/lui/llm-memory-context\x12\x86\x01\n" +
	"\x13AddLLMMemoryContext\x12\".api.v1.AddLLMMemoryContextRequest\x1a .api.v1.AddLLMMemoryContextReply\")\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/v1/lui/add-llm-memory-context\x12i\n" +
	"\fExitLuiScene\x12\x1b.api.v1.ExitLuiSceneRequest\x1a\x19.api.v1.ExitLuiSceneReply\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/v1/lui/exit-lui-scene2l\n" +
	"\n" +
	"UCenterApi\x12^\n" +
	"\vLuiUserInfo\x12\x1a.api.v1.LuiUserInfoRequest\x1a\x18.api.v1.LuiUserInfoReply\"\x19\x82\xd3\xe4\x93\x02\x13\x12\x11/v1/lui/user-infoB,\n" +
	"\n" +
	"api.lui.v1B\rLuiApiProtoV1P\x01Z\rapi/lui/v1;v1b\x06proto3"

var (
	file_api_lui_v1_api_proto_rawDescOnce sync.Once
	file_api_lui_v1_api_proto_rawDescData []byte
)

func file_api_lui_v1_api_proto_rawDescGZIP() []byte {
	file_api_lui_v1_api_proto_rawDescOnce.Do(func() {
		file_api_lui_v1_api_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_lui_v1_api_proto_rawDesc), len(file_api_lui_v1_api_proto_rawDesc)))
	})
	return file_api_lui_v1_api_proto_rawDescData
}

var file_api_lui_v1_api_proto_msgTypes = make([]protoimpl.MessageInfo, 47)
var file_api_lui_v1_api_proto_goTypes = []any{
	(*LlmResponse)(nil),                              // 0: api.v1.LlmResponse
	(*LuiNluRequest)(nil),                            // 1: api.v1.LuiNluRequest
	(*LlmAgent)(nil),                                 // 2: api.v1.LlmAgent
	(*SlotFill)(nil),                                 // 3: api.v1.SlotFill
	(*LuiSugRequest)(nil),                            // 4: api.v1.LuiSugRequest
	(*LuiNluEnsureRequest)(nil),                      // 5: api.v1.LuiNluEnsureRequest
	(*LuiUserInfoRequest)(nil),                       // 6: api.v1.LuiUserInfoRequest
	(*LuiUserInfoReply)(nil),                         // 7: api.v1.LuiUserInfoReply
	(*Any)(nil),                                      // 8: api.v1.Any
	(*LuiUserChoiceRequest)(nil),                     // 9: api.v1.LuiUserChoiceRequest
	(*LuiUserChoiceReply)(nil),                       // 10: api.v1.LuiUserChoiceReply
	(*LuiUserChoiceReplyItem)(nil),                   // 11: api.v1.LuiUserChoiceReplyItem
	(*LuiToolsUserChoiceRequest)(nil),                // 12: api.v1.LuiToolsUserChoiceRequest
	(*LuiToolsUserChoiceReply)(nil),                  // 13: api.v1.LuiToolsUserChoiceReply
	(*QueryTraceFeedbackRequest)(nil),                // 14: api.v1.QueryTraceFeedbackRequest
	(*QueryTraceFeedbackReply)(nil),                  // 15: api.v1.QueryTraceFeedbackReply
	(*LuiSimulateQueryRequest)(nil),                  // 16: api.v1.LuiSimulateQueryRequest
	(*LuiRejectRequest)(nil),                         // 17: api.v1.LuiRejectRequest
	(*MemoryContextRequest)(nil),                     // 18: api.v1.MemoryContextRequest
	(*MemoryContextReply)(nil),                       // 19: api.v1.MemoryContextReply
	(*LLMMemoryContextRequest)(nil),                  // 20: api.v1.LLMMemoryContextRequest
	(*LLMMemoryContextReply)(nil),                    // 21: api.v1.LLMMemoryContextReply
	(*FullViewContextRequest)(nil),                   // 22: api.v1.FullViewContextRequest
	(*FullViewContextReply)(nil),                     // 23: api.v1.FullViewContextReply
	(*LuiUserLlmSugRequest)(nil),                     // 24: api.v1.LuiUserLlmSugRequest
	(*LuiUserLlmSugReply)(nil),                       // 25: api.v1.LuiUserLlmSugReply
	(*LuiUserLlmTailsRequest)(nil),                   // 26: api.v1.LuiUserLlmTailsRequest
	(*AIEyeFingerImageJudgeRequest)(nil),             // 27: api.v1.AIEyeFingerImageJudgeRequest
	(*LuiNluConfirmRequest)(nil),                     // 28: api.v1.LuiNluConfirmRequest
	(*AddLLMMemoryContextRequest)(nil),               // 29: api.v1.AddLLMMemoryContextRequest
	(*AddLLMMemoryContextReply)(nil),                 // 30: api.v1.AddLLMMemoryContextReply
	(*ExitLuiSceneRequest)(nil),                      // 31: api.v1.ExitLuiSceneRequest
	(*ExitLuiSceneReply)(nil),                        // 32: api.v1.ExitLuiSceneReply
	(*LuiSugRequest_AsrInput)(nil),                   // 33: api.v1.LuiSugRequest.AsrInput
	(*LuiSugRequest_UserSystem)(nil),                 // 34: api.v1.LuiSugRequest.UserSystem
	(*MemoryContextReply_MemoryContextItem)(nil),     // 35: api.v1.MemoryContextReply.MemoryContextItem
	(*LLMMemoryContextReply_ContextItem)(nil),        // 36: api.v1.LLMMemoryContextReply.ContextItem
	(*FullViewContextReply_Media)(nil),               // 37: api.v1.FullViewContextReply.Media
	(*FullViewContextReply_XiaosiCommands)(nil),      // 38: api.v1.FullViewContextReply.XiaosiCommands
	(*FullViewContextReply_CommandApp)(nil),          // 39: api.v1.FullViewContextReply.CommandApp
	(*FullViewContextReply_FullViewContextItem)(nil), // 40: api.v1.FullViewContextReply.FullViewContextItem
	(*LuiNluConfirmRequest_AsrInput)(nil),            // 41: api.v1.LuiNluConfirmRequest.AsrInput
	(*LuiNluConfirmRequest_OcrInput)(nil),            // 42: api.v1.LuiNluConfirmRequest.OcrInput
	(*LuiNluConfirmRequest_ConfirmRequest)(nil),      // 43: api.v1.LuiNluConfirmRequest.ConfirmRequest
	(*LuiNluConfirmRequest_HandleInput)(nil),         // 44: api.v1.LuiNluConfirmRequest.HandleInput
	(*LuiNluConfirmRequest_UserSystem)(nil),          // 45: api.v1.LuiNluConfirmRequest.UserSystem
	(*AddLLMMemoryContextRequest_ContextItem)(nil),   // 46: api.v1.AddLLMMemoryContextRequest.ContextItem
	(*structpb.Struct)(nil),                          // 47: google.protobuf.Struct
}
var file_api_lui_v1_api_proto_depIdxs = []int32{
	47, // 0: api.v1.LlmResponse.mixed_modal_response:type_name -> google.protobuf.Struct
	3,  // 1: api.v1.LuiNluRequest.slot_fill_list:type_name -> api.v1.SlotFill
	2,  // 2: api.v1.LuiNluRequest.llm_agent:type_name -> api.v1.LlmAgent
	47, // 3: api.v1.LuiNluRequest.image_prompt:type_name -> google.protobuf.Struct
	33, // 4: api.v1.LuiSugRequest.asr_input:type_name -> api.v1.LuiSugRequest.AsrInput
	34, // 5: api.v1.LuiSugRequest.user_system:type_name -> api.v1.LuiSugRequest.UserSystem
	11, // 6: api.v1.LuiUserChoiceReply.list:type_name -> api.v1.LuiUserChoiceReplyItem
	3,  // 7: api.v1.LuiSimulateQueryRequest.slot_fill_list:type_name -> api.v1.SlotFill
	35, // 8: api.v1.MemoryContextReply.list:type_name -> api.v1.MemoryContextReply.MemoryContextItem
	36, // 9: api.v1.LLMMemoryContextReply.list:type_name -> api.v1.LLMMemoryContextReply.ContextItem
	40, // 10: api.v1.FullViewContextReply.list:type_name -> api.v1.FullViewContextReply.FullViewContextItem
	41, // 11: api.v1.LuiNluConfirmRequest.asr_input:type_name -> api.v1.LuiNluConfirmRequest.AsrInput
	45, // 12: api.v1.LuiNluConfirmRequest.user_system:type_name -> api.v1.LuiNluConfirmRequest.UserSystem
	42, // 13: api.v1.LuiNluConfirmRequest.ocr_input:type_name -> api.v1.LuiNluConfirmRequest.OcrInput
	44, // 14: api.v1.LuiNluConfirmRequest.handle_input:type_name -> api.v1.LuiNluConfirmRequest.HandleInput
	46, // 15: api.v1.AddLLMMemoryContextRequest.messages:type_name -> api.v1.AddLLMMemoryContextRequest.ContextItem
	39, // 16: api.v1.FullViewContextReply.XiaosiCommands.apps:type_name -> api.v1.FullViewContextReply.CommandApp
	37, // 17: api.v1.FullViewContextReply.FullViewContextItem.media:type_name -> api.v1.FullViewContextReply.Media
	38, // 18: api.v1.FullViewContextReply.FullViewContextItem.xiaosi_command_list:type_name -> api.v1.FullViewContextReply.XiaosiCommands
	47, // 19: api.v1.FullViewContextReply.FullViewContextItem.rn_response:type_name -> google.protobuf.Struct
	43, // 20: api.v1.LuiNluConfirmRequest.HandleInput.confirm_request:type_name -> api.v1.LuiNluConfirmRequest.ConfirmRequest
	1,  // 21: api.v1.LuiContentApi.LuiNluResult:input_type -> api.v1.LuiNluRequest
	1,  // 22: api.v1.LuiContentApi.LuiNlu4Lui:input_type -> api.v1.LuiNluRequest
	4,  // 23: api.v1.LuiContentApi.LuiSug2Lui:input_type -> api.v1.LuiSugRequest
	9,  // 24: api.v1.LuiContentApi.LuiUserChoice:input_type -> api.v1.LuiUserChoiceRequest
	12, // 25: api.v1.LuiContentApi.LuiToolsUserChoice:input_type -> api.v1.LuiToolsUserChoiceRequest
	14, // 26: api.v1.LuiContentApi.QueryTraceFeedback:input_type -> api.v1.QueryTraceFeedbackRequest
	16, // 27: api.v1.LuiContentApi.LuiSimulateQuery:input_type -> api.v1.LuiSimulateQueryRequest
	17, // 28: api.v1.LuiContentApi.RejectNLU:input_type -> api.v1.LuiRejectRequest
	18, // 29: api.v1.LuiContentApi.MemoryContext:input_type -> api.v1.MemoryContextRequest
	24, // 30: api.v1.LuiContentApi.LuiUserLlmSug:input_type -> api.v1.LuiUserLlmSugRequest
	22, // 31: api.v1.LuiContentApi.FullViewContext:input_type -> api.v1.FullViewContextRequest
	26, // 32: api.v1.LuiContentApi.LuiUserLlmTails:input_type -> api.v1.LuiUserLlmTailsRequest
	27, // 33: api.v1.LuiContentApi.AIEyeFingerImageJudge:input_type -> api.v1.AIEyeFingerImageJudgeRequest
	28, // 34: api.v1.LuiContentApi.ZhWordConfirm:input_type -> api.v1.LuiNluConfirmRequest
	0,  // 35: api.v1.LuiContentApi.UpdateMemoryContextLlmResponse:input_type -> api.v1.LlmResponse
	20, // 36: api.v1.LuiContentApi.GetLLMMemoryContext:input_type -> api.v1.LLMMemoryContextRequest
	29, // 37: api.v1.LuiContentApi.AddLLMMemoryContext:input_type -> api.v1.AddLLMMemoryContextRequest
	31, // 38: api.v1.LuiContentApi.ExitLuiScene:input_type -> api.v1.ExitLuiSceneRequest
	6,  // 39: api.v1.UCenterApi.LuiUserInfo:input_type -> api.v1.LuiUserInfoRequest
	47, // 40: api.v1.LuiContentApi.LuiNluResult:output_type -> google.protobuf.Struct
	47, // 41: api.v1.LuiContentApi.LuiNlu4Lui:output_type -> google.protobuf.Struct
	47, // 42: api.v1.LuiContentApi.LuiSug2Lui:output_type -> google.protobuf.Struct
	10, // 43: api.v1.LuiContentApi.LuiUserChoice:output_type -> api.v1.LuiUserChoiceReply
	13, // 44: api.v1.LuiContentApi.LuiToolsUserChoice:output_type -> api.v1.LuiToolsUserChoiceReply
	15, // 45: api.v1.LuiContentApi.QueryTraceFeedback:output_type -> api.v1.QueryTraceFeedbackReply
	47, // 46: api.v1.LuiContentApi.LuiSimulateQuery:output_type -> google.protobuf.Struct
	47, // 47: api.v1.LuiContentApi.RejectNLU:output_type -> google.protobuf.Struct
	19, // 48: api.v1.LuiContentApi.MemoryContext:output_type -> api.v1.MemoryContextReply
	25, // 49: api.v1.LuiContentApi.LuiUserLlmSug:output_type -> api.v1.LuiUserLlmSugReply
	23, // 50: api.v1.LuiContentApi.FullViewContext:output_type -> api.v1.FullViewContextReply
	47, // 51: api.v1.LuiContentApi.LuiUserLlmTails:output_type -> google.protobuf.Struct
	47, // 52: api.v1.LuiContentApi.AIEyeFingerImageJudge:output_type -> google.protobuf.Struct
	47, // 53: api.v1.LuiContentApi.ZhWordConfirm:output_type -> google.protobuf.Struct
	47, // 54: api.v1.LuiContentApi.UpdateMemoryContextLlmResponse:output_type -> google.protobuf.Struct
	21, // 55: api.v1.LuiContentApi.GetLLMMemoryContext:output_type -> api.v1.LLMMemoryContextReply
	30, // 56: api.v1.LuiContentApi.AddLLMMemoryContext:output_type -> api.v1.AddLLMMemoryContextReply
	32, // 57: api.v1.LuiContentApi.ExitLuiScene:output_type -> api.v1.ExitLuiSceneReply
	7,  // 58: api.v1.UCenterApi.LuiUserInfo:output_type -> api.v1.LuiUserInfoReply
	40, // [40:59] is the sub-list for method output_type
	21, // [21:40] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_api_lui_v1_api_proto_init() }
func file_api_lui_v1_api_proto_init() {
	if File_api_lui_v1_api_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_lui_v1_api_proto_rawDesc), len(file_api_lui_v1_api_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   47,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_api_lui_v1_api_proto_goTypes,
		DependencyIndexes: file_api_lui_v1_api_proto_depIdxs,
		MessageInfos:      file_api_lui_v1_api_proto_msgTypes,
	}.Build()
	File_api_lui_v1_api_proto = out.File
	file_api_lui_v1_api_proto_goTypes = nil
	file_api_lui_v1_api_proto_depIdxs = nil
}
