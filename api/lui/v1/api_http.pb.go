// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.7.3
// - protoc             v3.20.3
// source: api/lui/v1/api.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationLuiContentApiAIEyeFingerImageJudge = "/api.v1.LuiContentApi/AIEyeFingerImageJudge"
const OperationLuiContentApiAddLLMMemoryContext = "/api.v1.LuiContentApi/AddLLMMemoryContext"
const OperationLuiContentApiExitLuiScene = "/api.v1.LuiContentApi/ExitLuiScene"
const OperationLuiContentApiFullViewContext = "/api.v1.LuiContentApi/FullViewContext"
const OperationLuiContentApiGetLLMMemoryContext = "/api.v1.LuiContentApi/GetLLMMemoryContext"
const OperationLuiContentApiLuiNlu4Lui = "/api.v1.LuiContentApi/LuiNlu4Lui"
const OperationLuiContentApiLuiNluResult = "/api.v1.LuiContentApi/LuiNluResult"
const OperationLuiContentApiLuiSimulateQuery = "/api.v1.LuiContentApi/LuiSimulateQuery"
const OperationLuiContentApiLuiSug2Lui = "/api.v1.LuiContentApi/LuiSug2Lui"
const OperationLuiContentApiLuiToolsUserChoice = "/api.v1.LuiContentApi/LuiToolsUserChoice"
const OperationLuiContentApiLuiUserChoice = "/api.v1.LuiContentApi/LuiUserChoice"
const OperationLuiContentApiLuiUserLlmSug = "/api.v1.LuiContentApi/LuiUserLlmSug"
const OperationLuiContentApiLuiUserLlmTails = "/api.v1.LuiContentApi/LuiUserLlmTails"
const OperationLuiContentApiMemoryContext = "/api.v1.LuiContentApi/MemoryContext"
const OperationLuiContentApiQueryTraceFeedback = "/api.v1.LuiContentApi/QueryTraceFeedback"
const OperationLuiContentApiRejectNLU = "/api.v1.LuiContentApi/RejectNLU"
const OperationLuiContentApiUpdateMemoryContextLlmResponse = "/api.v1.LuiContentApi/UpdateMemoryContextLlmResponse"
const OperationLuiContentApiZhWordConfirm = "/api.v1.LuiContentApi/ZhWordConfirm"

type LuiContentApiHTTPServer interface {
	// AIEyeFingerImageJudge 用户大模型Tails
	AIEyeFingerImageJudge(context.Context, *AIEyeFingerImageJudgeRequest) (*structpb.Struct, error)
	AddLLMMemoryContext(context.Context, *AddLLMMemoryContextRequest) (*AddLLMMemoryContextReply, error)
	// ExitLuiScene 用户主动退出某个场景
	ExitLuiScene(context.Context, *ExitLuiSceneRequest) (*ExitLuiSceneReply, error)
	// FullViewContext 全屏对话页上下文
	FullViewContext(context.Context, *FullViewContextRequest) (*FullViewContextReply, error)
	GetLLMMemoryContext(context.Context, *LLMMemoryContextRequest) (*LLMMemoryContextReply, error)
	// LuiNlu4Lui 二次确认调用
	LuiNlu4Lui(context.Context, *LuiNluRequest) (*structpb.Struct, error)
	// LuiNluResult nlu数据
	LuiNluResult(context.Context, *LuiNluRequest) (*structpb.Struct, error)
	// LuiSimulateQueryLuiSimulateQuery
	LuiSimulateQuery(context.Context, *LuiSimulateQueryRequest) (*structpb.Struct, error)
	// LuiSug2Lui sug点击调用
	LuiSug2Lui(context.Context, *LuiSugRequest) (*structpb.Struct, error)
	// LuiToolsUserChoice LUI资源 用户选择的版本 from工具
	LuiToolsUserChoice(context.Context, *LuiToolsUserChoiceRequest) (*LuiToolsUserChoiceReply, error)
	// LuiUserChoice LUI资源 用户选择的版本 from学习系统
	LuiUserChoice(context.Context, *LuiUserChoiceRequest) (*LuiUserChoiceReply, error)
	// LuiUserLlmSug 用户大模型SUG
	LuiUserLlmSug(context.Context, *LuiUserLlmSugRequest) (*LuiUserLlmSugReply, error)
	// LuiUserLlmTails 用户大模型Tails
	LuiUserLlmTails(context.Context, *LuiUserLlmTailsRequest) (*structpb.Struct, error)
	// MemoryContext 拒识接口上下文呢
	MemoryContext(context.Context, *MemoryContextRequest) (*MemoryContextReply, error)
	QueryTraceFeedback(context.Context, *QueryTraceFeedbackRequest) (*QueryTraceFeedbackReply, error)
	RejectNLU(context.Context, *LuiRejectRequest) (*structpb.Struct, error)
	UpdateMemoryContextLlmResponse(context.Context, *LlmResponse) (*structpb.Struct, error)
	// ZhWordConfirm 中文字词确认
	ZhWordConfirm(context.Context, *LuiNluConfirmRequest) (*structpb.Struct, error)
}

func RegisterLuiContentApiHTTPServer(s *http.Server, srv LuiContentApiHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/lui/nlu", _LuiContentApi_LuiNluResult0_HTTP_Handler(srv))
	r.POST("/v1/lui/nlu-lui", _LuiContentApi_LuiNlu4Lui0_HTTP_Handler(srv))
	r.POST("/v1/lui/sug-lui", _LuiContentApi_LuiSug2Lui0_HTTP_Handler(srv))
	r.POST("/v1/lui/choice", _LuiContentApi_LuiUserChoice0_HTTP_Handler(srv))
	r.POST("/v1/lui/tools/choice", _LuiContentApi_LuiToolsUserChoice0_HTTP_Handler(srv))
	r.POST("/v1/lui/trace/feedback", _LuiContentApi_QueryTraceFeedback0_HTTP_Handler(srv))
	r.POST("/v1/lui/simulate-query", _LuiContentApi_LuiSimulateQuery0_HTTP_Handler(srv))
	r.POST("/v1/lui/nlu-reject", _LuiContentApi_RejectNLU0_HTTP_Handler(srv))
	r.POST("/v1/lui/memory-context", _LuiContentApi_MemoryContext0_HTTP_Handler(srv))
	r.POST("/v1/lui/user-llm-sug", _LuiContentApi_LuiUserLlmSug0_HTTP_Handler(srv))
	r.POST("/v1/lui/full-view-context", _LuiContentApi_FullViewContext0_HTTP_Handler(srv))
	r.POST("/v1/lui/user-llm-tails", _LuiContentApi_LuiUserLlmTails0_HTTP_Handler(srv))
	r.POST("/v1/lui/ai-eye-image-judge", _LuiContentApi_AIEyeFingerImageJudge0_HTTP_Handler(srv))
	r.POST("/v1/lui/zh-word-confirm", _LuiContentApi_ZhWordConfirm0_HTTP_Handler(srv))
	r.POST("/v1/lui/updateMemoryContextLlmResponse", _LuiContentApi_UpdateMemoryContextLlmResponse0_HTTP_Handler(srv))
	r.POST("/v1/lui/llm-memory-context", _LuiContentApi_GetLLMMemoryContext0_HTTP_Handler(srv))
	r.POST("/v1/lui/add-llm-memory-context", _LuiContentApi_AddLLMMemoryContext0_HTTP_Handler(srv))
	r.POST("/v1/lui/exit-lui-scene", _LuiContentApi_ExitLuiScene0_HTTP_Handler(srv))
}

func _LuiContentApi_LuiNluResult0_HTTP_Handler(srv LuiContentApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LuiNluRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLuiContentApiLuiNluResult)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LuiNluResult(ctx, req.(*LuiNluRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*structpb.Struct)
		return ctx.Result(200, reply)
	}
}

func _LuiContentApi_LuiNlu4Lui0_HTTP_Handler(srv LuiContentApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LuiNluRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLuiContentApiLuiNlu4Lui)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LuiNlu4Lui(ctx, req.(*LuiNluRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*structpb.Struct)
		return ctx.Result(200, reply)
	}
}

func _LuiContentApi_LuiSug2Lui0_HTTP_Handler(srv LuiContentApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LuiSugRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLuiContentApiLuiSug2Lui)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LuiSug2Lui(ctx, req.(*LuiSugRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*structpb.Struct)
		return ctx.Result(200, reply)
	}
}

func _LuiContentApi_LuiUserChoice0_HTTP_Handler(srv LuiContentApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LuiUserChoiceRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLuiContentApiLuiUserChoice)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LuiUserChoice(ctx, req.(*LuiUserChoiceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LuiUserChoiceReply)
		return ctx.Result(200, reply)
	}
}

func _LuiContentApi_LuiToolsUserChoice0_HTTP_Handler(srv LuiContentApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LuiToolsUserChoiceRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLuiContentApiLuiToolsUserChoice)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LuiToolsUserChoice(ctx, req.(*LuiToolsUserChoiceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LuiToolsUserChoiceReply)
		return ctx.Result(200, reply)
	}
}

func _LuiContentApi_QueryTraceFeedback0_HTTP_Handler(srv LuiContentApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryTraceFeedbackRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLuiContentApiQueryTraceFeedback)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryTraceFeedback(ctx, req.(*QueryTraceFeedbackRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryTraceFeedbackReply)
		return ctx.Result(200, reply)
	}
}

func _LuiContentApi_LuiSimulateQuery0_HTTP_Handler(srv LuiContentApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LuiSimulateQueryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLuiContentApiLuiSimulateQuery)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LuiSimulateQuery(ctx, req.(*LuiSimulateQueryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*structpb.Struct)
		return ctx.Result(200, reply)
	}
}

func _LuiContentApi_RejectNLU0_HTTP_Handler(srv LuiContentApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LuiRejectRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLuiContentApiRejectNLU)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RejectNLU(ctx, req.(*LuiRejectRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*structpb.Struct)
		return ctx.Result(200, reply)
	}
}

func _LuiContentApi_MemoryContext0_HTTP_Handler(srv LuiContentApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in MemoryContextRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLuiContentApiMemoryContext)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.MemoryContext(ctx, req.(*MemoryContextRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*MemoryContextReply)
		return ctx.Result(200, reply)
	}
}

func _LuiContentApi_LuiUserLlmSug0_HTTP_Handler(srv LuiContentApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LuiUserLlmSugRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLuiContentApiLuiUserLlmSug)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LuiUserLlmSug(ctx, req.(*LuiUserLlmSugRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LuiUserLlmSugReply)
		return ctx.Result(200, reply)
	}
}

func _LuiContentApi_FullViewContext0_HTTP_Handler(srv LuiContentApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FullViewContextRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLuiContentApiFullViewContext)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FullViewContext(ctx, req.(*FullViewContextRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FullViewContextReply)
		return ctx.Result(200, reply)
	}
}

func _LuiContentApi_LuiUserLlmTails0_HTTP_Handler(srv LuiContentApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LuiUserLlmTailsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLuiContentApiLuiUserLlmTails)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LuiUserLlmTails(ctx, req.(*LuiUserLlmTailsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*structpb.Struct)
		return ctx.Result(200, reply)
	}
}

func _LuiContentApi_AIEyeFingerImageJudge0_HTTP_Handler(srv LuiContentApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AIEyeFingerImageJudgeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLuiContentApiAIEyeFingerImageJudge)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AIEyeFingerImageJudge(ctx, req.(*AIEyeFingerImageJudgeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*structpb.Struct)
		return ctx.Result(200, reply)
	}
}

func _LuiContentApi_ZhWordConfirm0_HTTP_Handler(srv LuiContentApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LuiNluConfirmRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLuiContentApiZhWordConfirm)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ZhWordConfirm(ctx, req.(*LuiNluConfirmRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*structpb.Struct)
		return ctx.Result(200, reply)
	}
}

func _LuiContentApi_UpdateMemoryContextLlmResponse0_HTTP_Handler(srv LuiContentApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LlmResponse
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLuiContentApiUpdateMemoryContextLlmResponse)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateMemoryContextLlmResponse(ctx, req.(*LlmResponse))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*structpb.Struct)
		return ctx.Result(200, reply)
	}
}

func _LuiContentApi_GetLLMMemoryContext0_HTTP_Handler(srv LuiContentApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LLMMemoryContextRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLuiContentApiGetLLMMemoryContext)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLLMMemoryContext(ctx, req.(*LLMMemoryContextRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LLMMemoryContextReply)
		return ctx.Result(200, reply)
	}
}

func _LuiContentApi_AddLLMMemoryContext0_HTTP_Handler(srv LuiContentApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddLLMMemoryContextRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLuiContentApiAddLLMMemoryContext)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddLLMMemoryContext(ctx, req.(*AddLLMMemoryContextRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddLLMMemoryContextReply)
		return ctx.Result(200, reply)
	}
}

func _LuiContentApi_ExitLuiScene0_HTTP_Handler(srv LuiContentApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExitLuiSceneRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLuiContentApiExitLuiScene)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExitLuiScene(ctx, req.(*ExitLuiSceneRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExitLuiSceneReply)
		return ctx.Result(200, reply)
	}
}

type LuiContentApiHTTPClient interface {
	AIEyeFingerImageJudge(ctx context.Context, req *AIEyeFingerImageJudgeRequest, opts ...http.CallOption) (rsp *structpb.Struct, err error)
	AddLLMMemoryContext(ctx context.Context, req *AddLLMMemoryContextRequest, opts ...http.CallOption) (rsp *AddLLMMemoryContextReply, err error)
	ExitLuiScene(ctx context.Context, req *ExitLuiSceneRequest, opts ...http.CallOption) (rsp *ExitLuiSceneReply, err error)
	FullViewContext(ctx context.Context, req *FullViewContextRequest, opts ...http.CallOption) (rsp *FullViewContextReply, err error)
	GetLLMMemoryContext(ctx context.Context, req *LLMMemoryContextRequest, opts ...http.CallOption) (rsp *LLMMemoryContextReply, err error)
	LuiNlu4Lui(ctx context.Context, req *LuiNluRequest, opts ...http.CallOption) (rsp *structpb.Struct, err error)
	LuiNluResult(ctx context.Context, req *LuiNluRequest, opts ...http.CallOption) (rsp *structpb.Struct, err error)
	LuiSimulateQuery(ctx context.Context, req *LuiSimulateQueryRequest, opts ...http.CallOption) (rsp *structpb.Struct, err error)
	LuiSug2Lui(ctx context.Context, req *LuiSugRequest, opts ...http.CallOption) (rsp *structpb.Struct, err error)
	LuiToolsUserChoice(ctx context.Context, req *LuiToolsUserChoiceRequest, opts ...http.CallOption) (rsp *LuiToolsUserChoiceReply, err error)
	LuiUserChoice(ctx context.Context, req *LuiUserChoiceRequest, opts ...http.CallOption) (rsp *LuiUserChoiceReply, err error)
	LuiUserLlmSug(ctx context.Context, req *LuiUserLlmSugRequest, opts ...http.CallOption) (rsp *LuiUserLlmSugReply, err error)
	LuiUserLlmTails(ctx context.Context, req *LuiUserLlmTailsRequest, opts ...http.CallOption) (rsp *structpb.Struct, err error)
	MemoryContext(ctx context.Context, req *MemoryContextRequest, opts ...http.CallOption) (rsp *MemoryContextReply, err error)
	QueryTraceFeedback(ctx context.Context, req *QueryTraceFeedbackRequest, opts ...http.CallOption) (rsp *QueryTraceFeedbackReply, err error)
	RejectNLU(ctx context.Context, req *LuiRejectRequest, opts ...http.CallOption) (rsp *structpb.Struct, err error)
	UpdateMemoryContextLlmResponse(ctx context.Context, req *LlmResponse, opts ...http.CallOption) (rsp *structpb.Struct, err error)
	ZhWordConfirm(ctx context.Context, req *LuiNluConfirmRequest, opts ...http.CallOption) (rsp *structpb.Struct, err error)
}

type LuiContentApiHTTPClientImpl struct {
	cc *http.Client
}

func NewLuiContentApiHTTPClient(client *http.Client) LuiContentApiHTTPClient {
	return &LuiContentApiHTTPClientImpl{client}
}

func (c *LuiContentApiHTTPClientImpl) AIEyeFingerImageJudge(ctx context.Context, in *AIEyeFingerImageJudgeRequest, opts ...http.CallOption) (*structpb.Struct, error) {
	var out structpb.Struct
	pattern := "/v1/lui/ai-eye-image-judge"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLuiContentApiAIEyeFingerImageJudge))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LuiContentApiHTTPClientImpl) AddLLMMemoryContext(ctx context.Context, in *AddLLMMemoryContextRequest, opts ...http.CallOption) (*AddLLMMemoryContextReply, error) {
	var out AddLLMMemoryContextReply
	pattern := "/v1/lui/add-llm-memory-context"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLuiContentApiAddLLMMemoryContext))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LuiContentApiHTTPClientImpl) ExitLuiScene(ctx context.Context, in *ExitLuiSceneRequest, opts ...http.CallOption) (*ExitLuiSceneReply, error) {
	var out ExitLuiSceneReply
	pattern := "/v1/lui/exit-lui-scene"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLuiContentApiExitLuiScene))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LuiContentApiHTTPClientImpl) FullViewContext(ctx context.Context, in *FullViewContextRequest, opts ...http.CallOption) (*FullViewContextReply, error) {
	var out FullViewContextReply
	pattern := "/v1/lui/full-view-context"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLuiContentApiFullViewContext))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LuiContentApiHTTPClientImpl) GetLLMMemoryContext(ctx context.Context, in *LLMMemoryContextRequest, opts ...http.CallOption) (*LLMMemoryContextReply, error) {
	var out LLMMemoryContextReply
	pattern := "/v1/lui/llm-memory-context"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLuiContentApiGetLLMMemoryContext))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LuiContentApiHTTPClientImpl) LuiNlu4Lui(ctx context.Context, in *LuiNluRequest, opts ...http.CallOption) (*structpb.Struct, error) {
	var out structpb.Struct
	pattern := "/v1/lui/nlu-lui"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLuiContentApiLuiNlu4Lui))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LuiContentApiHTTPClientImpl) LuiNluResult(ctx context.Context, in *LuiNluRequest, opts ...http.CallOption) (*structpb.Struct, error) {
	var out structpb.Struct
	pattern := "/v1/lui/nlu"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLuiContentApiLuiNluResult))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LuiContentApiHTTPClientImpl) LuiSimulateQuery(ctx context.Context, in *LuiSimulateQueryRequest, opts ...http.CallOption) (*structpb.Struct, error) {
	var out structpb.Struct
	pattern := "/v1/lui/simulate-query"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLuiContentApiLuiSimulateQuery))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LuiContentApiHTTPClientImpl) LuiSug2Lui(ctx context.Context, in *LuiSugRequest, opts ...http.CallOption) (*structpb.Struct, error) {
	var out structpb.Struct
	pattern := "/v1/lui/sug-lui"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLuiContentApiLuiSug2Lui))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LuiContentApiHTTPClientImpl) LuiToolsUserChoice(ctx context.Context, in *LuiToolsUserChoiceRequest, opts ...http.CallOption) (*LuiToolsUserChoiceReply, error) {
	var out LuiToolsUserChoiceReply
	pattern := "/v1/lui/tools/choice"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLuiContentApiLuiToolsUserChoice))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LuiContentApiHTTPClientImpl) LuiUserChoice(ctx context.Context, in *LuiUserChoiceRequest, opts ...http.CallOption) (*LuiUserChoiceReply, error) {
	var out LuiUserChoiceReply
	pattern := "/v1/lui/choice"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLuiContentApiLuiUserChoice))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LuiContentApiHTTPClientImpl) LuiUserLlmSug(ctx context.Context, in *LuiUserLlmSugRequest, opts ...http.CallOption) (*LuiUserLlmSugReply, error) {
	var out LuiUserLlmSugReply
	pattern := "/v1/lui/user-llm-sug"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLuiContentApiLuiUserLlmSug))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LuiContentApiHTTPClientImpl) LuiUserLlmTails(ctx context.Context, in *LuiUserLlmTailsRequest, opts ...http.CallOption) (*structpb.Struct, error) {
	var out structpb.Struct
	pattern := "/v1/lui/user-llm-tails"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLuiContentApiLuiUserLlmTails))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LuiContentApiHTTPClientImpl) MemoryContext(ctx context.Context, in *MemoryContextRequest, opts ...http.CallOption) (*MemoryContextReply, error) {
	var out MemoryContextReply
	pattern := "/v1/lui/memory-context"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLuiContentApiMemoryContext))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LuiContentApiHTTPClientImpl) QueryTraceFeedback(ctx context.Context, in *QueryTraceFeedbackRequest, opts ...http.CallOption) (*QueryTraceFeedbackReply, error) {
	var out QueryTraceFeedbackReply
	pattern := "/v1/lui/trace/feedback"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLuiContentApiQueryTraceFeedback))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LuiContentApiHTTPClientImpl) RejectNLU(ctx context.Context, in *LuiRejectRequest, opts ...http.CallOption) (*structpb.Struct, error) {
	var out structpb.Struct
	pattern := "/v1/lui/nlu-reject"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLuiContentApiRejectNLU))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LuiContentApiHTTPClientImpl) UpdateMemoryContextLlmResponse(ctx context.Context, in *LlmResponse, opts ...http.CallOption) (*structpb.Struct, error) {
	var out structpb.Struct
	pattern := "/v1/lui/updateMemoryContextLlmResponse"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLuiContentApiUpdateMemoryContextLlmResponse))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LuiContentApiHTTPClientImpl) ZhWordConfirm(ctx context.Context, in *LuiNluConfirmRequest, opts ...http.CallOption) (*structpb.Struct, error) {
	var out structpb.Struct
	pattern := "/v1/lui/zh-word-confirm"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLuiContentApiZhWordConfirm))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

const OperationUCenterApiLuiUserInfo = "/api.v1.UCenterApi/LuiUserInfo"

type UCenterApiHTTPServer interface {
	// LuiUserInfo 用户信息
	LuiUserInfo(context.Context, *LuiUserInfoRequest) (*LuiUserInfoReply, error)
}

func RegisterUCenterApiHTTPServer(s *http.Server, srv UCenterApiHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/lui/user-info", _UCenterApi_LuiUserInfo0_HTTP_Handler(srv))
}

func _UCenterApi_LuiUserInfo0_HTTP_Handler(srv UCenterApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LuiUserInfoRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUCenterApiLuiUserInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LuiUserInfo(ctx, req.(*LuiUserInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LuiUserInfoReply)
		return ctx.Result(200, reply)
	}
}

type UCenterApiHTTPClient interface {
	LuiUserInfo(ctx context.Context, req *LuiUserInfoRequest, opts ...http.CallOption) (rsp *LuiUserInfoReply, err error)
}

type UCenterApiHTTPClientImpl struct {
	cc *http.Client
}

func NewUCenterApiHTTPClient(client *http.Client) UCenterApiHTTPClient {
	return &UCenterApiHTTPClientImpl{client}
}

func (c *UCenterApiHTTPClientImpl) LuiUserInfo(ctx context.Context, in *LuiUserInfoRequest, opts ...http.CallOption) (*LuiUserInfoReply, error) {
	var out LuiUserInfoReply
	pattern := "/v1/lui/user-info"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUCenterApiLuiUserInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
