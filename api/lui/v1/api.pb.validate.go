// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/lui/v1/api.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on LlmResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LlmResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LlmResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LlmResponseMultiError, or
// nil if none found.
func (m *LlmResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *LlmResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LlmSkill

	// no validation rules for LlmModel

	// no validation rules for Response

	// no validation rules for ImageUrl

	// no validation rules for VideoUrl

	// no validation rules for LlmResponse

	// no validation rules for DialogueId

	// no validation rules for SessionId

	// no validation rules for Type

	// no validation rules for Cover

	// no validation rules for CoverTitle

	// no validation rules for Url

	// no validation rules for TtsInfo

	// no validation rules for MixedModalQuery

	if all {
		switch v := interface{}(m.GetMixedModalResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LlmResponseValidationError{
					field:  "MixedModalResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LlmResponseValidationError{
					field:  "MixedModalResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMixedModalResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LlmResponseValidationError{
				field:  "MixedModalResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TtsNorm

	if len(errors) > 0 {
		return LlmResponseMultiError(errors)
	}

	return nil
}

// LlmResponseMultiError is an error wrapping multiple validation errors
// returned by LlmResponse.ValidateAll() if the designated constraints aren't met.
type LlmResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LlmResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LlmResponseMultiError) AllErrors() []error { return m }

// LlmResponseValidationError is the validation error returned by
// LlmResponse.Validate if the designated constraints aren't met.
type LlmResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LlmResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LlmResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LlmResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LlmResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LlmResponseValidationError) ErrorName() string { return "LlmResponseValidationError" }

// Error satisfies the builtin error interface
func (e LlmResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLlmResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LlmResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LlmResponseValidationError{}

// Validate checks the field values on LuiNluRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LuiNluRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiNluRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LuiNluRequestMultiError, or
// nil if none found.
func (m *LuiNluRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiNluRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	// no validation rules for AsrInfo

	// no validation rules for AsrPinyin

	// no validation rules for Location

	// no validation rules for Grade

	// no validation rules for TalId

	for idx, item := range m.GetSlotFillList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LuiNluRequestValidationError{
						field:  fmt.Sprintf("SlotFillList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LuiNluRequestValidationError{
						field:  fmt.Sprintf("SlotFillList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LuiNluRequestValidationError{
					field:  fmt.Sprintf("SlotFillList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for GradeId

	// no validation rules for BizType

	// no validation rules for Continuous

	// no validation rules for SentenceId

	// no validation rules for WakeUpType

	// no validation rules for PagePrompt

	// no validation rules for SceneMode

	if all {
		switch v := interface{}(m.GetLlmAgent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LuiNluRequestValidationError{
					field:  "LlmAgent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LuiNluRequestValidationError{
					field:  "LlmAgent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLlmAgent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LuiNluRequestValidationError{
				field:  "LlmAgent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RnVersion

	// no validation rules for ScreenMode

	for idx, item := range m.GetImagePrompt() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LuiNluRequestValidationError{
						field:  fmt.Sprintf("ImagePrompt[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LuiNluRequestValidationError{
						field:  fmt.Sprintf("ImagePrompt[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LuiNluRequestValidationError{
					field:  fmt.Sprintf("ImagePrompt[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for BizExtra

	// no validation rules for From

	// no validation rules for AsrStartTimestamp

	// no validation rules for AsrEndTimestamp

	if len(errors) > 0 {
		return LuiNluRequestMultiError(errors)
	}

	return nil
}

// LuiNluRequestMultiError is an error wrapping multiple validation errors
// returned by LuiNluRequest.ValidateAll() if the designated constraints
// aren't met.
type LuiNluRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiNluRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiNluRequestMultiError) AllErrors() []error { return m }

// LuiNluRequestValidationError is the validation error returned by
// LuiNluRequest.Validate if the designated constraints aren't met.
type LuiNluRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiNluRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiNluRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiNluRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiNluRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiNluRequestValidationError) ErrorName() string { return "LuiNluRequestValidationError" }

// Error satisfies the builtin error interface
func (e LuiNluRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiNluRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiNluRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiNluRequestValidationError{}

// Validate checks the field values on LlmAgent with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LlmAgent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LlmAgent with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LlmAgentMultiError, or nil
// if none found.
func (m *LlmAgent) ValidateAll() error {
	return m.validate(true)
}

func (m *LlmAgent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Status

	if len(errors) > 0 {
		return LlmAgentMultiError(errors)
	}

	return nil
}

// LlmAgentMultiError is an error wrapping multiple validation errors returned
// by LlmAgent.ValidateAll() if the designated constraints aren't met.
type LlmAgentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LlmAgentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LlmAgentMultiError) AllErrors() []error { return m }

// LlmAgentValidationError is the validation error returned by
// LlmAgent.Validate if the designated constraints aren't met.
type LlmAgentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LlmAgentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LlmAgentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LlmAgentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LlmAgentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LlmAgentValidationError) ErrorName() string { return "LlmAgentValidationError" }

// Error satisfies the builtin error interface
func (e LlmAgentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLlmAgent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LlmAgentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LlmAgentValidationError{}

// Validate checks the field values on SlotFill with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SlotFill) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SlotFill with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SlotFillMultiError, or nil
// if none found.
func (m *SlotFill) ValidateAll() error {
	return m.validate(true)
}

func (m *SlotFill) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Key

	// no validation rules for Id

	// no validation rules for Name

	if len(errors) > 0 {
		return SlotFillMultiError(errors)
	}

	return nil
}

// SlotFillMultiError is an error wrapping multiple validation errors returned
// by SlotFill.ValidateAll() if the designated constraints aren't met.
type SlotFillMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SlotFillMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SlotFillMultiError) AllErrors() []error { return m }

// SlotFillValidationError is the validation error returned by
// SlotFill.Validate if the designated constraints aren't met.
type SlotFillValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SlotFillValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SlotFillValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SlotFillValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SlotFillValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SlotFillValidationError) ErrorName() string { return "SlotFillValidationError" }

// Error satisfies the builtin error interface
func (e SlotFillValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSlotFill.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SlotFillValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SlotFillValidationError{}

// Validate checks the field values on LuiSugRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LuiSugRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiSugRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LuiSugRequestMultiError, or
// nil if none found.
func (m *LuiSugRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiSugRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for RequestId

	// no validation rules for SessionId

	// no validation rules for Timestamp

	// no validation rules for Source

	// no validation rules for Version

	if all {
		switch v := interface{}(m.GetAsrInput()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LuiSugRequestValidationError{
					field:  "AsrInput",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LuiSugRequestValidationError{
					field:  "AsrInput",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAsrInput()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LuiSugRequestValidationError{
				field:  "AsrInput",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUserSystem()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LuiSugRequestValidationError{
					field:  "UserSystem",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LuiSugRequestValidationError{
					field:  "UserSystem",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserSystem()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LuiSugRequestValidationError{
				field:  "UserSystem",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BizType

	// no validation rules for SceneMode

	// no validation rules for RnVersion

	// no validation rules for ScreenState

	// no validation rules for BizExtra

	// no validation rules for From

	// no validation rules for ExerciseOcr

	if len(errors) > 0 {
		return LuiSugRequestMultiError(errors)
	}

	return nil
}

// LuiSugRequestMultiError is an error wrapping multiple validation errors
// returned by LuiSugRequest.ValidateAll() if the designated constraints
// aren't met.
type LuiSugRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiSugRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiSugRequestMultiError) AllErrors() []error { return m }

// LuiSugRequestValidationError is the validation error returned by
// LuiSugRequest.Validate if the designated constraints aren't met.
type LuiSugRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiSugRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiSugRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiSugRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiSugRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiSugRequestValidationError) ErrorName() string { return "LuiSugRequestValidationError" }

// Error satisfies the builtin error interface
func (e LuiSugRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiSugRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiSugRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiSugRequestValidationError{}

// Validate checks the field values on LuiNluEnsureRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LuiNluEnsureRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiNluEnsureRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LuiNluEnsureRequestMultiError, or nil if none found.
func (m *LuiNluEnsureRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiNluEnsureRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return LuiNluEnsureRequestMultiError(errors)
	}

	return nil
}

// LuiNluEnsureRequestMultiError is an error wrapping multiple validation
// errors returned by LuiNluEnsureRequest.ValidateAll() if the designated
// constraints aren't met.
type LuiNluEnsureRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiNluEnsureRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiNluEnsureRequestMultiError) AllErrors() []error { return m }

// LuiNluEnsureRequestValidationError is the validation error returned by
// LuiNluEnsureRequest.Validate if the designated constraints aren't met.
type LuiNluEnsureRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiNluEnsureRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiNluEnsureRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiNluEnsureRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiNluEnsureRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiNluEnsureRequestValidationError) ErrorName() string {
	return "LuiNluEnsureRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LuiNluEnsureRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiNluEnsureRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiNluEnsureRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiNluEnsureRequestValidationError{}

// Validate checks the field values on LuiUserInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LuiUserInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiUserInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LuiUserInfoRequestMultiError, or nil if none found.
func (m *LuiUserInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiUserInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	if len(errors) > 0 {
		return LuiUserInfoRequestMultiError(errors)
	}

	return nil
}

// LuiUserInfoRequestMultiError is an error wrapping multiple validation errors
// returned by LuiUserInfoRequest.ValidateAll() if the designated constraints
// aren't met.
type LuiUserInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiUserInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiUserInfoRequestMultiError) AllErrors() []error { return m }

// LuiUserInfoRequestValidationError is the validation error returned by
// LuiUserInfoRequest.Validate if the designated constraints aren't met.
type LuiUserInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiUserInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiUserInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiUserInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiUserInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiUserInfoRequestValidationError) ErrorName() string {
	return "LuiUserInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LuiUserInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiUserInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiUserInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiUserInfoRequestValidationError{}

// Validate checks the field values on LuiUserInfoReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LuiUserInfoReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiUserInfoReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LuiUserInfoReplyMultiError, or nil if none found.
func (m *LuiUserInfoReply) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiUserInfoReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TalId

	// no validation rules for Grade

	// no validation rules for GradeName

	// no validation rules for Nickname

	if len(errors) > 0 {
		return LuiUserInfoReplyMultiError(errors)
	}

	return nil
}

// LuiUserInfoReplyMultiError is an error wrapping multiple validation errors
// returned by LuiUserInfoReply.ValidateAll() if the designated constraints
// aren't met.
type LuiUserInfoReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiUserInfoReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiUserInfoReplyMultiError) AllErrors() []error { return m }

// LuiUserInfoReplyValidationError is the validation error returned by
// LuiUserInfoReply.Validate if the designated constraints aren't met.
type LuiUserInfoReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiUserInfoReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiUserInfoReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiUserInfoReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiUserInfoReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiUserInfoReplyValidationError) ErrorName() string { return "LuiUserInfoReplyValidationError" }

// Error satisfies the builtin error interface
func (e LuiUserInfoReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiUserInfoReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiUserInfoReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiUserInfoReplyValidationError{}

// Validate checks the field values on Any with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Any) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Any with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AnyMultiError, or nil if none found.
func (m *Any) ValidateAll() error {
	return m.validate(true)
}

func (m *Any) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for B

	if len(errors) > 0 {
		return AnyMultiError(errors)
	}

	return nil
}

// AnyMultiError is an error wrapping multiple validation errors returned by
// Any.ValidateAll() if the designated constraints aren't met.
type AnyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnyMultiError) AllErrors() []error { return m }

// AnyValidationError is the validation error returned by Any.Validate if the
// designated constraints aren't met.
type AnyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnyValidationError) ErrorName() string { return "AnyValidationError" }

// Error satisfies the builtin error interface
func (e AnyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAny.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnyValidationError{}

// Validate checks the field values on LuiUserChoiceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LuiUserChoiceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiUserChoiceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LuiUserChoiceRequestMultiError, or nil if none found.
func (m *LuiUserChoiceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiUserChoiceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TalId

	// no validation rules for CourseSystem

	// no validation rules for Subject

	// no validation rules for Grade

	// no validation rules for DeviceId

	if len(errors) > 0 {
		return LuiUserChoiceRequestMultiError(errors)
	}

	return nil
}

// LuiUserChoiceRequestMultiError is an error wrapping multiple validation
// errors returned by LuiUserChoiceRequest.ValidateAll() if the designated
// constraints aren't met.
type LuiUserChoiceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiUserChoiceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiUserChoiceRequestMultiError) AllErrors() []error { return m }

// LuiUserChoiceRequestValidationError is the validation error returned by
// LuiUserChoiceRequest.Validate if the designated constraints aren't met.
type LuiUserChoiceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiUserChoiceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiUserChoiceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiUserChoiceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiUserChoiceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiUserChoiceRequestValidationError) ErrorName() string {
	return "LuiUserChoiceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LuiUserChoiceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiUserChoiceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiUserChoiceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiUserChoiceRequestValidationError{}

// Validate checks the field values on LuiUserChoiceReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LuiUserChoiceReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiUserChoiceReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LuiUserChoiceReplyMultiError, or nil if none found.
func (m *LuiUserChoiceReply) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiUserChoiceReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LuiUserChoiceReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LuiUserChoiceReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LuiUserChoiceReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return LuiUserChoiceReplyMultiError(errors)
	}

	return nil
}

// LuiUserChoiceReplyMultiError is an error wrapping multiple validation errors
// returned by LuiUserChoiceReply.ValidateAll() if the designated constraints
// aren't met.
type LuiUserChoiceReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiUserChoiceReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiUserChoiceReplyMultiError) AllErrors() []error { return m }

// LuiUserChoiceReplyValidationError is the validation error returned by
// LuiUserChoiceReply.Validate if the designated constraints aren't met.
type LuiUserChoiceReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiUserChoiceReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiUserChoiceReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiUserChoiceReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiUserChoiceReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiUserChoiceReplyValidationError) ErrorName() string {
	return "LuiUserChoiceReplyValidationError"
}

// Error satisfies the builtin error interface
func (e LuiUserChoiceReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiUserChoiceReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiUserChoiceReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiUserChoiceReplyValidationError{}

// Validate checks the field values on LuiUserChoiceReplyItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LuiUserChoiceReplyItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiUserChoiceReplyItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LuiUserChoiceReplyItemMultiError, or nil if none found.
func (m *LuiUserChoiceReplyItem) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiUserChoiceReplyItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CourseSystem

	// no validation rules for CourseSystemAlias

	// no validation rules for Version

	// no validation rules for VersionName

	// no validation rules for Subject

	// no validation rules for SubjectName

	if len(errors) > 0 {
		return LuiUserChoiceReplyItemMultiError(errors)
	}

	return nil
}

// LuiUserChoiceReplyItemMultiError is an error wrapping multiple validation
// errors returned by LuiUserChoiceReplyItem.ValidateAll() if the designated
// constraints aren't met.
type LuiUserChoiceReplyItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiUserChoiceReplyItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiUserChoiceReplyItemMultiError) AllErrors() []error { return m }

// LuiUserChoiceReplyItemValidationError is the validation error returned by
// LuiUserChoiceReplyItem.Validate if the designated constraints aren't met.
type LuiUserChoiceReplyItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiUserChoiceReplyItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiUserChoiceReplyItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiUserChoiceReplyItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiUserChoiceReplyItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiUserChoiceReplyItemValidationError) ErrorName() string {
	return "LuiUserChoiceReplyItemValidationError"
}

// Error satisfies the builtin error interface
func (e LuiUserChoiceReplyItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiUserChoiceReplyItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiUserChoiceReplyItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiUserChoiceReplyItemValidationError{}

// Validate checks the field values on LuiToolsUserChoiceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LuiToolsUserChoiceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiToolsUserChoiceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LuiToolsUserChoiceRequestMultiError, or nil if none found.
func (m *LuiToolsUserChoiceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiToolsUserChoiceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TalId

	// no validation rules for Subject

	if len(errors) > 0 {
		return LuiToolsUserChoiceRequestMultiError(errors)
	}

	return nil
}

// LuiToolsUserChoiceRequestMultiError is an error wrapping multiple validation
// errors returned by LuiToolsUserChoiceRequest.ValidateAll() if the
// designated constraints aren't met.
type LuiToolsUserChoiceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiToolsUserChoiceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiToolsUserChoiceRequestMultiError) AllErrors() []error { return m }

// LuiToolsUserChoiceRequestValidationError is the validation error returned by
// LuiToolsUserChoiceRequest.Validate if the designated constraints aren't met.
type LuiToolsUserChoiceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiToolsUserChoiceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiToolsUserChoiceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiToolsUserChoiceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiToolsUserChoiceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiToolsUserChoiceRequestValidationError) ErrorName() string {
	return "LuiToolsUserChoiceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LuiToolsUserChoiceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiToolsUserChoiceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiToolsUserChoiceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiToolsUserChoiceRequestValidationError{}

// Validate checks the field values on LuiToolsUserChoiceReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LuiToolsUserChoiceReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiToolsUserChoiceReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LuiToolsUserChoiceReplyMultiError, or nil if none found.
func (m *LuiToolsUserChoiceReply) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiToolsUserChoiceReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VersionName

	if len(errors) > 0 {
		return LuiToolsUserChoiceReplyMultiError(errors)
	}

	return nil
}

// LuiToolsUserChoiceReplyMultiError is an error wrapping multiple validation
// errors returned by LuiToolsUserChoiceReply.ValidateAll() if the designated
// constraints aren't met.
type LuiToolsUserChoiceReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiToolsUserChoiceReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiToolsUserChoiceReplyMultiError) AllErrors() []error { return m }

// LuiToolsUserChoiceReplyValidationError is the validation error returned by
// LuiToolsUserChoiceReply.Validate if the designated constraints aren't met.
type LuiToolsUserChoiceReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiToolsUserChoiceReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiToolsUserChoiceReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiToolsUserChoiceReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiToolsUserChoiceReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiToolsUserChoiceReplyValidationError) ErrorName() string {
	return "LuiToolsUserChoiceReplyValidationError"
}

// Error satisfies the builtin error interface
func (e LuiToolsUserChoiceReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiToolsUserChoiceReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiToolsUserChoiceReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiToolsUserChoiceReplyValidationError{}

// Validate checks the field values on QueryTraceFeedbackRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryTraceFeedbackRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryTraceFeedbackRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryTraceFeedbackRequestMultiError, or nil if none found.
func (m *QueryTraceFeedbackRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryTraceFeedbackRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SessionId

	// no validation rules for FeedbackType

	// no validation rules for Feedback

	if len(errors) > 0 {
		return QueryTraceFeedbackRequestMultiError(errors)
	}

	return nil
}

// QueryTraceFeedbackRequestMultiError is an error wrapping multiple validation
// errors returned by QueryTraceFeedbackRequest.ValidateAll() if the
// designated constraints aren't met.
type QueryTraceFeedbackRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryTraceFeedbackRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryTraceFeedbackRequestMultiError) AllErrors() []error { return m }

// QueryTraceFeedbackRequestValidationError is the validation error returned by
// QueryTraceFeedbackRequest.Validate if the designated constraints aren't met.
type QueryTraceFeedbackRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryTraceFeedbackRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryTraceFeedbackRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryTraceFeedbackRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryTraceFeedbackRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryTraceFeedbackRequestValidationError) ErrorName() string {
	return "QueryTraceFeedbackRequestValidationError"
}

// Error satisfies the builtin error interface
func (e QueryTraceFeedbackRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryTraceFeedbackRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryTraceFeedbackRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryTraceFeedbackRequestValidationError{}

// Validate checks the field values on QueryTraceFeedbackReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryTraceFeedbackReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryTraceFeedbackReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryTraceFeedbackReplyMultiError, or nil if none found.
func (m *QueryTraceFeedbackReply) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryTraceFeedbackReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return QueryTraceFeedbackReplyMultiError(errors)
	}

	return nil
}

// QueryTraceFeedbackReplyMultiError is an error wrapping multiple validation
// errors returned by QueryTraceFeedbackReply.ValidateAll() if the designated
// constraints aren't met.
type QueryTraceFeedbackReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryTraceFeedbackReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryTraceFeedbackReplyMultiError) AllErrors() []error { return m }

// QueryTraceFeedbackReplyValidationError is the validation error returned by
// QueryTraceFeedbackReply.Validate if the designated constraints aren't met.
type QueryTraceFeedbackReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryTraceFeedbackReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryTraceFeedbackReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryTraceFeedbackReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryTraceFeedbackReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryTraceFeedbackReplyValidationError) ErrorName() string {
	return "QueryTraceFeedbackReplyValidationError"
}

// Error satisfies the builtin error interface
func (e QueryTraceFeedbackReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryTraceFeedbackReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryTraceFeedbackReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryTraceFeedbackReplyValidationError{}

// Validate checks the field values on LuiSimulateQueryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LuiSimulateQueryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiSimulateQueryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LuiSimulateQueryRequestMultiError, or nil if none found.
func (m *LuiSimulateQueryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiSimulateQueryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AsrInfo

	// no validation rules for AsrPinyin

	// no validation rules for Location

	// no validation rules for Grade

	// no validation rules for GradeId

	// no validation rules for TalId

	// no validation rules for BizType

	for idx, item := range m.GetSlotFillList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LuiSimulateQueryRequestValidationError{
						field:  fmt.Sprintf("SlotFillList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LuiSimulateQueryRequestValidationError{
						field:  fmt.Sprintf("SlotFillList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LuiSimulateQueryRequestValidationError{
					field:  fmt.Sprintf("SlotFillList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return LuiSimulateQueryRequestMultiError(errors)
	}

	return nil
}

// LuiSimulateQueryRequestMultiError is an error wrapping multiple validation
// errors returned by LuiSimulateQueryRequest.ValidateAll() if the designated
// constraints aren't met.
type LuiSimulateQueryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiSimulateQueryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiSimulateQueryRequestMultiError) AllErrors() []error { return m }

// LuiSimulateQueryRequestValidationError is the validation error returned by
// LuiSimulateQueryRequest.Validate if the designated constraints aren't met.
type LuiSimulateQueryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiSimulateQueryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiSimulateQueryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiSimulateQueryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiSimulateQueryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiSimulateQueryRequestValidationError) ErrorName() string {
	return "LuiSimulateQueryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LuiSimulateQueryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiSimulateQueryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiSimulateQueryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiSimulateQueryRequestValidationError{}

// Validate checks the field values on LuiRejectRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LuiRejectRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiRejectRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LuiRejectRequestMultiError, or nil if none found.
func (m *LuiRejectRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiRejectRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	// no validation rules for DeviceId

	// no validation rules for Version

	// no validation rules for AsrInfo

	// no validation rules for WakeupType

	// no validation rules for RejectResult

	// no validation rules for Continuous

	// no validation rules for TalId

	// no validation rules for BizType

	if len(errors) > 0 {
		return LuiRejectRequestMultiError(errors)
	}

	return nil
}

// LuiRejectRequestMultiError is an error wrapping multiple validation errors
// returned by LuiRejectRequest.ValidateAll() if the designated constraints
// aren't met.
type LuiRejectRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiRejectRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiRejectRequestMultiError) AllErrors() []error { return m }

// LuiRejectRequestValidationError is the validation error returned by
// LuiRejectRequest.Validate if the designated constraints aren't met.
type LuiRejectRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiRejectRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiRejectRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiRejectRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiRejectRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiRejectRequestValidationError) ErrorName() string { return "LuiRejectRequestValidationError" }

// Error satisfies the builtin error interface
func (e LuiRejectRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiRejectRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiRejectRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiRejectRequestValidationError{}

// Validate checks the field values on MemoryContextRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemoryContextRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemoryContextRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemoryContextRequestMultiError, or nil if none found.
func (m *MemoryContextRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *MemoryContextRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TalId

	// no validation rules for BizType

	if len(errors) > 0 {
		return MemoryContextRequestMultiError(errors)
	}

	return nil
}

// MemoryContextRequestMultiError is an error wrapping multiple validation
// errors returned by MemoryContextRequest.ValidateAll() if the designated
// constraints aren't met.
type MemoryContextRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemoryContextRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemoryContextRequestMultiError) AllErrors() []error { return m }

// MemoryContextRequestValidationError is the validation error returned by
// MemoryContextRequest.Validate if the designated constraints aren't met.
type MemoryContextRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemoryContextRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemoryContextRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemoryContextRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemoryContextRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemoryContextRequestValidationError) ErrorName() string {
	return "MemoryContextRequestValidationError"
}

// Error satisfies the builtin error interface
func (e MemoryContextRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemoryContextRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemoryContextRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemoryContextRequestValidationError{}

// Validate checks the field values on MemoryContextReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemoryContextReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemoryContextReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemoryContextReplyMultiError, or nil if none found.
func (m *MemoryContextReply) ValidateAll() error {
	return m.validate(true)
}

func (m *MemoryContextReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemoryContextReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemoryContextReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemoryContextReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MemoryContextReplyMultiError(errors)
	}

	return nil
}

// MemoryContextReplyMultiError is an error wrapping multiple validation errors
// returned by MemoryContextReply.ValidateAll() if the designated constraints
// aren't met.
type MemoryContextReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemoryContextReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemoryContextReplyMultiError) AllErrors() []error { return m }

// MemoryContextReplyValidationError is the validation error returned by
// MemoryContextReply.Validate if the designated constraints aren't met.
type MemoryContextReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemoryContextReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemoryContextReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemoryContextReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemoryContextReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemoryContextReplyValidationError) ErrorName() string {
	return "MemoryContextReplyValidationError"
}

// Error satisfies the builtin error interface
func (e MemoryContextReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemoryContextReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemoryContextReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemoryContextReplyValidationError{}

// Validate checks the field values on LLMMemoryContextRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LLMMemoryContextRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LLMMemoryContextRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LLMMemoryContextRequestMultiError, or nil if none found.
func (m *LLMMemoryContextRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LLMMemoryContextRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BizType

	// no validation rules for CurrentSessionId

	// no validation rules for TalId

	// no validation rules for Version

	if len(errors) > 0 {
		return LLMMemoryContextRequestMultiError(errors)
	}

	return nil
}

// LLMMemoryContextRequestMultiError is an error wrapping multiple validation
// errors returned by LLMMemoryContextRequest.ValidateAll() if the designated
// constraints aren't met.
type LLMMemoryContextRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LLMMemoryContextRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LLMMemoryContextRequestMultiError) AllErrors() []error { return m }

// LLMMemoryContextRequestValidationError is the validation error returned by
// LLMMemoryContextRequest.Validate if the designated constraints aren't met.
type LLMMemoryContextRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LLMMemoryContextRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LLMMemoryContextRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LLMMemoryContextRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LLMMemoryContextRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LLMMemoryContextRequestValidationError) ErrorName() string {
	return "LLMMemoryContextRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LLMMemoryContextRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLLMMemoryContextRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LLMMemoryContextRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LLMMemoryContextRequestValidationError{}

// Validate checks the field values on LLMMemoryContextReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LLMMemoryContextReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LLMMemoryContextReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LLMMemoryContextReplyMultiError, or nil if none found.
func (m *LLMMemoryContextReply) ValidateAll() error {
	return m.validate(true)
}

func (m *LLMMemoryContextReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LLMMemoryContextReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LLMMemoryContextReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LLMMemoryContextReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return LLMMemoryContextReplyMultiError(errors)
	}

	return nil
}

// LLMMemoryContextReplyMultiError is an error wrapping multiple validation
// errors returned by LLMMemoryContextReply.ValidateAll() if the designated
// constraints aren't met.
type LLMMemoryContextReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LLMMemoryContextReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LLMMemoryContextReplyMultiError) AllErrors() []error { return m }

// LLMMemoryContextReplyValidationError is the validation error returned by
// LLMMemoryContextReply.Validate if the designated constraints aren't met.
type LLMMemoryContextReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LLMMemoryContextReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LLMMemoryContextReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LLMMemoryContextReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LLMMemoryContextReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LLMMemoryContextReplyValidationError) ErrorName() string {
	return "LLMMemoryContextReplyValidationError"
}

// Error satisfies the builtin error interface
func (e LLMMemoryContextReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLLMMemoryContextReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LLMMemoryContextReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LLMMemoryContextReplyValidationError{}

// Validate checks the field values on FullViewContextRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FullViewContextRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FullViewContextRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FullViewContextRequestMultiError, or nil if none found.
func (m *FullViewContextRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FullViewContextRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TalId

	// no validation rules for BizType

	if len(errors) > 0 {
		return FullViewContextRequestMultiError(errors)
	}

	return nil
}

// FullViewContextRequestMultiError is an error wrapping multiple validation
// errors returned by FullViewContextRequest.ValidateAll() if the designated
// constraints aren't met.
type FullViewContextRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FullViewContextRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FullViewContextRequestMultiError) AllErrors() []error { return m }

// FullViewContextRequestValidationError is the validation error returned by
// FullViewContextRequest.Validate if the designated constraints aren't met.
type FullViewContextRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FullViewContextRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FullViewContextRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FullViewContextRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FullViewContextRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FullViewContextRequestValidationError) ErrorName() string {
	return "FullViewContextRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FullViewContextRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFullViewContextRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FullViewContextRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FullViewContextRequestValidationError{}

// Validate checks the field values on FullViewContextReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FullViewContextReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FullViewContextReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FullViewContextReplyMultiError, or nil if none found.
func (m *FullViewContextReply) ValidateAll() error {
	return m.validate(true)
}

func (m *FullViewContextReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FullViewContextReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FullViewContextReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FullViewContextReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FullViewContextReplyMultiError(errors)
	}

	return nil
}

// FullViewContextReplyMultiError is an error wrapping multiple validation
// errors returned by FullViewContextReply.ValidateAll() if the designated
// constraints aren't met.
type FullViewContextReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FullViewContextReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FullViewContextReplyMultiError) AllErrors() []error { return m }

// FullViewContextReplyValidationError is the validation error returned by
// FullViewContextReply.Validate if the designated constraints aren't met.
type FullViewContextReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FullViewContextReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FullViewContextReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FullViewContextReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FullViewContextReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FullViewContextReplyValidationError) ErrorName() string {
	return "FullViewContextReplyValidationError"
}

// Error satisfies the builtin error interface
func (e FullViewContextReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFullViewContextReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FullViewContextReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FullViewContextReplyValidationError{}

// Validate checks the field values on LuiUserLlmSugRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LuiUserLlmSugRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiUserLlmSugRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LuiUserLlmSugRequestMultiError, or nil if none found.
func (m *LuiUserLlmSugRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiUserLlmSugRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	if len(errors) > 0 {
		return LuiUserLlmSugRequestMultiError(errors)
	}

	return nil
}

// LuiUserLlmSugRequestMultiError is an error wrapping multiple validation
// errors returned by LuiUserLlmSugRequest.ValidateAll() if the designated
// constraints aren't met.
type LuiUserLlmSugRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiUserLlmSugRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiUserLlmSugRequestMultiError) AllErrors() []error { return m }

// LuiUserLlmSugRequestValidationError is the validation error returned by
// LuiUserLlmSugRequest.Validate if the designated constraints aren't met.
type LuiUserLlmSugRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiUserLlmSugRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiUserLlmSugRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiUserLlmSugRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiUserLlmSugRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiUserLlmSugRequestValidationError) ErrorName() string {
	return "LuiUserLlmSugRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LuiUserLlmSugRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiUserLlmSugRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiUserLlmSugRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiUserLlmSugRequestValidationError{}

// Validate checks the field values on LuiUserLlmSugReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LuiUserLlmSugReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiUserLlmSugReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LuiUserLlmSugReplyMultiError, or nil if none found.
func (m *LuiUserLlmSugReply) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiUserLlmSugReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for Sug

	if len(errors) > 0 {
		return LuiUserLlmSugReplyMultiError(errors)
	}

	return nil
}

// LuiUserLlmSugReplyMultiError is an error wrapping multiple validation errors
// returned by LuiUserLlmSugReply.ValidateAll() if the designated constraints
// aren't met.
type LuiUserLlmSugReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiUserLlmSugReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiUserLlmSugReplyMultiError) AllErrors() []error { return m }

// LuiUserLlmSugReplyValidationError is the validation error returned by
// LuiUserLlmSugReply.Validate if the designated constraints aren't met.
type LuiUserLlmSugReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiUserLlmSugReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiUserLlmSugReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiUserLlmSugReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiUserLlmSugReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiUserLlmSugReplyValidationError) ErrorName() string {
	return "LuiUserLlmSugReplyValidationError"
}

// Error satisfies the builtin error interface
func (e LuiUserLlmSugReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiUserLlmSugReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiUserLlmSugReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiUserLlmSugReplyValidationError{}

// Validate checks the field values on LuiUserLlmTailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LuiUserLlmTailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiUserLlmTailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LuiUserLlmTailsRequestMultiError, or nil if none found.
func (m *LuiUserLlmTailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiUserLlmTailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for ScreenMode

	// no validation rules for BizType

	if len(errors) > 0 {
		return LuiUserLlmTailsRequestMultiError(errors)
	}

	return nil
}

// LuiUserLlmTailsRequestMultiError is an error wrapping multiple validation
// errors returned by LuiUserLlmTailsRequest.ValidateAll() if the designated
// constraints aren't met.
type LuiUserLlmTailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiUserLlmTailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiUserLlmTailsRequestMultiError) AllErrors() []error { return m }

// LuiUserLlmTailsRequestValidationError is the validation error returned by
// LuiUserLlmTailsRequest.Validate if the designated constraints aren't met.
type LuiUserLlmTailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiUserLlmTailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiUserLlmTailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiUserLlmTailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiUserLlmTailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiUserLlmTailsRequestValidationError) ErrorName() string {
	return "LuiUserLlmTailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LuiUserLlmTailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiUserLlmTailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiUserLlmTailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiUserLlmTailsRequestValidationError{}

// Validate checks the field values on AIEyeFingerImageJudgeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AIEyeFingerImageJudgeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AIEyeFingerImageJudgeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AIEyeFingerImageJudgeRequestMultiError, or nil if none found.
func (m *AIEyeFingerImageJudgeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AIEyeFingerImageJudgeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BizType

	// no validation rules for ImageUrl

	if len(errors) > 0 {
		return AIEyeFingerImageJudgeRequestMultiError(errors)
	}

	return nil
}

// AIEyeFingerImageJudgeRequestMultiError is an error wrapping multiple
// validation errors returned by AIEyeFingerImageJudgeRequest.ValidateAll() if
// the designated constraints aren't met.
type AIEyeFingerImageJudgeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AIEyeFingerImageJudgeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AIEyeFingerImageJudgeRequestMultiError) AllErrors() []error { return m }

// AIEyeFingerImageJudgeRequestValidationError is the validation error returned
// by AIEyeFingerImageJudgeRequest.Validate if the designated constraints
// aren't met.
type AIEyeFingerImageJudgeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AIEyeFingerImageJudgeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AIEyeFingerImageJudgeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AIEyeFingerImageJudgeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AIEyeFingerImageJudgeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AIEyeFingerImageJudgeRequestValidationError) ErrorName() string {
	return "AIEyeFingerImageJudgeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AIEyeFingerImageJudgeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAIEyeFingerImageJudgeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AIEyeFingerImageJudgeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AIEyeFingerImageJudgeRequestValidationError{}

// Validate checks the field values on LuiNluConfirmRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LuiNluConfirmRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiNluConfirmRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LuiNluConfirmRequestMultiError, or nil if none found.
func (m *LuiNluConfirmRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiNluConfirmRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for RequestId

	// no validation rules for SessionId

	// no validation rules for Version

	if all {
		switch v := interface{}(m.GetAsrInput()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LuiNluConfirmRequestValidationError{
					field:  "AsrInput",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LuiNluConfirmRequestValidationError{
					field:  "AsrInput",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAsrInput()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LuiNluConfirmRequestValidationError{
				field:  "AsrInput",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUserSystem()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LuiNluConfirmRequestValidationError{
					field:  "UserSystem",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LuiNluConfirmRequestValidationError{
					field:  "UserSystem",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserSystem()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LuiNluConfirmRequestValidationError{
				field:  "UserSystem",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOcrInput()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LuiNluConfirmRequestValidationError{
					field:  "OcrInput",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LuiNluConfirmRequestValidationError{
					field:  "OcrInput",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOcrInput()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LuiNluConfirmRequestValidationError{
				field:  "OcrInput",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHandleInput()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LuiNluConfirmRequestValidationError{
					field:  "HandleInput",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LuiNluConfirmRequestValidationError{
					field:  "HandleInput",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHandleInput()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LuiNluConfirmRequestValidationError{
				field:  "HandleInput",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LuiNluConfirmRequestMultiError(errors)
	}

	return nil
}

// LuiNluConfirmRequestMultiError is an error wrapping multiple validation
// errors returned by LuiNluConfirmRequest.ValidateAll() if the designated
// constraints aren't met.
type LuiNluConfirmRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiNluConfirmRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiNluConfirmRequestMultiError) AllErrors() []error { return m }

// LuiNluConfirmRequestValidationError is the validation error returned by
// LuiNluConfirmRequest.Validate if the designated constraints aren't met.
type LuiNluConfirmRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiNluConfirmRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiNluConfirmRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiNluConfirmRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiNluConfirmRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiNluConfirmRequestValidationError) ErrorName() string {
	return "LuiNluConfirmRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LuiNluConfirmRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiNluConfirmRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiNluConfirmRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiNluConfirmRequestValidationError{}

// Validate checks the field values on AddLLMMemoryContextRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddLLMMemoryContextRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddLLMMemoryContextRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddLLMMemoryContextRequestMultiError, or nil if none found.
func (m *AddLLMMemoryContextRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddLLMMemoryContextRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TalId

	// no validation rules for SessionId

	for idx, item := range m.GetMessages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AddLLMMemoryContextRequestValidationError{
						field:  fmt.Sprintf("Messages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AddLLMMemoryContextRequestValidationError{
						field:  fmt.Sprintf("Messages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AddLLMMemoryContextRequestValidationError{
					field:  fmt.Sprintf("Messages[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for BizType

	if len(errors) > 0 {
		return AddLLMMemoryContextRequestMultiError(errors)
	}

	return nil
}

// AddLLMMemoryContextRequestMultiError is an error wrapping multiple
// validation errors returned by AddLLMMemoryContextRequest.ValidateAll() if
// the designated constraints aren't met.
type AddLLMMemoryContextRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddLLMMemoryContextRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddLLMMemoryContextRequestMultiError) AllErrors() []error { return m }

// AddLLMMemoryContextRequestValidationError is the validation error returned
// by AddLLMMemoryContextRequest.Validate if the designated constraints aren't met.
type AddLLMMemoryContextRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddLLMMemoryContextRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddLLMMemoryContextRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddLLMMemoryContextRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddLLMMemoryContextRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddLLMMemoryContextRequestValidationError) ErrorName() string {
	return "AddLLMMemoryContextRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddLLMMemoryContextRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddLLMMemoryContextRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddLLMMemoryContextRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddLLMMemoryContextRequestValidationError{}

// Validate checks the field values on AddLLMMemoryContextReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddLLMMemoryContextReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddLLMMemoryContextReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddLLMMemoryContextReplyMultiError, or nil if none found.
func (m *AddLLMMemoryContextReply) ValidateAll() error {
	return m.validate(true)
}

func (m *AddLLMMemoryContextReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AddLLMMemoryContextReplyMultiError(errors)
	}

	return nil
}

// AddLLMMemoryContextReplyMultiError is an error wrapping multiple validation
// errors returned by AddLLMMemoryContextReply.ValidateAll() if the designated
// constraints aren't met.
type AddLLMMemoryContextReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddLLMMemoryContextReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddLLMMemoryContextReplyMultiError) AllErrors() []error { return m }

// AddLLMMemoryContextReplyValidationError is the validation error returned by
// AddLLMMemoryContextReply.Validate if the designated constraints aren't met.
type AddLLMMemoryContextReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddLLMMemoryContextReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddLLMMemoryContextReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddLLMMemoryContextReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddLLMMemoryContextReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddLLMMemoryContextReplyValidationError) ErrorName() string {
	return "AddLLMMemoryContextReplyValidationError"
}

// Error satisfies the builtin error interface
func (e AddLLMMemoryContextReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddLLMMemoryContextReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddLLMMemoryContextReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddLLMMemoryContextReplyValidationError{}

// Validate checks the field values on LuiSugRequest_AsrInput with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LuiSugRequest_AsrInput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiSugRequest_AsrInput with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LuiSugRequest_AsrInputMultiError, or nil if none found.
func (m *LuiSugRequest_AsrInput) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiSugRequest_AsrInput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AsrLen

	// no validation rules for AsrInfo

	// no validation rules for AsrPinyin

	if len(errors) > 0 {
		return LuiSugRequest_AsrInputMultiError(errors)
	}

	return nil
}

// LuiSugRequest_AsrInputMultiError is an error wrapping multiple validation
// errors returned by LuiSugRequest_AsrInput.ValidateAll() if the designated
// constraints aren't met.
type LuiSugRequest_AsrInputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiSugRequest_AsrInputMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiSugRequest_AsrInputMultiError) AllErrors() []error { return m }

// LuiSugRequest_AsrInputValidationError is the validation error returned by
// LuiSugRequest_AsrInput.Validate if the designated constraints aren't met.
type LuiSugRequest_AsrInputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiSugRequest_AsrInputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiSugRequest_AsrInputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiSugRequest_AsrInputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiSugRequest_AsrInputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiSugRequest_AsrInputValidationError) ErrorName() string {
	return "LuiSugRequest_AsrInputValidationError"
}

// Error satisfies the builtin error interface
func (e LuiSugRequest_AsrInputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiSugRequest_AsrInput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiSugRequest_AsrInputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiSugRequest_AsrInputValidationError{}

// Validate checks the field values on LuiSugRequest_UserSystem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LuiSugRequest_UserSystem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiSugRequest_UserSystem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LuiSugRequest_UserSystemMultiError, or nil if none found.
func (m *LuiSugRequest_UserSystem) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiSugRequest_UserSystem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Location

	// no validation rules for Grade

	if len(errors) > 0 {
		return LuiSugRequest_UserSystemMultiError(errors)
	}

	return nil
}

// LuiSugRequest_UserSystemMultiError is an error wrapping multiple validation
// errors returned by LuiSugRequest_UserSystem.ValidateAll() if the designated
// constraints aren't met.
type LuiSugRequest_UserSystemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiSugRequest_UserSystemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiSugRequest_UserSystemMultiError) AllErrors() []error { return m }

// LuiSugRequest_UserSystemValidationError is the validation error returned by
// LuiSugRequest_UserSystem.Validate if the designated constraints aren't met.
type LuiSugRequest_UserSystemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiSugRequest_UserSystemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiSugRequest_UserSystemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiSugRequest_UserSystemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiSugRequest_UserSystemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiSugRequest_UserSystemValidationError) ErrorName() string {
	return "LuiSugRequest_UserSystemValidationError"
}

// Error satisfies the builtin error interface
func (e LuiSugRequest_UserSystemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiSugRequest_UserSystem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiSugRequest_UserSystemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiSugRequest_UserSystemValidationError{}

// Validate checks the field values on MemoryContextReply_MemoryContextItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *MemoryContextReply_MemoryContextItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemoryContextReply_MemoryContextItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// MemoryContextReply_MemoryContextItemMultiError, or nil if none found.
func (m *MemoryContextReply_MemoryContextItem) ValidateAll() error {
	return m.validate(true)
}

func (m *MemoryContextReply_MemoryContextItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AsrInfo

	// no validation rules for Intent

	// no validation rules for Response

	// no validation rules for TtsInfo

	// no validation rules for RewriteQuery

	// no validation rules for TimeStamp

	// no validation rules for TtsNorm

	if len(errors) > 0 {
		return MemoryContextReply_MemoryContextItemMultiError(errors)
	}

	return nil
}

// MemoryContextReply_MemoryContextItemMultiError is an error wrapping multiple
// validation errors returned by
// MemoryContextReply_MemoryContextItem.ValidateAll() if the designated
// constraints aren't met.
type MemoryContextReply_MemoryContextItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemoryContextReply_MemoryContextItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemoryContextReply_MemoryContextItemMultiError) AllErrors() []error { return m }

// MemoryContextReply_MemoryContextItemValidationError is the validation error
// returned by MemoryContextReply_MemoryContextItem.Validate if the designated
// constraints aren't met.
type MemoryContextReply_MemoryContextItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemoryContextReply_MemoryContextItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemoryContextReply_MemoryContextItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemoryContextReply_MemoryContextItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemoryContextReply_MemoryContextItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemoryContextReply_MemoryContextItemValidationError) ErrorName() string {
	return "MemoryContextReply_MemoryContextItemValidationError"
}

// Error satisfies the builtin error interface
func (e MemoryContextReply_MemoryContextItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemoryContextReply_MemoryContextItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemoryContextReply_MemoryContextItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemoryContextReply_MemoryContextItemValidationError{}

// Validate checks the field values on LLMMemoryContextReply_ContextItem with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *LLMMemoryContextReply_ContextItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LLMMemoryContextReply_ContextItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LLMMemoryContextReply_ContextItemMultiError, or nil if none found.
func (m *LLMMemoryContextReply_ContextItem) ValidateAll() error {
	return m.validate(true)
}

func (m *LLMMemoryContextReply_ContextItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Role

	// no validation rules for Content

	// no validation rules for DialogueId

	if len(errors) > 0 {
		return LLMMemoryContextReply_ContextItemMultiError(errors)
	}

	return nil
}

// LLMMemoryContextReply_ContextItemMultiError is an error wrapping multiple
// validation errors returned by
// LLMMemoryContextReply_ContextItem.ValidateAll() if the designated
// constraints aren't met.
type LLMMemoryContextReply_ContextItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LLMMemoryContextReply_ContextItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LLMMemoryContextReply_ContextItemMultiError) AllErrors() []error { return m }

// LLMMemoryContextReply_ContextItemValidationError is the validation error
// returned by LLMMemoryContextReply_ContextItem.Validate if the designated
// constraints aren't met.
type LLMMemoryContextReply_ContextItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LLMMemoryContextReply_ContextItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LLMMemoryContextReply_ContextItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LLMMemoryContextReply_ContextItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LLMMemoryContextReply_ContextItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LLMMemoryContextReply_ContextItemValidationError) ErrorName() string {
	return "LLMMemoryContextReply_ContextItemValidationError"
}

// Error satisfies the builtin error interface
func (e LLMMemoryContextReply_ContextItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLLMMemoryContextReply_ContextItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LLMMemoryContextReply_ContextItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LLMMemoryContextReply_ContextItemValidationError{}

// Validate checks the field values on FullViewContextReply_Media with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FullViewContextReply_Media) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FullViewContextReply_Media with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FullViewContextReply_MediaMultiError, or nil if none found.
func (m *FullViewContextReply_Media) ValidateAll() error {
	return m.validate(true)
}

func (m *FullViewContextReply_Media) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Cover

	// no validation rules for Url

	// no validation rules for CoverTitle

	if len(errors) > 0 {
		return FullViewContextReply_MediaMultiError(errors)
	}

	return nil
}

// FullViewContextReply_MediaMultiError is an error wrapping multiple
// validation errors returned by FullViewContextReply_Media.ValidateAll() if
// the designated constraints aren't met.
type FullViewContextReply_MediaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FullViewContextReply_MediaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FullViewContextReply_MediaMultiError) AllErrors() []error { return m }

// FullViewContextReply_MediaValidationError is the validation error returned
// by FullViewContextReply_Media.Validate if the designated constraints aren't met.
type FullViewContextReply_MediaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FullViewContextReply_MediaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FullViewContextReply_MediaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FullViewContextReply_MediaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FullViewContextReply_MediaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FullViewContextReply_MediaValidationError) ErrorName() string {
	return "FullViewContextReply_MediaValidationError"
}

// Error satisfies the builtin error interface
func (e FullViewContextReply_MediaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFullViewContextReply_Media.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FullViewContextReply_MediaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FullViewContextReply_MediaValidationError{}

// Validate checks the field values on FullViewContextReply_XiaosiCommands with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FullViewContextReply_XiaosiCommands) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FullViewContextReply_XiaosiCommands
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FullViewContextReply_XiaosiCommandsMultiError, or nil if none found.
func (m *FullViewContextReply_XiaosiCommands) ValidateAll() error {
	return m.validate(true)
}

func (m *FullViewContextReply_XiaosiCommands) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	// no validation rules for Query

	for idx, item := range m.GetApps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FullViewContextReply_XiaosiCommandsValidationError{
						field:  fmt.Sprintf("Apps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FullViewContextReply_XiaosiCommandsValidationError{
						field:  fmt.Sprintf("Apps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FullViewContextReply_XiaosiCommandsValidationError{
					field:  fmt.Sprintf("Apps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FullViewContextReply_XiaosiCommandsMultiError(errors)
	}

	return nil
}

// FullViewContextReply_XiaosiCommandsMultiError is an error wrapping multiple
// validation errors returned by
// FullViewContextReply_XiaosiCommands.ValidateAll() if the designated
// constraints aren't met.
type FullViewContextReply_XiaosiCommandsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FullViewContextReply_XiaosiCommandsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FullViewContextReply_XiaosiCommandsMultiError) AllErrors() []error { return m }

// FullViewContextReply_XiaosiCommandsValidationError is the validation error
// returned by FullViewContextReply_XiaosiCommands.Validate if the designated
// constraints aren't met.
type FullViewContextReply_XiaosiCommandsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FullViewContextReply_XiaosiCommandsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FullViewContextReply_XiaosiCommandsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FullViewContextReply_XiaosiCommandsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FullViewContextReply_XiaosiCommandsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FullViewContextReply_XiaosiCommandsValidationError) ErrorName() string {
	return "FullViewContextReply_XiaosiCommandsValidationError"
}

// Error satisfies the builtin error interface
func (e FullViewContextReply_XiaosiCommandsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFullViewContextReply_XiaosiCommands.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FullViewContextReply_XiaosiCommandsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FullViewContextReply_XiaosiCommandsValidationError{}

// Validate checks the field values on FullViewContextReply_CommandApp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FullViewContextReply_CommandApp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FullViewContextReply_CommandApp with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FullViewContextReply_CommandAppMultiError, or nil if none found.
func (m *FullViewContextReply_CommandApp) ValidateAll() error {
	return m.validate(true)
}

func (m *FullViewContextReply_CommandApp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Icon

	if len(errors) > 0 {
		return FullViewContextReply_CommandAppMultiError(errors)
	}

	return nil
}

// FullViewContextReply_CommandAppMultiError is an error wrapping multiple
// validation errors returned by FullViewContextReply_CommandApp.ValidateAll()
// if the designated constraints aren't met.
type FullViewContextReply_CommandAppMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FullViewContextReply_CommandAppMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FullViewContextReply_CommandAppMultiError) AllErrors() []error { return m }

// FullViewContextReply_CommandAppValidationError is the validation error
// returned by FullViewContextReply_CommandApp.Validate if the designated
// constraints aren't met.
type FullViewContextReply_CommandAppValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FullViewContextReply_CommandAppValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FullViewContextReply_CommandAppValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FullViewContextReply_CommandAppValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FullViewContextReply_CommandAppValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FullViewContextReply_CommandAppValidationError) ErrorName() string {
	return "FullViewContextReply_CommandAppValidationError"
}

// Error satisfies the builtin error interface
func (e FullViewContextReply_CommandAppValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFullViewContextReply_CommandApp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FullViewContextReply_CommandAppValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FullViewContextReply_CommandAppValidationError{}

// Validate checks the field values on FullViewContextReply_FullViewContextItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *FullViewContextReply_FullViewContextItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FullViewContextReply_FullViewContextItem with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// FullViewContextReply_FullViewContextItemMultiError, or nil if none found.
func (m *FullViewContextReply_FullViewContextItem) ValidateAll() error {
	return m.validate(true)
}

func (m *FullViewContextReply_FullViewContextItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SessionId

	// no validation rules for AsrInfo

	// no validation rules for Response

	// no validation rules for TtsInfo

	// no validation rules for Source

	// no validation rules for ImageUrl

	// no validation rules for VideoUrl

	// no validation rules for IsLlm

	// no validation rules for CardType

	if all {
		switch v := interface{}(m.GetMedia()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FullViewContextReply_FullViewContextItemValidationError{
					field:  "Media",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FullViewContextReply_FullViewContextItemValidationError{
					field:  "Media",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMedia()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FullViewContextReply_FullViewContextItemValidationError{
				field:  "Media",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetXiaosiCommandList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FullViewContextReply_FullViewContextItemValidationError{
						field:  fmt.Sprintf("XiaosiCommandList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FullViewContextReply_FullViewContextItemValidationError{
						field:  fmt.Sprintf("XiaosiCommandList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FullViewContextReply_FullViewContextItemValidationError{
					field:  fmt.Sprintf("XiaosiCommandList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetRnResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FullViewContextReply_FullViewContextItemValidationError{
					field:  "RnResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FullViewContextReply_FullViewContextItemValidationError{
					field:  "RnResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRnResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FullViewContextReply_FullViewContextItemValidationError{
				field:  "RnResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ReasoningContent

	// no validation rules for TtsNorm

	if len(errors) > 0 {
		return FullViewContextReply_FullViewContextItemMultiError(errors)
	}

	return nil
}

// FullViewContextReply_FullViewContextItemMultiError is an error wrapping
// multiple validation errors returned by
// FullViewContextReply_FullViewContextItem.ValidateAll() if the designated
// constraints aren't met.
type FullViewContextReply_FullViewContextItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FullViewContextReply_FullViewContextItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FullViewContextReply_FullViewContextItemMultiError) AllErrors() []error { return m }

// FullViewContextReply_FullViewContextItemValidationError is the validation
// error returned by FullViewContextReply_FullViewContextItem.Validate if the
// designated constraints aren't met.
type FullViewContextReply_FullViewContextItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FullViewContextReply_FullViewContextItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FullViewContextReply_FullViewContextItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FullViewContextReply_FullViewContextItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FullViewContextReply_FullViewContextItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FullViewContextReply_FullViewContextItemValidationError) ErrorName() string {
	return "FullViewContextReply_FullViewContextItemValidationError"
}

// Error satisfies the builtin error interface
func (e FullViewContextReply_FullViewContextItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFullViewContextReply_FullViewContextItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FullViewContextReply_FullViewContextItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FullViewContextReply_FullViewContextItemValidationError{}

// Validate checks the field values on LuiNluConfirmRequest_AsrInput with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LuiNluConfirmRequest_AsrInput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiNluConfirmRequest_AsrInput with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// LuiNluConfirmRequest_AsrInputMultiError, or nil if none found.
func (m *LuiNluConfirmRequest_AsrInput) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiNluConfirmRequest_AsrInput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AsrLen

	// no validation rules for AsrInfo

	// no validation rules for AsrPinyin

	if len(errors) > 0 {
		return LuiNluConfirmRequest_AsrInputMultiError(errors)
	}

	return nil
}

// LuiNluConfirmRequest_AsrInputMultiError is an error wrapping multiple
// validation errors returned by LuiNluConfirmRequest_AsrInput.ValidateAll()
// if the designated constraints aren't met.
type LuiNluConfirmRequest_AsrInputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiNluConfirmRequest_AsrInputMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiNluConfirmRequest_AsrInputMultiError) AllErrors() []error { return m }

// LuiNluConfirmRequest_AsrInputValidationError is the validation error
// returned by LuiNluConfirmRequest_AsrInput.Validate if the designated
// constraints aren't met.
type LuiNluConfirmRequest_AsrInputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiNluConfirmRequest_AsrInputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiNluConfirmRequest_AsrInputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiNluConfirmRequest_AsrInputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiNluConfirmRequest_AsrInputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiNluConfirmRequest_AsrInputValidationError) ErrorName() string {
	return "LuiNluConfirmRequest_AsrInputValidationError"
}

// Error satisfies the builtin error interface
func (e LuiNluConfirmRequest_AsrInputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiNluConfirmRequest_AsrInput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiNluConfirmRequest_AsrInputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiNluConfirmRequest_AsrInputValidationError{}

// Validate checks the field values on LuiNluConfirmRequest_OcrInput with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LuiNluConfirmRequest_OcrInput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiNluConfirmRequest_OcrInput with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// LuiNluConfirmRequest_OcrInputMultiError, or nil if none found.
func (m *LuiNluConfirmRequest_OcrInput) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiNluConfirmRequest_OcrInput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OcrAlldocLen

	// no validation rules for OcrKeyLen

	// no validation rules for OcrAlldocInfo

	if len(errors) > 0 {
		return LuiNluConfirmRequest_OcrInputMultiError(errors)
	}

	return nil
}

// LuiNluConfirmRequest_OcrInputMultiError is an error wrapping multiple
// validation errors returned by LuiNluConfirmRequest_OcrInput.ValidateAll()
// if the designated constraints aren't met.
type LuiNluConfirmRequest_OcrInputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiNluConfirmRequest_OcrInputMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiNluConfirmRequest_OcrInputMultiError) AllErrors() []error { return m }

// LuiNluConfirmRequest_OcrInputValidationError is the validation error
// returned by LuiNluConfirmRequest_OcrInput.Validate if the designated
// constraints aren't met.
type LuiNluConfirmRequest_OcrInputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiNluConfirmRequest_OcrInputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiNluConfirmRequest_OcrInputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiNluConfirmRequest_OcrInputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiNluConfirmRequest_OcrInputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiNluConfirmRequest_OcrInputValidationError) ErrorName() string {
	return "LuiNluConfirmRequest_OcrInputValidationError"
}

// Error satisfies the builtin error interface
func (e LuiNluConfirmRequest_OcrInputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiNluConfirmRequest_OcrInput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiNluConfirmRequest_OcrInputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiNluConfirmRequest_OcrInputValidationError{}

// Validate checks the field values on LuiNluConfirmRequest_ConfirmRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *LuiNluConfirmRequest_ConfirmRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiNluConfirmRequest_ConfirmRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LuiNluConfirmRequest_ConfirmRequestMultiError, or nil if none found.
func (m *LuiNluConfirmRequest_ConfirmRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiNluConfirmRequest_ConfirmRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ConfirmRequestIntent

	// no validation rules for ConfirmRequestInput

	// no validation rules for ConfirmRequestFinger

	// no validation rules for ConfirmRequestPinyin

	if len(errors) > 0 {
		return LuiNluConfirmRequest_ConfirmRequestMultiError(errors)
	}

	return nil
}

// LuiNluConfirmRequest_ConfirmRequestMultiError is an error wrapping multiple
// validation errors returned by
// LuiNluConfirmRequest_ConfirmRequest.ValidateAll() if the designated
// constraints aren't met.
type LuiNluConfirmRequest_ConfirmRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiNluConfirmRequest_ConfirmRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiNluConfirmRequest_ConfirmRequestMultiError) AllErrors() []error { return m }

// LuiNluConfirmRequest_ConfirmRequestValidationError is the validation error
// returned by LuiNluConfirmRequest_ConfirmRequest.Validate if the designated
// constraints aren't met.
type LuiNluConfirmRequest_ConfirmRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiNluConfirmRequest_ConfirmRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiNluConfirmRequest_ConfirmRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiNluConfirmRequest_ConfirmRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiNluConfirmRequest_ConfirmRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiNluConfirmRequest_ConfirmRequestValidationError) ErrorName() string {
	return "LuiNluConfirmRequest_ConfirmRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LuiNluConfirmRequest_ConfirmRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiNluConfirmRequest_ConfirmRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiNluConfirmRequest_ConfirmRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiNluConfirmRequest_ConfirmRequestValidationError{}

// Validate checks the field values on LuiNluConfirmRequest_HandleInput with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *LuiNluConfirmRequest_HandleInput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiNluConfirmRequest_HandleInput with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// LuiNluConfirmRequest_HandleInputMultiError, or nil if none found.
func (m *LuiNluConfirmRequest_HandleInput) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiNluConfirmRequest_HandleInput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HandleTaskType

	// no validation rules for IsEngCorrect

	if all {
		switch v := interface{}(m.GetConfirmRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LuiNluConfirmRequest_HandleInputValidationError{
					field:  "ConfirmRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LuiNluConfirmRequest_HandleInputValidationError{
					field:  "ConfirmRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConfirmRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LuiNluConfirmRequest_HandleInputValidationError{
				field:  "ConfirmRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LuiNluConfirmRequest_HandleInputMultiError(errors)
	}

	return nil
}

// LuiNluConfirmRequest_HandleInputMultiError is an error wrapping multiple
// validation errors returned by
// LuiNluConfirmRequest_HandleInput.ValidateAll() if the designated
// constraints aren't met.
type LuiNluConfirmRequest_HandleInputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiNluConfirmRequest_HandleInputMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiNluConfirmRequest_HandleInputMultiError) AllErrors() []error { return m }

// LuiNluConfirmRequest_HandleInputValidationError is the validation error
// returned by LuiNluConfirmRequest_HandleInput.Validate if the designated
// constraints aren't met.
type LuiNluConfirmRequest_HandleInputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiNluConfirmRequest_HandleInputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiNluConfirmRequest_HandleInputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiNluConfirmRequest_HandleInputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiNluConfirmRequest_HandleInputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiNluConfirmRequest_HandleInputValidationError) ErrorName() string {
	return "LuiNluConfirmRequest_HandleInputValidationError"
}

// Error satisfies the builtin error interface
func (e LuiNluConfirmRequest_HandleInputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiNluConfirmRequest_HandleInput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiNluConfirmRequest_HandleInputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiNluConfirmRequest_HandleInputValidationError{}

// Validate checks the field values on LuiNluConfirmRequest_UserSystem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LuiNluConfirmRequest_UserSystem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LuiNluConfirmRequest_UserSystem with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// LuiNluConfirmRequest_UserSystemMultiError, or nil if none found.
func (m *LuiNluConfirmRequest_UserSystem) ValidateAll() error {
	return m.validate(true)
}

func (m *LuiNluConfirmRequest_UserSystem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Location

	// no validation rules for Grade

	// no validation rules for Gender

	// no validation rules for Nickname

	// no validation rules for Semester

	// no validation rules for TextbookVersion

	if len(errors) > 0 {
		return LuiNluConfirmRequest_UserSystemMultiError(errors)
	}

	return nil
}

// LuiNluConfirmRequest_UserSystemMultiError is an error wrapping multiple
// validation errors returned by LuiNluConfirmRequest_UserSystem.ValidateAll()
// if the designated constraints aren't met.
type LuiNluConfirmRequest_UserSystemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LuiNluConfirmRequest_UserSystemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LuiNluConfirmRequest_UserSystemMultiError) AllErrors() []error { return m }

// LuiNluConfirmRequest_UserSystemValidationError is the validation error
// returned by LuiNluConfirmRequest_UserSystem.Validate if the designated
// constraints aren't met.
type LuiNluConfirmRequest_UserSystemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LuiNluConfirmRequest_UserSystemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LuiNluConfirmRequest_UserSystemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LuiNluConfirmRequest_UserSystemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LuiNluConfirmRequest_UserSystemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LuiNluConfirmRequest_UserSystemValidationError) ErrorName() string {
	return "LuiNluConfirmRequest_UserSystemValidationError"
}

// Error satisfies the builtin error interface
func (e LuiNluConfirmRequest_UserSystemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLuiNluConfirmRequest_UserSystem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LuiNluConfirmRequest_UserSystemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LuiNluConfirmRequest_UserSystemValidationError{}

// Validate checks the field values on AddLLMMemoryContextRequest_ContextItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *AddLLMMemoryContextRequest_ContextItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AddLLMMemoryContextRequest_ContextItem with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// AddLLMMemoryContextRequest_ContextItemMultiError, or nil if none found.
func (m *AddLLMMemoryContextRequest_ContextItem) ValidateAll() error {
	return m.validate(true)
}

func (m *AddLLMMemoryContextRequest_ContextItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Role

	// no validation rules for Content

	// no validation rules for DialogueId

	if len(errors) > 0 {
		return AddLLMMemoryContextRequest_ContextItemMultiError(errors)
	}

	return nil
}

// AddLLMMemoryContextRequest_ContextItemMultiError is an error wrapping
// multiple validation errors returned by
// AddLLMMemoryContextRequest_ContextItem.ValidateAll() if the designated
// constraints aren't met.
type AddLLMMemoryContextRequest_ContextItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddLLMMemoryContextRequest_ContextItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddLLMMemoryContextRequest_ContextItemMultiError) AllErrors() []error { return m }

// AddLLMMemoryContextRequest_ContextItemValidationError is the validation
// error returned by AddLLMMemoryContextRequest_ContextItem.Validate if the
// designated constraints aren't met.
type AddLLMMemoryContextRequest_ContextItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddLLMMemoryContextRequest_ContextItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddLLMMemoryContextRequest_ContextItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddLLMMemoryContextRequest_ContextItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddLLMMemoryContextRequest_ContextItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddLLMMemoryContextRequest_ContextItemValidationError) ErrorName() string {
	return "AddLLMMemoryContextRequest_ContextItemValidationError"
}

// Error satisfies the builtin error interface
func (e AddLLMMemoryContextRequest_ContextItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddLLMMemoryContextRequest_ContextItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddLLMMemoryContextRequest_ContextItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddLLMMemoryContextRequest_ContextItemValidationError{}
