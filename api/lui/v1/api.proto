syntax = "proto3";

package api.v1;


import "google/protobuf/struct.proto";
import "google/api/annotations.proto";

option go_package = "api/lui/v1;v1";
option java_multiple_files = true;
option java_package = "api.lui.v1";
option java_outer_classname = "LuiApiProtoV1";

// The greeting service definition.
service LuiContentApi {

  // nlu数据
  rpc LuiNluResult (LuiNluRequest) returns (google.protobuf.Struct)  {
    option (google.api.http) = {
      post: "/v1/lui/nlu"
      body: "*"
    };
  }

  // 二次确认调用
  rpc LuiNlu4Lui (LuiNluRequest) returns (google.protobuf.Struct) {
    option (google.api.http) = {
      post: "/v1/lui/nlu-lui"
      body: "*"
    };
  }

  // sug点击调用
  rpc LuiSug2Lui (LuiSugRequest) returns (google.protobuf.Struct) {
    option (google.api.http) = {
      post: "/v1/lui/sug-lui"
      body: "*"
    };
  }

  // LUI资源 用户选择的版本 from学习系统
  rpc LuiUserChoice (LuiUserChoiceRequest) returns (LuiUserChoiceReply)  {
    option (google.api.http) = {
      post: "/v1/lui/choice"
      body: "*"
    };
  }

  // LUI资源 用户选择的版本 from工具
  rpc LuiToolsUserChoice (LuiToolsUserChoiceRequest) returns (LuiToolsUserChoiceReply)  {
    option (google.api.http) = {
      post: "/v1/lui/tools/choice"
      body: "*"
    };
  }

  rpc QueryTraceFeedback (QueryTraceFeedbackRequest) returns (QueryTraceFeedbackReply)  {
    option (google.api.http) = {
      post: "/v1/lui/trace/feedback"
      body: "*"
    };
  }

  //LuiSimulateQuery
  rpc LuiSimulateQuery (LuiSimulateQueryRequest) returns (google.protobuf.Struct)  {
    option (google.api.http) = {
      post: "/v1/lui/simulate-query"
      body: "*"
    };
  }

  rpc RejectNLU (LuiRejectRequest) returns (google.protobuf.Struct)  {
    option (google.api.http) = {
      post: "/v1/lui/nlu-reject"
      body: "*"
    };
  }

  // 拒识接口上下文呢
  rpc MemoryContext (MemoryContextRequest) returns (MemoryContextReply)  {
    option (google.api.http) = {
      post: "/v1/lui/memory-context"
      body: "*"
    };
  }

  // 用户大模型SUG
  rpc LuiUserLlmSug (LuiUserLlmSugRequest) returns (LuiUserLlmSugReply)  {
    option (google.api.http) = {
      post: "/v1/lui/user-llm-sug"
      body: "*"
    };
  }

  // 全屏对话页上下文
  rpc FullViewContext (FullViewContextRequest) returns (FullViewContextReply) {
    option (google.api.http) = {
      post: "/v1/lui/full-view-context"
      body: "*"
    };
  }

  // 用户大模型Tails
  rpc LuiUserLlmTails (LuiUserLlmTailsRequest) returns (google.protobuf.Struct)  {
    option (google.api.http) = {
      post: "/v1/lui/user-llm-tails"
      body: "*"
    };
  }

  // 用户大模型Tails
  rpc AIEyeFingerImageJudge (AIEyeFingerImageJudgeRequest) returns (google.protobuf.Struct)  {
    option (google.api.http) = {
      post: "/v1/lui/ai-eye-image-judge"
      body: "*"
    };
  }

    // 中文字词确认
    rpc ZhWordConfirm (LuiNluConfirmRequest) returns (google.protobuf.Struct) {
        option (google.api.http) = {
            post: "/v1/lui/zh-word-confirm"
            body: "*"
        };
    }

    rpc UpdateMemoryContextLlmResponse (LlmResponse) returns (google.protobuf.Struct) {
        option (google.api.http) = {
            post: "/v1/lui/updateMemoryContextLlmResponse"
            body: "*"
        };
    }

    rpc GetLLMMemoryContext (LLMMemoryContextRequest) returns (LLMMemoryContextReply) {
        option (google.api.http) = {
            post: "/v1/lui/llm-memory-context"
            body: "*"
        };
    }

    rpc AddLLMMemoryContext (AddLLMMemoryContextRequest) returns (AddLLMMemoryContextReply) {
      option (google.api.http) = {
        post: "/v1/lui/add-llm-memory-context"
        body: "*"
      };
    }

    // 用户主动退出某个场景
    rpc ExitLuiScene (ExitLuiSceneRequest) returns (ExitLuiSceneReply) {
      option (google.api.http) = {
        post: "/v1/lui/exit-lui-scene"
        body: "*"
      };
    }
}

service UCenterApi {
  // 用户信息
  rpc LuiUserInfo (LuiUserInfoRequest) returns (LuiUserInfoReply)  {
    option (google.api.http) = {
      get: "/v1/lui/user-info"
    };
  }
}

message LlmResponse {
    string llm_skill = 1;
    string llm_model = 2;
    string response = 3;
    string image_url = 4;
    string video_url = 5;
    string llm_response = 6;
    string dialogue_id = 7;
    string session_id = 9;
    int32 type = 10;
    string cover = 11;
    string cover_title = 12;
    string url = 13;
    string tts_info = 14;
    string mixed_modal_query = 15;
    google.protobuf.Struct mixed_modal_response = 16;
    string tts_norm = 17;
}

message LuiNluRequest {
  string request_id = 1;
  string asr_info = 7;
  string asr_pinyin = 8;
  string location = 9;
  string grade = 10;
  string tal_id = 11;
  repeated SlotFill slot_fill_list = 12;
  string grade_id = 13;
  string biz_type = 14;
  bool continuous = 15;
  int32 sentence_id = 16;
  string wake_up_type = 17;
  string page_prompt = 18;
  int32 scene_mode = 19;
  LlmAgent llm_agent = 20;
  string rn_version = 21;
  int32 screen_mode = 22;
  repeated google.protobuf.Struct image_prompt = 23;
  string biz_extra = 24;
  string from = 25;
  int64 asr_start_timestamp = 26;
  int64 asr_end_timestamp = 27;
}

message LlmAgent {
  string name = 1;
  int32 status = 2;
}

message SlotFill {
  string key = 1;
  string id = 2;
  string name = 3;
}

message LuiSugRequest {
  message AsrInput {
    int32 asr_len = 1;
    string asr_info = 2;
    string asr_pinyin = 3;
  }

  message UserSystem {
    string location = 1;
    int32 grade = 2;
  }

  string user_id = 1;
  string request_id = 2;
  string session_id = 3;
  int64 timestamp = 4;
  string source = 5;
  string version = 6;
  AsrInput asr_input = 7;
  UserSystem user_system = 8;
  string biz_type = 9;
  int32 scene_mode = 10;
  string rn_version = 11;
  string screenState = 12;
  string biz_extra = 13;
  string from = 14;
  string exercise_ocr = 15;
}

message LuiNluEnsureRequest {

}

message LuiUserInfoRequest {
  string token = 1;
}

message LuiUserInfoReply {
  string tal_id = 2;
  int32 grade = 3;
  string grade_name = 4;
  string nickname = 5;
}

message Any {
  bytes b = 1;
}

message LuiUserChoiceRequest {
  string tal_id = 1;
  int32 course_system = 2;
  int32 subject = 3;
  int32 grade = 4;
  string device_id = 5;
}

message LuiUserChoiceReply {
  repeated LuiUserChoiceReplyItem list = 1;
}

message LuiUserChoiceReplyItem {
  int32 course_system = 1;
  string course_system_alias = 2;
  int32 version = 3;
  string version_name = 4;
  int32 subject = 5;
  string subject_name = 6;
}
message LuiToolsUserChoiceRequest {
  string tal_id = 1;
  int32 subject = 2;
}
message LuiToolsUserChoiceReply {
  string version_name = 1;
}

message QueryTraceFeedbackRequest{
  string session_id = 1;
  int32 feedback_type = 2;
  string feedback = 3;
  repeated int32 feedback_types = 4;
}

message QueryTraceFeedbackReply{

}

message LuiSimulateQueryRequest{
  string asr_info = 1;
  string asr_pinyin = 2;
  string location = 3;
  string grade = 4;
  string grade_id = 5;
  string tal_id = 6;
  string biz_type = 7;
  repeated SlotFill slot_fill_list = 8;
}

message LuiRejectRequest {
  string request_id = 1;
  string device_id = 2;
  string version = 3;
  string asr_info = 4;
  string wakeup_type = 5;
  int32 reject_result = 6;
  bool continuous = 7;
  string tal_id = 8;
  string biz_type = 9;
}

message MemoryContextRequest{
  string tal_id = 1;
  string biz_type = 2;
}

message MemoryContextReply{
  message MemoryContextItem{
    string asr_info = 1;
    string intent = 2;
    string response = 3;
    string tts_info = 4;
    string rewrite_query = 5;
    int64 time_stamp = 6;
    string tts_norm = 7;
  }

  repeated MemoryContextItem list = 1;
}

message LLMMemoryContextRequest {
    string biz_type = 1;
    string current_session_id = 2;
    string tal_id = 3;
    repeated string need_llm_skill = 4;
    string version = 5;
}

message LLMMemoryContextReply{
    message ContextItem {
        string role = 1;
        string content = 2;
        string dialogue_id = 3;
    }

    repeated ContextItem list = 1;
}

message FullViewContextRequest {
    string tal_id = 1;
    string biz_type = 2;
}

message FullViewContextReply{
  message Media {
    int32 type = 1;
    string cover = 2;
    string url = 3;
    string cover_title = 4;
  }
  message XiaosiCommands {
    string title = 1;
    string query = 2;
    repeated CommandApp apps = 3;
  }

  message CommandApp {
    string name = 1;
    string icon = 2;
  }

  message FullViewContextItem{
    string session_id = 1;
    string asr_info = 2;
    string response = 3;
    string tts_info = 4;
    string source = 5;
    string image_url = 6;
    string video_url = 7;
    bool is_llm = 8;
    int32 card_type = 9;
    Media media = 10;
    repeated XiaosiCommands xiaosi_command_list = 11;
    google.protobuf.Struct rn_response = 12;
    string reasoning_content = 13;
    string tts_norm = 14;
  }

  repeated FullViewContextItem list = 1;
}

message LuiUserLlmSugRequest {
  string user_id = 1;
}

message LuiUserLlmSugReply {
  string user_id = 1;
  string sug = 2;
}

message LuiUserLlmTailsRequest {
  string user_id = 1;
  int32 screen_mode = 2;
  string biz_type = 3;
}

message AIEyeFingerImageJudgeRequest {
  int32 biz_type = 1;
  string image_url = 2;
}

//从paas-service的/api/ai/v1/nlu接口迁移过来的，参考paas-service的NluPadReq
message LuiNluConfirmRequest {
    message AsrInput {
        int32 asr_len = 1;
        string asr_info = 2;
        string asr_pinyin = 3;
    }
    message OcrInput {
        int32 ocr_alldoc_len = 1;
        int32 ocr_key_len = 2;
        string ocr_alldoc_info = 3;
        repeated string ocr_key_info = 4;
        repeated string ocr_keyword_info = 5;
        repeated int32 ocr_key_info_offset = 6;
    }
    message ConfirmRequest {
        string confirm_request_intent = 1;
        string confirm_request_input = 2;
        string confirm_request_finger = 3;
        string confirm_request_pinyin = 4;
    }
    message HandleInput {
        string handle_task_type = 1;
        bool is_eng_correct = 2;
        ConfirmRequest confirm_request = 3;
    }
    message UserSystem {
        string location = 1;
        int32 grade = 2;
        string gender = 3;
        string nickname = 4;
        string semester = 5;
        string textbook_version = 6;
    }

    string userId = 1;
    string requestId = 2;
    string sessionId = 3;
    string version = 4;
    AsrInput asr_input = 5;
    UserSystem user_system = 6;
    OcrInput ocr_input = 7;
    HandleInput handle_input = 8;
}

message AddLLMMemoryContextRequest {
  message ContextItem {
    string role = 1;
    string content = 2;
    string dialogue_id = 3;
  }
  string tal_id = 1;
  string session_id = 2;
  repeated ContextItem messages = 3;
  string biz_type = 4;

}

message AddLLMMemoryContextReply {

}

message ExitLuiSceneRequest {
  string biz_type = 1;
  string tal_id = 2;
}

message ExitLuiSceneReply {

}