syntax = "proto3";

package api.v1;
import "errors/errors.proto";

option go_package = "api/lui/v1;v1";
option java_multiple_files = true;
option java_package = "lui.v1.errors";
option objc_class_prefix = "APILUIErrors";

enum ErrorReason {
  option (errors.default_code) = 500;

  USER_NOT_FOUND = 0 [(errors.code) = 404];
  CONTENT_MISSING = 1 [(errors.code) = 400];
  PARAM_ERROR = 2 [(errors.code) = 400];
  INTERNAL_ERROR = 3 [(errors.code) = 500];
}
