// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.2
// - protoc             v5.29.3
// source: api/skill/v1/api.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationSkillApiSkillDetail = "/api.v1.SkillApi/SkillDetail"
const OperationSkillApiSkillList = "/api.v1.SkillApi/SkillList"

type SkillApiHTTPServer interface {
	// SkillDetail skill detail
	SkillDetail(context.Context, *SkillDetailRequest) (*structpb.Struct, error)
	// SkillList skill list
	SkillList(context.Context, *emptypb.Empty) (*structpb.Struct, error)
}

func RegisterSkillApiHTTPServer(s *http.Server, srv SkillApiHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/assist/skill/list", _SkillApi_SkillList0_HTTP_Handler(srv))
	r.POST("/api/v1/assist/skill/detail", _SkillApi_SkillDetail0_HTTP_Handler(srv))
}

func _SkillApi_SkillList0_HTTP_Handler(srv SkillApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSkillApiSkillList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SkillList(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*structpb.Struct)
		return ctx.Result(200, reply)
	}
}

func _SkillApi_SkillDetail0_HTTP_Handler(srv SkillApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SkillDetailRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSkillApiSkillDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SkillDetail(ctx, req.(*SkillDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*structpb.Struct)
		return ctx.Result(200, reply)
	}
}

type SkillApiHTTPClient interface {
	SkillDetail(ctx context.Context, req *SkillDetailRequest, opts ...http.CallOption) (rsp *structpb.Struct, err error)
	SkillList(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *structpb.Struct, err error)
}

type SkillApiHTTPClientImpl struct {
	cc *http.Client
}

func NewSkillApiHTTPClient(client *http.Client) SkillApiHTTPClient {
	return &SkillApiHTTPClientImpl{client}
}

func (c *SkillApiHTTPClientImpl) SkillDetail(ctx context.Context, in *SkillDetailRequest, opts ...http.CallOption) (*structpb.Struct, error) {
	var out structpb.Struct
	pattern := "/api/v1/assist/skill/detail"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSkillApiSkillDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SkillApiHTTPClientImpl) SkillList(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*structpb.Struct, error) {
	var out structpb.Struct
	pattern := "/api/v1/assist/skill/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSkillApiSkillList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
