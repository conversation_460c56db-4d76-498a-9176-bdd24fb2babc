syntax = "proto3";

package api.v1;


import "google/protobuf/struct.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

option go_package = "api/skill/v1;v1";
option java_multiple_files = true;
option java_package = "api.lui.v1";
option java_outer_classname = "SkillApiProtoV1";

// The greeting service definition.
service SkillApi {

  // skill list
  rpc SkillList (.google.protobuf.Empty) returns (google.protobuf.Struct)  {
    option (google.api.http) = {
      post: "/api/v1/assist/skill/list"
      body: "*"
    };
  }

  // skill detail
  rpc SkillDetail (SkillDetailRequest) returns (google.protobuf.Struct) {
    option (google.api.http) = {
      post: "/api/v1/assist/skill/detail"
      body: "*"
    };
  }
}

message SkillDetailRequest {
  string skill_id = 1;
}
