// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: api/skill/v1/api.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	SkillApi_SkillList_FullMethodName   = "/api.v1.SkillApi/SkillList"
	SkillApi_SkillDetail_FullMethodName = "/api.v1.SkillApi/SkillDetail"
)

// SkillApiClient is the client API for SkillApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// The greeting service definition.
type SkillApiClient interface {
	// skill list
	SkillList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*structpb.Struct, error)
	// skill detail
	SkillDetail(ctx context.Context, in *SkillDetailRequest, opts ...grpc.CallOption) (*structpb.Struct, error)
}

type skillApiClient struct {
	cc grpc.ClientConnInterface
}

func NewSkillApiClient(cc grpc.ClientConnInterface) SkillApiClient {
	return &skillApiClient{cc}
}

func (c *skillApiClient) SkillList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*structpb.Struct, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, SkillApi_SkillList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *skillApiClient) SkillDetail(ctx context.Context, in *SkillDetailRequest, opts ...grpc.CallOption) (*structpb.Struct, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, SkillApi_SkillDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SkillApiServer is the server API for SkillApi service.
// All implementations must embed UnimplementedSkillApiServer
// for forward compatibility.
//
// The greeting service definition.
type SkillApiServer interface {
	// skill list
	SkillList(context.Context, *emptypb.Empty) (*structpb.Struct, error)
	// skill detail
	SkillDetail(context.Context, *SkillDetailRequest) (*structpb.Struct, error)
	mustEmbedUnimplementedSkillApiServer()
}

// UnimplementedSkillApiServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSkillApiServer struct{}

func (UnimplementedSkillApiServer) SkillList(context.Context, *emptypb.Empty) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SkillList not implemented")
}
func (UnimplementedSkillApiServer) SkillDetail(context.Context, *SkillDetailRequest) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SkillDetail not implemented")
}
func (UnimplementedSkillApiServer) mustEmbedUnimplementedSkillApiServer() {}
func (UnimplementedSkillApiServer) testEmbeddedByValue()                  {}

// UnsafeSkillApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SkillApiServer will
// result in compilation errors.
type UnsafeSkillApiServer interface {
	mustEmbedUnimplementedSkillApiServer()
}

func RegisterSkillApiServer(s grpc.ServiceRegistrar, srv SkillApiServer) {
	// If the following call pancis, it indicates UnimplementedSkillApiServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SkillApi_ServiceDesc, srv)
}

func _SkillApi_SkillList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SkillApiServer).SkillList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SkillApi_SkillList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SkillApiServer).SkillList(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _SkillApi_SkillDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SkillDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SkillApiServer).SkillDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SkillApi_SkillDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SkillApiServer).SkillDetail(ctx, req.(*SkillDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SkillApi_ServiceDesc is the grpc.ServiceDesc for SkillApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SkillApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.v1.SkillApi",
	HandlerType: (*SkillApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SkillList",
			Handler:    _SkillApi_SkillList_Handler,
		},
		{
			MethodName: "SkillDetail",
			Handler:    _SkillApi_SkillDetail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/skill/v1/api.proto",
}
