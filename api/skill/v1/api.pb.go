// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v5.29.3
// source: api/skill/v1/api.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SkillDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SkillId string `protobuf:"bytes,1,opt,name=skill_id,json=skillId,proto3" json:"skill_id,omitempty"`
}

func (x *SkillDetailRequest) Reset() {
	*x = SkillDetailRequest{}
	mi := &file_api_skill_v1_api_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SkillDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkillDetailRequest) ProtoMessage() {}

func (x *SkillDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_skill_v1_api_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkillDetailRequest.ProtoReflect.Descriptor instead.
func (*SkillDetailRequest) Descriptor() ([]byte, []int) {
	return file_api_skill_v1_api_proto_rawDescGZIP(), []int{0}
}

func (x *SkillDetailRequest) GetSkillId() string {
	if x != nil {
		return x.SkillId
	}
	return ""
}

var File_api_skill_v1_api_proto protoreflect.FileDescriptor

var file_api_skill_v1_api_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2f, 0x0a, 0x12, 0x53, 0x6b, 0x69,
	0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x49, 0x64, 0x32, 0xda, 0x01, 0x0a, 0x08, 0x53,
	0x6b, 0x69, 0x6c, 0x6c, 0x41, 0x70, 0x69, 0x12, 0x62, 0x0a, 0x09, 0x53, 0x6b, 0x69, 0x6c, 0x6c,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x17, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a,
	0x22, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74,
	0x2f, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x6a, 0x0a, 0x0b, 0x53,
	0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x22,
	0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x3a, 0x01, 0x2a, 0x22, 0x1b, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x2f, 0x73, 0x6b, 0x69, 0x6c, 0x6c,
	0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x30, 0x0a, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x6c,
	0x75, 0x69, 0x2e, 0x76, 0x31, 0x42, 0x0f, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x41, 0x70, 0x69, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x56, 0x31, 0x50, 0x01, 0x5a, 0x0f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x6b,
	0x69, 0x6c, 0x6c, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_skill_v1_api_proto_rawDescOnce sync.Once
	file_api_skill_v1_api_proto_rawDescData = file_api_skill_v1_api_proto_rawDesc
)

func file_api_skill_v1_api_proto_rawDescGZIP() []byte {
	file_api_skill_v1_api_proto_rawDescOnce.Do(func() {
		file_api_skill_v1_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_skill_v1_api_proto_rawDescData)
	})
	return file_api_skill_v1_api_proto_rawDescData
}

var file_api_skill_v1_api_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_skill_v1_api_proto_goTypes = []any{
	(*SkillDetailRequest)(nil), // 0: api.v1.SkillDetailRequest
	(*emptypb.Empty)(nil),      // 1: google.protobuf.Empty
	(*structpb.Struct)(nil),    // 2: google.protobuf.Struct
}
var file_api_skill_v1_api_proto_depIdxs = []int32{
	1, // 0: api.v1.SkillApi.SkillList:input_type -> google.protobuf.Empty
	0, // 1: api.v1.SkillApi.SkillDetail:input_type -> api.v1.SkillDetailRequest
	2, // 2: api.v1.SkillApi.SkillList:output_type -> google.protobuf.Struct
	2, // 3: api.v1.SkillApi.SkillDetail:output_type -> google.protobuf.Struct
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_skill_v1_api_proto_init() }
func file_api_skill_v1_api_proto_init() {
	if File_api_skill_v1_api_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_skill_v1_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_skill_v1_api_proto_goTypes,
		DependencyIndexes: file_api_skill_v1_api_proto_depIdxs,
		MessageInfos:      file_api_skill_v1_api_proto_msgTypes,
	}.Build()
	File_api_skill_v1_api_proto = out.File
	file_api_skill_v1_api_proto_rawDesc = nil
	file_api_skill_v1_api_proto_goTypes = nil
	file_api_skill_v1_api_proto_depIdxs = nil
}
